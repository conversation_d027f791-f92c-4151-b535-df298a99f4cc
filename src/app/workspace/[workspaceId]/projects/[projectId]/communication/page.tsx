'use client'

import React, { useState } from 'react'
import { WorkspaceLayout } from '@/components/layout/workspace-layout'
import { useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { MessageSquare, Send, Phone, Mail, Calendar, User, Clock, Plus, Search } from 'lucide-react'

// Mock data structure for communication
interface CommunicationMessage {
  id: string
  type: 'message' | 'email' | 'call' | 'meeting'
  subject?: string
  content: string
  sender: string
  recipient: string
  timestamp: Date
  status: 'sent' | 'delivered' | 'read'
  attachments?: string[]
  isFromClient: boolean
}

export default function CommunicationPage() {
  const params = useParams()
  const { workspaceId, projectId } = params

  // Mock communication data
  const [messages, setMessages] = useState<CommunicationMessage[]>([
    {
      id: '1',
      type: 'email',
      subject: 'Project Kickoff - Welcome to DevHQ!',
      content: 'Hi <PERSON>,\n\nWelcome to the DevHQ project! I\'m excited to work with you on this. I\'ve reviewed the requirements document and have a few questions:\n\n1. Do you have a preferred color scheme?\n2. Any specific fonts or branding guidelines?\n3. Timeline for the first milestone review?\n\nLooking forward to creating something amazing together!\n\nBest regards,\nSarah Johnson',
      sender: 'Sarah Johnson (Client)',
      recipient: 'John Doe',
      timestamp: new Date('2024-01-08T09:30:00'),
      status: 'read',
      isFromClient: true
    },
    {
      id: '2',
      type: 'message',
      content: 'Thanks for the warm welcome! I\'ve prepared some initial wireframes based on our discussion. The color scheme follows your brand guidelines - primarily blue (#2563eb) with green accents (#10b981). I\'ll send over the wireframes for your review by end of day.',
      sender: 'John Doe',
      recipient: 'Sarah Johnson (Client)',
      timestamp: new Date('2024-01-08T14:15:00'),
      status: 'read',
      isFromClient: false
    },
    {
      id: '3',
      type: 'call',
      subject: 'Design Review Call',
      content: 'Discussed homepage wireframes and overall design direction. Client loves the clean, modern approach. Requested to add a testimonials section and make the CTA buttons more prominent. Duration: 45 minutes.',
      sender: 'John Doe',
      recipient: 'Sarah Johnson (Client)',
      timestamp: new Date('2024-01-12T15:00:00'),
      status: 'delivered',
      isFromClient: false
    },
    {
      id: '4',
      type: 'email',
      subject: 'Wireframe Feedback',
      content: 'Hi John,\n\nI\'ve reviewed the wireframes and they look fantastic! A few minor adjustments:\n\n- Can we make the search bar more prominent in the header?\n- The testimonials section is perfect\n- Could we add a dark mode toggle option?\n\nOverall, I\'m very happy with the direction. When can we expect the high-fidelity designs?\n\nThanks!\nSarah',
      sender: 'Sarah Johnson (Client)',
      recipient: 'John Doe',
      timestamp: new Date('2024-01-15T11:20:00'),
      status: 'read',
      isFromClient: true
    },
    {
      id: '5',
      type: 'message',
      content: 'Great feedback! I\'ll implement those changes today. The dark mode toggle is a great idea - I\'ll include that in the design system. High-fidelity designs should be ready by Friday. I\'ll also prepare an interactive prototype so you can test the user flow.',
      sender: 'John Doe',
      recipient: 'Sarah Johnson (Client)',
      timestamp: new Date('2024-01-15T16:45:00'),
      status: 'delivered',
      isFromClient: false
    },
    {
      id: '6',
      type: 'meeting',
      subject: 'Weekly Progress Check-in',
      content: 'Weekly status meeting scheduled for tomorrow at 2 PM EST. Agenda:\n- Review completed wireframes\n- Discuss development timeline\n- Plan next milestone deliverables\n- Q&A session',
      sender: 'John Doe',
      recipient: 'Sarah Johnson (Client)',
      timestamp: new Date('2024-01-18T10:00:00'),
      status: 'sent',
      isFromClient: false
    }
  ])

  const [newMessage, setNewMessage] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail className="h-4 w-4 text-blue-400" />
      case 'call':
        return <Phone className="h-4 w-4 text-green-400" />
      case 'meeting':
        return <Calendar className="h-4 w-4 text-purple-400" />
      default:
        return <MessageSquare className="h-4 w-4 text-gray-400" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'email':
        return 'text-blue-400 border-blue-400'
      case 'call':
        return 'text-green-400 border-green-400'
      case 'meeting':
        return 'text-purple-400 border-purple-400'
      default:
        return 'text-gray-400 border-gray-400'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'read':
        return 'text-green-400'
      case 'delivered':
        return 'text-blue-400'
      default:
        return 'text-gray-400'
    }
  }

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.sender.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || message.type === selectedType
    return matchesSearch && matchesType
  })

  const messageTypes = [
    { key: 'all', label: 'All', count: messages.length },
    { key: 'message', label: 'Messages', count: messages.filter(m => m.type === 'message').length },
    { key: 'email', label: 'Emails', count: messages.filter(m => m.type === 'email').length },
    { key: 'call', label: 'Calls', count: messages.filter(m => m.type === 'call').length },
    { key: 'meeting', label: 'Meetings', count: messages.filter(m => m.type === 'meeting').length }
  ]

  const totalMessages = messages.length
  const clientMessages = messages.filter(m => m.isFromClient).length
  const unreadMessages = messages.filter(m => m.status !== 'read').length

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message: CommunicationMessage = {
        id: Date.now().toString(),
        type: 'message',
        content: newMessage,
        sender: 'John Doe',
        recipient: 'Sarah Johnson (Client)',
        timestamp: new Date(),
        status: 'sent',
        isFromClient: false
      }
      setMessages(prev => [message, ...prev])
      setNewMessage('')
    }
  }

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 space-y-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-white flex items-center gap-2">
              <MessageSquare className="h-6 w-6 text-blue-400" />
              Communication Hub
            </h1>
            <p className="text-sm text-gray-400 mt-1">
              Centralized client communication and project updates
            </p>
          </div>
          <Button className="bg-green-500 hover:bg-green-600 text-black">
            <Plus className="h-4 w-4 mr-2" />
            New Message
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <MessageSquare className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{totalMessages}</div>
                  <div className="text-sm text-gray-400">Total Messages</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <User className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{clientMessages}</div>
                  <div className="text-sm text-gray-400">From Client</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
                  <Clock className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{unreadMessages}</div>
                  <div className="text-sm text-gray-400">Unread</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search messages, subjects, or contacts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-transparent border-white/20 text-white"
                />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-2">
                {messageTypes.map((type) => (
                  <Button
                    key={type.key}
                    variant={selectedType === type.key ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedType(type.key)}
                    className="text-xs"
                  >
                    {type.label} ({type.count})
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Message */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white text-lg">Send Quick Message</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-3">
              <Input
                placeholder="Type your message to the client..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                className="flex-1 bg-transparent border-white/20 text-white"
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              />
              <Button onClick={handleSendMessage} className="bg-blue-500 hover:bg-blue-600">
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Messages Timeline */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Communication Timeline</CardTitle>
            <CardDescription>
              All project communication in chronological order
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredMessages.map((message) => (
                <div key={message.id} className="border border-white/10 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${message.isFromClient ? 'bg-blue-500/20' : 'bg-green-500/20'}`}>
                      {getTypeIcon(message.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-medium">
                          {message.subject || `${message.type.charAt(0).toUpperCase() + message.type.slice(1)} from ${message.sender}`}
                        </h3>
                        <Badge variant="outline" className={`text-xs ${getTypeColor(message.type)}`}>
                          {message.type}
                        </Badge>
                        <Badge variant="outline" className={`text-xs ${message.isFromClient ? 'text-blue-400 border-blue-400' : 'text-green-400 border-green-400'}`}>
                          {message.isFromClient ? 'Client' : 'You'}
                        </Badge>
                      </div>
                      <p className="text-gray-300 text-sm mb-3 whitespace-pre-line">
                        {message.content}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {message.sender}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {message.timestamp.toLocaleDateString()} at {message.timestamp.toLocaleTimeString()}
                        </div>
                        <div className={`flex items-center gap-1 ${getStatusColor(message.status)}`}>
                          <span className="w-2 h-2 rounded-full bg-current"></span>
                          {message.status}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredMessages.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No messages found. Start a conversation with your client!</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Communication Guidelines */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white text-lg">Communication Best Practices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="text-white font-medium mb-2">Professional Communication</h4>
                <ul className="text-gray-400 space-y-1">
                  <li>• Respond to client messages within 24 hours</li>
                  <li>• Use clear, jargon-free language</li>
                  <li>• Provide regular project updates</li>
                  <li>• Document important decisions</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Project Updates</h4>
                <ul className="text-gray-400 space-y-1">
                  <li>• Send weekly progress summaries</li>
                  <li>• Share milestone completions immediately</li>
                  <li>• Proactively communicate any delays</li>
                  <li>• Include visual progress when possible</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </WorkspaceLayout>
  )
}
