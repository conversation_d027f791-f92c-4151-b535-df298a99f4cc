'use client'

import React, { useState } from 'react'
import { WorkspaceLayout } from '@/components/layout/workspace-layout'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Clock, XCircle, MessageSquare, Upload, Eye, Plus } from 'lucide-react'

// Mock data structure based on technical decisions
interface ClientApproval {
  id: string
  deliverableTitle: string
  status: 'pending' | 'approved' | 'revision_requested'
  submittedAt: Date
  clientFeedback?: string
  approvedAt?: Date
  version: number
  description: string
  attachments: string[]
  milestoneId?: string
}

export default function ClientApprovalsPage() {
  const params = useParams()
  const { workspaceId, projectId } = params

  // Mock approvals data
  const [approvals, setApprovals] = useState<ClientApproval[]>([
    {
      id: '1',
      deliverableTitle: 'Homepage Design Mockups',
      status: 'approved',
      submittedAt: new Date('2024-01-10T14:30:00'),
      approvedAt: new Date('2024-01-12T09:15:00'),
      version: 2,
      description: 'Complete homepage design with responsive layouts for desktop and mobile',
      attachments: ['homepage-desktop.png', 'homepage-mobile.png', 'style-guide.pdf'],
      milestoneId: 'milestone-1'
    },
    {
      id: '2',
      deliverableTitle: 'User Authentication Flow',
      status: 'revision_requested',
      submittedAt: new Date('2024-01-15T16:45:00'),
      clientFeedback: 'The login form looks great, but could we add a "Remember Me" checkbox? Also, the forgot password link should be more prominent.',
      version: 1,
      description: 'Complete user authentication system including login, registration, and password reset',
      attachments: ['auth-flow.png', 'wireframes.pdf'],
      milestoneId: 'milestone-2'
    },
    {
      id: '3',
      deliverableTitle: 'Dashboard Wireframes',
      status: 'pending',
      submittedAt: new Date('2024-01-18T11:20:00'),
      version: 1,
      description: 'Initial wireframes for the main dashboard showing key metrics and navigation',
      attachments: ['dashboard-wireframe.png', 'navigation-flow.pdf'],
      milestoneId: 'milestone-2'
    }
  ])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-400" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-400" />
      case 'revision_requested':
        return <XCircle className="h-5 w-5 text-red-400" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-400 border-green-400'
      case 'pending':
        return 'text-yellow-400 border-yellow-400'
      case 'revision_requested':
        return 'text-red-400 border-red-400'
      default:
        return 'text-gray-400 border-gray-400'
    }
  }

  const pendingCount = approvals.filter(a => a.status === 'pending').length
  const approvedCount = approvals.filter(a => a.status === 'approved').length
  const revisionCount = approvals.filter(a => a.status === 'revision_requested').length

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 space-y-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-white flex items-center gap-2">
              <CheckCircle className="h-6 w-6 text-green-400" />
              Client Approvals
            </h1>
            <p className="text-sm text-gray-400 mt-1">
              Track deliverable submissions and client feedback
            </p>
          </div>
          <Button className="bg-green-500 hover:bg-green-600 text-black">
            <Plus className="h-4 w-4 mr-2" />
            Submit for Approval
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
                  <Clock className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{pendingCount}</div>
                  <div className="text-sm text-gray-400">Pending Review</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{approvedCount}</div>
                  <div className="text-sm text-gray-400">Approved</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center">
                  <XCircle className="h-5 w-5 text-red-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{revisionCount}</div>
                  <div className="text-sm text-gray-400">Needs Revision</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Approvals List */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Deliverable Submissions</CardTitle>
            <CardDescription>
              Track the status of your submitted work and client feedback
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {approvals.map((approval) => (
                <div key={approval.id} className="border border-white/10 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(approval.status)}
                        <h3 className="text-white font-medium">{approval.deliverableTitle}</h3>
                        <Badge variant="outline" className={`text-xs ${getStatusColor(approval.status)}`}>
                          {approval.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          v{approval.version}
                        </Badge>
                      </div>
                      <p className="text-gray-400 text-sm mb-3">{approval.description}</p>
                      
                      {/* Attachments */}
                      <div className="flex items-center gap-2 mb-3">
                        <span className="text-xs text-gray-500">Attachments:</span>
                        {approval.attachments.map((attachment, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {attachment}
                          </Badge>
                        ))}
                      </div>

                      {/* Timeline */}
                      <div className="text-xs text-gray-500">
                        Submitted: {approval.submittedAt.toLocaleDateString()} at {approval.submittedAt.toLocaleTimeString()}
                        {approval.approvedAt && (
                          <span className="ml-4">
                            Approved: {approval.approvedAt.toLocaleDateString()} at {approval.approvedAt.toLocaleTimeString()}
                          </span>
                        )}
                      </div>

                      {/* Client Feedback */}
                      {approval.clientFeedback && (
                        <div className="mt-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                          <div className="flex items-start gap-2">
                            <MessageSquare className="h-4 w-4 text-red-400 mt-0.5" />
                            <div>
                              <div className="text-sm font-medium text-red-300 mb-1">Client Feedback:</div>
                              <p className="text-sm text-red-200">{approval.clientFeedback}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      {approval.status === 'revision_requested' && (
                        <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white">
                          <Upload className="h-4 w-4 mr-2" />
                          Resubmit
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {approvals.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No submissions yet. Submit your first deliverable for client review!</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Approval Workflow Guide */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white text-lg">How Approvals Work</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mx-auto mb-3">
                  <Upload className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-white font-medium mb-2">1. Submit</h3>
                <p className="text-sm text-gray-400">Upload your deliverable and request client approval</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center mx-auto mb-3">
                  <Clock className="h-6 w-6 text-yellow-400" />
                </div>
                <h3 className="text-white font-medium mb-2">2. Review</h3>
                <p className="text-sm text-gray-400">Client reviews your work and provides feedback</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
                <h3 className="text-white font-medium mb-2">3. Approve</h3>
                <p className="text-sm text-gray-400">Get approval or make revisions based on feedback</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </WorkspaceLayout>
  )
}
