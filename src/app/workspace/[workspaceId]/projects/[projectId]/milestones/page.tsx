'use client'

import React, { useState } from 'react'
import { WorkspaceLayout } from '@/components/layout/workspace-layout'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Target, CheckCircle, Clock, AlertTriangle, Calendar, DollarSign, Plus, Play } from 'lucide-react'

// Mock data structure for milestones
interface Milestone {
  id: string
  name: string
  description: string
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue'
  startDate: Date
  dueDate: Date
  completedDate?: Date
  estimatedHours: number
  actualHours: number
  allocatedBudget: number
  spentBudget: number
  deliverables: Deliverable[]
  progress: number
}

interface Deliverable {
  id: string
  name: string
  status: 'pending' | 'in_progress' | 'completed'
  assignee: string
}

export default function MilestonesPage() {
  const params = useParams()
  const { workspaceId, projectId } = params

  // Mock milestones data
  const [milestones, setMilestones] = useState<Milestone[]>([
    {
      id: '1',
      name: 'Discovery & Planning',
      description: 'Requirements gathering, wireframes, and project setup',
      status: 'completed',
      startDate: new Date('2024-01-08'),
      dueDate: new Date('2024-01-15'),
      completedDate: new Date('2024-01-14'),
      estimatedHours: 40,
      actualHours: 38,
      allocatedBudget: 3000,
      spentBudget: 2850,
      progress: 100,
      deliverables: [
        { id: '1', name: 'Project Requirements Document', status: 'completed', assignee: 'John Doe' },
        { id: '2', name: 'Initial Wireframes', status: 'completed', assignee: 'John Doe' },
        { id: '3', name: 'Technical Architecture Plan', status: 'completed', assignee: 'John Doe' }
      ]
    },
    {
      id: '2',
      name: 'Design & Prototyping',
      description: 'UI/UX design, interactive prototypes, and client review',
      status: 'in_progress',
      startDate: new Date('2024-01-15'),
      dueDate: new Date('2024-02-01'),
      estimatedHours: 60,
      actualHours: 32,
      allocatedBudget: 4500,
      spentBudget: 2400,
      progress: 65,
      deliverables: [
        { id: '4', name: 'Homepage Design', status: 'completed', assignee: 'John Doe' },
        { id: '5', name: 'Dashboard Wireframes', status: 'in_progress', assignee: 'John Doe' },
        { id: '6', name: 'Interactive Prototype', status: 'pending', assignee: 'John Doe' },
        { id: '7', name: 'Design System Documentation', status: 'pending', assignee: 'John Doe' }
      ]
    },
    {
      id: '3',
      name: 'Development Phase 1',
      description: 'Core functionality, database setup, and API development',
      status: 'not_started',
      startDate: new Date('2024-02-01'),
      dueDate: new Date('2024-02-20'),
      estimatedHours: 80,
      actualHours: 0,
      allocatedBudget: 6000,
      spentBudget: 0,
      progress: 0,
      deliverables: [
        { id: '8', name: 'Database Schema Implementation', status: 'pending', assignee: 'John Doe' },
        { id: '9', name: 'Authentication System', status: 'pending', assignee: 'John Doe' },
        { id: '10', name: 'Core API Endpoints', status: 'pending', assignee: 'John Doe' },
        { id: '11', name: 'Frontend Components', status: 'pending', assignee: 'John Doe' }
      ]
    },
    {
      id: '4',
      name: 'Testing & Deployment',
      description: 'Quality assurance, deployment setup, and client training',
      status: 'not_started',
      startDate: new Date('2024-02-20'),
      dueDate: new Date('2024-03-01'),
      estimatedHours: 20,
      actualHours: 0,
      allocatedBudget: 1500,
      spentBudget: 0,
      progress: 0,
      deliverables: [
        { id: '12', name: 'Testing Suite', status: 'pending', assignee: 'John Doe' },
        { id: '13', name: 'Production Deployment', status: 'pending', assignee: 'John Doe' },
        { id: '14', name: 'Client Training Materials', status: 'pending', assignee: 'John Doe' }
      ]
    }
  ])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-400" />
      case 'in_progress':
        return <Play className="h-5 w-5 text-blue-400" />
      case 'overdue':
        return <AlertTriangle className="h-5 w-5 text-red-400" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 border-green-400'
      case 'in_progress':
        return 'text-blue-400 border-blue-400'
      case 'overdue':
        return 'text-red-400 border-red-400'
      default:
        return 'text-gray-400 border-gray-400'
    }
  }

  const getDeliverableStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 border-green-400'
      case 'in_progress':
        return 'text-blue-400 border-blue-400'
      default:
        return 'text-gray-400 border-gray-400'
    }
  }

  const completedMilestones = milestones.filter(m => m.status === 'completed').length
  const totalMilestones = milestones.length
  const overallProgress = (completedMilestones / totalMilestones) * 100

  const totalBudget = milestones.reduce((sum, m) => sum + m.allocatedBudget, 0)
  const spentBudget = milestones.reduce((sum, m) => sum + m.spentBudget, 0)

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 space-y-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-white flex items-center gap-2">
              <Target className="h-6 w-6 text-green-400" />
              Project Milestones
            </h1>
            <p className="text-sm text-gray-400 mt-1">
              Track project phases and deliverable completion
            </p>
          </div>
          <Button className="bg-green-500 hover:bg-green-600 text-black">
            <Plus className="h-4 w-4 mr-2" />
            Add Milestone
          </Button>
        </div>

        {/* Progress Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Target className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{completedMilestones}/{totalMilestones}</div>
                  <div className="text-sm text-gray-400">Milestones</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{overallProgress.toFixed(0)}%</div>
                  <div className="text-sm text-gray-400">Overall Progress</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">${spentBudget.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Budget Spent</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {milestones.filter(m => m.status === 'in_progress').length}
                  </div>
                  <div className="text-sm text-gray-400">Active</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Overall Progress Bar */}
        <Card className="bg-black/20 border-white/10">
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-white font-medium">Project Progress</span>
              <span className="text-white">{overallProgress.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-3">
              <div 
                className="h-3 rounded-full bg-green-500"
                style={{ width: `${overallProgress}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        {/* Milestones Timeline */}
        <div className="space-y-6">
          {milestones.map((milestone, index) => (
            <Card key={milestone.id} className="bg-black/20 border-white/10">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center text-green-400 font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <CardTitle className="text-white flex items-center gap-2">
                        {getStatusIcon(milestone.status)}
                        {milestone.name}
                        <Badge variant="outline" className={`text-xs ${getStatusColor(milestone.status)}`}>
                          {milestone.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </CardTitle>
                      <CardDescription className="mt-1">
                        {milestone.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-white">{milestone.progress}%</div>
                    <div className="text-sm text-gray-400">Complete</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${milestone.status === 'completed' ? 'bg-green-500' : milestone.status === 'in_progress' ? 'bg-blue-500' : 'bg-gray-500'}`}
                      style={{ width: `${milestone.progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Milestone Details */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-gray-400">Timeline</div>
                    <div className="text-white text-sm">
                      {milestone.startDate.toLocaleDateString()} - {milestone.dueDate.toLocaleDateString()}
                    </div>
                    {milestone.completedDate && (
                      <div className="text-green-400 text-xs">
                        Completed: {milestone.completedDate.toLocaleDateString()}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Hours</div>
                    <div className="text-white text-sm">
                      {milestone.actualHours}h / {milestone.estimatedHours}h
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Budget</div>
                    <div className="text-white text-sm">
                      ${milestone.spentBudget.toLocaleString()} / ${milestone.allocatedBudget.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Deliverables</div>
                    <div className="text-white text-sm">
                      {milestone.deliverables.filter(d => d.status === 'completed').length} / {milestone.deliverables.length}
                    </div>
                  </div>
                </div>

                {/* Deliverables */}
                <div>
                  <h4 className="text-white font-medium mb-3">Deliverables</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {milestone.deliverables.map((deliverable) => (
                      <div key={deliverable.id} className="border border-white/10 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h5 className="text-white text-sm font-medium">{deliverable.name}</h5>
                              <Badge variant="outline" className={`text-xs ${getDeliverableStatusColor(deliverable.status)}`}>
                                {deliverable.status.replace('_', ' ')}
                              </Badge>
                            </div>
                            <div className="text-xs text-gray-400">
                              Assigned to: {deliverable.assignee}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Milestone Planning Tips */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white text-lg">Milestone Best Practices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="text-white font-medium mb-2">Planning Tips</h4>
                <ul className="text-gray-400 space-y-1">
                  <li>• Break large tasks into smaller deliverables</li>
                  <li>• Set realistic timelines with buffer time</li>
                  <li>• Define clear success criteria</li>
                  <li>• Include client review checkpoints</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Tracking Tips</h4>
                <ul className="text-gray-400 space-y-1">
                  <li>• Update progress regularly</li>
                  <li>• Track actual vs estimated hours</li>
                  <li>• Document lessons learned</li>
                  <li>• Celebrate milestone completions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </WorkspaceLayout>
  )
}
