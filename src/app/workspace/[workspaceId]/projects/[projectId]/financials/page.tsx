'use client'

import React, { useState } from 'react'
import { WorkspaceLayout } from '@/components/layout/workspace-layout'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DollarSign, TrendingUp, TrendingDown, AlertTriangle, Target, Clock, Calculator } from 'lucide-react'

// Mock data structure for financial tracking
interface ProjectFinancials {
  totalBudget: number
  spentAmount: number
  remainingBudget: number
  totalRevenue: number
  expenses: Expense[]
  profitMargin: number
  hourlyRate: number
  totalHours: number
  billableHours: number
}

interface Expense {
  id: string
  description: string
  amount: number
  category: 'software' | 'hardware' | 'service' | 'other'
  date: Date
  isReimbursable: boolean
}

export default function FinancialsPage() {
  const params = useParams()
  const { workspaceId, projectId } = params

  // Mock financial data
  const [financials, setFinancials] = useState<ProjectFinancials>({
    totalBudget: 15000,
    spentAmount: 8750,
    remainingBudget: 6250,
    totalRevenue: 8750,
    profitMargin: 68.5,
    hourlyRate: 75,
    totalHours: 125,
    billableHours: 117,
    expenses: [
      {
        id: '1',
        description: 'Figma Pro Subscription',
        amount: 144,
        category: 'software',
        date: new Date('2024-01-01'),
        isReimbursable: false
      },
      {
        id: '2',
        description: 'Stock Photos License',
        amount: 89,
        category: 'service',
        date: new Date('2024-01-15'),
        isReimbursable: true
      },
      {
        id: '3',
        description: 'Domain Registration',
        amount: 12,
        category: 'service',
        date: new Date('2024-01-10'),
        isReimbursable: true
      }
    ]
  })

  const totalExpenses = financials.expenses.reduce((sum, expense) => sum + expense.amount, 0)
  const reimbursableExpenses = financials.expenses.filter(e => e.isReimbursable).reduce((sum, expense) => sum + expense.amount, 0)
  const netProfit = financials.totalRevenue - totalExpenses
  const profitMargin = ((netProfit / financials.totalRevenue) * 100)
  const budgetUtilization = ((financials.spentAmount / financials.totalBudget) * 100)
  const effectiveHourlyRate = financials.totalRevenue / financials.billableHours

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'software':
        return 'text-blue-400 border-blue-400'
      case 'hardware':
        return 'text-purple-400 border-purple-400'
      case 'service':
        return 'text-green-400 border-green-400'
      default:
        return 'text-gray-400 border-gray-400'
    }
  }

  const getHealthStatus = (percentage: number) => {
    if (percentage > 90) return { color: 'text-red-400', icon: TrendingDown }
    if (percentage > 75) return { color: 'text-yellow-400', icon: AlertTriangle }
    return { color: 'text-green-400', icon: TrendingUp }
  }

  const budgetHealth = getHealthStatus(budgetUtilization)
  const BudgetIcon = budgetHealth.icon

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 space-y-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-white flex items-center gap-2">
              <DollarSign className="h-6 w-6 text-green-400" />
              Financial Dashboard
            </h1>
            <p className="text-sm text-gray-400 mt-1">
              Track project profitability and financial health
            </p>
          </div>
          <Badge variant="outline" className={profitMargin > 60 ? 'text-green-400 border-green-400' : profitMargin > 30 ? 'text-yellow-400 border-yellow-400' : 'text-red-400 border-red-400'}>
            {profitMargin.toFixed(1)}% Profit Margin
          </Badge>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">${financials.totalRevenue.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Total Revenue</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <Target className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">${financials.remainingBudget.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Remaining Budget</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">${netProfit.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Net Profit</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
                  <Calculator className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">${effectiveHourlyRate.toFixed(0)}</div>
                  <div className="text-sm text-gray-400">Effective Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Budget Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-black/20 border-white/10">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-400" />
                Budget Analysis
              </CardTitle>
              <CardDescription>
                Track spending against your project budget
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Budget Utilization</span>
                  <div className="flex items-center gap-2">
                    <BudgetIcon className={`h-4 w-4 ${budgetHealth.color}`} />
                    <span className="text-white">{budgetUtilization.toFixed(1)}%</span>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-3">
                  <div 
                    className={`h-3 rounded-full ${budgetUtilization > 90 ? 'bg-red-500' : budgetUtilization > 75 ? 'bg-yellow-500' : 'bg-green-500'}`}
                    style={{ width: `${Math.min(budgetUtilization, 100)}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-white/10">
                <div className="text-center">
                  <div className="text-xl font-bold text-white">${financials.spentAmount.toLocaleString()}</div>
                  <div className="text-xs text-gray-400">Spent</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-white">${financials.totalBudget.toLocaleString()}</div>
                  <div className="text-xs text-gray-400">Total Budget</div>
                </div>
              </div>

              {budgetUtilization > 85 && (
                <div className="flex items-center gap-2 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span className="text-sm text-yellow-300">
                    Approaching budget limit. Monitor spending carefully.
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-400" />
                Profitability Analysis
              </CardTitle>
              <CardDescription>
                Understand your project's financial performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-400">{profitMargin.toFixed(1)}%</div>
                  <div className="text-xs text-gray-400">Profit Margin</div>
                </div>
                <div className="text-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-400">{financials.billableHours}h</div>
                  <div className="text-xs text-gray-400">Billable Hours</div>
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t border-white/10">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Revenue</span>
                  <span className="text-green-400">${financials.totalRevenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Expenses</span>
                  <span className="text-red-400">-${totalExpenses.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm font-medium pt-2 border-t border-white/10">
                  <span className="text-white">Net Profit</span>
                  <span className="text-green-400">${netProfit.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Expenses */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-red-400" />
              Project Expenses
            </CardTitle>
            <CardDescription>
              Track costs and reimbursable expenses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {financials.expenses.map((expense) => (
                <div key={expense.id} className="border border-white/10 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-medium">{expense.description}</h3>
                        <Badge variant="outline" className={`text-xs ${getCategoryColor(expense.category)}`}>
                          {expense.category}
                        </Badge>
                        {expense.isReimbursable && (
                          <Badge variant="outline" className="text-xs text-green-400 border-green-400">
                            Reimbursable
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-400">
                        {expense.date.toLocaleDateString()}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold text-white">${expense.amount}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t border-white/10">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-white">${totalExpenses}</div>
                  <div className="text-sm text-gray-400">Total Expenses</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">${reimbursableExpenses}</div>
                  <div className="text-sm text-gray-400">Reimbursable</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{financials.expenses.length}</div>
                  <div className="text-sm text-gray-400">Line Items</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial Health Indicators */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white text-lg">Financial Health Indicators</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${profitMargin > 60 ? 'bg-green-500/20' : profitMargin > 30 ? 'bg-yellow-500/20' : 'bg-red-500/20'}`}>
                  <TrendingUp className={`h-6 w-6 ${profitMargin > 60 ? 'text-green-400' : profitMargin > 30 ? 'text-yellow-400' : 'text-red-400'}`} />
                </div>
                <h3 className="text-white font-medium mb-2">Profitability</h3>
                <p className="text-sm text-gray-400">
                  {profitMargin > 60 ? 'Excellent' : profitMargin > 30 ? 'Good' : 'Needs Attention'}
                </p>
              </div>
              <div className="text-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${budgetUtilization < 75 ? 'bg-green-500/20' : budgetUtilization < 90 ? 'bg-yellow-500/20' : 'bg-red-500/20'}`}>
                  <Target className={`h-6 w-6 ${budgetUtilization < 75 ? 'text-green-400' : budgetUtilization < 90 ? 'text-yellow-400' : 'text-red-400'}`} />
                </div>
                <h3 className="text-white font-medium mb-2">Budget Control</h3>
                <p className="text-sm text-gray-400">
                  {budgetUtilization < 75 ? 'On Track' : budgetUtilization < 90 ? 'Monitor' : 'Over Budget'}
                </p>
              </div>
              <div className="text-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${effectiveHourlyRate >= financials.hourlyRate ? 'bg-green-500/20' : 'bg-yellow-500/20'}`}>
                  <Clock className={`h-6 w-6 ${effectiveHourlyRate >= financials.hourlyRate ? 'text-green-400' : 'text-yellow-400'}`} />
                </div>
                <h3 className="text-white font-medium mb-2">Rate Efficiency</h3>
                <p className="text-sm text-gray-400">
                  {effectiveHourlyRate >= financials.hourlyRate ? 'Meeting Target' : 'Below Target'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </WorkspaceLayout>
  )
}
