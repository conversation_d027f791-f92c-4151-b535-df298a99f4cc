'use client'

import React, { useState, useEffect } from 'react'
import { WorkspaceLayout } from '@/components/layout/workspace-layout'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Play, Pause, Square, Clock, DollarSign, Calendar, Plus, Edit } from 'lucide-react'

// Mock data structure based on technical decisions
interface TimeEntry {
  id: string
  startTime: Date
  endTime?: Date
  durationMinutes: number
  description: string
  isBillable: boolean
  hourlyRate: number
  billableAmount: number
  taskId?: string
  date: string
}

export default function TimeTrackingPage() {
  const params = useParams()
  const { workspaceId, projectId } = params

  // Timer state
  const [isRunning, setIsRunning] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [currentDescription, setCurrentDescription] = useState('')

  // Mock time entries data
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([
    {
      id: '1',
      startTime: new Date('2024-01-15T09:00:00'),
      endTime: new Date('2024-01-15T12:30:00'),
      durationMinutes: 210,
      description: 'Frontend component development',
      isBillable: true,
      hourlyRate: 75,
      billableAmount: 262.50,
      date: '2024-01-15'
    },
    {
      id: '2',
      startTime: new Date('2024-01-15T14:00:00'),
      endTime: new Date('2024-01-15T16:45:00'),
      durationMinutes: 165,
      description: 'API integration and testing',
      isBillable: true,
      hourlyRate: 75,
      billableAmount: 206.25,
      date: '2024-01-15'
    },
    {
      id: '3',
      startTime: new Date('2024-01-16T10:30:00'),
      endTime: new Date('2024-01-16T11:30:00'),
      durationMinutes: 60,
      description: 'Team meeting and planning',
      isBillable: false,
      hourlyRate: 75,
      billableAmount: 0,
      date: '2024-01-16'
    }
  ])

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isRunning) {
      interval = setInterval(() => {
        setCurrentTime(prev => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isRunning])

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  const handleStartTimer = () => {
    setIsRunning(true)
  }

  const handlePauseTimer = () => {
    setIsRunning(false)
  }

  const handleStopTimer = () => {
    if (currentTime > 0) {
      const newEntry: TimeEntry = {
        id: Date.now().toString(),
        startTime: new Date(Date.now() - currentTime * 1000),
        endTime: new Date(),
        durationMinutes: Math.floor(currentTime / 60),
        description: currentDescription || 'Untitled work session',
        isBillable: true,
        hourlyRate: 75,
        billableAmount: (Math.floor(currentTime / 60) / 60) * 75,
        date: new Date().toISOString().split('T')[0]
      }
      setTimeEntries(prev => [newEntry, ...prev])
    }
    setIsRunning(false)
    setCurrentTime(0)
    setCurrentDescription('')
  }

  // Calculate totals
  const totalHours = timeEntries.reduce((sum, entry) => sum + entry.durationMinutes, 0) / 60
  const billableHours = timeEntries.filter(entry => entry.isBillable).reduce((sum, entry) => sum + entry.durationMinutes, 0) / 60
  const totalRevenue = timeEntries.reduce((sum, entry) => sum + entry.billableAmount, 0)

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 space-y-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-white flex items-center gap-2">
              <Clock className="h-6 w-6 text-blue-400" />
              Time Tracking
            </h1>
            <p className="text-sm text-gray-400 mt-1">
              Track your time with precision and ease
            </p>
          </div>
          <Button className="bg-green-500 hover:bg-green-600 text-black">
            <Plus className="h-4 w-4 mr-2" />
            Manual Entry
          </Button>
        </div>

        {/* Timer Widget */}
        <Card className="bg-black/20 border-white/10">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="text-4xl font-mono font-bold text-white mb-2">
                  {formatTime(currentTime)}
                </div>
                <Input
                  placeholder="What are you working on?"
                  value={currentDescription}
                  onChange={(e) => setCurrentDescription(e.target.value)}
                  className="bg-transparent border-white/20 text-white placeholder-gray-500"
                />
              </div>
              <div className="flex items-center gap-3 ml-6">
                {!isRunning ? (
                  <Button
                    onClick={handleStartTimer}
                    size="lg"
                    className="bg-green-500 hover:bg-green-600 text-black"
                  >
                    <Play className="h-5 w-5 mr-2" />
                    Start
                  </Button>
                ) : (
                  <>
                    <Button
                      onClick={handlePauseTimer}
                      size="lg"
                      variant="outline"
                      className="border-yellow-500 text-yellow-500 hover:bg-yellow-500/10"
                    >
                      <Pause className="h-5 w-5 mr-2" />
                      Pause
                    </Button>
                    <Button
                      onClick={handleStopTimer}
                      size="lg"
                      variant="outline"
                      className="border-red-500 text-red-500 hover:bg-red-500/10"
                    >
                      <Square className="h-5 w-5 mr-2" />
                      Stop
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Time Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <Clock className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{totalHours.toFixed(1)}h</div>
                  <div className="text-sm text-gray-400">Total Hours</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{billableHours.toFixed(1)}h</div>
                  <div className="text-sm text-gray-400">Billable Hours</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">${totalRevenue.toFixed(0)}</div>
                  <div className="text-sm text-gray-400">Total Revenue</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Time Entries */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Calendar className="h-5 w-5 text-green-400" />
              Recent Time Entries
            </CardTitle>
            <CardDescription>
              Your logged work sessions for this project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {timeEntries.map((entry) => (
                <div key={entry.id} className="border border-white/10 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant={entry.isBillable ? 'default' : 'secondary'} className="text-xs">
                          {entry.isBillable ? 'Billable' : 'Non-billable'}
                        </Badge>
                        <span className="text-sm text-gray-400">{entry.date}</span>
                      </div>
                      <h3 className="text-white font-medium mb-1">{entry.description}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-400">
                        <span>{entry.startTime.toLocaleTimeString()} - {entry.endTime?.toLocaleTimeString()}</span>
                        <span>{formatDuration(entry.durationMinutes)}</span>
                        {entry.isBillable && (
                          <span className="text-green-400">${entry.billableAmount.toFixed(2)}</span>
                        )}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {timeEntries.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No time entries yet. Start tracking your work!</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </WorkspaceLayout>
  )
}
