'use client'

import React, { useState } from 'react'
import { WorkspaceLayout } from '@/components/layout/workspace-layout'
import { useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { FolderOpen, Upload, Download, Eye, Share, Trash2, Image, FileText, Archive, Plus } from 'lucide-react'

// Mock data structure for files
interface ProjectFile {
  id: string
  name: string
  type: 'image' | 'document' | 'archive' | 'other'
  size: number
  uploadedAt: Date
  uploadedBy: string
  category: 'design' | 'document' | 'asset' | 'deliverable'
  isSharedWithClient: boolean
  version: number
  description?: string
}

export default function FilesPage() {
  const params = useParams()
  const { workspaceId, projectId } = params

  // Mock files data
  const [files, setFiles] = useState<ProjectFile[]>([
    {
      id: '1',
      name: 'homepage-design-v2.fig',
      type: 'other',
      size: 2048000,
      uploadedAt: new Date('2024-01-15T10:30:00'),
      uploadedBy: '<PERSON>',
      category: 'design',
      isSharedWithClient: true,
      version: 2,
      description: 'Final homepage design with client revisions'
    },
    {
      id: '2',
      name: 'brand-guidelines.pdf',
      type: 'document',
      size: 1536000,
      uploadedAt: new Date('2024-01-12T14:20:00'),
      uploadedBy: 'Client',
      category: 'document',
      isSharedWithClient: false,
      version: 1,
      description: 'Company brand guidelines and style requirements'
    },
    {
      id: '3',
      name: 'hero-image.png',
      type: 'image',
      size: 512000,
      uploadedAt: new Date('2024-01-18T09:15:00'),
      uploadedBy: 'John Doe',
      category: 'asset',
      isSharedWithClient: true,
      version: 1,
      description: 'Hero section background image'
    },
    {
      id: '4',
      name: 'project-requirements.docx',
      type: 'document',
      size: 256000,
      uploadedAt: new Date('2024-01-10T16:45:00'),
      uploadedBy: 'Client',
      category: 'document',
      isSharedWithClient: false,
      version: 3,
      description: 'Detailed project requirements and specifications'
    },
    {
      id: '5',
      name: 'wireframes-v1.zip',
      type: 'archive',
      size: 1024000,
      uploadedAt: new Date('2024-01-14T11:30:00'),
      uploadedBy: 'John Doe',
      category: 'deliverable',
      isSharedWithClient: true,
      version: 1,
      description: 'Initial wireframes for all pages'
    }
  ])

  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-5 w-5 text-purple-400" />
      case 'document':
        return <FileText className="h-5 w-5 text-blue-400" />
      case 'archive':
        return <Archive className="h-5 w-5 text-yellow-400" />
      default:
        return <FileText className="h-5 w-5 text-gray-400" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'design':
        return 'text-purple-400 border-purple-400'
      case 'document':
        return 'text-blue-400 border-blue-400'
      case 'asset':
        return 'text-green-400 border-green-400'
      case 'deliverable':
        return 'text-orange-400 border-orange-400'
      default:
        return 'text-gray-400 border-gray-400'
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const filteredFiles = selectedCategory === 'all' 
    ? files 
    : files.filter(file => file.category === selectedCategory)

  const categories = [
    { key: 'all', label: 'All Files', count: files.length },
    { key: 'design', label: 'Design', count: files.filter(f => f.category === 'design').length },
    { key: 'document', label: 'Documents', count: files.filter(f => f.category === 'document').length },
    { key: 'asset', label: 'Assets', count: files.filter(f => f.category === 'asset').length },
    { key: 'deliverable', label: 'Deliverables', count: files.filter(f => f.category === 'deliverable').length }
  ]

  const totalSize = files.reduce((sum, file) => sum + file.size, 0)
  const sharedFiles = files.filter(file => file.isSharedWithClient).length

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 space-y-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-white flex items-center gap-2">
              <FolderOpen className="h-6 w-6 text-purple-400" />
              Files & Assets
            </h1>
            <p className="text-sm text-gray-400 mt-1">
              Manage project files, designs, and deliverables
            </p>
          </div>
          <Button className="bg-green-500 hover:bg-green-600 text-black">
            <Plus className="h-4 w-4 mr-2" />
            Upload Files
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <FolderOpen className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{files.length}</div>
                  <div className="text-sm text-gray-400">Total Files</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <Archive className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{formatFileSize(totalSize)}</div>
                  <div className="text-sm text-gray-400">Total Size</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 border-white/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Share className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{sharedFiles}</div>
                  <div className="text-sm text-gray-400">Shared with Client</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Category Filter */}
        <Card className="bg-black/20 border-white/10">
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.key}
                  variant={selectedCategory === category.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category.key)}
                  className="text-xs"
                >
                  {category.label} ({category.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Files List */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white">Project Files</CardTitle>
            <CardDescription>
              All files related to this project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredFiles.map((file) => (
                <div key={file.id} className="border border-white/10 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 flex-1">
                      {getFileIcon(file.type)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-white font-medium">{file.name}</h3>
                          <Badge variant="outline" className={`text-xs ${getCategoryColor(file.category)}`}>
                            {file.category}
                          </Badge>
                          {file.version > 1 && (
                            <Badge variant="outline" className="text-xs">
                              v{file.version}
                            </Badge>
                          )}
                          {file.isSharedWithClient && (
                            <Badge variant="outline" className="text-xs text-green-400 border-green-400">
                              Shared
                            </Badge>
                          )}
                        </div>
                        {file.description && (
                          <p className="text-gray-400 text-sm mb-2">{file.description}</p>
                        )}
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>{formatFileSize(file.size)}</span>
                          <span>Uploaded by {file.uploadedBy}</span>
                          <span>{file.uploadedAt.toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Share className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredFiles.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No files in this category. Upload your first file!</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Upload Guidelines */}
        <Card className="bg-black/20 border-white/10">
          <CardHeader>
            <CardTitle className="text-white text-lg">File Organization Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="text-white font-medium mb-2">Design Files</h4>
                <ul className="text-gray-400 space-y-1">
                  <li>• Use version numbers (v1, v2, etc.)</li>
                  <li>• Include file format in name</li>
                  <li>• Share final versions with client</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Documents</h4>
                <ul className="text-gray-400 space-y-1">
                  <li>• Keep requirements and specs organized</li>
                  <li>• Use clear, descriptive names</li>
                  <li>• Mark client-provided files</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </WorkspaceLayout>
  )
}
