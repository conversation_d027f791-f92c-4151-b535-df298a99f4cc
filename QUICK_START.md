# DevHQ Quick Start Guide

🚀 **Get DevHQ running in under 5 minutes!**

This guide will help you set up the DevHQ development environment quickly and efficiently.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Python 3.11+** - [Download here](https://www.python.org/downloads/)
- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Git** - [Download here](https://git-scm.com/downloads)
- **Docker & Docker Compose** - [Download here](https://www.docker.com/get-started)

## One-Command Setup

### For Unix/Linux/macOS:

```bash
# Clone and setup in one command
curl -fsSL https://raw.githubusercontent.com/your-repo/devhq/main/scripts/quick-setup.sh | bash
```

**OR** if you've already cloned the repository:

```bash
# Run the enhanced setup script
./scripts/dev-setup.sh
```

### For Windows:

```powershell
# PowerShell (Run as Administrator)
iwr -useb https://raw.githubusercontent.com/your-repo/devhq/main/scripts/quick-setup.ps1 | iex
```

**OR** if you've already cloned the repository:

```powershell
# PowerShell
.\scripts\setup-windows.ps1

# OR Command Prompt
.\scripts\setup-windows.bat
```

## Manual Setup (Step by Step)

If you prefer to set up manually or the one-command setup doesn't work:

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/devhq.git
cd devhq
```

### 2. Run Setup Script

**Unix/Linux/macOS:**
```bash
./scripts/dev-setup.sh
```

**Windows:**
```powershell
# PowerShell
.\scripts\setup-windows.ps1

# OR Command Prompt
.\scripts\setup-windows.bat
```

### 3. Validate Setup

```bash
# Run the validation script
python3 validate-setup.py
```

### 4. Start Development Servers

**Backend:**
```bash
cd backend
./setup_dev.sh      # First time only
./start_dev_server.sh
```

**Frontend (in a new terminal):**
```bash
cd frontend
npm install          # First time only
npm run dev
```

## Access Your Application

Once everything is running:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: PostgreSQL on localhost:5433
- **Redis**: Redis on localhost:6380

## Environment Configuration

After setup, you'll need to configure your environment files:

### Backend Configuration (`backend/.env`)

```bash
# Required: Update these with your actual values
SECRET_KEY=your-secure-secret-key
DATABASE_URL=postgresql://devhq_user:devhq_password@localhost:5433/devhq

# Optional: External services (for full functionality)
PAYSTACK_SECRET_KEY=sk_test_your_paystack_key
STRIPE_SECRET_KEY=sk_test_your_stripe_key
CLOUDINARY_URL=cloudinary://your_cloudinary_url
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### Frontend Configuration (`frontend/.env.local`)

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000

# Optional: External services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_name
```

## Common Issues & Solutions

### 🔧 Docker Issues

**Problem**: Docker daemon not running
```bash
# Solution: Start Docker
sudo systemctl start docker  # Linux
# OR start Docker Desktop on Windows/macOS
```

**Problem**: Port conflicts
```bash
# Solution: Stop conflicting services
sudo lsof -i :5433  # Check what's using port 5433
sudo kill -9 <PID>  # Kill the process
```

### 🔧 Python Issues

**Problem**: Python version too old
```bash
# Solution: Install Python 3.11+
# Use pyenv for version management
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv global 3.11.0
```

**Problem**: Virtual environment issues
```bash
# Solution: Recreate virtual environment
cd backend
rm -rf venv
python3 -m venv venv
source venv/bin/activate  # Unix
# OR
venv\Scripts\activate     # Windows
pip install -r requirements-dev.txt
```

### 🔧 Node.js Issues

**Problem**: Node version too old
```bash
# Solution: Install Node 18+ using nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

**Problem**: npm install fails
```bash
# Solution: Clear cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### 🔧 Database Issues

**Problem**: Database connection fails
```bash
# Solution: Check if PostgreSQL container is running
docker-compose ps
docker-compose logs postgres

# Restart if needed
docker-compose restart postgres
```

**Problem**: Migration errors
```bash
# Solution: Reset database
cd backend
source venv/bin/activate
alembic downgrade base
alembic upgrade head
```

## Development Workflow

### Daily Development

1. **Start services**:
   ```bash
   docker-compose up -d postgres redis
   ```

2. **Start backend**:
   ```bash
   cd backend
   source venv/bin/activate
   uvicorn app.main:app --reload
   ```

3. **Start frontend**:
   ```bash
   cd frontend
   npm run dev
   ```

### Running Tests

```bash
# Backend tests
cd backend
source venv/bin/activate
pytest

# Frontend tests
cd frontend
npm test

# Run all tests
npm run test:all
```

### Code Quality

```bash
# Backend formatting
cd backend
black .
isort .

# Frontend linting
cd frontend
npm run lint
npm run lint:fix
```

## Useful Commands

### Docker Management

```bash
# View logs
docker-compose logs -f [service]

# Restart services
docker-compose restart [service]

# Stop all services
docker-compose down

# Clean up
docker system prune -a
```

### Database Management

```bash
# Access database
docker-compose exec postgres psql -U devhq_user -d devhq

# Create migration
cd backend
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

### Backup & Restore

```bash
# Backup database
docker-compose exec postgres pg_dump -U devhq_user devhq > backup.sql

# Restore database
docker-compose exec -T postgres psql -U devhq_user devhq < backup.sql
```

## Getting Help

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Create an issue on GitHub if you encounter problems
- **Validation**: Run `python3 validate-setup.py` to check your setup
- **Logs**: Check `docker-compose logs` for service-specific issues

## Next Steps

Once you have DevHQ running:

1. **Explore the API**: Visit http://localhost:8000/docs
2. **Check the frontend**: Visit http://localhost:3000
3. **Read the documentation**: Check the `/docs` folder
4. **Run tests**: Ensure everything works with `pytest` and `npm test`
5. **Start developing**: Check out the contributing guidelines

---

**Happy coding! 🎉**

If you encounter any issues, please check the troubleshooting section above or create an issue on GitHub.