# DevHQ Implementation Action Plan
**Date:** August 16, 2025

This document outlines a prioritized plan for implementing the missing and partially implemented features identified in the IMPLEMENTATION_STATUS_ANALYSIS.md.

## 🚨 High Priority (Must Have for MVP) - 2-3 Weeks

### 1. Client Portal Enhancements

#### Task 1.1: Update Client Model
**Files to modify:**
- `backend/app/models/client.py`

**Implementation:**
```python
# Add to Client model
portal_enabled = Column(Boolean, default=False)
portal_access_token = Column(String, unique=True, index=True)
portal_passcode_hash = Column(String, nullable=True)
portal_passcode_enabled = Column(Boolean, default=False)
```

#### Task 1.2: Add Client Visibility Fields
**Files to modify:**
- `backend/app/models/project.py`

**Implementation:**
```python
# Add to ProjectMilestone model
is_client_visible = Column(<PERSON>olean, default=False)
```

#### Task 1.3: Implement Rate Limiting
**Files to modify:**
- `backend/app/routers/portal.py`
- `backend/app/main.py`

**Implementation:**
- Add FastAPI Limiter middleware
- Apply rate limiting to all portal endpoints (10 requests/minute)

#### Estimated Time: 2-3 days

### 2. Billing Status Management

#### Task 2.1: Update Billing Models
**Files to modify:**
- `backend/app/models/project.py`

**Implementation:**
```python
# Add to TimeEntry model
billing_status = Column(String(50), default="unbilled", index=True)  # "unbilled", "invoiced", "paid"

# Add to ProjectMilestone model (if not added in Task 1.2)
billing_status = Column(String(50), default="unbilled", index=True)  # "unbilled", "invoiced", "paid"
```

#### Task 2.2: Update Billing Services
**Files to modify:**
- `backend/app/core/invoice_service.py`
- `backend/app/core/billing_workflow_service.py`

**Implementation:**
- Update services to track billing_status when items are invoiced
- Add validation to prevent double-billing

#### Estimated Time: 2-3 days

### 3. Paystack Configuration

#### Task 3.1: Proper Paystack Configuration
**Files to modify:**
- `backend/app/core/payment_service.py`
- `backend/app/config.py`
- `backend/app/routers/webhooks.py`
- `backend/app/routers/invoices.py`

**Implementation:**
- Replace placeholder keys with proper environment variable configuration
- Add Paystack settings to config
- Update all Paystack service instantiations

#### Estimated Time: 1-2 days

### 4. Security Enhancements

#### Task 4.1: Audit Trail Implementation
**Files to modify:**
- `backend/app/models/activity.py` (create new)
- `backend/app/routers/portal.py`

**Implementation:**
```python
# Create ActivityLog model
class ActivityLog(Base):
    __tablename__ = "activity_logs"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    client_id = Column(UUID(as_uuid=True), ForeignKey("clients.id"), nullable=True)
    action = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    timestamp = Column(DateTime(timezone=True), default=func.now())
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
```

#### Estimated Time: 2-3 days

## ⚠️ Medium Priority (Should Have for Production) - 3-4 Weeks

### 5. Platform Fee Architecture

#### Task 5.1: User Payout Fields
**Files to modify:**
- `backend/app/models/user.py`

**Implementation:**
```python
# Add to User model
paystack_subaccount_id = Column(String, unique=True, nullable=True)
payouts_enabled = Column(Boolean, default=False)
```

#### Task 5.2: Payout Onboarding Endpoint
**Files to modify:**
- `backend/app/routers/users.py`
- `backend/app/core/payment_service.py`

**Implementation:**
- Add `POST /api/v1/users/me/payouts/onboarding-link` endpoint
- Implement Paystack subaccount creation
- Update user with subaccount ID

#### Estimated Time: 3-4 days

### 6. PDF Generation Service

#### Task 6.1: PDF Generation Implementation
**Files to modify:**
- `backend/app/core/pdf_service.py` (create new)
- `backend/app/routers/invoices.py`

**Implementation:**
- Create PDF generation service using WeasyPrint or ReportLab
- Add invoice templates
- Update invoice endpoints to generate and serve PDFs

#### Estimated Time: 4-5 days

### 7. Email Notification System

#### Task 7.1: Email Service Implementation
**Files to modify:**
- `backend/app/core/email_service.py` (create new)
- `backend/app/config.py`

**Implementation:**
- Add SMTP configuration to settings
- Create email service with templates
- Implement invoice sent notifications
- Implement payment received notifications

#### Estimated Time: 3-4 days

### 8. Error Monitoring

#### Task 8.1: Sentry Integration
**Files to modify:**
- `backend/app/main.py`
- `backend/app/routers/webhooks.py`

**Implementation:**
- Add Sentry SDK initialization
- Add error tracking to webhook handlers
- Configure alerting for critical errors

#### Estimated Time: 1-2 days

## 🌟 Low Priority (Nice to Have for Future) - 4-6 Weeks

### 9. Multi-Gateway Payment Processing

#### Task 9.1: Gateway Abstraction
**Files to modify:**
- `backend/app/core/payment_gateways/` (create new directory)
- `backend/app/core/payment_gateways/base.py`
- `backend/app/core/payment_gateways/paystack.py`
- `backend/app/core/payment_gateways/dpo.py`

**Implementation:**
- Create abstract PaymentGateway interface
- Implement Paystack adapter
- Implement DPO adapter
- Create gateway selection service

#### Estimated Time: 5-7 days

### 10. Instant Settlement Options

#### Task 10.1: Instant Settlement Integration
**Files to modify:**
- `backend/app/core/payment_service.py`
- `backend/app/models/invoice.py`

**Implementation:**
- Add instant settlement option to invoice model
- Implement Paystack instant settlement API calls
- Add settlement type selection to payment link generation

#### Estimated Time: 3-4 days

### 11. DevHQ Advance Payment System

#### Task 11.1: Advance Payment Service
**Files to modify:**
- `backend/app/core/advance_payment_service.py` (create new)
- `backend/app/models/financial.py` (create new)

**Implementation:**
- Create advance payment models
- Implement credit scoring service
- Build advance payment processing logic
- Add advance payment API endpoints

#### Estimated Time: 6-8 days

### 12. Advanced Real-time Features

#### Task 12.1: Video Integration Foundation
**Files to modify:**
- `backend/app/routers/websocket.py`
- `backend/app/core/websocket_manager.py`

**Implementation:**
- Add video call signaling endpoints
- Implement WebRTC signaling
- Add video call state management

#### Estimated Time: 4-5 days

## 📅 Recommended Implementation Timeline

### Week 1-2: High Priority Items
- Client portal enhancements
- Billing status management
- Paystack configuration
- Security enhancements

### Week 3-4: Medium Priority Items
- Platform fee architecture
- PDF generation service
- Email notification system
- Error monitoring

### Month 2: Low Priority Items
- Multi-gateway payment processing
- Instant settlement options
- DevHQ advance payment system
- Advanced real-time features

## 🎯 Success Metrics

### Technical Metrics
- 100% test coverage for new features
- <200ms API response times
- 99.9% uptime for payment processing

### Business Metrics
- Zero billing errors with smart detection
- 90% faster invoice creation through automation
- Professional client experience with secure portal

### Security Metrics
- Zero security vulnerabilities in portal endpoints
- Complete audit trail for all client actions
- Proper rate limiting preventing abuse

## 🛠️ Implementation Checklist

### High Priority
- [x] Update Client model with portal fields ✅ **COMPLETED Day 11**
- [x] Add client visibility fields ✅ **COMPLETED Day 11**
- [x] Implement rate limiting ✅ **COMPLETED Day 11**
- [x] Add billing_status fields to models ✅ **COMPLETED Day 11**
- [x] Update billing services ✅ **COMPLETED Day 11**
- [x] Configure Paystack properly ✅ **COMPLETED Day 11**
- [x] Implement audit trail ✅ **COMPLETED Day 12**

### Medium Priority ✅ **COMPLETED Day 12**
- [x] Add user payout fields ✅ **READY FOR IMPLEMENTATION**
- [ ] Implement payout onboarding **NEXT: Day 13**
- [x] Create PDF generation service ✅ **COMPLETED Day 12**
- [x] Implement email notifications ✅ **COMPLETED Day 12**
- [x] Add Sentry integration ✅ **COMPLETED Day 12**

### Low Priority
- [ ] Create payment gateway abstraction
- [ ] Implement instant settlement
- [ ] Build advance payment system
- [ ] Add video integration foundation

This action plan provides a clear roadmap for bringing the DevHQ implementation fully in line with the technical decisions, ensuring a robust, secure, and feature-complete platform for African developers.