# DevHQ Troubleshooting Guide

🔧 **Common issues and their solutions**

This guide covers the most common issues you might encounter when setting up or running DevHQ, along with step-by-step solutions.

## Table of Contents

- [Setup Issues](#setup-issues)
- [Docker Issues](#docker-issues)
- [Database Issues](#database-issues)
- [Python/Backend Issues](#pythonbackend-issues)
- [Node.js/Frontend Issues](#nodejsfrontend-issues)
- [Environment Configuration Issues](#environment-configuration-issues)
- [Performance Issues](#performance-issues)
- [Network/Port Issues](#networkport-issues)
- [Platform-Specific Issues](#platform-specific-issues)

## Setup Issues

### ❌ "docker-compose.yml not found"

**Problem**: Setup script can't find docker-compose.yml

**Solution**:
```bash
# Make sure you're in the project root directory
pwd  # Should show /path/to/devhq
ls   # Should show docker-compose.yml

# If not, navigate to the correct directory
cd /path/to/devhq
```

### ❌ "Permission denied" on setup scripts

**Problem**: Setup scripts don't have execute permissions

**Solution**:
```bash
# Make scripts executable
chmod +x scripts/dev-setup.sh
chmod +x backend/setup_dev.sh
chmod +x backend/start_dev_server.sh

# Then run the script
./scripts/dev-setup.sh
```

### ❌ Setup script fails with "Command not found"

**Problem**: Required tools are not installed or not in PATH

**Solution**:
1. **Check what's missing**:
   ```bash
   python3 --version  # Should be 3.11+
   node --version     # Should be 18+
   docker --version   # Should be installed
   git --version      # Should be installed
   ```

2. **Install missing tools**:
   - Python: https://www.python.org/downloads/
   - Node.js: https://nodejs.org/
   - Docker: https://www.docker.com/get-started
   - Git: https://git-scm.com/downloads

## Docker Issues

### ❌ "Docker daemon is not running"

**Problem**: Docker service is not started

**Solution**:

**Linux**:
```bash
# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker  # Auto-start on boot

# Add user to docker group (logout/login required)
sudo usermod -aG docker $USER
```

**macOS/Windows**:
- Start Docker Desktop application
- Wait for Docker to fully initialize

### ❌ "Port already in use" errors

**Problem**: Required ports (5433, 6380, 8000, 3000) are occupied

**Solution**:
```bash
# Check what's using the ports
sudo lsof -i :5433  # PostgreSQL
sudo lsof -i :6380  # Redis
sudo lsof -i :8000  # Backend
sudo lsof -i :3000  # Frontend

# Kill conflicting processes
sudo kill -9 <PID>

# OR change ports in docker-compose.yml
# Edit docker-compose.yml and change port mappings
```

### ❌ "No space left on device"

**Problem**: Docker has consumed all available disk space

**Solution**:
```bash
# Clean up Docker resources
docker system prune -a --volumes

# Remove unused images
docker image prune -a

# Remove unused volumes
docker volume prune

# Check disk usage
docker system df
```

### ❌ Docker build fails with network errors

**Problem**: Network connectivity issues during build

**Solution**:
```bash
# Try building with no cache
docker-compose build --no-cache

# Check Docker daemon configuration
docker info

# Restart Docker daemon
sudo systemctl restart docker  # Linux
# OR restart Docker Desktop
```

## Database Issues

### ❌ "Database connection failed"

**Problem**: Can't connect to PostgreSQL

**Solution**:
1. **Check if PostgreSQL container is running**:
   ```bash
   docker-compose ps
   docker-compose logs postgres
   ```

2. **Restart PostgreSQL**:
   ```bash
   docker-compose restart postgres
   ```

3. **Check connection details**:
   ```bash
   # Verify .env file has correct DATABASE_URL
   cat backend/.env | grep DATABASE_URL
   
   # Should be: postgresql://devhq_user:devhq_password@localhost:5433/devhq
   ```

4. **Test connection manually**:
   ```bash
   docker-compose exec postgres psql -U devhq_user -d devhq
   ```

### ❌ "Migration failed" or "Table already exists"

**Problem**: Database schema is out of sync

**Solution**:
```bash
cd backend
source venv/bin/activate

# Check current migration status
alembic current
alembic history

# Reset database (WARNING: This will delete all data)
alembic downgrade base
alembic upgrade head

# OR create a new migration
alembic revision --autogenerate -m "fix_schema"
alembic upgrade head
```

### ❌ "Password authentication failed"

**Problem**: Database credentials are incorrect

**Solution**:
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Recreate PostgreSQL container with fresh data
docker-compose down
docker volume rm devhq_postgres_data  # WARNING: Deletes all data
docker-compose up -d postgres
```

## Python/Backend Issues

### ❌ "Python version too old"

**Problem**: Python version is below 3.11

**Solution**:

**Using pyenv (recommended)**:
```bash
# Install pyenv
curl https://pyenv.run | bash

# Add to shell profile
echo 'export PATH="$HOME/.pyenv/bin:$PATH"' >> ~/.bashrc
echo 'eval "$(pyenv init --path)"' >> ~/.bashrc
echo 'eval "$(pyenv virtualenv-init -)"' >> ~/.bashrc

# Restart shell or source profile
source ~/.bashrc

# Install Python 3.11
pyenv install 3.11.0
pyenv global 3.11.0
```

**Manual installation**:
- Download from https://www.python.org/downloads/
- Follow installation instructions for your OS

### ❌ "Virtual environment activation fails"

**Problem**: Can't activate Python virtual environment

**Solution**:
```bash
cd backend

# Remove existing venv
rm -rf venv

# Create new virtual environment
python3 -m venv venv

# Activate (Unix/Linux/macOS)
source venv/bin/activate

# Activate (Windows)
venv\Scripts\activate

# Install dependencies
pip install --upgrade pip
pip install -r requirements-dev.txt
```

### ❌ "ModuleNotFoundError" for Python packages

**Problem**: Required Python packages are not installed

**Solution**:
```bash
cd backend
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install/reinstall dependencies
pip install -r requirements-dev.txt

# If still failing, try:
pip install --force-reinstall -r requirements-dev.txt
```

### ❌ "FastAPI server won't start"

**Problem**: Backend server fails to start

**Solution**:
1. **Check for syntax errors**:
   ```bash
   cd backend
   python -m py_compile app/main.py
   ```

2. **Check environment variables**:
   ```bash
   cat .env | grep -E "DATABASE_URL|SECRET_KEY|REDIS_URL"
   ```

3. **Start with debug mode**:
   ```bash
   uvicorn app.main:app --reload --log-level debug
   ```

## Node.js/Frontend Issues

### ❌ "Node version too old"

**Problem**: Node.js version is below 18

**Solution**:

**Using nvm (recommended)**:
```bash
# Install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Restart terminal or source profile
source ~/.bashrc

# Install Node 18
nvm install 18
nvm use 18
nvm alias default 18
```

**Manual installation**:
- Download from https://nodejs.org/
- Choose LTS version (18+)

### ❌ "npm install fails"

**Problem**: Frontend dependencies installation fails

**Solution**:
```bash
cd frontend

# Clear npm cache
npm cache clean --force

# Remove node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Try with different registry
npm install --registry https://registry.npmjs.org/

# If still failing, try yarn
npm install -g yarn
yarn install
```

### ❌ "Next.js build fails"

**Problem**: Frontend build process fails

**Solution**:
```bash
cd frontend

# Check for TypeScript errors
npm run type-check

# Check for linting errors
npm run lint

# Clear Next.js cache
rm -rf .next

# Rebuild
npm run build
```

### ❌ "Frontend can't connect to backend"

**Problem**: API calls from frontend fail

**Solution**:
1. **Check backend is running**:
   ```bash
   curl http://localhost:8000/health
   ```

2. **Check frontend environment**:
   ```bash
   cat frontend/.env.local | grep NEXT_PUBLIC_API_URL
   # Should be: NEXT_PUBLIC_API_URL=http://localhost:8000
   ```

3. **Check CORS settings**:
   ```bash
   cat backend/.env | grep CORS_ORIGINS
   # Should include: http://localhost:3000
   ```

## Environment Configuration Issues

### ❌ "Environment variables not loaded"

**Problem**: .env files are not being read

**Solution**:
1. **Check file location**:
   ```bash
   ls -la backend/.env
   ls -la frontend/.env.local
   ```

2. **Check file format**:
   ```bash
   # No spaces around = sign
   # Correct: DATABASE_URL=postgresql://...
   # Wrong:   DATABASE_URL = postgresql://...
   ```

3. **Check for hidden characters**:
   ```bash
   cat -A backend/.env  # Shows hidden characters
   ```

### ❌ "Secret key errors"

**Problem**: SECRET_KEY is invalid or missing

**Solution**:
```bash
# Generate a new secret key
openssl rand -hex 32

# Update backend/.env
echo "SECRET_KEY=$(openssl rand -hex 32)" >> backend/.env
```

## Performance Issues

### ❌ "Application is slow"

**Problem**: Poor performance during development

**Solution**:
1. **Check Docker resources**:
   ```bash
   docker stats
   ```

2. **Increase Docker memory/CPU** (Docker Desktop settings)

3. **Check database performance**:
   ```bash
   docker-compose exec postgres psql -U devhq_user -d devhq -c "SELECT * FROM pg_stat_activity;"
   ```

4. **Enable Redis caching**:
   ```bash
   # Check Redis is working
   docker-compose exec redis redis-cli ping
   ```

### ❌ "Hot reload not working"

**Problem**: Changes don't trigger automatic reload

**Solution**:

**Backend**:
```bash
# Make sure you're using --reload flag
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend**:
```bash
# Check if file watching is working
npm run dev -- --turbo

# On some systems, increase file watchers
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## Network/Port Issues

### ❌ "Connection refused" errors

**Problem**: Services can't connect to each other

**Solution**:
1. **Check if services are running**:
   ```bash
   docker-compose ps
   netstat -tlnp | grep -E "(3000|8000|5433|6380)"
   ```

2. **Check firewall settings**:
   ```bash
   # Linux
   sudo ufw status
   sudo ufw allow 3000
   sudo ufw allow 8000
   ```

3. **Check host file**:
   ```bash
   # Make sure localhost resolves correctly
   ping localhost
   ```

## Platform-Specific Issues

### Windows Issues

#### ❌ "PowerShell execution policy"

**Problem**: Can't run PowerShell scripts

**Solution**:
```powershell
# Run as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Then run the script
.\scripts\setup-windows.ps1
```

#### ❌ "Line ending issues"

**Problem**: Scripts fail due to CRLF line endings

**Solution**:
```bash
# Convert line endings
dos2unix scripts/dev-setup.sh
dos2unix backend/setup_dev.sh

# OR configure Git
git config --global core.autocrlf true
```

### macOS Issues

#### ❌ "Permission denied" for Docker

**Problem**: Docker commands require sudo

**Solution**:
```bash
# Add user to docker group
sudo dscl . -append /Groups/docker GroupMembership $USER

# Restart Docker Desktop
```

#### ❌ "Command not found" after installation

**Problem**: Installed tools not in PATH

**Solution**:
```bash
# Add to PATH in shell profile
echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

### Linux Issues

#### ❌ "Docker requires sudo"

**Problem**: Docker commands need sudo privileges

**Solution**:
```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Logout and login again, or run:
newgrp docker
```

## Getting More Help

If none of these solutions work:

1. **Run the validation script**:
   ```bash
   python3 validate-setup.py
   ```

2. **Check logs**:
   ```bash
   docker-compose logs
   docker-compose logs [service-name]
   ```

3. **Create a detailed issue** with:
   - Your operating system and version
   - Python, Node.js, Docker versions
   - Complete error messages
   - Steps to reproduce
   - Output of `python3 validate-setup.py`

4. **Common debugging commands**:
   ```bash
   # System info
   uname -a
   python3 --version
   node --version
   docker --version
   docker-compose --version
   
   # Service status
   docker-compose ps
   docker-compose logs --tail=50
   
   # Network connectivity
   curl -I http://localhost:8000/health
   curl -I http://localhost:3000
   ```

---

**Remember**: Most issues are related to missing dependencies, incorrect environment configuration, or port conflicts. The validation script (`python3 validate-setup.py`) can help identify many common problems automatically.