# DevHQ Windows Setup Script
# PowerShell script for setting up DevHQ on Windows

# Enable strict mode for better error handling
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

# Colors for output
function Write-Info { param($Message) Write-Host "[INFO] $Message" -ForegroundColor Blue }
function Write-Success { param($Message) Write-Host "[SUCCESS] $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "[ERROR] $Message" -ForegroundColor Red }

Write-Host "🚀 Setting up DevHQ Development Environment on Windows..." -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Warning "Running without administrator privileges. Some operations may fail."
    Write-Info "Consider running PowerShell as Administrator for best results."
}

# Function to check if a command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Check system requirements
Write-Info "Checking system requirements..."

$requirements = @(
    @{Name="Python"; Command="python"; Version="--version"; Required=$true},
    @{Name="Node.js"; Command="node"; Version="--version"; Required=$true},
    @{Name="npm"; Command="npm"; Version="--version"; Required=$true},
    @{Name="Git"; Command="git"; Version="--version"; Required=$true},
    @{Name="Docker"; Command="docker"; Version="--version"; Required=$true},
    @{Name="Docker Compose"; Command="docker-compose"; Version="--version"; Required=$false}
)

$missingRequirements = @()

foreach ($req in $requirements) {
    if (Test-Command $req.Command) {
        $version = & $req.Command $req.Version 2>$null
        Write-Success "✓ $($req.Name) is installed: $($version.Split([Environment]::NewLine)[0])"
    } else {
        if ($req.Required) {
            $missingRequirements += $req.Name
            Write-Error "✗ $($req.Name) is not installed or not in PATH"
        } else {
            Write-Warning "⚠ $($req.Name) is not installed (optional)"
        }
    }
}

if ($missingRequirements.Count -gt 0) {
    Write-Error "Missing required dependencies: $($missingRequirements -join ', ')"
    Write-Info "Please install the missing dependencies and run this script again."
    Write-Info ""
    Write-Info "Installation links:"
    Write-Info "- Python 3.11+: https://www.python.org/downloads/"
    Write-Info "- Node.js 18+: https://nodejs.org/"
    Write-Info "- Git: https://git-scm.com/download/win"
    Write-Info "- Docker Desktop: https://www.docker.com/products/docker-desktop/"
    exit 1
}

Write-Success "All system requirements met!"
Write-Info ""

# Create project directories
Write-Info "Creating project directories..."
$directories = @(
    "backend\logs",
    "frontend\.next",
    "nginx\ssl",
    "data\postgres",
    "data\redis"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Success "Created directory: $dir"
    } else {
        Write-Info "Directory already exists: $dir"
    }
}

# Setup backend environment
Write-Info "Setting up backend environment..."
Set-Location backend

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Info "Creating Python virtual environment..."
    python -m venv venv
    Write-Success "Virtual environment created!"
} else {
    Write-Info "Virtual environment already exists."
}

# Activate virtual environment and install dependencies
Write-Info "Activating virtual environment and installing dependencies..."
& ".\venv\Scripts\Activate.ps1"
python -m pip install --upgrade pip
pip install -r requirements-dev.txt
Write-Success "Backend dependencies installed!"

# Create .env file if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Info "Creating .env file from template..."
    Copy-Item ".env.example" ".env"
    Write-Success ".env file created!"
    Write-Warning "Please update .env file with your actual configuration values."
} else {
    Write-Warning ".env file already exists, skipping..."
}

# Go back to root directory
Set-Location ..

# Setup frontend environment
Write-Info "Setting up frontend environment..."
Set-Location frontend

if (Test-Path "package.json") {
    Write-Info "Installing frontend dependencies..."
    npm install
    Write-Success "Frontend dependencies installed!"
    
    # Create frontend .env.local if it doesn't exist
    if (-not (Test-Path ".env.local")) {
        Write-Info "Creating frontend .env.local file..."
        @"
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# External Services (replace with your keys)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_name

# Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_analytics_id
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Feature Flags
NEXT_PUBLIC_ENABLE_PAYMENTS=true
NEXT_PUBLIC_ENABLE_CLIENT_PORTAL=true

# Environment
NODE_ENV=development
"@ | Out-File -FilePath ".env.local" -Encoding UTF8
        Write-Success "Frontend .env.local file created!"
    } else {
        Write-Warning "Frontend .env.local file already exists, skipping..."
    }
} else {
    Write-Warning "Frontend package.json not found, skipping frontend setup."
}

# Go back to root directory
Set-Location ..

# Start Docker services
Write-Info "Starting Docker services..."
try {
    docker-compose up -d postgres redis
    Write-Success "Docker services started!"
    
    # Wait for database to be ready
    Write-Info "Waiting for database to be ready..."
    Start-Sleep -Seconds 10
    
    # Run database migrations
    Write-Info "Running database migrations..."
    Set-Location backend
    & ".\venv\Scripts\Activate.ps1"
    alembic upgrade head
    Write-Success "Database migrations completed!"
    Set-Location ..
    
} catch {
    Write-Warning "Failed to start Docker services. Please ensure Docker Desktop is running."
    Write-Info "You can start services manually with: docker-compose up -d"
}

# Setup complete
Write-Host "" 
Write-Host "🎉 DevHQ development environment setup complete!" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Info "Next steps:"
Write-Info "1. Update the .env files with your actual API keys"
Write-Info "2. Start the development servers:"
Write-Info "   Backend:  cd backend && .\venv\Scripts\Activate.ps1 && uvicorn app.main:app --reload"
Write-Info "   Frontend: cd frontend && npm run dev"
Write-Info ""
Write-Info "Access points:"
Write-Info "- Frontend: http://localhost:3000"
Write-Info "- Backend API: http://localhost:8000"
Write-Info "- API Documentation: http://localhost:8000/docs"
Write-Info ""
Write-Info "Useful commands:"
Write-Info "- Start all services: docker-compose up -d"
Write-Info "- View logs: docker-compose logs -f"
Write-Info "- Stop services: docker-compose down"
Write-Info "- Run tests: cd backend && .\venv\Scripts\Activate.ps1 && pytest"
Write-Host ""
Write-Success "Happy coding! 🚀"