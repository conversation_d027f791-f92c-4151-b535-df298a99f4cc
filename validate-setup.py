#!/usr/bin/env python3
"""
DevHQ Setup Validation Script

This script validates that the DevHQ development environment is properly set up.
It checks dependencies, configuration files, database connectivity, and more.
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Colors for terminal output
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_header(text: str):
    """Print a formatted header"""
    print(f"\n{Colors.CYAN}{Colors.BOLD}{'='*60}{Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}{text.center(60)}{Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}{'='*60}{Colors.END}\n")

def print_success(text: str):
    """Print success message"""
    print(f"{Colors.GREEN}✓ {text}{Colors.END}")

def print_error(text: str):
    """Print error message"""
    print(f"{Colors.RED}✗ {text}{Colors.END}")

def print_warning(text: str):
    """Print warning message"""
    print(f"{Colors.YELLOW}⚠ {text}{Colors.END}")

def print_info(text: str):
    """Print info message"""
    print(f"{Colors.BLUE}ℹ {text}{Colors.END}")

def run_command(command: str, capture_output: bool = True) -> Tuple[bool, str]:
    """Run a shell command and return success status and output"""
    try:
        if capture_output:
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True, timeout=30
            )
            return result.returncode == 0, result.stdout.strip()
        else:
            result = subprocess.run(command, shell=True, timeout=30)
            return result.returncode == 0, ""
    except subprocess.TimeoutExpired:
        return False, "Command timed out"
    except Exception as e:
        return False, str(e)

def check_command_exists(command: str) -> Tuple[bool, str]:
    """Check if a command exists in PATH"""
    if os.name == 'nt':  # Windows
        success, output = run_command(f"where {command}")
    else:  # Unix-like
        success, output = run_command(f"which {command}")
    return success, output

def check_system_requirements() -> Dict[str, bool]:
    """Check system requirements"""
    print_header("SYSTEM REQUIREMENTS")
    
    requirements = {
        'python': False,
        'node': False,
        'npm': False,
        'git': False,
        'docker': False,
        'docker-compose': False
    }
    
    # Check Python
    success, output = run_command("python --version")
    if success and "Python" in output:
        version = output.split()[1]
        major, minor = map(int, version.split('.')[:2])
        if major == 3 and minor >= 11:
            print_success(f"Python {version} (✓ >= 3.11)")
            requirements['python'] = True
        else:
            print_error(f"Python {version} (✗ requires >= 3.11)")
    else:
        print_error("Python not found or not working")
    
    # Check Node.js
    success, output = run_command("node --version")
    if success:
        version = output.replace('v', '')
        major = int(version.split('.')[0])
        if major >= 18:
            print_success(f"Node.js {version} (✓ >= 18)")
            requirements['node'] = True
        else:
            print_error(f"Node.js {version} (✗ requires >= 18)")
    else:
        print_error("Node.js not found")
    
    # Check npm
    success, output = run_command("npm --version")
    if success:
        print_success(f"npm {output}")
        requirements['npm'] = True
    else:
        print_error("npm not found")
    
    # Check Git
    success, output = run_command("git --version")
    if success:
        print_success(f"Git {output.split()[-1]}")
        requirements['git'] = True
    else:
        print_error("Git not found")
    
    # Check Docker
    success, output = run_command("docker --version")
    if success:
        print_success(f"Docker {output.split()[2].rstrip(',')}")
        requirements['docker'] = True
    else:
        print_error("Docker not found")
    
    # Check Docker Compose
    success, output = run_command("docker-compose --version")
    if success:
        print_success(f"Docker Compose {output.split()[-1]}")
        requirements['docker-compose'] = True
    else:
        print_warning("Docker Compose not found (optional)")
    
    return requirements

def check_project_structure() -> Dict[str, bool]:
    """Check project structure"""
    print_header("PROJECT STRUCTURE")
    
    structure = {
        'backend_dir': False,
        'frontend_dir': False,
        'docker_compose': False,
        'backend_requirements': False,
        'frontend_package': False
    }
    
    # Check directories
    if Path('backend').is_dir():
        print_success("Backend directory exists")
        structure['backend_dir'] = True
    else:
        print_error("Backend directory missing")
    
    if Path('frontend').is_dir():
        print_success("Frontend directory exists")
        structure['frontend_dir'] = True
    else:
        print_error("Frontend directory missing")
    
    # Check key files
    if Path('docker-compose.yml').is_file():
        print_success("docker-compose.yml exists")
        structure['docker_compose'] = True
    else:
        print_error("docker-compose.yml missing")
    
    if Path('backend/requirements.txt').is_file():
        print_success("Backend requirements.txt exists")
        structure['backend_requirements'] = True
    else:
        print_error("Backend requirements.txt missing")
    
    if Path('frontend/package.json').is_file():
        print_success("Frontend package.json exists")
        structure['frontend_package'] = True
    else:
        print_error("Frontend package.json missing")
    
    return structure

def check_environment_files() -> Dict[str, bool]:
    """Check environment configuration files"""
    print_header("ENVIRONMENT CONFIGURATION")
    
    env_status = {
        'backend_env_example': False,
        'backend_env': False,
        'frontend_env': False
    }
    
    # Check backend .env.example
    if Path('backend/.env.example').is_file():
        print_success("Backend .env.example exists")
        env_status['backend_env_example'] = True
    else:
        print_error("Backend .env.example missing")
    
    # Check backend .env
    if Path('backend/.env').is_file():
        print_success("Backend .env exists")
        env_status['backend_env'] = True
        
        # Check if .env has required variables
        try:
            with open('backend/.env', 'r') as f:
                env_content = f.read()
                required_vars = ['DATABASE_URL', 'SECRET_KEY', 'REDIS_URL']
                missing_vars = []
                for var in required_vars:
                    if var not in env_content or f"{var}=your_" in env_content:
                        missing_vars.append(var)
                
                if missing_vars:
                    print_warning(f"Backend .env needs configuration: {', '.join(missing_vars)}")
                else:
                    print_success("Backend .env appears configured")
        except Exception as e:
            print_warning(f"Could not validate .env content: {e}")
    else:
        print_error("Backend .env missing (copy from .env.example)")
    
    # Check frontend .env.local
    if Path('frontend/.env.local').is_file():
        print_success("Frontend .env.local exists")
        env_status['frontend_env'] = True
    else:
        print_warning("Frontend .env.local missing (optional)")
    
    return env_status

def check_python_environment() -> Dict[str, bool]:
    """Check Python virtual environment and dependencies"""
    print_header("PYTHON ENVIRONMENT")
    
    python_status = {
        'venv_exists': False,
        'venv_activated': False,
        'dependencies_installed': False
    }
    
    # Check if virtual environment exists
    venv_path = Path('backend/venv')
    if venv_path.is_dir():
        print_success("Virtual environment exists")
        python_status['venv_exists'] = True
        
        # Check if we're in a virtual environment
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            print_success("Virtual environment is activated")
            python_status['venv_activated'] = True
        else:
            print_warning("Virtual environment not activated")
        
        # Check if dependencies are installed
        try:
            import fastapi
            import sqlalchemy
            import alembic
            print_success("Key Python dependencies are installed")
            python_status['dependencies_installed'] = True
        except ImportError as e:
            print_error(f"Missing Python dependencies: {e}")
    else:
        print_error("Virtual environment missing (run setup script)")
    
    return python_status

def check_docker_services() -> Dict[str, bool]:
    """Check Docker services status"""
    print_header("DOCKER SERVICES")
    
    docker_status = {
        'docker_running': False,
        'postgres_running': False,
        'redis_running': False
    }
    
    # Check if Docker is running
    success, output = run_command("docker info")
    if success:
        print_success("Docker daemon is running")
        docker_status['docker_running'] = True
        
        # Check for DevHQ containers
        success, output = run_command("docker ps --format 'table {{.Names}}\t{{.Status}}'")
        if success:
            if 'devhq-postgres' in output:
                print_success("PostgreSQL container is running")
                docker_status['postgres_running'] = True
            else:
                print_warning("PostgreSQL container not running")
            
            if 'devhq-redis' in output:
                print_success("Redis container is running")
                docker_status['redis_running'] = True
            else:
                print_warning("Redis container not running")
        else:
            print_warning("Could not check container status")
    else:
        print_error("Docker daemon not running")
    
    return docker_status

def check_database_connectivity() -> bool:
    """Check database connectivity"""
    print_header("DATABASE CONNECTIVITY")
    
    try:
        # Change to backend directory
        os.chdir('backend')
        
        # Try to import and test database connection
        sys.path.insert(0, '.')
        
        try:
            from app.database import engine
            from sqlalchemy import text
            
            with engine.connect() as conn:
                result = conn.execute(text('SELECT 1'))
                if result.fetchone()[0] == 1:
                    print_success("Database connection successful")
                    return True
        except Exception as e:
            print_error(f"Database connection failed: {e}")
            return False
        
    except Exception as e:
        print_error(f"Could not test database: {e}")
        return False
    finally:
        # Go back to root directory
        os.chdir('..')

def generate_report(results: Dict[str, Dict[str, bool]]) -> None:
    """Generate a summary report"""
    print_header("VALIDATION SUMMARY")
    
    total_checks = 0
    passed_checks = 0
    
    for category, checks in results.items():
        category_passed = sum(checks.values())
        category_total = len(checks)
        total_checks += category_total
        passed_checks += category_passed
        
        if category_passed == category_total:
            status = f"{Colors.GREEN}✓{Colors.END}"
        elif category_passed > 0:
            status = f"{Colors.YELLOW}⚠{Colors.END}"
        else:
            status = f"{Colors.RED}✗{Colors.END}"
        
        print(f"{status} {category.replace('_', ' ').title()}: {category_passed}/{category_total}")
    
    print(f"\n{Colors.BOLD}Overall: {passed_checks}/{total_checks} checks passed{Colors.END}")
    
    if passed_checks == total_checks:
        print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 All checks passed! Your DevHQ environment is ready!{Colors.END}")
    elif passed_checks >= total_checks * 0.8:
        print(f"\n{Colors.YELLOW}{Colors.BOLD}⚠ Most checks passed. Review warnings above.{Colors.END}")
    else:
        print(f"\n{Colors.RED}{Colors.BOLD}❌ Several issues found. Please fix errors above.{Colors.END}")
    
    # Provide next steps
    print(f"\n{Colors.CYAN}{Colors.BOLD}Next Steps:{Colors.END}")
    if passed_checks < total_checks:
        print("1. Fix the issues identified above")
        print("2. Run this validation script again")
        print("3. Consult the setup documentation if needed")
    else:
        print("1. Start the development servers:")
        print("   Backend:  cd backend && source venv/bin/activate && uvicorn app.main:app --reload")
        print("   Frontend: cd frontend && npm run dev")
        print("2. Access the application:")
        print("   - Frontend: http://localhost:3000")
        print("   - Backend API: http://localhost:8000")
        print("   - API Docs: http://localhost:8000/docs")

def main():
    """Main validation function"""
    print_header("DevHQ Setup Validation")
    print_info("This script will validate your DevHQ development environment setup.")
    
    # Store all results
    results = {}
    
    # Run all checks
    results['system_requirements'] = check_system_requirements()
    results['project_structure'] = check_project_structure()
    results['environment_files'] = check_environment_files()
    results['python_environment'] = check_python_environment()
    results['docker_services'] = check_docker_services()
    
    # Database connectivity check
    db_connected = check_database_connectivity()
    results['database'] = {'connectivity': db_connected}
    
    # Generate final report
    generate_report(results)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}Validation interrupted by user.{Colors.END}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.RED}Validation failed with error: {e}{Colors.END}")
        sys.exit(1)