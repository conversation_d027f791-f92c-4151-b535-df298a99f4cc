@echo off
REM DevHQ Windows Setup Script (Batch)
REM Simple batch script for setting up DevHQ on Windows

setlocal enabledelayedexpansion

echo.
echo ========================================
echo   DevHQ Windows Setup (Batch Version)
echo ========================================
echo.

REM Check if Python is installed
echo [INFO] Checking system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.11+ from https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    echo [SUCCESS] Python is installed
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
) else (
    echo [SUCCESS] Node.js is installed
)

REM Check if Git is installed
git --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Git is not installed or not in PATH
    echo Please install Git from https://git-scm.com/download/win
    pause
    exit /b 1
) else (
    echo [SUCCESS] Git is installed
)

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not in PATH
    echo Please install Docker Desktop from https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
) else (
    echo [SUCCESS] Docker is installed
)

echo.
echo [INFO] All system requirements met!
echo.

REM Create project directories
echo [INFO] Creating project directories...
if not exist "backend\logs" mkdir "backend\logs"
if not exist "frontend\.next" mkdir "frontend\.next"
if not exist "nginx\ssl" mkdir "nginx\ssl"
if not exist "data\postgres" mkdir "data\postgres"
if not exist "data\redis" mkdir "data\redis"
echo [SUCCESS] Project directories created!

REM Setup backend
echo.
echo [INFO] Setting up backend environment...
cd backend

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo [INFO] Creating Python virtual environment...
    python -m venv venv
    echo [SUCCESS] Virtual environment created!
) else (
    echo [INFO] Virtual environment already exists
)

REM Activate virtual environment and install dependencies
echo [INFO] Installing backend dependencies...
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
pip install -r requirements-dev.txt
echo [SUCCESS] Backend dependencies installed!

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo [INFO] Creating .env file from template...
    copy ".env.example" ".env" >nul
    echo [SUCCESS] .env file created!
    echo [WARNING] Please update .env file with your actual configuration values
) else (
    echo [WARNING] .env file already exists, skipping...
)

REM Go back to root
cd ..

REM Setup frontend
echo.
echo [INFO] Setting up frontend environment...
cd frontend

if exist "package.json" (
    echo [INFO] Installing frontend dependencies...
    npm install
    echo [SUCCESS] Frontend dependencies installed!
    
    REM Create frontend .env.local if it doesn't exist
    if not exist ".env.local" (
        echo [INFO] Creating frontend .env.local file...
        (
            echo # API Configuration
            echo NEXT_PUBLIC_API_URL=http://localhost:8000
            echo NEXT_PUBLIC_API_VERSION=v1
            echo.
            echo # External Services ^(replace with your keys^)
            echo NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
            echo NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_name
            echo.
            echo # Feature Flags
            echo NEXT_PUBLIC_ENABLE_PAYMENTS=true
            echo NEXT_PUBLIC_ENABLE_CLIENT_PORTAL=true
            echo.
            echo # Environment
            echo NODE_ENV=development
        ) > ".env.local"
        echo [SUCCESS] Frontend .env.local file created!
    ) else (
        echo [WARNING] Frontend .env.local file already exists, skipping...
    )
) else (
    echo [WARNING] Frontend package.json not found, skipping frontend setup
)

REM Go back to root
cd ..

REM Start Docker services
echo.
echo [INFO] Starting Docker services...
docker-compose up -d postgres redis
if errorlevel 1 (
    echo [WARNING] Failed to start Docker services
    echo Please ensure Docker Desktop is running and try: docker-compose up -d
) else (
    echo [SUCCESS] Docker services started!
    
    REM Wait for database
    echo [INFO] Waiting for database to be ready...
    timeout /t 10 /nobreak >nul
    
    REM Run database migrations
    echo [INFO] Running database migrations...
    cd backend
    call venv\Scripts\activate.bat
    alembic upgrade head
    if errorlevel 1 (
        echo [WARNING] Database migrations failed. You may need to run them manually.
    ) else (
        echo [SUCCESS] Database migrations completed!
    )
    cd ..
)

REM Setup complete
echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Update the .env files with your actual API keys
echo 2. Start the development servers:
echo    Backend:  cd backend ^&^& venv\Scripts\activate ^&^& uvicorn app.main:app --reload
echo    Frontend: cd frontend ^&^& npm run dev
echo.
echo Access points:
echo - Frontend: http://localhost:3000
echo - Backend API: http://localhost:8000
echo - API Documentation: http://localhost:8000/docs
echo.
echo Useful commands:
echo - Start all services: docker-compose up -d
echo - View logs: docker-compose logs -f
echo - Stop services: docker-compose down
echo - Run tests: cd backend ^&^& venv\Scripts\activate ^&^& pytest
echo.
echo Happy coding! 🚀
echo.
pause