# DevHQ Backend Implementation Guide

## 🏗️ Payment Gateway Integration & Payout System

### 1. Payment Gateway Setup Flow

#### API Endpoint: `POST /api/v1/users/me/payouts/onboarding-link`

```javascript
// Request Body
{
  "gateway": "paystack" | "korapay",
  "return_url": "https://devhq.app/payouts/callback"
}

// Response
{
  "onboarding_url": "https://connect.paystack.co/start/xyz123",
  "gateway": "paystack",
  "status": "pending",
  "expires_at": "2024-01-15T10:30:00Z"
}
```

#### Implementation Steps:

**For Paystack:**
```javascript
// 1. Create Paystack Subaccount
const subaccount = await paystack.subaccounts.create({
  business_name: user.company_name || `${user.first_name} ${user.last_name}`,
  settlement_bank: "pending", // Will be filled during onboarding
  account_number: "pending",
  percentage_charge: 2.9, // Platform fee
  description: `DevHQ Developer: ${user.email}`,
  primary_contact_email: user.email,
  primary_contact_name: user.first_name,
  primary_contact_phone: user.phone,
  metadata: {
    user_id: user.id,
    platform: "devhq"
  }
});

// 2. Generate onboarding link
const onboardingLink = `https://connect.paystack.co/start/${subaccount.subaccount_code}`;
```

**For Korapay:**
```javascript
// 1. Create Korapay Connected Account
const connectedAccount = await korapay.connectedAccounts.create({
  business_name: user.company_name || `${user.first_name} ${user.last_name}`,
  business_email: user.email,
  business_phone: user.phone,
  country: user.country,
  platform_fee: 2.9,
  metadata: {
    user_id: user.id,
    platform: "devhq"
  }
});

// 2. Generate onboarding link
const onboardingLink = korapay.connectedAccounts.getOnboardingUrl(connectedAccount.id);
```

### 2. Webhook Handling

#### Paystack Webhook: `POST /api/v1/webhooks/paystack`

```javascript
app.post('/api/v1/webhooks/paystack', async (req, res) => {
  const event = req.body;
  
  switch (event.event) {
    case 'subaccount.updated':
      // Update user payout status when bank details are verified
      await updateUserPayoutStatus(event.data.subaccount_code, 'active');
      break;
      
    case 'charge.success':
      // Handle successful payment
      if (event.data.subaccount) {
        await processPayment(event.data);
      }
      break;
      
    case 'transfer.success':
      // Handle successful payout to developer
      await markPayoutComplete(event.data);
      break;
  }
  
  res.status(200).send('OK');
});
```

#### Korapay Webhook: `POST /api/v1/webhooks/korapay`

```javascript
app.post('/api/v1/webhooks/korapay', async (req, res) => {
  const event = req.body;
  
  switch (event.event_type) {
    case 'connected_account.verified':
      await updateUserPayoutStatus(event.data.account_id, 'active');
      break;
      
    case 'payment.successful':
      if (event.data.connected_account_id) {
        await processPayment(event.data);
      }
      break;
      
    case 'payout.successful':
      await markPayoutComplete(event.data);
      break;
  }
  
  res.status(200).send('OK');
});
```

### 3. Payment Processing & Fee Calculation

```javascript
async function processPayment(paymentData) {
  const invoice = await Invoice.findOne({ 
    payment_reference: paymentData.reference 
  });
  
  if (!invoice) return;
  
  const developer = await User.findById(invoice.developer_id);
  const platformFeeRate = 0.029; // 2.9%
  const platformFeeFixed = 30; // $0.30
  
  // Calculate fees
  const grossAmount = paymentData.amount / 100; // Convert from kobo/cents
  const platformFee = (grossAmount * platformFeeRate) + platformFeeFixed;
  const gatewayFee = paymentData.fees / 100; // Gateway's fee
  const netAmount = grossAmount - platformFee - gatewayFee;
  
  // Create payout record
  const payout = await Payout.create({
    developer_id: developer.id,
    invoice_id: invoice.id,
    gross_amount: grossAmount,
    platform_fee: platformFee,
    gateway_fee: gatewayFee,
    net_amount: netAmount,
    gateway: developer.payout_gateway,
    gateway_reference: paymentData.reference,
    status: 'pending',
    scheduled_for: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // T+2
  });
  
  // Update invoice status
  await Invoice.findByIdAndUpdate(invoice.id, {
    status: 'paid',
    paid_at: new Date(),
    payment_reference: paymentData.reference
  });
  
  // Send receipt to client
  await sendClientReceipt(invoice, developer);
  
  // Notify developer
  await sendDeveloperNotification(developer, payout);
}
```

### 4. Automatic Payout Processing

```javascript
// Cron job: Run daily to process scheduled payouts
async function processScheduledPayouts() {
  const pendingPayouts = await Payout.find({
    status: 'pending',
    scheduled_for: { $lte: new Date() }
  }).populate('developer_id');
  
  for (const payout of pendingPayouts) {
    try {
      if (payout.gateway === 'paystack') {
        await processPaystackPayout(payout);
      } else if (payout.gateway === 'korapay') {
        await processKorapayPayout(payout);
      }
    } catch (error) {
      console.error(`Payout failed for ${payout.id}:`, error);
      await Payout.findByIdAndUpdate(payout.id, {
        status: 'failed',
        failure_reason: error.message
      });
    }
  }
}

async function processPaystackPayout(payout) {
  const developer = payout.developer_id;
  
  // Paystack automatically handles payouts to subaccounts
  // Just mark as processing and wait for webhook confirmation
  await Payout.findByIdAndUpdate(payout.id, {
    status: 'processing',
    processed_at: new Date()
  });
}

async function processKorapayPayout(payout) {
  const developer = payout.developer_id;
  
  const transfer = await korapay.transfers.create({
    connected_account_id: developer.korapay_account_id,
    amount: payout.net_amount * 100, // Convert to kobo
    currency: 'NGN',
    narration: `DevHQ Payout - Invoice ${payout.invoice_id}`,
    reference: `devhq_payout_${payout.id}`
  });
  
  await Payout.findByIdAndUpdate(payout.id, {
    status: 'processing',
    gateway_transfer_id: transfer.id,
    processed_at: new Date()
  });
}
```

## 📧 SMTP & White-label Communication System

### 1. Developer Branding Setup

#### API Endpoint: `PUT /api/v1/users/me/branding`

```javascript
{
  "company_name": "Acme Development",
  "company_logo_url": "https://cdn.devhq.app/logos/user123.png",
  "company_email": "<EMAIL>",
  "company_phone": "+**********",
  "company_address": "123 Tech Street, Lagos, Nigeria",
  "use_devhq_branding": false, // If true, use DevHQ branding instead
  "email_signature": "Best regards,\nJohn Doe\nAcme Development"
}
```

### 2. Email Templates

#### Payment Link Email Template
```html
<!DOCTYPE html>
<html>
<head>
  <title>Invoice Payment - {{company_name}}</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
    <div style="text-align: center; padding: 20px;">
      {{#if company_logo_url}}
        <img src="{{company_logo_url}}" alt="{{company_name}}" style="max-height: 60px;">
      {{else}}
        <h2>{{company_name}}</h2>
      {{/if}}
    </div>
    
    <h3>Invoice Payment Request</h3>
    <p>Hello {{client_name}},</p>
    <p>Please find your invoice for <strong>{{project_name}}</strong> below:</p>
    
    <div style="background: #f5f5f5; padding: 20px; margin: 20px 0;">
      <p><strong>Invoice #:</strong> {{invoice_number}}</p>
      <p><strong>Amount:</strong> {{currency}}{{amount}}</p>
      <p><strong>Due Date:</strong> {{due_date}}</p>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="{{payment_link}}" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px;">
        Pay Invoice
      </a>
    </div>
    
    <p>{{email_signature}}</p>
  </div>
</body>
</html>
```

### 3. SMTP Integration

```javascript
// Email service configuration
const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: true,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
});

async function sendPaymentLink(invoice, developer, clientEmail) {
  const template = await renderEmailTemplate('payment-link', {
    company_name: developer.company_name || 'DevHQ Developer',
    company_logo_url: developer.company_logo_url,
    client_name: invoice.client_name,
    project_name: invoice.project_name,
    invoice_number: invoice.invoice_number,
    amount: invoice.amount,
    currency: invoice.currency,
    due_date: invoice.due_date,
    payment_link: `https://devhq.app/payment/${invoice.id}`,
    email_signature: developer.email_signature
  });
  
  await transporter.sendMail({
    from: `"${developer.company_name}" <<EMAIL>>`,
    to: clientEmail,
    subject: `Invoice Payment - ${invoice.project_name}`,
    html: template
  });
}
```

### 4. Frontend Modal Implementation

```javascript
// Send Payment Link Modal Component
const SendPaymentLinkModal = ({ invoice, isOpen, onClose }) => {
  const [clientEmail, setClientEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleSend = async () => {
    setIsLoading(true);
    try {
      await fetch(`/api/v1/invoices/${invoice.id}/send-payment-link`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_email: clientEmail,
          custom_message: message
        })
      });
      
      alert('Payment link sent successfully!');
      onClose();
    } catch (error) {
      alert('Failed to send payment link');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <h3>Send Payment Link</h3>
      <input
        type="email"
        placeholder="Client email address"
        value={clientEmail}
        onChange={(e) => setClientEmail(e.target.value)}
      />
      <textarea
        placeholder="Additional message (optional)"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
      />
      <button onClick={handleSend} disabled={isLoading}>
        {isLoading ? 'Sending...' : 'Send Payment Link'}
      </button>
    </Modal>
  );
};
```

This implementation provides:
- ✅ Proper gateway integration with real onboarding flows
- ✅ Automated fee calculation and payout processing
- ✅ White-label email communications
- ✅ SMTP integration for client communications
- ✅ No client accounts needed - everything via email
