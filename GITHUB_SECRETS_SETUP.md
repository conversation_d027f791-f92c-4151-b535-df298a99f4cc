# 🔐 GitHub Secrets Configuration Guide

_Complete setup guide for DevHQ CI/CD pipeline secrets_

## 🎯 Overview

DevHQ's revolutionary developer business platform requires several secrets for automated testing, building, and deployment of features including smart time tracking, no-account client portals, AI tax preparation, and integrated payment processing. This guide walks you through setting up all required secrets.

---

## 🚀 **Required Secrets**

### **Deployment Secrets**

#### **Fly.io Backend Deployment**

```bash
FLY_API_TOKEN
```

- **Purpose**: Deploy backend to Fly.io
- **How to get**:
  1. Install Fly CLI: `curl -L https://fly.io/install.sh | sh`
  2. Login: `fly auth login`
  3. Get token: `fly auth token`
- **Example**: `fo1_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

#### **Vercel Frontend Deployment**

```bash
VERCEL_TOKEN
VERCEL_ORG_ID
VERCEL_PROJECT_ID
```

- **Purpose**: Deploy frontend to Vercel
- **How to get**:
  1. **Login to Vercel with the CORRECT account** (codegoddy, not Spid3rmvn)
  2. Go to [Vercel Dashboard](https://vercel.com/dashboard)
  3. Settings → Tokens → Create new token
  4. **IMPORTANT**: Copy Organization ID from YOUR account settings (not another account)
  5. Import your project under YOUR account and copy Project ID from settings
  6. **Verify**: Make sure the org ID matches your GitHub username (codegoddy)
- **Example**:
  - `VERCEL_TOKEN`: `xxxxxxxxxxxxxxxxxxxxxxxxxx`
  - `VERCEL_ORG_ID`: `team_xxxxxxxxxxxxxxxxxxxxxxxx` (Must be YOUR org ID)
  - `VERCEL_PROJECT_ID`: `prj_xxxxxxxxxxxxxxxxxxxxxxxx` (Must be YOUR project ID)

### **Notification Secrets**

#### **Slack Notifications (Optional but Recommended)**

```bash
SLACK_WEBHOOK_URL
```

- **Purpose**: Send deployment and failure notifications to Slack
- **How to get**:
  1. Go to your Slack workspace
  2. Apps → Incoming Webhooks → Add to Slack
  3. Choose channel and copy webhook URL
- **Example**: `*****************************************************************************`

### **External Service Secrets (For Testing)**

#### **Paystack (Payment Testing)**

```bash
PAYSTACK_SECRET_KEY_TEST
PAYSTACK_PUBLIC_KEY_TEST
```

- **Purpose**: Test payment functionality in CI/CD
- **How to get**:
  1. Go to [Paystack Dashboard](https://dashboard.paystack.com/)
  2. Settings → API Keys & Webhooks → Copy test keys
  3. Note: Webhook secret is auto-generated when you create webhook endpoints
- **Example**:
  - `PAYSTACK_SECRET_KEY_TEST`: `sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
  - `PAYSTACK_PUBLIC_KEY_TEST`: `pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

#### **Cloudinary (File Upload Testing)**

```bash
CLOUDINARY_URL_TEST
```

- **Purpose**: Test file upload functionality
- **How to get**:
  1. Go to [Cloudinary Console](https://cloudinary.com/console)
  2. Dashboard → Copy Environment variable
- **Example**: `cloudinary://123456789012345:abcdefghijklmnopqrstuvwxyz1234567890@your-cloud-name`

---

## ⚙️ **How to Set Up Secrets**

### **Step 1: Access Repository Settings**

1. Go to your GitHub repository
2. Click **Settings** tab
3. In the left sidebar, click **Secrets and variables** → **Actions**

### **Step 2: Add Repository Secrets**

1. Click **New repository secret**
2. Enter the secret name (exactly as shown above)
3. Paste the secret value
4. Click **Add secret**

### **Step 3: Verify Secrets**

After adding all secrets, you should see:

```
Repository secrets (8)
├── FLY_API_TOKEN                 ✅
├── VERCEL_TOKEN                  ✅
├── VERCEL_ORG_ID                 ✅
├── VERCEL_PROJECT_ID             ✅
├── SLACK_WEBHOOK_URL             ✅ (Optional)
├── PAYSTACK_SECRET_KEY_TEST      ✅ (For payment tests)
├── PAYSTACK_PUBLIC_KEY_TEST      ✅ (For payment tests)
└── CLOUDINARY_URL_TEST           ✅ (For upload tests)
```

---

## 🔧 **Environment-Specific Secrets**

### **Development Environment**

For local development, create `.env` files (never commit these!):

```bash
# backend/.env
DATABASE_URL=postgresql://devhq_user:devhq_password@localhost:5432/devhq
SECRET_KEY=your-local-secret-key
PAYSTACK_SECRET_KEY=sk_test_your_test_key
PAYSTACK_PUBLIC_KEY=pk_test_your_test_key
CLOUDINARY_URL=cloudinary://your_test_url

# frontend/.env.local
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY=pk_test_your_test_key
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_test_cloud
```

### **Production Environment**

Production secrets are managed through:

- **Fly.io**: `fly secrets set SECRET_KEY=your-production-key`
- **Vercel**: Environment Variables in project settings

---

## 🧪 **Testing Your Setup**

### **Verify CI/CD Pipeline**

1. Push a commit to `develop` branch
2. Check Actions tab for running workflows
3. Verify all jobs pass:
   - ✅ Backend Tests & Quality
   - ✅ Frontend Tests & Quality
   - ✅ Security Scanning
   - ✅ Build Docker Images
   - ✅ Deploy to Staging

### **Check Notifications**

If Slack is configured, you should receive:

- ✅ Success notifications for deployments
- ❌ Failure notifications for any issues

### **Verify Deployments**

- **Staging**: Check if staging URLs are accessible
- **Production**: Verify production deployment on main branch

---

## 🔒 **Security Best Practices**

### **Secret Management**

- ✅ **Never commit secrets** to version control
- ✅ **Use different secrets** for dev/staging/production
- ✅ **Rotate secrets regularly** (every 90 days)
- ✅ **Use least privilege** - only grant necessary permissions
- ✅ **Monitor secret usage** in GitHub Actions logs

### **Access Control**

- ✅ **Limit repository access** to trusted team members
- ✅ **Use branch protection** rules for main/develop
- ✅ **Require PR reviews** before merging
- ✅ **Enable 2FA** on all accounts (GitHub, Fly.io, Vercel)

### **Audit Trail**

- ✅ **Monitor Actions usage** in repository insights
- ✅ **Review deployment logs** regularly
- ✅ **Set up alerts** for failed deployments
- ✅ **Track secret access** in audit logs

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **"Secret not found" Error**

```bash
Error: Secret VERCEL_TOKEN not found
```

**Solution**:

1. Check secret name spelling (case-sensitive)
2. Verify secret is added to repository (not organization)
3. Ensure secret has a value (not empty)

#### **"Invalid token" Error**

```bash
Error: Invalid or expired token
```

**Solution**:

1. Regenerate the token from the service provider
2. Update the secret in GitHub
3. Re-run the workflow

#### **"Permission denied" Error**

```bash
Error: Permission denied for deployment
```

**Solution**:

1. Check token permissions in service provider
2. Ensure token has deployment permissions
3. Verify organization/project access

#### **"Wrong Vercel Account" Error**

```bash
Error: Using wrong GitHub account (Spid3rmvn instead of codegoddy)
```

**This is a common issue when VERCEL_ORG_ID points to the wrong account.**

**Solution**:

1. **Login to Vercel with YOUR account**:

   ```bash
   npx vercel login
   # Make sure you login with codegoddy, not Spid3rmvn
   ```

2. **Get YOUR correct Organization ID**:

   ```bash
   npx vercel teams ls
   ```

   Or go to [Vercel Dashboard](https://vercel.com/dashboard) → Settings → General

3. **Update GitHub Secret**:
   - Go to GitHub repo → Settings → Secrets and variables → Actions
   - Find `VERCEL_ORG_ID` and click **Update**
   - Replace with YOUR organization ID (should match codegoddy account)

4. **If project was created under wrong account**:

   ```bash
   # Import project under YOUR account
   npx vercel --scope=your-correct-org-id
   ```

   Then update `VERCEL_PROJECT_ID` secret with the new project ID

5. **Verify the fix**:
   - Push a commit to trigger deployment
   - Check that deployment uses codegoddy account, not Spid3rmvn

### **Debug Steps**

1. **Check Actions logs** for detailed error messages
2. **Verify secret values** (without exposing them)
3. **Test tokens manually** using CLI tools
4. **Contact support** if service-specific issues persist

---

## 📚 **Additional Resources**

### **Documentation Links**

- [GitHub Secrets Documentation](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
- [Fly.io Deployment Guide](https://fly.io/docs/hands-on/deploy-app/)
- [Vercel Deployment Documentation](https://vercel.com/docs/concepts/deployments/overview)
- [Stripe API Keys Guide](https://stripe.com/docs/keys)
- [Cloudinary Setup Guide](https://cloudinary.com/documentation/how_to_integrate_cloudinary)

### **Security Resources**

- [GitHub Security Best Practices](https://docs.github.com/en/actions/security-guides/security-hardening-for-github-actions)
- [OWASP Secrets Management](https://owasp.org/www-community/vulnerabilities/Insufficient_Cryptography)
- [12-Factor App Config](https://12factor.net/config)

---

## ✅ **Setup Checklist**

Use this checklist to ensure everything is configured correctly:

### **Required Secrets**

- [ ] `FLY_API_TOKEN` - Backend deployment
- [ ] `VERCEL_TOKEN` - Frontend deployment
- [ ] `VERCEL_ORG_ID` - Vercel organization
- [ ] `VERCEL_PROJECT_ID` - Vercel project

### **Optional Secrets**

- [ ] `SLACK_WEBHOOK_URL` - Notifications
- [ ] `PAYSTACK_SECRET_KEY_TEST` - Payment testing
- [ ] `PAYSTACK_PUBLIC_KEY_TEST` - Payment testing
- [ ] `CLOUDINARY_URL_TEST` - File upload testing

### **Verification**

- [ ] Push to `develop` branch triggers staging deployment
- [ ] Push to `main` branch triggers production deployment
- [ ] All CI/CD jobs pass successfully
- [ ] Slack notifications work (if configured)
- [ ] Staging and production URLs are accessible

### **Security**

- [ ] All secrets are properly configured
- [ ] No secrets committed to version control
- [ ] Branch protection rules enabled
- [ ] 2FA enabled on all accounts
- [ ] Regular secret rotation scheduled

---

**🎉 Once all secrets are configured, your DevHQ CI/CD pipeline will be fully automated and production-ready!**
