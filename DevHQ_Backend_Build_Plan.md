# DevHQ Backend Build Plan - 4 Week Sprint

_Aggressive timeline to get game-changing MVP live in production_

## 🎯 Project Overview

**Goal**: Complete DevHQ backend with revolutionary features deployed to production in 4 weeks
**Stack**: FastAPI + PostgreSQL + SQLAlchemy + Alembic + Docker + Fly.io + Cloudinary + Paystack
**Database**: Enterprise-ready schema optimized for <$5/month with workspace isolation
**Game-Changers**: Smart time tracking, No-account client portal, AI tax preparation, Enhanced CRM, Client approval workflows, Integrated payments

---

## 📅 Week 1: Foundation & Authentication (Days 1-7)

### Day 1-2: Project Setup & Database

```bash
# Project structure
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI app
│   ├── config.py            # Settings & environment
│   ├── database.py          # DB connection & session
│   └── models/              # SQLAlchemy models
│       ├── __init__.py
│       ├── user.py
│       ├── organization.py
│       └── base.py
├── alembic/                 # Database migrations
├── tests/
├── requirements.txt
├── Dockerfile
└── docker-compose.yml
```

**Tasks:**

- [ ] Initialize FastAPI project with proper structure
- [ ] Set up PostgreSQL with Docker Compose
- [ ] Create SQLAlchemy models for Phase 1 tables (users, user_settings, user_sessions)
- [ ] Set up Alembic for migrations
- [ ] Create initial migration
- [ ] Basic FastAPI app with health check endpoint

### Day 3-4: Authentication System

**Models to implement:**

- `User` (with password hashing)
- `UserSession` (JWT refresh tokens)
- `UserSettings` (preferences)

**Endpoints:**

```python
POST /auth/register          # User registration
POST /auth/login            # Login with JWT
POST /auth/refresh          # Refresh access token
POST /auth/logout           # Invalidate refresh token
GET  /auth/me               # Get current user profile
PUT  /auth/me               # Update user profile
```

**Key Features:**

- Argon2 password hashing
- JWT access (15min) + refresh (7d) tokens
- Rate limiting on auth endpoints
- Input validation with Pydantic

### Day 5-7: User Settings & Testing

**Endpoints:**

```python
GET  /settings              # Get user preferences
PUT  /settings              # Update preferences
```

**Testing Setup:**

- Pytest configuration
- Test database setup
- Authentication test suite
- CI/CD pipeline basics

**Deliverable**: Working auth system with user management

---

## 📅 Week 2: Core Business Logic (Days 8-14)

### Day 8-9: Clients & Projects (Enhanced CRM)

**Models:**

- `Client` (with CRM features + portal access)
- `Project` (with progress tracking)
- `ProjectMilestone`

**Endpoints:**

```python
# Clients (Enhanced CRM)
GET    /clients             # List clients with lead status filtering
POST   /clients             # Create client with CRM fields
GET    /clients/{id}        # Get client details + CRM data
PUT    /clients/{id}        # Update client + CRM tracking
DELETE /clients/{id}        # Soft delete client
PUT    /clients/{id}/status # Update lead status (prospect->active->etc)
POST   /clients/{id}/portal # Generate portal access token
GET    /clients/{id}/portal/disable # Disable portal access

# Projects (Enhanced with Progress)
GET    /projects            # List projects with completion %
POST   /projects            # Create project with milestones
GET    /projects/{id}       # Get project + milestones + time tracking
PUT    /projects/{id}       # Update project + recalculate progress
DELETE /projects/{id}       # Soft delete project
GET    /projects/{id}/stats # Enhanced stats with time/billing data

# Project Milestones
GET    /projects/{id}/milestones    # List project milestones
POST   /projects/{id}/milestones    # Create milestone
PUT    /milestones/{id}             # Update milestone progress
DELETE /milestones/{id}             # Delete milestone
```

### Day 10-11: Tasks & Time Tracking (Game-Changer!)

**Models:**

- `Task` (enhanced with billable tracking)
- `TimeEntry` (the productivity revolution!)

**Endpoints:**

```python
# Tasks (Enhanced)
GET    /tasks               # List tasks with time tracking data
POST   /tasks               # Create task with billable settings
GET    /tasks/{id}          # Get task + time entries
PUT    /tasks/{id}          # Update task
DELETE /tasks/{id}          # Soft delete task
PUT    /tasks/{id}/status   # Update task status + auto-stop timer

# Time Tracking (Revolutionary Feature!)
POST   /tasks/{id}/time/start    # Start timer for task
POST   /tasks/{id}/time/stop     # Stop timer + calculate duration
GET    /tasks/{id}/time          # Get all time entries for task
PUT    /time-entries/{id}        # Edit time entry
DELETE /time-entries/{id}        # Delete time entry

# Time Analytics & Billing
GET    /time/dashboard           # Time tracking dashboard
GET    /time/weekly             # Weekly time report
GET    /time/billable           # Unbilled time entries
GET    /projects/{id}/time      # Project time summary
GET    /time/export             # Export time data for invoicing
```

### Day 12-14: Wallet System (Enhanced with Tax Features!)

**Models:**

- `WalletAccount`
- `Transaction` (enhanced with tax categorization)

**Endpoints:**

```python
# Wallet Accounts
GET    /wallet/accounts     # List accounts
POST   /wallet/accounts     # Create account
PUT    /wallet/accounts/{id} # Update account
DELETE /wallet/accounts/{id} # Soft delete account

# Transactions (Enhanced with Tax Features)
GET    /wallet/transactions # List transactions with tax filtering
POST   /wallet/transactions # Create transaction with tax category
GET    /wallet/transactions/{id} # Get transaction + receipt
PUT    /wallet/transactions/{id} # Update transaction + tax info
DELETE /wallet/transactions/{id} # Soft delete transaction
POST   /wallet/transactions/{id}/receipt # Upload receipt to Cloudinary

# Tax & Financial Analytics (Game-Changer for Freelancers!)
GET    /wallet/tax-summary/{year}    # Annual tax summary
GET    /wallet/tax-categories        # List all tax categories
GET    /wallet/deductible-expenses   # Tax-deductible expenses
GET    /wallet/tax-export/{year}     # Export tax data for accountant
GET    /wallet/dashboard             # Enhanced financial dashboard
GET    /wallet/export                # Export transactions (CSV/JSON)
```

**Deliverable**: Core business logic with full CRUD operations

---

## 📅 Week 3: Advanced Features (Days 15-21)

### Day 15-16: Notes & File Uploads

**Models:**

- `ProjectNote`
- `DesignUpload`

**Endpoints:**

```python
# Project Notes
GET    /projects/{id}/notes # List project notes
POST   /projects/{id}/notes # Create note
GET    /notes/{id}          # Get note
PUT    /notes/{id}          # Update note (auto-save)
DELETE /notes/{id}          # Soft delete note

# Design Uploads
POST   /uploads             # Upload to Cloudinary
GET    /uploads             # List uploads
DELETE /uploads/{id}        # Delete upload
```

**Cloudinary Integration:**

- File upload handling
- Image optimization
- Secure URL generation

### Day 17-18: Revolutionary Payment System! 💳

**Models:**

- `Invoice` (enhanced with payment integration)
- `InvoiceItem`
- `PaymentTransaction`
- `DeveloperPaymentSettings`
- `DeveloperPayout`

**Core Invoicing Endpoints:**

```python
GET    /invoices            # List invoices with payment status
POST   /invoices            # Create invoice with payment options
GET    /invoices/{id}       # Get invoice + payment history
PUT    /invoices/{id}       # Update invoice
DELETE /invoices/{id}       # Soft delete invoice
PUT    /invoices/{id}/status # Update status (sent, paid, etc.)
POST   /invoices/from-time  # Create invoice from time entries
```

**🚀 GAME-CHANGING PAYMENT ENDPOINTS:**

```python
# Payment Setup (One-time developer onboarding)
POST   /payments/setup      # Initialize Stripe Connect
GET    /payments/onboard    # Stripe Connect onboarding URL
GET    /payments/status     # Check onboarding completion

# Payment Link Generation
POST   /invoices/{id}/payment-link    # Generate secure payment link
GET    /invoices/{id}/payment-link    # Get existing payment link
DELETE /invoices/{id}/payment-link    # Disable payment link

# Public Payment Processing (No Auth Required!)
GET    /pay/{token}         # Payment page for clients
POST   /pay/{token}/process # Process client payment
GET    /pay/{token}/status  # Check payment status

# Payment Tracking & Analytics
GET    /payments/dashboard  # Payment analytics dashboard
GET    /payments/transactions # List payment transactions
GET    /payments/payouts    # Developer payout history
POST   /webhooks/stripe     # Stripe webhook handler
```

**Why This Changes Everything:**

- **Zero-friction client payments** - No accounts needed!
- **Automatic wallet integration** - Payments sync instantly
- **Professional payment pages** - Developer branding
- **Real-time status updates** - Know immediately when paid
- **Automated payouts** - Weekly transfers to bank account
- **Platform revenue** - 2.9% + $0.30 per transaction

### Day 19-21: Client Portal & Analytics (Revolutionary!)

**The Game-Changer: No-Account Client Portal**

**Client Portal Endpoints (Public - No Auth Required!):**

```python
# Client Portal (Revolutionary Feature!)
GET    /portal/{token}                    # Client portal dashboard
GET    /portal/{token}/projects           # Client's projects overview
GET    /portal/{token}/projects/{id}      # Detailed project view
GET    /portal/{token}/milestones         # Project milestones progress
GET    /portal/{token}/invoices           # Client's invoices
GET    /portal/{token}/invoices/{id}      # Invoice details
GET    /portal/{token}/time-summary       # Billable hours summary
POST   /portal/{token}/approve/{item_id}  # Approve deliverable/milestone
GET    /portal/{token}/files              # Project files/designs
```

**Analytics & Export:**

```python
GET    /export/projects     # Export projects (CSV/JSON)
GET    /export/tasks        # Export tasks
GET    /export/transactions # Export financial data
GET    /export/time         # Export time tracking data
GET    /export/full         # Full data export

GET    /analytics/dashboard # Main dashboard data
GET    /analytics/projects  # Project analytics
GET    /analytics/financial # Financial analytics
GET    /analytics/time      # Time tracking analytics
GET    /analytics/client-engagement # Portal usage stats
```

**Why This Changes Everything:**

- Clients see real-time progress without accounts
- Builds trust and transparency
- Reduces "where are we?" emails by 90%
- Professional differentiation from competitors
- Milestone approvals streamline payments

**Deliverable**: Complete game-changing MVP feature set

---

## 📅 Week 4: Production & Deployment (Days 22-28)

### Day 22-23: Strategic Implementation & Polish

**🎯 MVP-First Approach (Based on Strategic Insights):**

**Simplified Payment System:**
- ✅ **Stripe Checkout/Payment Links** (no Connect complexity)
- ✅ **Direct developer payouts** (manual for MVP)
- ✅ **Zero KYC requirements** (funds don't flow through platform)
- ⏳ **Stripe Connect** → Phase 2 (when ready for platform fees)

**Background Jobs (Sync First, Async Later):**
```python
# MVP: Synchronous operations
- PDF generation: Generate immediately
- Email sending: Send directly via SMTP
- Invoice reminders: Simple cron jobs

# Phase 2: Move to Celery/Redis
- Async PDF generation
- Reliable email delivery
- Advanced scheduling
```

**🔐 Enhanced Client Portal Security:**
```python
# Revolutionary security features
POST /clients/{id}/portal/generate    # Generate secure portal access
PUT  /clients/{id}/portal/passcode    # Set optional passcode
POST /clients/{id}/portal/regenerate  # Invalidate old token, create new
GET  /clients/{id}/portal/analytics   # Portal usage analytics
```

**🎯 Client Approval Workflow System:**
```python
# Game-changing collaboration features
POST /approvals                       # Submit deliverable for approval
GET  /approvals                       # List pending approvals
PUT  /approvals/{id}/approve          # Client approves deliverable
PUT  /approvals/{id}/request-revision # Client requests changes
POST /approvals/{id}/feedback         # Client leaves feedback

# Public approval endpoints (no auth required!)
GET  /approve/{token}                 # Client approval page
POST /approve/{token}/submit          # Submit approval decision
```

### Day 24-25: Production Setup

**Infrastructure:**

- Fly.io deployment configuration
- Environment variable management
- Database migration strategy
- SSL/HTTPS setup
- CORS configuration
- Rate limiting
- Logging and monitoring

**Security Hardening:**

- Input sanitization
- SQL injection prevention
- XSS protection
- Rate limiting
- HTTPS enforcement

### Day 26-27: Testing & Bug Fixes

- Comprehensive test suite
- Integration testing
- Performance testing
- Security testing
- Bug fixes and optimizations

### Day 28: Go Live!

- Final deployment
- Database migration
- Smoke testing
- Documentation
- Monitoring setup

**Deliverable**: Production-ready DevHQ backend

---

## 🛠️ Technical Implementation Strategy

### Database Strategy

```python
# Revolutionary feature implementation in phases:
Phase 1: users, user_settings, user_sessions, clients (with CRM), projects (with progress)
Phase 2: tasks, time_entries (time tracking), project_milestones
Phase 3: wallet_accounts, transactions (with tax features), project_notes, design_uploads
Phase 4: invoices, invoice_items, background_jobs, notifications
```

### FastAPI Structure (Enhanced)

```python
# app/routers/
auth.py          # Authentication endpoints
clients.py       # Client management + CRM features
projects.py      # Project management + milestones + progress tracking
tasks.py         # Task management + time tracking integration
time.py          # Time tracking + productivity analytics (GAME-CHANGER!)
wallet.py        # Financial tracking + tax categorization
notes.py         # Note management
uploads.py       # File uploads + receipt management
invoices.py      # Invoicing + time entry integration
portal.py        # Client portal (NO-ACCOUNT ACCESS!) 🚀
analytics.py     # Dashboard & analytics + client engagement
export.py        # Data export + tax reports
```

### Revolutionary Features Implementation

```python
# Game-changing features that set DevHQ apart:

1. Time Tracking Revolution:
   - Real-time timer start/stop
   - Automatic billable hours calculation
   - Integration with invoicing
   - Productivity analytics

2. No-Account Client Portal:
   - Secure token-based access
   - Real-time project progress
   - Milestone approvals
   - Invoice viewing
   - Zero friction for clients

3. Tax Preparation Automation:
   - Expense categorization
   - Receipt storage (Cloudinary)
   - Annual tax summaries
   - Deductible expense tracking

4. Enhanced CRM Pipeline:
   - Lead status tracking
   - Source attribution
   - Project value estimation
   - Contact history

5. Milestone-Driven Development:
   - Client-visible progress
   - Payment milestones
   - Automatic progress calculation
   - Timeline management
```

### Key Libraries

```txt
fastapi==0.104.1
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
passlib[argon2]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
cloudinary==1.36.0
pytest==7.4.3
pytest-asyncio==0.21.1
```

---

## 🚀 Daily Workflow

### Morning (2-3 hours)

1. Review previous day's work
2. Plan current day tasks
3. Implement core features
4. Write tests as you go

### Afternoon (2-3 hours)

1. Continue implementation
2. Test endpoints with Postman/curl
3. Fix bugs and refactor
4. Update documentation

### Evening (1 hour)

1. Commit and push code
2. Plan next day
3. Update progress tracking

---

## 📊 Success Metrics

**Week 1**: ✅ Authentication working, users can register/login
**Week 2**: ✅ CRUD operations for clients, projects, tasks, wallet
**Week 3**: ✅ File uploads, invoicing, data export working
**Week 4**: ✅ Production deployment, all features tested

---

## 🔥 Pro Tips for Speed

1. **Use FastAPI's automatic docs** - Test endpoints instantly at `/docs`
2. **SQLAlchemy relationships** - Let the ORM handle joins
3. **Pydantic models** - Automatic validation and serialization
4. **Docker Compose** - Consistent development environment
5. **Alembic auto-generate** - Let it create migrations from model changes
6. **pytest fixtures** - Reusable test data setup
7. **GitHub Copilot** - Speed up boilerplate code

This plan is aggressive but totally doable. Focus on MVP features first, skip nice-to-haves, and you'll have a production-ready DevHQ backend in 4 weeks!

Ready to start? Let's begin with Day 1 setup! 🚀
