# DevHQ Backend - Dependency Installation Guide

## New Dependencies Added

### Security Dependencies
```bash
# Install bcrypt for enhanced password hashing
pip install bcrypt

# Install python-jose for JWT token management (if not already installed)
pip install python-jose[cryptography]

# Install passlib for additional password utilities (already in requirements)
# pip install passlib
```

### Development Dependencies
```bash
# Install pytest for testing (if not already installed)
pip install pytest

# Install pytest-asyncio for async testing
pip install pytest-asyncio

# Install httpx for HTTP client testing
pip install httpx

# Install pytest-mock for mocking in tests
pip install pytest-mock
```

## Complete Installation Commands

### For Development Environment
```bash
# Navigate to project root
cd /home/<USER>/Desktop/dev-hq

# Activate virtual environment
source backend/venv/bin/activate

# Install new security dependencies
pip install bcrypt

# Install development dependencies (if needed)
pip install pytest pytest-asyncio httpx pytest-mock

# Run tests to verify installation
python -m pytest backend/tests/ -v
```

### For Production Environment
```bash
# Navigate to project root
cd /home/<USER>/Desktop/dev-hq

# Activate virtual environment
source backend/venv/bin/activate

# Install only runtime dependencies
pip install bcrypt

# Verify installation
python -c "import bcrypt; print('bcrypt installed successfully')"
```

## Dependency Versions Used

### Security Packages
```
bcrypt==4.3.0
python-jose==3.3.0
passlib==1.7.4
```

### Testing Packages
```
pytest==7.4.4
pytest-asyncio==0.21.2
httpx==0.25.0
pytest-mock==3.14.1
```

## Verification Commands

### Check Installed Packages
```bash
# List all installed packages
pip list

# Check specific security packages
pip show bcrypt python-jose passlib

# Check testing packages
pip show pytest pytest-asyncio httpx pytest-mock
```

### Run Security Tests
```bash
# Navigate to backend directory
cd /home/<USER>/Desktop/dev-hq/backend

# Run security service tests
python -m pytest tests/test_security_service.py -v

# Run rate limiting tests
python -m pytest tests/test_rate_limiting.py -v

# Run security integration tests
python -m pytest tests/test_security_integration.py -v
```

### Run All Tests
```bash
# Navigate to backend directory
cd /home/<USER>/Desktop/dev-hq/backend

# Run all tests to verify complete installation
python -m pytest tests/ -v --tb=short
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
```bash
# Solution: Use --user flag or sudo (not recommended for venv)
pip install --user bcrypt
```

2. **Package Already Installed**
```bash
# Solution: Upgrade existing package
pip install --upgrade bcrypt
```

3. **Virtual Environment Not Activated**
```bash
# Solution: Activate virtual environment first
source backend/venv/bin/activate
```

4. **Python Version Compatibility**
```bash
# Check Python version
python --version

# Ensure Python 3.8+ is used
```

### Verification Script
```bash
#!/bin/bash
# verify_installation.sh

echo "Verifying DevHQ Backend Dependencies..."

# Check if in correct directory
if [[ ! -f "requirements.txt" ]]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Activate virtual environment
source backend/venv/bin/activate

# Check required packages
REQUIRED_PACKAGES=("bcrypt" "python-jose" "passlib" "pytest" "httpx")

echo "Checking installed packages..."
for package in "${REQUIRED_PACKAGES[@]}"; do
    if pip show "$package" > /dev/null 2>&1; then
        echo "✓ $package is installed"
    else
        echo "✗ $package is NOT installed"
        echo "Installing $package..."
        pip install "$package"
    fi
done

echo "Running security tests..."
cd backend
python -m pytest tests/test_security_service.py tests/test_rate_limiting.py -v --tb=short

echo "Installation verification complete!"
```

## Docker Environment (if applicable)

### Update Dockerfile
```dockerfile
# Add to existing Dockerfile
RUN pip install bcrypt

# Or update requirements.txt and rebuild
COPY requirements.txt .
RUN pip install -r requirements.txt
```

### Docker Compose (if applicable)
```yaml
# docker-compose.yml
services:
  backend:
    build: .
    depends_on:
      - db
      - redis
    environment:
      - PYTHONPATH=/app
```

## Next Steps

1. **Run All Tests**: 
   ```bash
   cd /home/<USER>/Desktop/dev-hq/backend
   python -m pytest tests/ -v
   ```

2. **Start Development Server**:
   ```bash
   cd /home/<USER>/Desktop/dev-hq/backend
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Verify Health Check**:
   ```bash
   curl http://localhost:8000/config/health
   ```

## Support

For any installation issues, contact:
- **Development Team**: <EMAIL>
- **Infrastructure Team**: <EMAIL>

### Emergency Contacts:
- **Primary**: +234-XXX-XXXX-XXXX (Development Lead)
- **Secondary**: +234-XXX-XXXX-XXXX (Infrastructure Lead)