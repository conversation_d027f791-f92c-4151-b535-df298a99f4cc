# 🧾 DevHQ Billing & Invoicing Workflows: Architecture for Flexibility

*A deep dive into how DevHQ supports various client billing agreements with flexible, developer-controlled invoicing.*

## 🎯 **1. Overview: Empowering the Developer**

A core principle of DevHQ is that the developer must have complete control over their business processes. This is especially true for invoicing and payments. There is no "one-size-fits-all" billing agreement, so the platform is designed to be a flexible toolkit rather than a rigid, automated system.

**Key Principle:** Invoices and their corresponding payment links are **never** sent automatically. The developer **always** initiates the invoicing process manually, ensuring it aligns perfectly with their client agreement.

### **Why This is Crucial:**

- **Supports All Billing Models:** Accommodates payment per milestone, upfront deposits, final payments, and recurring retainers.
- **Builds Client Trust:** Prevents accidental or premature invoices, ensuring the client is only billed when the developer deems it appropriate.
- **Gives the Developer Control:** The developer manages their cash flow and client relationship according to their own terms.

---

## 🌊 **2. Supported Billing Scenarios & Workflows**

DevHQ's invoicing system is designed to seamlessly handle the most common freelance billing models.

### **Scenario A: Payment Per Milestone**

*Ideal for large, phased projects.*

1. **Client Action:** The client approves a specific milestone (e.g., "Phase 1: Design") in their portal.
2. **Developer Action:** The developer receives the approval notification. They navigate to the "Invoicing" section.
3. **Invoice Creation:** The developer creates a new invoice for the project. The system presents a list of billable items, including the newly approved milestone.
4. **Finalization:** The developer selects only that milestone, generates the invoice, and sends it to the client.

### **Scenario B: Upfront Deposit (e.g., 50% Down)**

*Essential for securing new projects and covering initial costs.*

1. **Developer Action:** Before any work begins, the developer navigates to the "Invoicing" section.
2. **Invoice Creation:** They create a new invoice for the project.
3. **Manual Line Item:** Instead of selecting a milestone, they manually add a line item like:
    - *Description:* "Project Deposit (50% of Total Budget)"
    - *Amount:* A fixed value (e.g., $5,000).
4. **Finalization:** The developer sends this deposit invoice to the client to be paid before the project kicks off.

### **Scenario C: Time & Materials Billing**

*Perfect for projects with flexible scope.*

the client approves the final milestone of the project.
2. **Developer Action:** The developer creates a final invoice.
3. **Invoice Creation:** They select all remaining unbilled milestones and time entries.
4. **Finalization:** A comprehensive final invoice for the total project value is generated and sent.

---

## 🛠️ **3. Technical Implementation**

This flexibility is achieved through the design of our invoicing and data models.

### **Data Model Considerations:**

- **`Invoice` Model:** Is a standalone object. It is not automatically triggered by a change in a `ProjectMilestone`'s status.
- **`InvoiceItem` Model:** Can be linked to a `TimeEntry` or a `ProjectMilestone`, but can also be a simple, unlinked record (for manual items like deposits). This is the key to its flexibility.
- **Billable Status:** `TimeEntry` and `ProjectMilestone` models should have a status field like `billing_status` (`unbilled`, `invoiced`, `paid`) to prevent double-billing.

### **Backend & API Logic:**

- **`GET /api/v1/projects/{project_id}/billable-items`:** This is a new, crucial endpoint. When a developer starts creating an invoice, the frontend calls this endpoint. It returns a list of all items for that project that are currently in an "unbilled" state (e.g., approved milestones, tracked time entries).
- **`POST /api/v1/invoices`:** The request body for creating an invoice will accept a list of IDs corresponding to the billable items the user selected, as well as a list of manual line items.

### **Frontend UI:**

- The "Create Invoice" page will be a dynamic interface.
- It will first call the `/billable-items` endpoint to populate a checklist of available milestones and time entries.
- It will also have an "Add Manual Line Item" button to support deposits or other custom charges.

---

## ✅ **4. Implementation Checklist**

- [ ] **Backend:** Add a `billing_status` field to `ProjectMilestone` and `TimeEntry` models.
- [ ] **Backend:** Create the `GET /api/v1/projects/{project_id}/billable-items` endpoint.
- [ ] **Backend:** Enhance the `POST /api/v1/invoices` endpoint to handle creation from both billable item IDs and manual line items.
- [ ] **Backend:** Implement the logic to update the `billing_status` of items once they are added to an invoice.
- [ ] **Frontend:** Build the dynamic "Create Invoice" page that fetches and displays billable items.
- [ ] **Frontend:** Implement the UI for adding manual line items to an invoice.
- [ ] **Testing:** Write tests for each billing scenario to ensure correct invoice generation and status updates.

By architecting the system this way, we provide a powerful and flexible invoicing tool that adapts to the developer's needs, not the other way around.
