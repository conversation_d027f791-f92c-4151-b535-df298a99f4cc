# 💳 DevHQ Payment Platform: Architecture & Best Practices

*A deep dive into the integrated payment system, the commercial engine of DevHQ.*

## 🎯 **1. Overview: Seamless & Strategic Payments**

The payment platform is designed to be more than just a way to collect money; it's the final, crucial step in a seamless business workflow. It transforms tracked time and project milestones directly into revenue, all within a single system.

Our strategy is twofold:

1. **Deep Integration:** Payments are not an afterthought. They are intrinsically linked to invoices, time tracking, and financial wallets.
2. **Strategic Market Focus:** We prioritize **Paystack** to provide a best-in-class experience for our core target market: African developers. This includes support for local payment methods and currencies (e.g., KES, M-Pesa).

### **Why It's a Core Differentiator:**

- **For the Developer:** Automates the entire billing lifecycle, from time entry to cash-in-wallet, saving hours of administrative work.
- **For the Client:** Provides a simple, secure, and professional payment experience via a direct link, with no account needed.

---

## 🌊 **2. The Payment Flow: From Invoice to Wallet**

1. **Invoice Finalization:** A developer creates and finalizes an invoice within the DevHQ dashboard.
2. **Secure Link Generation:** The backend generates a unique, unguessable `payment_link_token` and associates it with the invoice.
3. **Client Interaction:** The developer shares the link (e.g., `https://app.devhq.com/pay/<token>`) with the client.
4. **Payment Page:** The client clicks the link and is taken to a branded, public payment page showing the invoice details.
5. **Checkout:** The client pays using the embedded Paystack widget.
6. **Webhook Notification:** Paystack processes the payment and sends a `charge.success` event to a secure webhook endpoint on the DevHQ backend.
7. **Automated Reconciliation:** The DevHQ backend verifies the webhook, updates the invoice status to `paid`, creates an `income` transaction, and updates the developer's wallet balance.

---

## 🛠️ **3. Technical Architecture Deep Dive**

### **Data Models (Backend)**

The database schema from `scripts/seed_test_data.py` is the foundation for this flow.

```python
# In app/models/invoice.py
class Invoice(Base):
    # ...
    status = Column(String, default="draft") # e.g., draft, sent, paid, overdue
    payment_link_token = Column(String, unique=True, index=True, nullable=True) # UUID for public access

# In app/models/wallet.py
class WalletAccount(Base):
    # ...
    balance = Column(Numeric, default=0)

class Transaction(Base):
    # ...
    type = Column(String) # 'income' or 'expense'
    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"))
```

### **Backend Components (FastAPI)**

1. **Payment Link Service:** A service that generates the `payment_link_token` for a given invoice.

2. **Public Invoice Endpoint:** An unauthenticated endpoint that uses the token to fetch invoice details for the payment page.

    ```python
    # In a new app/routers/public_payment.py
    @router.get("/pay/{token}")
    def get_invoice_for_payment(token: str, db: Session = Depends(get_db)):
        invoice = db.query(Invoice).filter(Invoice.payment_link_token == token).first()
        if not invoice or invoice.status == 'paid':
            raise HTTPException(status_code=404, detail="Invoice not found or already paid.")
        # Return a Pydantic schema with client-safe invoice data
        return invoice
    ```

3. **Paystack Webhook Handler:** This is the most critical component. A dedicated endpoint to receive and process notifications from Paystack.

    ```python
    # In a new app/routers/webhooks.py
    @router.post("/webhooks/paystack")
    async def handle_paystack_webhook(request: Request):
        # 1. Get request body and signature from header
        # 2. Verify the signature using our Paystack secret key
        # 3. If valid, process the event (e.g., 'charge.success')
        # 4. Perform database updates within an atomic transaction
        # 5. Return a 200 OK to acknowledge receipt
        return {"status": "ok"}
    ```

### **Frontend Components (Next.js)**

1. **Dynamic Payment Page:** A public page that renders the invoice and payment widget.
    - `frontend/src/app/pay/[token]/page.tsx`

2. **Paystack Integration:** Use the official Paystack React library (`react-paystack`) to create a seamless and secure checkout experience.

---

## 🔒 **4. Security & Reliability Best Practices (Non-Negotiable)**

Handling money requires the highest level of scrutiny.

1. **Webhook Signature Verification:** **This is mandatory.** Never trust a webhook's payload without verifying its cryptographic signature. This prevents attackers from spoofing payment success events.

2. **Idempotent Webhook Handling:** Networks are unreliable; webhooks can be sent more than once. Your handler must be ableto process the same event multiple times without causing errors or duplicate data (e.g., crediting a payment twice). Use the unique event ID from Paystack to track processed events.

3. **Atomic Database Transactions:** The entire reconciliation logic (updating invoice, creating a transaction, updating wallet) must be wrapped in a single database transaction. If any part fails, the entire operation must roll back to maintain data integrity.

4. **Secure Token Handling:** The `payment_link_token` should be a `UUIDv4` and should be invalidated or removed once an invoice is paid to prevent replay attacks or confusion.

5. **Error Monitoring & Alerting:** Use a service like Sentry (already in the tech stack) to immediately alert you to any failures in the webhook processing logic. A failed payment reconciliation is a critical bug.

6. **Platform Fee Architecture (Post-MVP):** We will use **Paystack Subaccounts** for our African market focus. This is the correct, scalable way to handle platform fees, as it allows for splitting a transaction at the source.

---

## ✅ **5. Implementation Checklist**

- [ ] **Backend:** Add `payment_link_token` to `Invoice` model and create migration.
- [ ] **Backend:** Implement service to generate and assign payment links.
- [ ] **Backend:** Create the public endpoint to fetch invoice data by token.
- [ ] **Backend:** Build the Paystack webhook handler with signature verification, idempotency, and atomic transactions.
- [ ] **Backend:** Implement logic to update `WalletAccount` and create `Transaction` on successful payment.
- [ ] **Frontend:** Create the dynamic `pay/[token]` page.
- [ ] **Frontend:** Integrate the `react-paystack` library for the checkout experience.
- [ ] **DevOps:** Add `PAYSTACK_SECRET_KEY` to production environment variables and configure the webhook URL in the Paystack dashboard.
- [ ] **Security:** Write tests to simulate webhook events, including duplicate events and invalid signatures.
- [ ] **Monitoring:** Configure Sentry to monitor the webhook endpoint for any errors.

By adhering to these principles, the DevHQ payment platform will be a secure, reliable, and powerful engine for developer success.
