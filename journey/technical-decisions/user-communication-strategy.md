# 📧 DevHQ User Communication Strategy: Emails, Notifications & Branding

*A deep dive into how DevHQ communicates with its users and their clients, ensuring a professional, trustworthy, and seamless experience.*

## 🎯 **1. Overview: Building Trust Through Communication**

Every email and notification sent from DevHQ is an extension of our brand and, more importantly, an extension of our users' professional brand. Our communication strategy is built on three core principles:

- **Clarity:** Every message must be clear, concise, and immediately understandable. No jargon, no marketing fluff.
- **Helpfulness:** Communications should be timely and provide value, guiding the user or their client to the next logical step.
- **Professionalism:** The design, tone, and reliability of our communications must reflect the high standards of the developers we serve.

### **Tone of Voice**

The DevHQ tone is **professional, yet friendly and empowering**. We are a tool built by developers, for developers. We speak their language—directly and efficiently—but with an encouraging and supportive undertone.

---

## 🎨 **2. Visual Branding & Email Templates**

Consistency is key. All emails sent from the DevHQ platform will adhere to a strict branding guide to ensure they are instantly recognizable and trusted.

- **Logo:** The DevHQ logo will be prominently and cleanly displayed in the header of every email.
- **Color Palette:** We will use our primary brand colors. Action buttons (like "Verify Email" or "View Invoice") will use a high-contrast, accessible brand color.
- **Typography:** Clean, readable, sans-serif fonts will be used for all text.
- **Footer:** A standardized footer will be on every email, containing:
  - A link to the user's settings to manage notifications.
  - Links to our social media profiles.
  - Our company name and address.
  - A clear "Unsubscribe" link for non-essential communications.

**Technology:** We will use a library like **MJML** to create beautiful, responsive HTML email templates that render perfectly on all devices. These templates will be powered by a templating engine like **Jinja2** on the backend.

---

## ✉️ **3. Transactional Email Breakdown**

Here is a comprehensive list of all automated emails the system will send, categorized by purpose.

### **A. Account Management Emails (To the DevHQ User)**

| Email Subject                       | Trigger                                      | Purpose                                                                 |
| ----------------------------------- | -------------------------------------------- | ----------------------------------------------------------------------- |
| **Welcome to DevHQ!**               | User successfully completes registration.    | To welcome the user, confirm their account, and guide them to the next steps. |
| **Please Verify Your Email Address**  | User registers or changes their email.       | To confirm ownership of the email address and activate the account fully. |
| **Password Reset Request for DevHQ**  | User requests a password reset.              | To provide a secure, time-sensitive link to reset their password.         |
| **Your Password Has Been Changed**    | User successfully resets their password.     | To confirm the security action and alert them in case it was not them.    |

### **B. Billing & Subscription Emails (To the DevHQ Pro User)**

| Email Subject                           | Trigger                                      | Purpose                                                                 |
| --------------------------------------- | -------------------------------------------- | ----------------------------------------------------------------------- |
| **Welcome to DevHQ Pro!**               | User successfully subscribes to a paid plan. | To thank them for upgrading and highlight the key Pro features they've unlocked. |
| **Your DevHQ Invoice/Receipt**          | A subscription payment is successful.        | To provide a detailed receipt for their records and tax purposes.       |
| **Your DevHQ Subscription Renews Soon** | 7 days before a subscription renewal.        | To remind the user of the upcoming charge and prevent surprises.        |
| **Action Required: Your Payment Failed**| A subscription payment fails.                | To alert the user and provide a link to update their payment information. |
| **Your DevHQ Subscription is Canceled** | User cancels their Pro subscription.         | To confirm the cancellation and inform them when their access will end.   |

### **C. Platform-Generated Emails (On Behalf of the User, to their Client)**

These emails are critical as they represent our user's brand. They will be clean, professional, and clearly state they are sent from "[Developer Name] via DevHQ."

| Email Subject                               | Trigger                                      | Purpose                                                                 |
| ------------------------------------------- | -------------------------------------------- | ----------------------------------------------------------------------- |
| **New Invoice from [Developer Name]**       | Developer sends an invoice from DevHQ.       | To notify the client of a new invoice with the amount, due date, and a secure payment link. |
| **Payment Receipt for Invoice [Number]**    | Client successfully pays an invoice.         | To provide the client with an instant receipt for their payment.        |
| **Invoice Reminder from [Developer Name]**  | An invoice is approaching its due date.      | A gentle, automated reminder to the client about the upcoming payment.  |

### **D. Workflow Notification Emails (To the DevHQ User)**

| Email Subject                               | Trigger                                      | Purpose                                                                 |
| ------------------------------------------- | -------------------------------------------- | ----------------------------------------------------------------------- |
| **✅ [Client Name] has approved a milestone!** | Client clicks "Approve" in their portal.     | To provide instant, positive feedback and unblock the developer to move forward. |
| **📝 [Client Name] has requested revisions.** | Client clicks "Request Revisions."           | To immediately alert the developer that feedback is waiting for them.   |

---

## ✍️ **4. Content Examples**

### **Example 1: The Welcome Email**

> **Subject: Welcome to DevHQ! Let's get you set up.**
>
> **[DevHQ Logo]**
>
> **Hi [User's First Name],**
>
> Welcome to DevHQ! We're thrilled to have you on board. You're one step closer to streamlining your business and focusing on what you do best: writing code.
>
> To get the most out of the platform, here are a few things you can do right away:
>
> 1. **Create Your First Client:** Start organizing your contacts in the CRM.
> 2. **Set Up a Project:** Break down your work into manageable milestones.
> 3. **Track Your Time:** Try out the one-click timer on your first task.
>
> <a href="[link-to-dashboard]" style="background-color: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Go to Your Dashboard</a>
>
> If you have any questions, just reply to this email. We're here to help.
>
> Cheers,
> The DevHQ Team

### **Example 2: Invoice Notification (to their client)**

> **Subject: New Invoice (INV-1001) from [Developer Name]**
>
> **[DevHQ Logo]**
>
> **Hello [Client Name],**
>
> This is a notification that **[Developer Name]** has sent you a new invoice for your project, **"[Project Name]"**.
>
> **Amount Due:** $2,500.00 USD
> **Due Date:** [Date]
>
> You can view the full invoice details and pay securely online by clicking the button below.
>
> <a href="[secure-payment-link]" style="background-color: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">View & Pay Invoice</a>
>
> If you have any questions about this invoice, please contact [Developer Name] directly at [<EMAIL>].
>
> ---
> *This invoice was generated and sent via DevHQ on behalf of [Developer Name].*

---

## 🔔 **4. In-App Notifications & Activity Center**

While email is for formal communication, the In-App Notification & Activity Center provides an immediate, at-a-glance view of all important events.

### **User Experience**

1. **Notification Indicator:** A bell icon (🔔) in the main navigation bar displays a badge for unread notifications.
2. **Notification Dropdown:** Clicking the bell opens a dropdown list of recent notifications.
3. **Interaction:** Each notification is a clickable link that navigates the user to the relevant item (e.g., an invoice or approval).
4. **Activity Page:** A "View All Activity" link leads to a dedicated page with a complete, searchable history.

### **Technical Implementation**

#### **Data Model: The `ActivityLog`**

A dedicated `ActivityLog` model stores these events.

```python
# In a new app/models/activity.py
class ActivityLog(Base):
    __tablename__ = "activity_logs"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    workspace_id = Column(UUID(as_uuid=True), ForeignKey("workspaces.id"), nullable=False)
    
    message = Column(String, nullable=False) # e.g., "Acme Corp approved milestone 'Phase 1'"
    category = Column(String) # e.g., 'payment', 'approval', 'comment', 'system'
    
    # For linking directly to the relevant resource
    resource_url = Column(String, nullable=True) 
    
    is_read = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
```

#### **Backend Components**

1. **`ActivityService`:** A centralized service (`app/services/activity_service.py`) is called by other services (e.g., Payments, Approvals) to create new log entries.
2. **API Endpoints:**
    - `GET /api/v1/activity-logs`: Fetches a paginated list of the user's activity logs.
    - `POST /api/v1/activity-logs/mark-all-as-read`: Marks all unread notifications as read.

---

## 🛠️ **5. Technical Implementation**

1. **Email Service Provider (ESP):** We will use a dedicated transactional email service like **SendGrid**, **Postmark**, or **Mailgun**. This is non-negotiable. Using a generic SMTP server will result in poor deliverability and emails landing in spam folders. An ESP provides tracking, analytics, and robust template management.

2. **Asynchronous Sending:** All emails will be sent via **asynchronous background tasks** using Celery. This ensures that API requests that trigger an email (like user registration) are not blocked waiting for the email to send, providing a fast user experience.

3. **Template Management:** HTML/MJML templates will be stored in the backend codebase and rendered with Jinja2, allowing dynamic data (like names, amounts, and links) to be injected safely.

4. **User Control:** The system will respect the notification preferences set by the user in their `UserSettings`. A user can choose to opt out of non-critical notifications.

---

## ✅ **6. Implementation Checklist**

- [ ] **Business:** Select and sign up for a transactional Email Service Provider (ESP).
- [ ] **Backend:** Integrate the ESP's SDK into a dedicated `EmailService`.
- [ ] **Backend:** Set up Celery for asynchronous email sending.
- [ ] **Backend:** Create MJML/HTML templates for all transactional emails.
- [ ] **Backend:** Implement the logic to trigger each email at the correct point in the application flow.
- [ ] **Backend:** Ensure all user-facing links (verification, password reset) are secure and time-limited.
- [ ] **Frontend:** Build the "Notification Preferences" section in the user settings page.
- [ ] **Testing:** Write tests to ensure emails are triggered correctly with the right data.
- [ ] **DevOps:** Add ESP API keys to production secrets management.

By executing this strategy, we ensure that every communication from DevHQ is a positive reflection on both our platform and our users' businesses.
