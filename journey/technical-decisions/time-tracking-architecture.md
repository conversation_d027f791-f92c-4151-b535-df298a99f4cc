# ⏱️ DevHQ Smart Time Tracking: Architecture & Best Practices

*A deep dive into the core engine of developer productivity in DevHQ.*

## 🎯 **1. Overview: Effortless & Accurate Tracking**

Time tracking is the foundation of a developer's business, yet it's often a tedious chore. The DevHQ Smart Time Tracking system is designed to be the opposite: fast, integrated, and almost invisible. It's a tool that works *for* the developer, not against them.

The core philosophy is to capture work as it happens with minimal friction, then seamlessly convert that time into billable hours, invoices, and financial insights.

### **Why It's a Foundational Feature:**

- **For the Developer:** Eliminates manual timesheets, ensures all billable work is captured, and provides insights into personal productivity.
- **For the Platform:** The data generated by time entries is the lifeblood of the invoicing, payment, and analytics features.

---

## 🌊 **2. The User Experience (UX) Flow**

1. **Start Tracking:** From anywhere in the app, the developer clicks on a task and hits "Start Timer." A global, floating widget appears.
2. **Work:** The widget unobtrusively shows the running time. The developer can navigate the app freely; the timer persists.
3. **Stop Tracking:** The developer clicks "Stop" on the widget. The time is instantly logged.
4. **Manual Entry:** For work done offline, the developer can go to a "Timesheets" page and manually add an entry (e.g., "2 hours on bug fixes").
5. **Invoice Generation:** When creating an invoice, the developer can pull all unbilled time entries for a project with one click.

---

## 🛠️ **3. Technical Architecture Deep Dive**

### **Data Models (Backend)**

The schema in `scripts/seed_test_data.py` provides a solid foundation.

```python
# In app/models/task.py
class Task(Base):
    # ...
    estimated_hours = Column(Numeric)
    actual_hours = Column(Numeric, default=0) # Aggregated from time entries
    is_billable = Column(Boolean, default=True)

class TimeEntry(Base):
    # ...
    start_time = Column(DateTime(timezone=True))
    end_time = Column(DateTime(timezone=True), nullable=True) # Null while timer is running
    duration_minutes = Column(Integer)
    is_billable = Column(Boolean)
    hourly_rate = Column(Numeric) # Snapshotted from the project at time of creation
    billable_amount = Column(Numeric) # Pre-calculated for performance
```

### **Backend Components (FastAPI)**

1. **Live Timer Endpoints:** These must be highly responsive.
    - `POST /api/v1/tasks/{task_id}/time/start`: Creates a `TimeEntry` with a `start_time` and `null` `end_time`.
    - `POST /api/v1/tasks/{task_id}/time/stop`: Finds the active entry, sets `end_time`, and calculates `duration_minutes` and `billable_amount`. This must be an **atomic transaction** to prevent data inconsistency.

2. **Manual & Bulk Operations:**
    - `POST /api/v1/time-entries`: Allows creating a `TimeEntry` with a specific duration.
    - `GET /api/v1/time-entries`: A paginated endpoint with filters (by project, date range, billable status) to populate the timesheet view.

3. **Analytics Service:** A background service or a set of complex queries to calculate productivity metrics (e.g., billable vs. non-billable hours, project budget tracking).

### **Frontend Components (Next.js)**

1. **Global Timer Widget:**
    - Managed by a global state manager (Zustand).
    - State (e.g., `activeTimer: { taskId, startTime }`) must be persisted in `localStorage` to survive page refreshes.
    - On app load, it syncs with the backend to see if a timer was left running in another session.

2. **Timesheet Page:**
    - Uses TanStack Query to fetch and display time entries.
    - Provides forms for editing and creating manual entries.

---

## 🔒 **4. Best Practices & Reliability**

1. **Handle "Forgotten Timers":** A developer can easily start a timer and forget it.
    - **Backend Safeguard:** The `stop` endpoint should enforce a maximum duration for a single time entry (e.g., 12 hours). If a timer runs longer, it should be flagged for manual review instead of being automatically logged.
    - **Frontend Nudge (Advanced):** Implement an idle detector. If no user activity is detected for X minutes, display a modal asking "Are you still working?" and offer to pause the timer.

2. **Ensure Data Integrity:**
    - The `stop` timer logic on the backend must be an **atomic database transaction**. If updating the `Task.actual_hours` fails, the `TimeEntry` update must also be rolled back.
    - When a timer starts, snapshot the project's `hourly_rate` into the `TimeEntry`. This prevents old time entries from changing value if the project rate is updated later.

3. **Implement Time Rounding:**
    - This is a critical business requirement for many freelancers.
    - Add a `time_rounding_policy` (e.g., `none`, `up_15_min`, `up_30_min`) field to the `Project` or `UserSettings` model.
    - Apply this rounding logic on the backend during the `stop` operation.

4. **Offline Support (Post-MVP):**
    - For a truly premium experience, the timer should function offline.
    - This requires using a Service Worker to intercept `start`/`stop` requests and storing them locally (e.g., in IndexedDB) via a library like `workbox`. When connectivity is restored, the queued events are sent to the server.

---

## ✅ **5. Implementation Checklist**

- [ ] **Backend:** Create `Task` and `TimeEntry` models and migrations.
- [ ] **Backend:** Implement `start` and `stop` timer endpoints with atomic transactions.
- [ ] **Backend:** Implement CRUD endpoints for manual time entries.
- [ ] **Backend:** Add "forgotten timer" safeguard logic.
- [ ] **Frontend:** Create the global state slice (Zustand) for the active timer.
- [ ] **Frontend:** Build the floating timer widget UI.
- [ ] **Frontend:** Persist the timer's state to `localStorage`.
- [ ] **Frontend:** Build the full Timesheet page for viewing and managing entries.
- [ ] **Testing:** Write tests for the timer logic, especially edge cases like overlapping timers or stopping a non-existent timer.

By building this feature with care, we create the core data-gathering engine that makes the rest of DevHQ's financial features so powerful and automated.
