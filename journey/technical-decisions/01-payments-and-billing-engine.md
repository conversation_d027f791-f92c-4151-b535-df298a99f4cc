# 💳 DevHQ Payments & Billing Engine: Architecture

*A comprehensive deep dive into the entire financial engine of DevHQ, covering payment processing, flexible invoicing, and user payouts.*

---

## Part 1: The Payment Platform

### 🎯 **1.1. Overview: Seamless & Strategic Payments**

The payment platform is designed to be more than just a way to collect money; it's the final, crucial step in a seamless business workflow. It transforms tracked time and project milestones directly into revenue, all within a single system.

Our strategy is twofold:

1. **Deep Integration:** Payments are not an afterthought. They are intrinsically linked to invoices, time tracking, and financial wallets.
2. **Strategic Market Focus:** We prioritize **Paystack** to provide a best-in-class experience for our core target market: African developers. This includes support for local payment methods and currencies (e.g., KES, M-Pesa).

### **Why It's a Core Differentiator:**

- **For the Developer:** Automates the entire billing lifecycle, from time entry to cash-in-wallet, saving hours of administrative work.
- **For the Client:** Provides a simple, secure, and professional payment experience via a direct link, with no account needed.

### 🌊 **1.2. The Payment Flow: From Invoice to Wallet**

1. **Invoice Finalization:** A developer creates and finalizes an invoice within the DevHQ dashboard.
2. **Secure Link Generation:** The backend generates a unique, unguessable `payment_link_token` and associates it with the invoice.
3. **Client Interaction:** The developer shares the link (e.g., `https://app.devhq.com/pay/<token>`) with the client.
4. **Payment Page:** The client clicks the link and is taken to a branded, public payment page showing the invoice details.
5. **Checkout:** The client pays using the embedded Paystack widget.
6. **Webhook Notification:** Paystack processes the payment and sends a `charge.success` event to a secure webhook endpoint on the DevHQ backend.
7. **Automated Reconciliation:** The DevHQ backend verifies the webhook, updates the invoice status to `paid`, creates an `income` transaction, and updates the developer's wallet balance.

### 🛠️ **1.3. Technical Architecture**

#### **Data Models (Backend)**

```python
# In app/models/invoice.py
class Invoice(Base):
    # ...
    status = Column(String, default="draft") # e.g., draft, sent, paid, overdue
    payment_link_token = Column(String, unique=True, index=True, nullable=True) # UUID for public access

# In app/models/wallet.py
class WalletAccount(Base):
    # ...
    balance = Column(Numeric, default=0)

class Transaction(Base):
    # ...
    type = Column(String) # 'income' or 'expense'
    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"))
```

#### **Backend Components (FastAPI)**

1. **Public Invoice Endpoint:** An unauthenticated endpoint that uses the token to fetch invoice details for the payment page.

    ```python
    # In a new app/routers/public_payment.py
    @router.get("/pay/{token}")
    def get_invoice_for_payment(token: str, db: Session = Depends(get_db)):
        invoice = db.query(Invoice).filter(Invoice.payment_link_token == token).first()
        if not invoice or invoice.status == 'paid':
            raise HTTPException(status_code=404, detail="Invoice not found or already paid.")
        return invoice
    ```

2. **Paystack Webhook Handler:** A dedicated endpoint to receive and process notifications from Paystack.

    ```python
    # In a new app/routers/webhooks.py
    @router.post("/webhooks/paystack")
    async def handle_paystack_webhook(request: Request):
        # 1. Get request body and signature from header
        # 2. Verify the signature using our Paystack secret key
        # 3. If valid, process the event (e.g., 'charge.success')
        # 4. Perform database updates within an atomic transaction
        # 5. Return a 200 OK to acknowledge receipt
        return {"status": "ok"}
    ```

### 🔒 **1.4. Security & Reliability Best Practices**

1. **Webhook Signature Verification:** **This is mandatory.** Never trust a webhook's payload without verifying its cryptographic signature.
2. **Idempotent Webhook Handling:** Your handler must be able to process the same event multiple times without causing duplicate data.
3. **Atomic Database Transactions:** The entire reconciliation logic must be wrapped in a single database transaction.
4. **Platform Fee Architecture (Post-MVP):** Use a "connected accounts" model (Paystack Subaccounts) to handle platform fees by splitting transactions at the source.

---

## Part 2: Flexible Billing & Invoicing Workflows

### 🎯 **2.1. Overview: Empowering the Developer**

A core principle of DevHQ is that the developer must have complete control over their business processes. There is no "one-size-fits-all" billing agreement, so the platform is designed to be a flexible toolkit.

**Key Principle:** Invoices and their corresponding payment links are **never** sent automatically. The developer **always** initiates the invoicing process manually.

### 🌊 **2.2. Supported Billing Scenarios**

#### **Scenario A: Payment Per Milestone**

The developer can create an invoice containing only a specific, approved milestone.

#### **Scenario B: Upfront Deposit (e.g., 50% Down)**

The developer can create an invoice with a manual line item for a deposit before work begins.

#### **Scenario C: Time & Materials Billing**

The developer can create an invoice that compiles all unbilled, tracked time entries for a given period.

#### **Scenario D: Full Payment on Completion**

The developer can create a final invoice that includes all remaining unbilled milestones and time.

### 🛠️ **2.3. Technical Implementation**

#### **Data Model Considerations:**

- **`InvoiceItem` Model:** Can be linked to a `TimeEntry` or a `ProjectMilestone`, but can also be a simple, unlinked record for manual items.
- **`billing_status` Field:** `TimeEntry` and `ProjectMilestone` models will have a status (`unbilled`, `invoiced`, `paid`) to prevent double-billing.

#### **Backend & API Logic:**

- **`GET /api/v1/projects/{project_id}/billable-items`:** This crucial endpoint returns a list of all items for a project that are currently in an "unbilled" state.
- **`POST /api/v1/invoices`:** The request body for creating an invoice accepts a list of IDs for billable items and/or a list of manual line items.

---

## Part 3: User Payouts Onboarding

### 🎯 **3.1. Overview: Connecting Work to Revenue**

This feature is the bridge that connects a developer's hard work to their bank account. It provides the mechanism for Pro users to set up their accounts to receive payouts from the invoices their clients pay through the platform.

### 🌊 **3.2. The User Experience (UX) Flow**

1. **Initiate Onboarding:** In settings, the user clicks "Set Up Payouts to Get Paid."
2. **Secure Redirect:** They are redirected to a secure, co-branded onboarding form hosted by Paystack.
3. **Provide Details:** They fill in their business and bank account details. This information **never** touches DevHQ's servers.
4. **Return to DevHQ:** After completion, they are redirected back to DevHQ with a confirmation message.

### 🛠️ **3.3. Technical Architecture (Using Paystack Subaccounts)**

This architecture relies on a "connected accounts" model.

#### **Data Model Enhancements**

```python
# In app/models/user.py
class User(Base):
    # ...
    # This ID represents the user's merchant account on Paystack
    paystack_subaccount_id = Column(String, unique=True, nullable=True)
    payouts_enabled = Column(Boolean, default=False)
```

#### **Backend Components (FastAPI)**

1. **Onboarding Link Generation Endpoint:**
    - `POST /api/v1/users/me/payouts/onboarding-link`:
    - **Logic:**
        1. Create a new subaccount via the Paystack API if one doesn't exist.
        2. Store the `paystack_subaccount_id` on the `User` record.
        3. Create an onboarding link via the Paystack API and return the unique URL.

2. **Enhancement to Payment Processing:**
    - When creating a payment, the backend must include:
        - `application_fee_amount`: The platform fee DevHQ will take.
        - `subaccount`: The `paystack_subaccount_id` of the user who should receive the funds.

### 🔒 **3.4. Security Best Practices**

1. **Never Handle Sensitive Data:** Sensitive financial information (bank accounts, tax IDs) is entered on a form hosted by the payment processor, never on DevHQ servers.
2. **Use Webhooks for Status Updates:** Rely on webhooks (e.g., `account.updated`) to be notified in real-time when a user's payout account becomes active.
