# 🐙 DevHQ GitHub Integration: Architecture & Best Practices

*A deep dive into the feature that bridges the gap between business management and code management.*

## 🎯 **1. Overview: The "Single Pane of Glass" Philosophy**

The GitHub Integration is designed to make DevHQ the true "single pane of glass" for a developer's entire professional life. By connecting directly to GitHub, we can automate project setup, link business tasks to code, and provide insights that are impossible when tools are separate.

The core idea is to create a seamless, two-way sync between DevHQ projects and GitHub repositories.

### **Why It's a Game-Changer:**

- **For the Developer:** Eliminates manual project creation, provides a clear line-of-sight from a task to a commit/PR, and centralizes project context.
- **For the Platform:** Creates an incredibly powerful moat. Once a user's business and code workflows are integrated, the value of DevHQ increases exponentially.

---

## 🌊 **2. The Phased User Experience (UX) Flow**

This is a major feature, so we'll roll it out in phases.

### **Phase 1: GitHub Authentication**

1. **Sign Up/In:** A user sees a "Continue with GitHub" button on the login page.
2. **Authorization:** They are redirected to GitHub to authorize the DevHQ application, granting it specific permissions (e.g., to read repos).
3. **Account Creation:** They are returned to DevHQ, and their account is created/logged in, pre-filled with their GitHub name, email, and avatar.

### **Phase 2: Repository Linking & Creation**

1. **Link Existing Repo:** In a DevHQ project's settings, the user can "Link a GitHub Repository." A list of their repos is fetched from GitHub, and they select one to associate with the project.
2. **Create New Repo:** When creating a new project in DevHQ, a checkbox appears: "Create a new GitHub repository." If checked, a new private repo is created on their GitHub account and automatically linked.

### **Phase 3: Deep Integration (The "Mini GitHub")**

1. **Repo Dashboard:** The project dashboard in DevHQ gains a "GitHub" tab, showing recent commits, open PRs, and key repo stats.
2. **Task-to-Issue Sync:** A `Task` in DevHQ can be linked to a GitHub Issue. Updating one can update the other.
3. **Commit-to-Task Linking:** When a developer's commit message includes a task ID (e.g., `git commit -m "Fix login bug (DHQ-123)"`), that commit automatically appears in the activity feed for that task in DevHQ.

---

## 🛠️ **3. Technical Architecture Deep Dive**

### **Data Model Enhancements**

We need to store GitHub-specific data for users and projects.

```python
# In app/models/user.py
class User(Base):
    # ...
    github_id = Column(String, unique=True, index=True, nullable=True)
    github_access_token = Column(String, nullable=True) # MUST be encrypted at rest
    github_refresh_token = Column(String, nullable=True) # MUST be encrypted at rest

# In app/models/project.py
class Project(Base):
    # ...
    github_repo_id = Column(String, nullable=True)
    github_repo_url = Column(String, nullable=True)
```

### **Backend Components (FastAPI)**

1. **GitHub OAuth Router:** A new router (`app/routers/auth_github.py`) to handle the OAuth2 flow.
    - `GET /auth/github/login`: Redirects the user to GitHub's authorization page.
    - `GET /auth/github/callback`: The endpoint GitHub redirects back to with an authorization code. This endpoint exchanges the code for an access token, then creates/logs in the user.

2. **GitHub Service (`app/services/github_service.py`):**
    - A dedicated service class that encapsulates all communication with the GitHub API.
    - It will use the user's stored `github_access_token` to make authenticated requests.
    - Methods would include `get_user_repos()`, `create_repo()`, `get_repo_details()`, etc.

3. **GitHub Webhook Handler:**
    - A new endpoint (`POST /webhooks/github`) to receive events from GitHub (e.g., `push`, `pull_request`).
    - This is how we'll implement the deep integration features like commit-to-task linking.

### **Frontend Components (Next.js)**

1. **"Continue with GitHub" Button:** On the login/signup pages.
2. **Integrations Page:** A new page in settings where users can manage their GitHub connection.
3. **Repository Linking UI:** A modal in the project settings to search and link repositories.

---

## 🔒 **4. Security & Best Practices (Non-Negotiable)**

1. **Encrypt Access Tokens:** GitHub access tokens are as sensitive as passwords. They **must** be encrypted in the database using a strong symmetric encryption key (like Fernet) that is stored as a separate application secret.

2. **Request Minimal Scopes:** During the OAuth flow, only request the permissions you absolutely need for the current feature set. For Phase 2, you might only need `repo` and `read:user`. You can always ask for more permissions later.

3. **Verify Webhook Signatures:** Every incoming webhook from GitHub **must** be verified using the shared secret. This prevents attackers from sending fake events to your server.

4. **State Parameter in OAuth:** Use a unique, unguessable `state` parameter during the OAuth flow to prevent Cross-Site Request Forgery (CSRF) attacks.

5. **Handle Token Expiration & Revocation:** Implement logic to use the `refresh_token` to get a new `access_token`. Also, provide a way for users to "Disconnect from GitHub," which should securely delete their tokens from your database.

---

## ✅ **5. Implementation Checklist**

### **Phase 1: Authentication**

- [ ] **Backend:** Register a new GitHub OAuth App in GitHub settings.
- [ ] **Backend:** Add `github_id` and encrypted token fields to the `User` model.
- [ ] **Backend:** Create the GitHub OAuth router and callback logic.
- [ ] **Backend:** Implement encryption for storing tokens securely.
- [ ] **Frontend:** Add the "Continue with GitHub" button to the UI.

### **Phase 2: Basic Integration**

- [ ] **Backend:** Add `github_repo_id` and `github_repo_url` to the `Project` model.
- [ ] **Backend:** Implement the `GitHubService` with methods to list and create repos.
- [ ] **Backend:** Create API endpoints for linking and creating repos.
- [ ] **Frontend:** Build the UI for linking an existing repo.
- [ ] **Frontend:** Add the "Create GitHub repository" checkbox to the new project form.

### **Phase 3: Deep Integration**

- [ ] **Backend:** Implement the GitHub webhook handler with signature verification.
- [ ] **Backend:** Create a system to parse commit messages for task IDs.
- [ ] **Backend:** Add models to link `Task` to GitHub `Issue`.
- [ ] **Frontend:** Build the "GitHub" tab on the project dashboard to display repo information.
- [ ] **Testing:** Write comprehensive tests for the entire integration flow.

This is a powerful, user-centric feature that aligns perfectly with the DevHQ vision. By building it out in phases, you can manage the complexity and deliver value incrementally.
