# 💎 DevHQ Freemium Model & User Tiers: Architecture & Strategy

*A deep dive into how we will handle non-premium users and create a sustainable business model.*

## 🎯 **1. Overview: The "Generous but Limited" Philosophy**

Our strategy for non-premium users is not to cripple their experience but to empower them. The free tier, "DevHQ Starter," will be a genuinely useful tool for developers who are just starting out or have a small number of clients.

The philosophy is simple: **Provide immense value for free, and tie limitations to business growth.** As a developer's business scales, they will naturally encounter the limits of the free tier and see the clear value in upgrading to a paid plan. This creates a smooth, value-driven upgrade path rather than a frustrating paywall.

---

## 📊 **2. User Tiers & Feature Breakdown**

We will launch with two primary tiers: **Starter (Free)** and **Pro (Paid)**.

| Feature                       | Starter (Free)                               | Pro (Paid)                                     | Upgrade Incentive                               |
| ----------------------------- | -------------------------------------------- | ---------------------------------------------- | ----------------------------------------------- |
| **Projects**                  | Up to **2** active projects                  | **Unlimited** projects                         | Manage a growing client list                    |
| **Clients (CRM)**             | Up to **3** clients                          | **Unlimited** clients                          | Scale business development efforts              |
| **Time Tracking**             | ✅ **Unlimited**                             | ✅ **Unlimited**                               | (Core feature, remains free)                    |
| **Invoicing**                 | Up to **3** invoices per month               | **Unlimited** invoices                         | Handle more client work and billing cycles      |
| **Payment Processing**        | ❌ **Not available** (PDF download only)     | ✅ **Fully Integrated** (Paystack)              | Get paid faster and more professionally         |
| **No-Account Client Portal**  | ❌ **Not available**                         | ✅ **Fully Featured**                          | Offer a revolutionary, zero-friction experience |
| **Financial Management**      | ✅ Basic Expense Tracking                    | ✅ **Advanced Reporting** (P&L, Tax Summary)   | Gain deep financial insights                    |
| **Notes & Design Uploads**    | **100 MB** total storage                     | **10 GB** total storage                        | Store all project assets without worry          |
| **Client Approval Workflows** | ❌ **Not available**                         | ✅ **Fully Featured**                          | Formalize feedback and reduce scope creep       |
| **Support**                   | Community Support                            | **Priority Email Support**                     | Get faster, dedicated help                      |

---

## 🌊 **3. The User Experience (The Upgrade Path)**

The application's UI will gently and contextually inform users about the limits and the benefits of upgrading.

1. **Approaching a Limit:** When a user has 1 active project left on the free plan, a small, non-intrusive banner might appear: "You're doing great! Down to your last project slot. Upgrade to Pro for unlimited projects."

2. **Hitting a Limit:** If a user tries to create a 3rd active project, a modal will appear:
    > **"Ready to Grow Your Business?"**
    > "You've reached the 2-project limit for the Starter plan. Upgrade to DevHQ Pro to manage unlimited projects, unlock the Client Portal, and get paid faster with integrated payments."
    > `[Upgrade to Pro]` `[Learn More]`

3. **Locked Feature Teasers:** Premium features will be visible but "locked" in the UI. For example, next to a finalized invoice, a button might say:
    > `[🚀 Send Professional Payment Link (Pro Feature)]`
    Clicking it would trigger the upgrade modal, explaining the benefits of the payment platform.

---

## 🛠️ **4. Technical Architecture Deep Dive**

Implementing this model requires changes to our backend data models and the addition of a new layer of logic to enforce limits.

### **Data Model Enhancements**

We need to track the user's plan and their current usage.

```python
# In app/models/user.py
import enum

class UserPlan(str, enum.Enum):
    STARTER = "starter"
    PRO = "pro"

class User(Base):
    # ...
    plan = Column(Enum(UserPlan), default=UserPlan.STARTER, nullable=False)

# In a new app/models/usage.py
class UserUsage(Base):
    __tablename__ = "user_usage"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    
    # Counters
    active_projects_count = Column(Integer, default=0)
    clients_count = Column(Integer, default=0)
    storage_bytes_used = Column(BigInteger, default=0)
    
    # Monthly counters (reset by a background job)
    invoices_this_month = Column(Integer, default=0)
    last_invoice_reset_date = Column(Date)
```

### **Backend Components (FastAPI)**

1. **Plan & Limit Definitions:** A central place in our config to define the limits for each plan.

    ```python
    # In app/core/config.py
    PLAN_LIMITS = {
        "starter": {
            "active_projects": 2,
            "clients": 3,
            "invoices_per_month": 3,
            "storage_bytes": 100 * 1024 * 1024, # 100 MB
            "client_portal_enabled": False,
            "payments_enabled": False,
        },
        "pro": {
            # Use None for unlimited
            "active_projects": None,
            "clients": None,
            "invoices_per_month": None,
            "storage_bytes": 10 * 1024 * 1024 * 1024, # 10 GB
            "client_portal_enabled": True,
            "payments_enabled": True,
        }
    }
    ```

2. **Usage Enforcement Dependency:** A FastAPI dependency that checks if a user is allowed to perform an action. This is the most critical component.

    ```python
    # In app/dependencies.py
    from app.core.config import PLAN_LIMITS

    async def check_usage_limit(
        limit_type: str, # e.g., "active_projects"
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
    ):
        user_plan = current_user.plan.value
        limit = PLAN_LIMITS[user_plan].get(limit_type)

        if limit is None: # Unlimited
            return True

        # Fetch current usage from the user_usage table
        usage = db.query(UserUsage).filter(UserUsage.user_id == current_user.id).first()
        current_count = getattr(usage, f"{limit_type}_count", 0)

        if current_count >= limit:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Usage limit for '{limit_type}' reached. Please upgrade your plan."
            )
        return True
    ```

3. **Applying the Dependency:** Use this dependency in the routers for actions that need to be limited.

    ```python
    # In app/routers/projects.py
    @router.post("/", dependencies=[Depends(check_usage_limit("active_projects"))])
    def create_project(project: ProjectCreate, ...):
        # ... logic to create project ...
        # IMPORTANT: Increment the usage counter after successful creation
    ```

---

## ✅ **5. Implementation Checklist**

- [ ] **Backend:** Add `plan` enum and column to `User` model.
- [ ] **Backend:** Create `UserUsage` model for tracking metrics.
- [ ] **Backend:** Create Alembic migrations for new models/columns.
- [ ] **Backend:** Implement a service to create a `UserUsage` record for new users.
- [ ] **Backend:** Define plan limits in `app/core/config.py`.
- [ ] **Backend:** Create the `check_usage_limit` dependency.
- [ ] **Backend:** Implement logic to increment/decrement usage counters when resources are created/deleted.
- [ ] **Backend:** Apply the dependency to all relevant endpoints (create project, create client, create invoice, etc.).
- [ ] **Backend:** Create a background job (Celery) to reset monthly counters.
- [ ] **Frontend:** Build the UI for upgrade modals and "locked" feature teasers.
- [ ] **Frontend:** Create a "Billing & Plan" page in user settings.
- [ ] **Testing:** Write tests to ensure usage limits are correctly enforced for each plan.

By thoughtfully implementing this freemium model, we can build a strong user base, create a clear path to monetization, and ensure the long-term success of DevHQ.
