# 💸 DevHQ Payments Onboarding: Architecture for User Payouts

*A deep dive into the feature that enables DevHQ users to receive payments from their clients seamlessly.*

## 🎯 **1. Overview: Connecting Work to Revenue**

This feature is the bridge that connects a developer's hard work to their bank account. It provides the mechanism for DevHQ users (our developers) to set up their accounts to receive payouts from the invoices their clients pay through the platform.

This is a cornerstone **Pro feature**. It represents the shift from simply *managing* a business to actively *automating* its revenue cycle.

### **The Core Problem It Solves:**

Without this, a developer must manually handle the entire payment collection and reconciliation process, which is slow, unprofessional, and error-prone. With this feature, the entire flow is automated, secure, and seamless.

---

## 🌊 **2. The User Experience (UX) Flow**

### **Developer's Perspective (One-Time Setup):**

1. **Navigate to Settings:** The developer goes to their "Settings" -> "Payouts" page.
2. **Initiate Onboarding:** They click a button like "Set Up Payouts to Get Paid."
3. **Secure Redirect:** They are redirected to a secure, co-branded onboarding form hosted by Paystack. This form is designed to build trust and ensure security.
4. **Provide Details:** They fill in their business information and bank account details for payouts. This information **never** touches DevHQ's servers; it's sent directly to the payment processor.
5. **Return to DevHQ:** After completing the form, they are redirected back to DevHQ with a confirmation message: "Your payout account is now active! You're ready to receive payments."

### **Ongoing Experience:**

- When a client pays an invoice, the funds are automatically processed and routed to the developer's connected bank account, typically within a few business days.

---

## 🛠️ **3. Technical Architecture Deep Dive (Using Paystack Subaccounts)**

This architecture relies on a "connected accounts" model, where DevHQ acts as the platform and our users are connected merchants.

### **Data Model Enhancements**

We need to store the ID of the user's merchant account.

```python
# In app/models/user.py
class User(Base):
    # ...
    # This ID represents the user's merchant account on Paystack
    # It's how we know where to send their money.
    paystack_subaccount_id = Column(String, unique=True, nullable=True)
    payouts_enabled = Column(Boolean, default=False)
```

### **Backend Components (FastAPI)**

1. **Onboarding Link Generation Endpoint:**
    - `POST /api/v1/users/me/payouts/onboarding-link`: This endpoint is called when the user clicks "Set Up Payouts."
    - **Logic:**
        1. Check if the user already has a `paystack_subaccount_id`. If not, call the Paystack API to create a new subaccount.
        2. Store the new `paystack_subaccount_id` on the `User` record.
        3. Create an onboarding link via the Paystack API. This returns a unique, one-time URL.
        4. Return this URL to the frontend.

2. **Onboarding Status Endpoint:**
    - `GET /api/v1/users/me/payouts/status`: An endpoint the frontend can poll to check the status of the user's payout account.
    - **Logic:** Calls the Paystack API to retrieve the subaccount details and checks if the account is active and verified.

3. **Enhancement to Payment Processing:**
    - When creating a payment intent for an invoice, the backend must now include two extra parameters:
        - `application_fee_amount`: The platform fee DevHQ will take from the transaction.
        - `subaccount`: The `paystack_subaccount_id` of the user who should receive the funds.

### **Frontend Components (Next.js)**

1. **Payouts Settings Page:** A new page at `/settings/payouts`.
2. **Onboarding Button:** This button calls the `/onboarding-link` endpoint and then uses the returned URL to redirect the user to the Paystack-hosted form.
3. **Status Display:** The page will use the `/status` endpoint to show the user the state of their account (e.g., "Pending," "Active," "More Information Required").

---

## 🔒 **4. Best Practices & Security**

1. **Never Handle Sensitive Data:** The entire point of this architecture is that sensitive financial information (bank accounts, tax IDs) is entered on a form hosted by the payment processor. It **never** passes through DevHQ's servers, which dramatically reduces our PCI compliance scope.

2. **Use Webhooks for Status Updates:** Rely on webhooks from Paystack (e.g., `subaccount.updated`) to be notified in real-time when a user's payout account becomes active or requires more information. This is more efficient than polling.

3. **Clear User Communication:** The UI must be very clear about what is happening. Explain that they are being redirected to a secure partner site and why it's necessary.

4. **Platform Fee Transparency:** Be upfront about the platform fee you will charge on each transaction. This builds trust.

---

## ✅ **5. Implementation Checklist**

- [ ] **Business:** Finalize partnership with Paystack for African market focus.
- [ ] **Backend:** Add `paystack_subaccount_id` and `payouts_enabled` to the `User` model and create a migration.
- [ ] **Backend:** Implement the `/payouts/onboarding-link` endpoint.
- [ ] **Backend:** Implement the `/payouts/status` endpoint.
- [ ] **Backend:** Enhance the payment creation logic to include `application_fee_amount` and `destination`.
- [ ] **Backend:** Create a webhook handler to listen for `account.updated` events.
- [ ] **Frontend:** Build the "Payouts" settings page.
- [ ] **Frontend:** Implement the logic to redirect the user to the onboarding URL.
- [ ] **Frontend:** Display the user's payout account status.
- [ ] **Testing:** Thoroughly test the entire onboarding flow in the payment processor's test mode.

By implementing this feature, you provide the final, crucial piece of the puzzle that makes DevHQ an indispensable tool for any serious freelance developer.
