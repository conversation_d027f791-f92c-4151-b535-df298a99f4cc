# 📈 DevHQ Project Planner & Budgeter: Architecture & Best Practices

*A deep dive into the feature that helps developers structure fixed-price projects intelligently.*

## 🎯 **1. Overview: From Tracker to Strategic Partner**

The Project Planner & Budgeter is a feature designed to solve a critical problem for freelancers: how to structure a fixed-price project to ensure profitability and feasibility. It acts as an intelligent assistant that takes a client's total budget and deadline and helps the developer create a realistic, milestone-based plan.

This feature moves DevHQ from being a passive tracking tool to an active planning partner.

### **Why It's a Game-Changer:**

- **For the Developer:** De-risks fixed-price projects by providing an immediate "sanity check" on the budget vs. time. It prevents underbidding and helps create a clear project roadmap that can be shared with the client.
- **For the Platform:** Provides immense upfront value, making DevHQ an indispensable tool from the very beginning of a project lifecycle.

---

## 🌊 **2. The User Experience (UX) Flow**

The wizard supports two primary scenarios for fixed-price projects:

### **Scenario A: "Am I charging enough?"**

1. **Input Constraints:** The developer knows their rate and the client's budget. They input:
    - `Total Project Budget`
    - `Your Internal Hourly Rate`
2. **Automated Calculation:** The system instantly calculates and displays the **Total Time Budget** (`Budget / Rate`).
3. **Milestone Allocation:** The developer allocates the time budget across milestones, seeing if the hours are realistic for the scope.

### **Scenario B: "What is my effective rate?"**

1. **Input Constraints:** The developer knows the client's budget and can estimate the work. They input:
    - `Total Project Budget`
2. **Estimate Hours:** The developer breaks the project into milestones and estimates the hours required for each.
3. **Automated Calculation:** The system sums the hours and calculates the **Effective Hourly Rate** (`Budget / Total Hours`). This provides a crucial "sanity check" on the project's profitability.

### **Final Steps (Both Scenarios):**

4. **Review & Create:** The user reviews the full breakdown (milestones, hours per milestone, value per milestone) and confirms.
5. **Automated Setup:** DevHQ creates the project and all its associated milestones with the calculated estimates, ready for tracking.

---

## 🛠️ **3. Technical Architecture Deep Dive**

### **Data Model Enhancements**

We need to add fields to our existing models to support these estimates.

```python
# In app/models/project.py
class Project(Base):
    # ...
    billing_type = Column(String, default="time_and_materials") # 'time_and_materials' or 'fixed_price'
    total_estimated_hours = Column(Numeric(10, 2), nullable=True)

# In app/models/project.py
class ProjectMilestone(Base):
    # ...
    estimated_hours = Column(Numeric(10, 2), nullable=True)
    # 'payment_amount' already exists, which is perfect for the milestone's value.
```

### **Backend Components (FastAPI)**

1. **Project Planning Endpoint:** A new, dedicated endpoint for the planning wizard. This is not a simple CRUD operation.
    - `POST /api/v1/projects/plan`:
        - **Request Body:** `{ "total_budget": 10000, "deadline": "YYYY-MM-DD", "internal_hourly_rate": 80 }`
        - **Response Body:** `{ "total_time_budget": 125.00, "suggested_milestones": [...] }`
    This endpoint performs the calculation and returns a suggested structure that the frontend can display before the user commits.

2. **Enhanced Project Creation Endpoint:** The existing `POST /api/v1/projects` endpoint will be enhanced.
    - It will accept an optional list of `milestones` to be created along with the project.
    - When a project is created from the planner, the frontend will call this endpoint with the final, user-approved structure.

### **Frontend Components (Next.js)**

1. **Multi-Step Modal/Wizard:** A new UI component for the "New Project" flow.
2. **Step 1:** Choose Project Type (`T&M` or `Fixed-Price`).
3. **Step 2 (if Fixed-Price):** The Planner UI with inputs for budget/deadline/rate and sliders for milestone allocation.
4. **API Integration:** The frontend will first call the `/projects/plan` endpoint to get the initial calculation. As the user adjusts the milestone sliders, the frontend can perform the calculations in real-time. Finally, it will call the main `POST /projects` endpoint with the complete data to create everything in one go.

---

## 🔒 **4. Best Practices**

1. **Clarity in UI:** The UI must clearly distinguish between the client's `Total Budget` and the developer's internal `Hourly Rate` and `Time Budget`. The latter are for planning and are not shown to the client.
2. **Template-Driven:** Offer several milestone templates (e.g., "Standard Web App," "Mobile App," "Simple Landing Page") to give users a better starting point.
3. **Flexibility:** While the planner provides a structure, the user should be able to add, remove, or rename milestones within the wizard before finalizing the project.

---

## ✅ **5. Implementation Checklist**

- [ ] **Backend:** Add `billing_type` and `total_estimated_hours` to `Project` model.
- [ ] **Backend:** Add `estimated_hours` to `ProjectMilestone` model.
- [ ] **Backend:** Create Alembic migrations for model changes.
- [ ] **Backend:** Implement the `POST /api/v1/projects/plan` endpoint and service logic.
- [ ] **Backend:** Enhance the `POST /api/v1/projects` endpoint to accept and create nested milestones.
- [ ] **Frontend:** Design and build the multi-step "New Project" wizard.
- [ ] **Frontend:** Add UI to toggle between the two planning scenarios ("Calculate Hours" vs. "Calculate Rate").
- [ ] **Frontend:** Implement the real-time calculation logic as the user interacts with the planner.
- [ ] **Frontend:** Integrate API calls to the planning and creation endpoints.
- [ ] **Frontend:** Add "Estimates vs. Actuals" variance tracking to the project dashboard UI.
- [ ] **Testing:** Write tests for the planning calculation logic and the enhanced project creation service.

By implementing this feature, you give developers a powerful tool to make smarter business decisions, which is a massive reason for them to use and pay for DevHQ.

---

## 🔄 **5. Integration with Smart Time Tracking**

The true power of this feature is realized when combined with the **Smart Time Tracker**. The planner sets the **estimate**, and the time tracker records the **actuals**.

### **Real-time Variance Analysis**

The project dashboard will provide a live comparison of estimated vs. actual hours for the project and for each milestone.

- **Data Points:** For each milestone, the UI will show `Estimated Hours`, `Actual Hours Logged`, and the `Variance`.
- **Visual Feedback:** The variance can be color-coded (e.g., green for under-budget, red for over-budget) to give the developer an at-a-glance view of project health.
- **Profitability Insights:** This allows the developer to see their effective hourly rate change in real-time as they work, helping them understand which projects are most profitable and where their estimation skills can be improved.

This feedback loop is a core value proposition, turning DevHQ into a business intelligence tool that helps developers improve their profitability over time.

```

<!--
[PROMPT_SUGGESTION]Based on this architecture, write the FastAPI endpoint and Pydantic schemas for the `POST /api/v1/projects/plan` endpoint.[/PROMPT_SUGGESTION]
[PROMPT_SUGGESTION]Draft the UI for the "Project Planner & Budgeter" wizard in Next.js.[/PROMPT_SUGGESTION]
