# 📝 DevHQ Project Notes & Design Uploads: Architecture & Best Practices

*A deep dive into the feature that serves as the internal knowledge base for every project.*

## 🎯 **1. Overview: Centralizing Project Knowledge**

The "Project Notes & Design Uploads" feature provides an integrated space within each project to store critical information and assets. It's designed to replace the need for external tools like Notion, Google Drive, or local folders, keeping the developer organized and focused within a single platform.

This feature is the internal brain of a project, capturing everything from technical specs to visual mockups.

### **Why It's an Essential Feature:**

- **For the Developer:** A single source of truth for all project-related information. Reduces context switching and time spent searching for files or notes.
- **For the Project:** Keeps documentation and assets tied directly to the project they belong to, ensuring nothing gets lost.

---

## 🌊 **2. The User Experience (UX) Flow**

1. **Taking Notes:** Inside a project, the developer navigates to the "Notes" tab. They create a new note using a Markdown editor to document requirements, meeting minutes, or code snippets. They can "pin" important notes to the top.
2. **Uploading Designs:** The developer goes to the "Files" tab and drags a set of design mockups into a dropzone. The files upload directly to the cloud.
3. **Viewing Assets:** The uploaded files appear as a gallery of thumbnails. Clicking a thumbnail opens a larger preview.
4. **Referencing:** The developer can easily switch between their notes, design files, and project tasks, all within the same project view.

---

## 🛠️ **3. Technical Architecture Deep Dive**

### **Data Models (Backend)**

We will use the existing `ProjectNote` model and create a new `DesignUpload` model.

```python
# In app/models/note.py
class ProjectNote(Base):
    # ...
    title = Column(String, nullable=False)
    content = Column(Text) # Will store Markdown text
    is_pinned = Column(Boolean, default=False)

# In a new app/models/upload.py
class DesignUpload(Base):
    __tablename__ = "design_uploads"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    file_name = Column(String, nullable=False)
    file_url = Column(String, nullable=False) # Public URL from Cloudinary
    file_type = Column(String)
    file_size_bytes = Column(Integer)
    cloudinary_public_id = Column(String, unique=True, nullable=False) # For API actions
```

### **Backend Components (FastAPI)**

1. **Notes CRUD Endpoints:** Standard RESTful endpoints for managing `ProjectNote` records, scoped by `project_id`.
    - `GET, POST, PUT, DELETE /api/v1/projects/{project_id}/notes`

2. **File Upload Flow (Direct-to-Cloud):** This is a two-step process for security and performance.
    - **Step 1: Get Signature:**
        - `POST /api/v1/projects/{project_id}/uploads/signature`: The frontend requests to upload. The backend uses the Cloudinary SDK and its secret key to generate a unique, temporary signature and upload parameters. It sends these back to the frontend.
    - **Step 2: Frontend Uploads Directly:**
        - The frontend uses the signature to make a `POST` request **directly to the Cloudinary API**, bypassing our server entirely.
    - **Step 3: Finalize Upload:**
        - `POST /api/v1/projects/{project_id}/uploads`: After Cloudinary confirms the upload was successful, the frontend sends the returned file metadata (like `public_id` and `secure_url`) to this endpoint. The backend then creates the `DesignUpload` record in the database.

3. **File Management Endpoints:**
    - `GET /api/v1/projects/{project_id}/uploads`: Lists all `DesignUpload` records for the project.
    - `DELETE /api/v1/projects/{project_id}/uploads/{upload_id}`: Deletes the database record.

### **Frontend Components (Next.js)**

1. **Notes & Files Tab:** A new section within the project detail page.

2. **Markdown Editor & Renderer:**
    - An editor component for writing notes (e.g., `react-simplemde-editor`).
    - A renderer component to safely display the saved Markdown (e.g., `react-markdown`).

3. **File Uploader Component:**
    - A drag-and-drop UI (e.g., using `react-dropzone`).
    - Logic to handle the two-step direct upload flow.

---

## 🔒 **4. Best Practices & Reliability**

1. **Embrace Markdown:** It is the de facto standard for developer documentation. It's lightweight, secure (when rendered correctly), and version-control friendly. Avoid storing raw HTML from a WYSIWYG editor.

2. **Isolate File Uploads with Signed URLs:** This is a critical security and performance pattern. It prevents malicious file uploads from ever reaching your server and offloads the bandwidth-intensive work to a specialized service (Cloudinary).

3. **Sanitize All Rendered Content:** When rendering user-generated Markdown or displaying file names, ensure they are properly sanitized to prevent Cross-Site Scripting (XSS) attacks.

4. **Use Soft Deletes for Uploads:** When a user deletes a file, it's safer to only delete the record from your `design_uploads` table (a "soft delete"). This makes the file inaccessible in the app but doesn't permanently remove it from Cloudinary, protecting against accidental deletion.

5. **Optimize Image Delivery:** Use Cloudinary's built-in transformations to serve optimized images. For the gallery view, request smaller, compressed thumbnails from the Cloudinary API instead of loading the full-size images.

---

## ✅ **5. Implementation Checklist**

- [ ] **Backend:** Create `DesignUpload` model and migration.
- [ ] **Backend:** Implement CRUD endpoints for `ProjectNote`.
- [ ] **Backend:** Implement the two-step direct upload flow for `DesignUpload` using the Cloudinary SDK.
- [ ] **Backend:** Implement GET and DELETE endpoints for `DesignUpload`.
- [ ] **Frontend:** Build the "Notes & Files" tab structure within a project.
- [ ] **Frontend:** Integrate a Markdown editor and renderer for notes.
- [ ] **Frontend:** Build a drag-and-drop file uploader component that works with the signed URL flow.
- [ ] **Frontend:** Create a gallery view to display uploaded images.
- [ ] **Security:** Ensure all rendered user content is sanitized.
- [ ] **Testing:** Write tests for both the notes and upload API endpoints.

With this feature, DevHQ becomes the all-in-one command center for every aspect of a developer's project, from initial client contact to final documentation.

```

<!--
[PROMPT_SUGGESTION]Based on all the architecture documents, generate a complete list of all database models needed for the project.[/PROMPT_SUGGESTION]
[PROMPT_SUGGESTION]Create a new architecture document for a post-MVP feature: "Team Collaboration & Roles".[/PROMPT_SUGGESTION]
