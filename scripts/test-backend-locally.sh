#!/bin/bash

# Local Backend Testing Script
# Mimics the GitHub Actions CI environment for backend testing

set -e

echo "🧪 DevHQ Backend Local Testing"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Change to backend directory
cd backend

echo "📦 Setting up Python environment..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install --upgrade pip
pip install -r requirements-dev.txt
pip install coverage-badge pip-audit

echo "🎨 Running code formatting..."
# Auto-format (like CI does)
black .
isort .

echo "🔍 Running linting checks..."
# Code formatting check
if ! black --check .; then
    echo -e "${RED}❌ Black formatting check failed${NC}"
    exit 1
fi

# Import sorting check
if ! isort --check-only .; then
    echo -e "${RED}❌ isort check failed${NC}"
    exit 1
fi

# Type checking (allow failures like CI)
echo "🔍 Running type checking..."
mypy app/ || echo -e "${YELLOW}⚠️  Type checking completed with warnings${NC}"

# Security audit (allow failures like CI)
echo "🔒 Running security audit..."
bandit -r app/ -f json -o bandit-report.json || echo -e "${YELLOW}⚠️  Security audit completed with warnings${NC}"

# Set up test environment variables (like CI)
export DATABASE_URL="sqlite:///./test.db"
export REDIS_URL="redis://localhost:6379/0"
export SECRET_KEY="test-secret-key-local"
export ENVIRONMENT="testing"

echo "🗄️  Setting up test database..."
# Note: For local testing, we're using SQLite instead of PostgreSQL
# In CI, this would be: alembic upgrade head
# For local, we'll let the tests handle database setup

echo "🧪 Running tests with coverage..."
# Run tests exactly like CI
if pytest -v --maxfail=1 --disable-warnings --cov=app --cov-report=xml --cov-report=html; then
    echo -e "${GREEN}✅ All tests passed!${NC}"
    
    # Generate coverage badge
    coverage-badge -o coverage.svg
    echo -e "${GREEN}📊 Coverage report generated${NC}"
    
    # Show coverage summary
    echo ""
    echo "📈 Coverage Summary:"
    coverage report --show-missing
    
else
    echo -e "${RED}❌ Tests failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Local backend testing completed successfully!${NC}"
echo ""
echo "📁 Generated files:"
echo "  - htmlcov/index.html (Coverage report)"
echo "  - coverage.xml (Coverage data)"
echo "  - coverage.svg (Coverage badge)"
echo "  - bandit-report.json (Security audit)"
echo ""
echo "💡 To view coverage report: open htmlcov/index.html in your browser"