#!/bin/bash
# 🗄️ Database Status Checker
# Quick script to check your database tables and data

set -e

echo "🗄️ DevHQ Database Status Check"
echo "================================"

# Check if PostgreSQL container is running
if ! docker compose -f docker-compose.dev.yml ps postgres | grep -q "Up"; then
    echo "❌ PostgreSQL container is not running!"
    echo "💡 Start it with: docker compose -f docker-compose.dev.yml up postgres -d"
    exit 1
fi

echo "✅ PostgreSQL container is running"
echo ""

# Function to run SQL commands
run_sql() {
    docker compose -f docker-compose.dev.yml exec -T postgres psql -U devhq_user -d devhq -c "$1"
}

# Check database connection
echo "🔗 Testing database connection..."
if run_sql "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    exit 1
fi

echo ""
echo "📊 Database Tables:"
echo "==================="
run_sql "\dt" 2>/dev/null || echo "No tables found (migrations might not be applied yet)"

echo ""
echo "📈 Table Record Counts:"
echo "======================="

# Check if tables exist and count records
tables=("users" "user_settings" "user_sessions")
for table in "${tables[@]}"; do
    count=$(run_sql "SELECT COUNT(*) FROM $table;" 2>/dev/null | grep -E '^[[:space:]]*[0-9]+[[:space:]]*$' | tr -d ' ' || echo "0")
    if [ "$count" != "0" ] && [ -n "$count" ]; then
        echo "📋 $table: $count records"
    else
        echo "📋 $table: Table not found or empty"
    fi
done

echo ""
echo "🔍 Recent Users (if any):"
echo "========================="
run_sql "SELECT id, email, first_name, last_name, created_at FROM users ORDER BY created_at DESC LIMIT 5;" 2>/dev/null || echo "No users found or users table doesn't exist"

echo ""
echo "🛠️ Migration Status:"
echo "===================="
cd backend
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    alembic current 2>/dev/null || echo "No migrations applied yet"
    echo ""
    echo "📝 Available migrations:"
    alembic history 2>/dev/null || echo "No migration history found"
else
    echo "❌ Virtual environment not found. Run setup_dev.sh first."
fi

echo ""
echo "💡 Useful commands:"
echo "  • Connect to DB: docker compose -f docker-compose.dev.yml exec postgres psql -U devhq_user -d devhq"
echo "  • View logs: docker compose -f docker-compose.dev.yml logs postgres"
echo "  • Apply migrations: cd backend && alembic upgrade head"