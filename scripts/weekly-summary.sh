#!/bin/bash
# 📊 Weekly Development Summary Generator
# Creates a summary of the week's progress

WEEK_NUM=$(date +"%U")
YEAR=$(date +"%Y")
WEEK_START=$(date -d "monday" +"%Y-%m-%d")
WEEK_END=$(date -d "sunday" +"%Y-%m-%d")

SUMMARY_FILE="journey/weekly-reflections/week-${WEEK_NUM}-${YEAR}.md"

echo "📊 Generating weekly summary for Week $WEEK_NUM..."

# Create weekly summary
cat > "$SUMMARY_FILE" << EOF
# Week $WEEK_NUM Summary - $WEEK_START to $WEEK_END

## 🏆 **Week's Biggest Wins**
1. 
2. 
3. 

## 📊 **Weekly Metrics**
- **Total hours worked:** [ ]
- **Features completed:** [ ]
- **Tests written:** [ ]
- **Bugs fixed:** [ ]
- **New dependencies added:** [ ]
- **Documentation pages:** [ ]

## 🎯 **Goals vs Reality**
### Planned Goals:
- [ ] Goal 1
- [ ] Goal 2
- [ ] Goal 3

### What Actually Happened:
- ✅ Achievement 1
- ✅ Achievement 2
- ❌ Missed goal (reason)

## 🧠 **Key Technical Learnings**
1. **Learning 1:** Description
2. **Learning 2:** Description
3. **Learning 3:** Description

## 🚧 **Major Challenges This Week**
### Challenge 1:
**Problem:** 
**Solution:** 
**Time Impact:** 

### Challenge 2:
**Problem:** 
**Solution:** 
**Time Impact:** 

## 💡 **Business Insights**
- 
- 
- 

## 🔄 **Next Week's Focus**
### Primary Goals:
- [ ] Priority 1
- [ ] Priority 2
- [ ] Priority 3

### Stretch Goals:
- [ ] Stretch 1
- [ ] Stretch 2

## 📈 **Progress Tracking**
### Completed Features:
- [ ] Feature 1
- [ ] Feature 2
- [ ] Feature 3

### In Progress:
- [ ] Feature A (X% complete)
- [ ] Feature B (Y% complete)

### Backlog:
- [ ] Future feature 1
- [ ] Future feature 2

## 🎭 **Reflection**
### What went well:
- 
- 

### What could be improved:
- 
- 

### Motivation level: [ /10]
### Confidence level: [ /10]

## 📸 **Visual Progress**
### Screenshots:
- 
- 

### Demos/Videos:
- 
- 

---
*Generated: $(date)*
EOF

echo "✅ Weekly summary created: $SUMMARY_FILE"

# Open the file
if command -v code &> /dev/null; then
    code "$SUMMARY_FILE"
else
    echo "📝 Weekly summary at: $SUMMARY_FILE"
fi