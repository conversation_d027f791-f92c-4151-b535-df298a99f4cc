#!/bin/bash

# DevHQ Development Environment Setup Script
# Automates the setup of the development environment with enhanced error handling

set -e

echo "🚀 Setting up DevHQ Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${CYAN}${BOLD}=== $1 ===${NC}\n"
}

# Error handling function
handle_error() {
    print_error "Setup failed at line $1. Exit code: $2"
    print_error "Please check the error above and try again."
    print_error "If you need help, please check the troubleshooting guide or create an issue."
    exit $2
}

# Set up error trap
trap 'handle_error $LINENO $?' ERR

# Function to check command existence
check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to check version requirements
check_version() {
    local cmd="$1"
    local version_cmd="$2"
    local min_version="$3"
    local current_version
    
    current_version=$(eval "$version_cmd" 2>/dev/null | head -1)
    print_status "Found $cmd: $current_version"
    
    # Basic version check (you can enhance this for more complex version comparisons)
    if [[ "$current_version" == *"$min_version"* ]] || [[ "$current_version" > *"$min_version"* ]]; then
        return 0
    else
        return 1
    fi
}

print_header "SYSTEM REQUIREMENTS CHECK"

# Check if we're in the right directory
if [[ ! -f "docker-compose.yml" ]]; then
    print_error "docker-compose.yml not found. Please run this script from the project root directory."
    exit 1
fi

# Check Python
print_status "Checking Python installation..."
if check_command python3; then
    python_version=$(python3 --version 2>&1)
    if [[ "$python_version" == *"3.11"* ]] || [[ "$python_version" == *"3.12"* ]]; then
        print_success "Python 3.11+ found: $python_version"
    else
        print_warning "Python 3.11+ recommended. Found: $python_version"
    fi
elif check_command python; then
    python_version=$(python --version 2>&1)
    if [[ "$python_version" == *"3.11"* ]] || [[ "$python_version" == *"3.12"* ]]; then
        print_success "Python 3.11+ found: $python_version"
    else
        print_error "Python 3.11+ required. Found: $python_version"
        exit 1
    fi
else
    print_error "Python is not installed. Please install Python 3.11+ first."
    exit 1
fi

# Check Node.js
print_status "Checking Node.js installation..."
if check_command node; then
    node_version=$(node --version 2>&1)
    major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
    if [[ "$major_version" -ge 18 ]]; then
        print_success "Node.js 18+ found: $node_version"
    else
        print_error "Node.js 18+ required. Found: $node_version"
        exit 1
    fi
else
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check npm
print_status "Checking npm installation..."
if check_command npm; then
    npm_version=$(npm --version 2>&1)
    print_success "npm found: $npm_version"
else
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

# Check Git
print_status "Checking Git installation..."
if check_command git; then
    git_version=$(git --version 2>&1)
    print_success "Git found: $git_version"
else
    print_error "Git is not installed. Please install Git first."
    exit 1
fi

# Check Docker
print_status "Checking Docker installation..."
if check_command docker; then
    docker_version=$(docker --version 2>&1)
    print_success "Docker found: $docker_version"
    
    # Check if Docker daemon is running
    if docker info &> /dev/null; then
        print_success "Docker daemon is running"
    else
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
else
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check Docker Compose
print_status "Checking Docker Compose installation..."
if check_command docker-compose; then
    compose_version=$(docker-compose --version 2>&1)
    print_success "Docker Compose found: $compose_version"
elif docker compose version &> /dev/null; then
    compose_version=$(docker compose version 2>&1)
    print_success "Docker Compose (plugin) found: $compose_version"
    # Create alias for compatibility
    alias docker-compose='docker compose'
else
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_success "All system requirements met!"

print_header "PROJECT SETUP"

# Create necessary directories
print_status "Creating project directories..."
mkdir -p backend/logs
mkdir -p frontend/.next
mkdir -p nginx/ssl
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p .devhq/backups
mkdir -p .devhq/logs

print_success "Project directories created!"

print_header "ENVIRONMENT CONFIGURATION"

print_status "Setting up environment files..."

# Setup backend environment
if [ ! -f backend/.env ]; then
    if [ -f backend/.env.example ]; then
        print_status "Creating backend .env file from .env.example..."
        cp backend/.env.example backend/.env
        
        # Generate a secure secret key
        if command -v openssl &> /dev/null; then
            secret_key=$(openssl rand -hex 32)
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                sed -i '' "s/your_secret_key_here/$secret_key/g" backend/.env
            else
                # Linux
                sed -i "s/your_secret_key_here/$secret_key/g" backend/.env
            fi
            print_success "Generated secure secret key"
        fi
        
        print_success "Backend .env file created from template!"
        print_warning "Please update the .env file with your actual API keys and credentials"
    else
        print_status "Creating backend .env file..."
        cat > backend/.env << EOF
# Application
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=dev-secret-key-$(openssl rand -hex 16 2>/dev/null || echo "change-me-$(date +%s)")

# Database
DATABASE_URL=postgresql://devhq_user:devhq_password@localhost:5433/devhq
TEST_DATABASE_URL=postgresql://devhq_user:devhq_password@localhost:5433/devhq_test

# Redis
REDIS_URL=redis://localhost:6380/0

# Security
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# External Services (replace with your keys)
PAYSTACK_SECRET_KEY=sk_test_your_paystack_key_here
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
CLOUDINARY_URL=cloudinary://your_cloudinary_url_here

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Monitoring
SENTRY_DSN=your_sentry_dsn_here

# CORS
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Performance
REDIS_CACHE_TTL=300
EOF
        print_success "Backend .env file created!"
        print_warning "Please update the .env file with your actual API keys and credentials"
    fi
else
    print_warning "Backend .env file already exists, skipping..."
fi

if [ ! -f frontend/.env.local ]; then
    print_status "Creating frontend .env.local file..."
    cat > frontend/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# External Services (replace with your keys)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_name

# Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_analytics_id
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Feature Flags
NEXT_PUBLIC_ENABLE_PAYMENTS=true
NEXT_PUBLIC_ENABLE_CLIENT_PORTAL=true

# Environment
NODE_ENV=development
EOF
    print_success "Frontend .env.local file created!"
else
    print_warning "Frontend .env.local file already exists, skipping..."
fi

# Set up Git hooks
print_status "Setting up Git hooks..."
if [ -d .git ]; then
    # Create pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# DevHQ Pre-commit Hook

echo "Running pre-commit checks..."

# Check if backend code exists and run checks
if [ -d "backend" ]; then
    echo "Checking backend code..."
    cd backend
    
    # Run black formatting check
    if command -v black &> /dev/null; then
        black --check . || {
            echo "❌ Backend code formatting issues found. Run 'black .' to fix."
            exit 1
        }
    fi
    
    # Run isort check
    if command -v isort &> /dev/null; then
        isort --check-only . || {
            echo "❌ Backend import sorting issues found. Run 'isort .' to fix."
            exit 1
        }
    fi
    
    cd ..
fi

# Check if frontend code exists and run checks
if [ -d "frontend" ]; then
    echo "Checking frontend code..."
    cd frontend
    
    # Run ESLint
    if [ -f "package.json" ] && command -v npm &> /dev/null; then
        npm run lint || {
            echo "❌ Frontend linting issues found. Run 'npm run lint:fix' to fix."
            exit 1
        }
    fi
    
    cd ..
fi

echo "✅ Pre-commit checks passed!"
EOF

    chmod +x .git/hooks/pre-commit
    print_success "Git hooks set up!"
else
    print_warning "Not a Git repository, skipping Git hooks setup..."
fi

print_header "DOCKER SERVICES SETUP"

# Clean up any existing containers
print_status "Cleaning up existing containers..."
docker-compose down --remove-orphans 2>/dev/null || true

# Build Docker images
print_status "Building Docker images..."
if ! docker-compose build --no-cache; then
    print_error "Failed to build Docker images"
    exit 1
fi

print_status "Starting database and Redis services..."
if ! docker-compose up -d postgres redis; then
    print_error "Failed to start services"
    exit 1
fi

# Wait for database to be ready with better checking
print_status "Waiting for database to be ready..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker-compose exec -T postgres pg_isready -U devhq_user -d devhq &>/dev/null; then
        print_success "Database is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Database failed to start after $max_attempts attempts"
        print_error "Please check Docker logs: docker-compose logs postgres"
        exit 1
    fi
    
    print_status "Waiting for database... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# Check Redis
print_status "Checking Redis connection..."
if docker-compose exec -T redis redis-cli ping &>/dev/null; then
    print_success "Redis is ready!"
else
    print_warning "Redis may not be fully ready, but continuing..."
fi

print_header "SETUP VALIDATION"

# Run validation script if it exists
if [ -f "validate-setup.py" ]; then
    print_status "Running setup validation..."
    if python3 validate-setup.py; then
        print_success "Setup validation passed!"
    else
        print_warning "Setup validation found some issues. Please review above."
    fi
fi

print_header "SETUP COMPLETE"

print_success "🎉 DevHQ development environment setup complete!"
echo
print_status "${BOLD}Next steps:${NC}"
print_status "1. ${YELLOW}Update environment files:${NC}"
print_status "   - backend/.env (database, API keys, email settings)"
print_status "   - frontend/.env.local (API keys, feature flags)"
print_status "2. ${YELLOW}Start the development servers:${NC}"
print_status "   - Backend: cd backend && ./setup_dev.sh && ./start_dev_server.sh"
print_status "   - Frontend: cd frontend && npm install && npm run dev"
print_status "3. ${YELLOW}Access the application:${NC}"
print_status "   - Frontend: http://localhost:3000"
print_status "   - Backend API: http://localhost:8000"
print_status "   - API Documentation: http://localhost:8000/docs"
echo
print_status "${BOLD}Useful commands:${NC}"
print_status "${CYAN}Docker:${NC}"
print_status "  docker-compose up -d           # Start all services in background"
print_status "  docker-compose logs -f         # View logs"
print_status "  docker-compose down            # Stop all services"
print_status "  docker-compose restart         # Restart services"
print_status "${CYAN}Development:${NC}"
print_status "  python3 validate-setup.py      # Validate setup"
print_status "  cd backend && pytest           # Run backend tests"
print_status "  cd frontend && npm test        # Run frontend tests"
print_status "${CYAN}Troubleshooting:${NC}"
print_status "  docker-compose logs [service]  # View service logs"
print_status "  docker system prune            # Clean up Docker resources"
echo
print_warning "${BOLD}Important:${NC} Make sure to update your .env files with real API keys before deploying!"