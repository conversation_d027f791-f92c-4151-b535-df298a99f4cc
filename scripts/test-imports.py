#!/usr/bin/env python3
"""
Quick test script to verify that all imports work correctly
This tests the specific issue we fixed in the CI pipeline
"""

import os
import sys

# Add backend directory to path (like our pytest.ini does)
backend_dir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)) + "/backend")
sys.path.insert(0, backend_dir)


def test_imports():
    """Test all critical imports that were failing in CI"""

    print("🧪 Testing critical imports...")

    try:
        # Test the main import that was failing
        from app.config import settings

        print("✅ app.config import successful")

        # Test other critical imports
        from app.database import Base, get_db

        print("✅ app.database import successful")

        from app.main import app

        print("✅ app.main import successful")

        # Test model imports
        from app.models.user import User, UserSettings

        print("✅ app.models.user import successful")

        from app.models.client import Client

        print("✅ app.models.client import successful")

        # Test FastAPI imports (these were also failing)
        from fastapi.testclient import TestClient

        print("✅ fastapi.testclient import successful")

        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker

        print("✅ sqlalchemy imports successful")

        print("\n🎉 All imports successful! The CI fix is working.")
        return True

    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
