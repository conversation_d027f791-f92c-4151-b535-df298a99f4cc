#!/bin/bash
# 🗄️ Quick Database Connection Script
# Connects you directly to the PostgreSQL database

echo "🗄️ Connecting to DevHQ PostgreSQL Database..."
echo "💡 Useful commands once connected:"
echo "   \\dt          - List all tables"
echo "   \\d users     - Describe users table"
echo "   \\q           - Quit"
echo "   SELECT * FROM users LIMIT 5; - View user data"
echo ""

# Connect to PostgreSQL
docker compose -f docker-compose.dev.yml exec postgres psql -U devhq_user -d devhq