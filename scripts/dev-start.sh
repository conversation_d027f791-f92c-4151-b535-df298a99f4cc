#!/bin/bash
# 🚀 DevHQ Development Startup Script
# This script starts your complete development environment

set -e  # Exit on any error

echo "🚀 Starting DevHQ Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start database services
echo "📊 Starting PostgreSQL and Redis..."
docker compose -f docker-compose.dev.yml up postgres redis -d

# Wait a moment for services to start
echo "⏳ Waiting for services to start..."
sleep 3

# Check if services are running
echo "🔍 Checking service status..."
docker compose -f docker-compose.dev.yml ps

# Navigate to backend
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Run ./setup_dev.sh first."
    exit 1
fi

# Activate virtual environment
echo "🐍 Activating Python virtual environment..."
source venv/bin/activate

# Apply database migrations
echo "🗄️ Applying database migrations..."
alembic upgrade head

# Check database connection
echo "🔗 Testing database connection..."
python -c "
from app.database import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        result = conn.execute(text('SELECT 1'))
        print('✅ Database connection successful!')
except Exception as e:
    print(f'❌ Database connection failed: {e}')
    exit(1)
"

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📋 Next steps:"
echo "   1. cd backend"
echo "   2. source venv/bin/activate"
echo "   3. uvicorn app.main:app --reload"
echo ""
echo "🌐 Once started, visit:"
echo "   • API Docs: http://localhost:8000/docs"
echo "   • Health Check: http://localhost:8000/health"
echo ""
echo "🗄️ Database connection details:"
echo "   • Host: localhost:5432"
echo "   • Database: devhq"
echo "   • Username: devhq_user"
echo "   • Password: devhq_password"