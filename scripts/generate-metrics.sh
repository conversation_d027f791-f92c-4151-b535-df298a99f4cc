#!/bin/bash
# 📊 Development Metrics Generator
# Automatically collects project metrics for documentation

echo "📊 Generating DevHQ Development Metrics..."

# Create metrics directory
mkdir -p journey/metrics

# Get current date
TODAY=$(date +"%Y-%m-%d")
METRICS_FILE="journey/metrics/metrics-${TODAY}.md"

# Initialize metrics file
cat > "$METRICS_FILE" << EOF
# DevHQ Development Metrics - $TODAY

## 📊 Code Metrics
EOF

# Count lines of code
echo "### Lines of Code" >> "$METRICS_FILE"
echo "\`\`\`" >> "$METRICS_FILE"
find backend/app -name "*.py" -exec wc -l {} + | tail -1 | awk '{print "Python (backend): " $1 " lines"}' >> "$METRICS_FILE"
if [ -d "frontend" ]; then
    find frontend -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | xargs wc -l | tail -1 | awk '{print "JavaScript/TypeScript: " $1 " lines"}' >> "$METRICS_FILE"
fi
echo "\`\`\`" >> "$METRICS_FILE"
echo "" >> "$METRICS_FILE"

# Count files
echo "### File Count" >> "$METRICS_FILE"
echo "\`\`\`" >> "$METRICS_FILE"
find backend/app -name "*.py" | wc -l | awk '{print "Python files: " $1}' >> "$METRICS_FILE"
find backend/tests -name "*.py" | wc -l | awk '{print "Test files: " $1}' >> "$METRICS_FILE"
if [ -d "frontend" ]; then
    find frontend -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" | wc -l | awk '{print "Frontend files: " $1}' >> "$METRICS_FILE"
fi
echo "\`\`\`" >> "$METRICS_FILE"
echo "" >> "$METRICS_FILE"

# Database metrics
echo "## 🗄️ Database Metrics" >> "$METRICS_FILE"
if docker compose -f docker-compose.dev.yml ps postgres | grep -q "Up"; then
    echo "### Table Count" >> "$METRICS_FILE"
    echo "\`\`\`" >> "$METRICS_FILE"
    docker compose -f docker-compose.dev.yml exec -T postgres psql -U devhq_user -d devhq -c "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'public' AND table_name != 'alembic_version';" 2>/dev/null | grep -E '^[[:space:]]*[0-9]+[[:space:]]*$' | awk '{print "Tables: " $1}' >> "$METRICS_FILE"
    echo "\`\`\`" >> "$METRICS_FILE"
    echo "" >> "$METRICS_FILE"
else
    echo "Database not running - skipping database metrics" >> "$METRICS_FILE"
    echo "" >> "$METRICS_FILE"
fi

# Git metrics
echo "## 📈 Git Metrics" >> "$METRICS_FILE"
if [ -d ".git" ]; then
    echo "### Commit History" >> "$METRICS_FILE"
    echo "\`\`\`" >> "$METRICS_FILE"
    git rev-list --count HEAD 2>/dev/null | awk '{print "Total commits: " $1}' >> "$METRICS_FILE" || echo "Total commits: Unable to count" >> "$METRICS_FILE"
    git log --oneline --since="1 week ago" | wc -l | awk '{print "Commits this week: " $1}' >> "$METRICS_FILE"
    git log --oneline --since="1 day ago" | wc -l | awk '{print "Commits today: " $1}' >> "$METRICS_FILE"
    echo "\`\`\`" >> "$METRICS_FILE"
    echo "" >> "$METRICS_FILE"
fi

# Dependencies
echo "## 📦 Dependencies" >> "$METRICS_FILE"
if [ -f "backend/requirements.txt" ]; then
    echo "### Backend Dependencies" >> "$METRICS_FILE"
    echo "\`\`\`" >> "$METRICS_FILE"
    grep -v "^#" backend/requirements.txt | grep -v "^$" | wc -l | awk '{print "Production dependencies: " $1}' >> "$METRICS_FILE"
    if [ -f "backend/requirements-dev.txt" ]; then
        grep -v "^#" backend/requirements-dev.txt | grep -v "^$" | wc -l | awk '{print "Development dependencies: " $1}' >> "$METRICS_FILE"
    fi
    echo "\`\`\`" >> "$METRICS_FILE"
    echo "" >> "$METRICS_FILE"
fi

# Test metrics
echo "## 🧪 Test Metrics" >> "$METRICS_FILE"
if [ -d "backend/tests" ]; then
    echo "### Test Coverage" >> "$METRICS_FILE"
    echo "\`\`\`" >> "$METRICS_FILE"
    find backend/tests -name "test_*.py" | wc -l | awk '{print "Test files: " $1}' >> "$METRICS_FILE"
    
    # Try to get test count
    cd backend
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
        python -m pytest --collect-only -q 2>/dev/null | grep "test session starts" -A 10 | grep "collected" | awk '{print "Total tests: " $1}' >> "../$METRICS_FILE" || echo "Total tests: Unable to count" >> "../$METRICS_FILE"
        cd ..
    else
        echo "Total tests: Virtual environment not found" >> "$METRICS_FILE"
    fi
    echo "\`\`\`" >> "$METRICS_FILE"
    echo "" >> "$METRICS_FILE"
fi

# Documentation metrics
echo "## 📚 Documentation Metrics" >> "$METRICS_FILE"
echo "\`\`\`" >> "$METRICS_FILE"
find . -name "*.md" | wc -l | awk '{print "Markdown files: " $1}' >> "$METRICS_FILE"
find . -name "README.md" | wc -l | awk '{print "README files: " $1}' >> "$METRICS_FILE"
if [ -d "journey" ]; then
    find journey -name "*.md" | wc -l | awk '{print "Journey documentation: " $1}' >> "$METRICS_FILE"
fi
echo "\`\`\`" >> "$METRICS_FILE"
echo "" >> "$METRICS_FILE"

# Add timestamp
echo "---" >> "$METRICS_FILE"
echo "*Generated: $(date)*" >> "$METRICS_FILE"

echo "✅ Metrics generated: $METRICS_FILE"

# Display summary
echo ""
echo "📊 Quick Summary:"
echo "=================="
grep -E "(Python|Total commits|Test files|Tables):" "$METRICS_FILE" | sed 's/^/   /'