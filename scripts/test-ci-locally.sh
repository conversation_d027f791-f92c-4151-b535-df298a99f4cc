#!/bin/bash

# Script to test CI/CD pipeline locally using Act
# This runs the exact same GitHub Actions workflow locally

set -e

echo "🚀 Testing DevHQ CI/CD Pipeline Locally"
echo "========================================"

# Check if act is installed
if ! command -v act &> /dev/null; then
    echo "❌ Act is not installed. Installing..."
    
    # Install act based on OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        brew install act
    else
        echo "Please install act manually: https://github.com/nektos/act#installation"
        exit 1
    fi
fi

echo "✅ Act is installed"

# Create act configuration if it doesn't exist
if [ ! -f .actrc ]; then
    echo "📝 Creating act configuration..."
    cat > .actrc << EOF
# Act configuration for DevHQ
-P ubuntu-latest=catthehacker/ubuntu:act-latest
-P ubuntu-20.04=catthehacker/ubuntu:act-20.04
-P ubuntu-18.04=catthehacker/ubuntu:act-18.04
--container-architecture linux/amd64
EOF
fi

# Run specific job or all jobs
JOB=${1:-"backend-test"}

echo "🧪 Running CI job: $JOB"
echo "This will take a few minutes..."

# Run the GitHub Actions workflow locally
act -j $JOB --verbose

echo "✅ Local CI test completed!"