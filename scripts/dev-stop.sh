#!/bin/bash
# 🛑 DevHQ Development Environment Stop Script
# Safely stops all development services

echo "🛑 Stopping DevHQ Development Environment..."

# Stop database services
echo "📊 Stopping PostgreSQL and Redis..."
docker compose -f docker-compose.dev.yml stop postgres redis

# Check status
echo "🔍 Final status check..."
docker compose -f docker-compose.dev.yml ps

echo ""
echo "✅ Development environment stopped successfully!"
echo ""
echo "💡 To start again, run: ./scripts/dev-start.sh"
echo "🗑️ To remove all data, run: docker compose -f docker-compose.dev.yml down -v"