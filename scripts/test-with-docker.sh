#!/bin/bash

# Docker-based CI Testing
# Runs tests in the exact same environment as GitHub Actions

set -e

echo "🐳 DevHQ Docker-based CI Testing"
echo "================================"

# Create a temporary docker-compose for testing
cat > docker-compose.test.yml << EOF
version: '3.8'

services:
  postgres-test:
    image: postgres:15
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: devhq_test
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis-test:
    image: redis:7
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    environment:
      DATABASE_URL: *************************************************/devhq_test
      REDIS_URL: redis://redis-test:6379/0
      SECRET_KEY: test-secret-key-docker
      ENVIRONMENT: testing
    volumes:
      - ./backend:/app
    working_dir: /app
    command: |
      bash -c "
        echo '🧪 Running backend tests in Docker...'
        
        # Install dev dependencies
        pip install -r requirements-dev.txt
        pip install coverage-badge pip-audit
        
        # Run formatting
        black .
        isort .
        
        # Run linting
        black --check .
        isort --check-only .
        mypy app/ || true
        bandit -r app/ -f json -o bandit-report.json || true
        
        # Run migrations
        alembic upgrade head
        
        # Run tests
        pytest -v --maxfail=1 --disable-warnings --cov=app --cov-report=xml --cov-report=html
        
        # Generate coverage badge
        coverage-badge -o coverage.svg
        
        echo '✅ Docker-based testing completed!'
      "
EOF

echo "🚀 Starting test services..."
docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit

echo "🧹 Cleaning up..."
docker-compose -f docker-compose.test.yml down -v
rm docker-compose.test.yml

echo "✅ Docker-based testing completed!"