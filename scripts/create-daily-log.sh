#!/bin/bash
# 📝 Daily Development Log Creator
# Automatically creates today's development log from template

# Get today's date
TODAY=$(date +"%Y-%m-%d")
WEEK_NUM=$(date +"%U")
DAY_COUNT_FILE=".day_counter"

# Create journey directory if it doesn't exist
mkdir -p journey/daily-logs
mkdir -p journey/weekly-reflections
mkdir -p journey/technical-decisions
mkdir -p journey/challenges-solutions
mkdir -p journey/milestones
mkdir -p journey/media/screenshots
mkdir -p journey/media/videos

# Get day counter
if [ -f "$DAY_COUNT_FILE" ]; then
    DAY_COUNT=$(cat $DAY_COUNT_FILE)
    DAY_COUNT=$((DAY_COUNT + 1))
else
    DAY_COUNT=1
fi
echo $DAY_COUNT > $DAY_COUNT_FILE

# Create daily log file
LOG_FILE="journey/daily-logs/${TODAY}-day${DAY_COUNT}.md"

if [ -f "$LOG_FILE" ]; then
    echo "📝 Log for today already exists: $LOG_FILE"
    echo "Opening existing log..."
else
    echo "📝 Creating new daily log: $LOG_FILE"
    
    # Create log from template
    cat > "$LOG_FILE" << EOF
# DevHQ Development Journey - Day $DAY_COUNT

## Date: $TODAY - Day $DAY_COUNT of Development

### 🎯 **Today's Goals**
- [ ] 
- [ ] 
- [ ] 

### ⚡ **What I Accomplished**
- ✅ 
- ✅ 
- ✅ 

### 🧠 **Technical Decisions Made**
| Decision | Why | Alternatives Considered | Impact |
|----------|-----|------------------------|---------|
|  |  |  |  |

### 🐛 **Challenges Faced & Solutions**
#### Challenge 1: 
**Problem:** 
**Solution:** 
**Code/Commands:**
\`\`\`bash
# Commands that fixed it
\`\`\`
**Lesson:** 

### 📈 **Metrics & Progress**
- **Time spent:** [ ] hours
- **Features completed:** [ ]
- **Tests written:** [ ]
- **Bugs fixed:** [ ]
- **Lines of code:** [ ]

### 🔍 **Code Snippets Worth Remembering**
\`\`\`python
# Useful code you wrote today

\`\`\`

### 💡 **Ideas & Future Features**
- 
- 
- 

### 📚 **Resources Used**
- [Title](URL) - What you learned

### 🎭 **Mood & Motivation**
**Energy Level:** [ /10]
**Confidence:** [ /10]
**Excitement:** [ /10]
**Notes:** 

### 🔄 **Tomorrow's Plan**
- [ ] 
- [ ] 
- [ ] 

### 📸 **Screenshots/Media**
- 

---
*Created: $(date)*
EOF

    echo "✅ Daily log created successfully!"
fi

# Open the file (adjust editor as needed)
if command -v code &> /dev/null; then
    code "$LOG_FILE"
elif command -v vim &> /dev/null; then
    vim "$LOG_FILE"
else
    echo "📝 Log file created at: $LOG_FILE"
fi