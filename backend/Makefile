# Makefile for DevHQ Backend Testing

# Variables
PYTHON := python3
PIP := pip
TEST_RUNNER := scripts/run_tests.py

# Default target
.PHONY: help
help:
	@echo "DevHQ Backend Test Commands"
	@echo "==========================="
	@echo "make test              - Run all tests"
	@echo "make test-fast         - Run fast tests only"
	@echo "make test-slow         - Run slow tests only"
	@echo "make test-unit         - Run unit tests"
	@echo "make test-integration   - Run integration tests"
	@echo "make test-e2e          - Run end-to-end tests"
	@echo "make test-coverage     - Run tests with coverage"
	@echo "make test-suite SUITE= - Run specific test suite"
	@echo "make list-suites       - List available test suites"
	@echo "make install-test-deps - Install test dependencies"
	@echo "make clean-test-data   - Clean test data"
	@echo ""
	@echo "Available test suites:"
	@echo "  auth, timer, project, client, invoice, analytics, workflow"
	@echo "  models, services, integration"

# Install test dependencies
.PHONY: install-test-deps
install-test-deps:
	$(PIP) install -r requirements-test.txt

# Run all tests
.PHONY: test
test:
	$(PYTHON) $(TEST_RUNNER)

# Run tests with verbose output
.PHONY: test-verbose
test-verbose:
	$(PYTHON) $(TEST_RUNNER) --verbose

# Run fast tests only
.PHONY: test-fast
test-fast:
	$(PYTHON) $(TEST_RUNNER) --pattern "not slow"

# Run slow tests only
.PHONY: test-slow
test-slow:
	$(PYTHON) $(TEST_RUNNER) --pattern "slow"

# Run unit tests
.PHONY: test-unit
test-unit:
	$(PYTHON) $(TEST_RUNNER) --pattern "unit"

# Run integration tests
.PHONY: test-integration
test-integration:
	$(PYTHON) $(TEST_RUNNER) --pattern "integration"

# Run end-to-end tests
.PHONY: test-e2e
test-e2e:
	$(PYTHON) $(TEST_RUNNER) --pattern "endtoend"

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	$(PYTHON) $(TEST_RUNNER) --coverage

# Run specific test suite
.PHONY: test-suite
test-suite:
ifndef SUITE
	@echo "Error: Please specify suite name with SUITE=<suite_name>"
	@echo "Example: make test-suite SUITE=auth"
	@exit 1
endif
	$(PYTHON) $(TEST_RUNNER) --suite $(SUITE)

# List available test suites
.PHONY: list-suites
list-suites:
	$(PYTHON) $(TEST_RUNNER) --list-suites

# Clean test data
.PHONY: clean-test-data
clean-test-data:
	@echo "Cleaning test data..."
	rm -f test.db
	rm -rf htmlcov/
	rm -rf .coverage
	@echo "Test data cleaned."

# Run smoke tests
.PHONY: test-smoke
test-smoke:
	$(PYTHON) -m pytest tests/test_smoke.py -v

# Run a single test file
.PHONY: test-single
test-single:
ifndef FILE
	@echo "Error: Please specify test file with FILE=<file_name>"
	@echo "Example: make test-single FILE=test_auth_endpoints.py"
	@exit 1
endif
	$(PYTHON) -m pytest tests/$(FILE) -v

# Run tests matching pattern
.PHONY: test-pattern
test-pattern:
ifndef PATTERN
	@echo "Error: Please specify pattern with PATTERN=<pattern>"
	@echo "Example: make test-pattern PATTERN=timer"
	@exit 1
endif
	$(PYTHON) -m pytest -k $(PATTERN) -v