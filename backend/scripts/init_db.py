"""
Database initialization script
Creates tables and sets up initial data
"""

import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

import structlog

from app.database import create_tables, engine
from app.models import Base

logger = structlog.get_logger()


def init_database():
    """Initialize the database with all tables"""
    try:
        logger.info("Creating database tables...")
        create_tables()
        logger.info("Database tables created successfully!")

    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        sys.exit(1)


if __name__ == "__main__":
    init_database()
