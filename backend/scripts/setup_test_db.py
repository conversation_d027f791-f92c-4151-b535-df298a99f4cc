#!/usr/bin/env python
"""
Setup test database for DevHQ backend tests

This script creates a test database in the Docker PostgreSQL instance
and sets up the necessary tables for testing.
"""

import os
import sys
import time

import psycopg2
from sqlalchemy import create_engine, text

# Database configuration
DB_USER = "devhq_user"
DB_PASSWORD = "devhq_password"
DB_HOST = "localhost"
DB_PORT = "5433"
TEST_DB_NAME = "devhq_test"


def wait_for_postgres(host, port, user, password, max_retries=10, retry_interval=2):
    """Wait for PostgreSQL to be ready"""
    retries = 0
    while retries < max_retries:
        try:
            conn = psycopg2.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                # Connect to default 'postgres' database
                dbname="postgres",
            )
            conn.close()
            return True
        except psycopg2.OperationalError:
            retries += 1
            print(f"Waiting for PostgreSQL to be ready... ({retries}/{max_retries})")
            time.sleep(retry_interval)

    return False


def create_test_database():
    """Create test database if it doesn't exist"""
    # Connect to default database
    conn = psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        user=DB_USER,
        password=DB_PASSWORD,
        dbname="postgres",
    )
    conn.autocommit = True
    cursor = conn.cursor()

    # Check if database exists
    cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{TEST_DB_NAME}'")
    exists = cursor.fetchone()

    if not exists:
        print(f"Creating test database '{TEST_DB_NAME}'...")
        cursor.execute(f"CREATE DATABASE {TEST_DB_NAME}")
        print(f"Test database '{TEST_DB_NAME}' created successfully.")
    else:
        print(f"Test database '{TEST_DB_NAME}' already exists.")

    cursor.close()
    conn.close()


def main():
    """Main function"""
    print("Setting up test database for DevHQ backend tests...")

    # Wait for PostgreSQL to be ready
    if not wait_for_postgres(DB_HOST, DB_PORT, DB_USER, DB_PASSWORD):
        print(
            "Error: PostgreSQL is not available. Make sure Docker containers are running."
        )
        sys.exit(1)

    # Create test database
    create_test_database()

    print("Test database setup completed successfully.")
    print("You can now run tests with Docker PostgreSQL using:")
    print("USE_POSTGRES_TESTS=true USE_DOCKER_DB=true pytest")


if __name__ == "__main__":
    main()
