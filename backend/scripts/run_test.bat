@echo off
REM Run DevHQ backend tests with different database configurations

REM Parse command line arguments
set USE_POSTGRES=false
set USE_DOCKER=false
set TEST_PATH=

:parse_args
if "%~1"=="" goto run_test
if "%~1"=="--postgres" set USE_POSTGRES=true
if "%~1"=="--docker" set USE_DOCKER=true
if "%~1"=="--test-path" set TEST_PATH=%~2 && shift
shift
goto parse_args

:run_test
REM Build the command
set CMD=python run_test.py
if "%USE_POSTGRES%"=="true" set CMD=%CMD% --postgres
if "%USE_DOCKER%"=="true" set CMD=%CMD% --docker
if not "%TEST_PATH%"=="" set CMD=%CMD% --test-path "%TEST_PATH%"

REM Run the test
echo Running: %CMD%
%CMD%