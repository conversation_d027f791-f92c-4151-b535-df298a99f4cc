#!/usr/bin/env python3
"""
Simple smoke test runner for DevHQ backend
"""

import subprocess
import sys
from pathlib import Path


def run_smoke_test():
    """Run a basic smoke test to verify the system works."""
    try:
        # Change to the backend directory
        backend_dir = Path(__file__).parent
        result = subprocess.run(
            ["python3", "-m", "pytest", "tests/test_smoke.py", "-v"],
            cwd=backend_dir,
            capture_output=True,
            text=True,
            timeout=30,
        )

        print("=== Smoke Test Results ===")
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)

        if result.returncode == 0:
            print("✅ Smoke test passed!")
            return True
        else:
            print("❌ Smoke test failed!")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Smoke test timed out!")
        return False
    except FileNotFoundError:
        print("❌ Python or pytest not found!")
        return False
    except Exception as e:
        print(f"❌ Smoke test error: {e}")
        return False


if __name__ == "__main__":
    success = run_smoke_test()
    sys.exit(0 if success else 1)
