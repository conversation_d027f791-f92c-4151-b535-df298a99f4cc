#!/usr/bin/env python3
"""
Test runner script for DevHQ backend tests
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_tests(test_pattern=None, verbose=False, coverage=False):
    """
    Run tests with optional pattern matching

    Args:
        test_pattern (str): Pattern to match test files
        verbose (bool): Enable verbose output
        coverage (bool): Run with coverage reporting
    """
    cmd = ["pytest"]

    if coverage:
        cmd.extend(
            [
                "--cov=app",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov",
                "--cov-fail-under=80",  # Require 80% coverage
            ]
        )

    if verbose:
        cmd.append("-v")

    if test_pattern:
        cmd.append(f"-k {test_pattern}")
    else:
        # Run all tests
        cmd.append("tests/")

    cmd.extend(["-x", "--tb=short"])  # Stop on first failure  # Short traceback format

    print(f"Running tests with command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Tests failed with return code: {e.returncode}")
        return False


def run_specific_test_suite(suite_name):
    """
    Run specific test suite

    Args:
        suite_name (str): Name of test suite to run
    """
    suite_map = {
        "auth": "test_auth_endpoints.py",
        "timer": "test_timer_endpoints_enhanced.py",
        "project": "test_project_endpoints_enhanced.py",
        "client": "test_client_endpoints_enhanced.py",
        "invoice": "test_invoice_endpoints.py",
        "analytics": "test_analytics_endpoints.py",
        "workflow": "test_end_to_end_workflow.py",
        "models": "test_*_models.py",
        "services": "test_*_service*.py",
        "integration": "test_*integration*.py",
    }

    if suite_name in suite_map:
        pattern = suite_map[suite_name]
        return run_tests(test_pattern=pattern)
    else:
        print(f"Unknown test suite: {suite_name}")
        print("Available suites:")
        for suite in suite_map.keys():
            print(f"  - {suite}")
        return False


def main():
    parser = argparse.ArgumentParser(description="DevHQ Backend Test Runner")
    parser.add_argument(
        "--suite",
        help="Run specific test suite (auth, timer, project, client, invoice, analytics, workflow, models, services, integration)",
    )
    parser.add_argument("--pattern", help="Run tests matching pattern")
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )
    parser.add_argument(
        "--coverage", "-c", action="store_true", help="Run with coverage reporting"
    )
    parser.add_argument(
        "--list-suites", action="store_true", help="List available test suites"
    )

    args = parser.parse_args()

    if args.list_suites:
        print("Available test suites:")
        print("  auth        - Authentication endpoints")
        print("  timer       - Timer functionality")
        print("  project     - Project management")
        print("  client      - Client management")
        print("  invoice     - Invoice processing")
        print("  analytics   - Analytics and reporting")
        print("  workflow    - End-to-end workflows")
        print("  models      - Database models")
        print("  services    - Business logic services")
        print("  integration - Integration tests")
        return 0

    if args.suite:
        success = run_specific_test_suite(args.suite)
    elif args.pattern:
        success = run_tests(
            test_pattern=args.pattern, verbose=args.verbose, coverage=args.coverage
        )
    else:
        success = run_tests(verbose=args.verbose, coverage=args.coverage)

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
