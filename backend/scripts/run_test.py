import argparse
import os
import subprocess
import sys


def run_test(use_postgres=False, use_docker=False, test_path=None):
    # Set environment variables
    os.environ["USE_POSTGRES_TESTS"] = "true" if use_postgres else "false"
    os.environ["USE_DOCKER_DB"] = "true" if use_docker else "false"

    print(f"Running test with {'PostgreSQL' if use_postgres else 'SQLite'}...")
    print(f"USE_POSTGRES_TESTS={os.environ.get('USE_POSTGRES_TESTS', 'Not set')}")
    print(f"USE_DOCKER_DB={os.environ.get('USE_DOCKER_DB', 'Not set')}")

    # Default test path if none provided
    if test_path is None:
        test_path = "tests/unit/test_project_service.py::TestProjectService::test_project_access_control"

    # Run the test
    cmd = [sys.executable, "-m", "pytest", test_path, "-v"]

    try:
        result = subprocess.run(cmd, check=True)
        print(f"Test completed with exit code: {result.returncode}")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"Test failed with exit code: {e.returncode}")
        return e.returncode


def main():
    parser = argparse.ArgumentParser(
        description="Run DevHQ backend tests with different database configurations"
    )
    parser.add_argument(
        "--postgres", action="store_true", help="Use PostgreSQL instead of SQLite"
    )
    parser.add_argument(
        "--docker",
        action="store_true",
        help="Use Docker PostgreSQL (only applies when --postgres is set)",
    )
    parser.add_argument("--test-path", type=str, help="Specific test path to run")

    args = parser.parse_args()

    return run_test(
        use_postgres=args.postgres, use_docker=args.docker, test_path=args.test_path
    )


if __name__ == "__main__":
    sys.exit(main())
