@echo off
REM Setup test database for DevHQ backend tests

echo Setting up test database for DevHQ backend tests...

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "%~dp0setup_test_db.ps1"

if %ERRORLEVEL% NEQ 0 (
    REM The PowerShell script already displays detailed error messages
    exit /b 1
)

echo.
echo Setup completed successfully!
echo.
echo To run tests with Docker PostgreSQL, use:
echo set USE_POSTGRES_TESTS=true
echo set USE_DOCKER_DB=true
echo pytest
echo.