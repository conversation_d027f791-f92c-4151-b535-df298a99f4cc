# Setup test database for DevHQ backend tests

# Check if Docker is running
$dockerRunning = $false
try {
    $dockerStatus = docker ps 2>&1
    if ($LASTEXITCODE -eq 0) {
        $dockerRunning = $true
    }
} catch {
    $dockerRunning = $false
}

if (-not $dockerRunning) {
    Write-Host "Error: Docker is not running." -ForegroundColor Red
    Write-Host ""
    Write-Host "To run tests with Docker PostgreSQL:" -ForegroundColor Yellow
    Write-Host "1. Start Docker Desktop application" -ForegroundColor Yellow
    Write-Host "2. Wait for Docker to be fully running" -ForegroundColor Yellow
    Write-Host "3. Run this script again" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Alternatively, you can run tests with SQLite:" -ForegroundColor Green
    Write-Host "set USE_POSTGRES_TESTS=false" -ForegroundColor Green
    Write-Host "pytest" -ForegroundColor Green
    Write-Host ""
    exit 1
}

# Check if PostgreSQL container is running
$postgresRunning = $false
try {
    $postgresContainer = docker ps --filter "name=devhq-postgres" --format "{{.Names}}"
    if ($postgresContainer -eq "devhq-postgres" -or $postgresContainer -eq "devhq-postgres-dev") {
        $postgresRunning = $true
    }
} catch {
    $postgresRunning = $false
}

if (-not $postgresRunning) {
    Write-Host "PostgreSQL container is not running. Starting Docker services..." -ForegroundColor Yellow
    
    # Start Docker services using docker-compose.dev.yml
    try {
        Set-Location -Path "$PSScriptRoot\..\.." # Navigate to project root
        docker-compose -f docker-compose.dev.yml up -d postgres
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to start PostgreSQL container." -ForegroundColor Red
            exit 1
        }
        
        Write-Host "PostgreSQL container started successfully." -ForegroundColor Green
    } catch {
        Write-Host "Error: Failed to start PostgreSQL container: $_" -ForegroundColor Red
        exit 1
    }
}

# Install required packages if not already installed
Write-Host "Checking required packages..." -ForegroundColor Cyan
try {
    pip show psycopg2-binary > $null 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Installing psycopg2-binary..." -ForegroundColor Yellow
        pip install psycopg2-binary
    }
} catch {
    Write-Host "Installing psycopg2-binary..." -ForegroundColor Yellow
    pip install psycopg2-binary
}

# Run the setup_test_db.py script
Write-Host "Setting up test database..." -ForegroundColor Cyan
python "$PSScriptRoot\setup_test_db.py"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Test database setup completed successfully." -ForegroundColor Green
    Write-Host "You can now run tests with Docker PostgreSQL using:" -ForegroundColor Cyan
    Write-Host "$env:USE_POSTGRES_TESTS='true'; $env:USE_DOCKER_DB='true'; pytest" -ForegroundColor Yellow
} else {
    Write-Host "Error: Failed to set up test database." -ForegroundColor Red
    exit 1
}