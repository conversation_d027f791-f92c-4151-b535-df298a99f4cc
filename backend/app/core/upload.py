"""
File upload service and validation utilities
Supports Cloudinary integration for image uploads
"""

import io
from dataclasses import dataclass
from typing import Optional, <PERSON><PERSON>

import cloudinary
import cloudinary.uploader
from PIL import Image

from app.config import settings

MAX_AVATAR_SIZE_BYTES = 5 * 1024 * 1024  # 5MB
ALLOWED_IMAGE_TYPES = {"jpeg", "png", "gif", "webp"}


@dataclass
class UploadedImage:
    url: str
    content_type: str
    width: int
    height: int
    size_bytes: int


class UploadService:
    def __init__(self):
        if (
            settings.cloudinary_cloud_name
            and settings.cloudinary_api_key
            and settings.cloudinary_api_secret
        ):
            cloudinary.config(
                cloud_name=settings.cloudinary_cloud_name,
                api_key=settings.cloudinary_api_key,
                api_secret=settings.cloudinary_api_secret,
                secure=True,
            )
        else:
            # Cloudinary not configured; service will simulate upload by returning None
            pass

    def validate_image(self, file_bytes: bytes) -> Tuple[str, int, int]:
        """Validate image content and return (format, width, height)."""
        if len(file_bytes) > MAX_AVATAR_SIZE_BYTES:
            raise ValueError("File too large. Max size is 5MB")

        # Try to open with PIL to validate and get dimensions
        try:
            with Image.open(io.BytesIO(file_bytes)) as img:
                img.verify()  # Verify file integrity
            # Reopen since verify() leaves file closed
            with Image.open(io.BytesIO(file_bytes)) as img2:
                fmt = (img2.format or "").lower()
                width, height = img2.size
        except Exception:
            raise ValueError("Invalid image file")

        if fmt not in ALLOWED_IMAGE_TYPES:
            raise ValueError("Unsupported image type. Allowed: jpeg, png, gif, webp")

        return fmt, width, height

    def upload_avatar(
        self, file_bytes: bytes, public_id: Optional[str] = None
    ) -> Optional[UploadedImage]:
        """Upload avatar to Cloudinary if configured; returns UploadedImage or None if not configured."""
        fmt, width, height = self.validate_image(file_bytes)

        if not (
            settings.cloudinary_cloud_name
            and settings.cloudinary_api_key
            and settings.cloudinary_api_secret
        ):
            # Not configured; return placeholder values to allow local dev
            return UploadedImage(
                url="https://placehold.co/200x200",
                content_type=f"image/{fmt}",
                width=width,
                height=height,
                size_bytes=len(file_bytes),
            )

        # Upload with transformation to limit size and quality
        result = cloudinary.uploader.upload(
            file_bytes,
            public_id=public_id,
            folder="avatars",
            resource_type="image",
            overwrite=True,
            transformation=[
                {"width": 512, "height": 512, "crop": "limit", "quality": "auto"}
            ],
        )

        return UploadedImage(
            url=result["secure_url"],
            content_type=f"image/{fmt}",
            width=result.get("width", width),
            height=result.get("height", height),
            size_bytes=result.get("bytes", len(file_bytes)),
        )


upload_service = UploadService()
