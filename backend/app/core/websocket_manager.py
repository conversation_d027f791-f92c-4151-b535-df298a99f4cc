"""
WebSocket Manager for Real-time Client Portal
Handles bidirectional communication with JW<PERSON> authentication and room isolation
"""

import asyncio
import json
import logging
import uuid
from collections import defaultdict, deque
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set

import socketio
from fastapi import HTTPException
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session

from app.config import settings
from app.database import get_db
from app.models.user import User

logger = logging.getLogger(__name__)

# Create Socket.IO server with optimized settings for scalability
sio = socketio.AsyncServer(
    cors_allowed_origins=settings.allowed_origins or "*",
    logger=False,  # Disable verbose logging in production
    engineio_logger=False,
    async_mode="asgi",
    cors_credentials=True,
    max_http_buffer_size=1024 * 1024,  # 1MB max message size
    ping_timeout=60,  # 60 seconds ping timeout
    ping_interval=25,  # 25 seconds ping interval
    transports=["websocket", "polling"],  # Allow both transports
)


class WebSocketManager:
    """
    Manages WebSocket connections with room-based isolation and JWT authentication
    Optimized for high-concurrency scenarios with efficient memory usage
    """

    def __init__(self, max_connections: int = 10000, activity_timeout: int = 300):
        """
        Initialize WebSocketManager

        Args:
            max_connections (int): Maximum number of concurrent connections
            activity_timeout (int): Timeout in seconds for inactive connections
        """
        self.max_connections = max_connections
        self.activity_timeout = activity_timeout

        # Use more memory-efficient data structures
        self.active_connections: Dict[
            str, Dict
        ] = {}  # sid -> connection info (dict for compatibility)
        self.user_connections: Dict[str, Set[str]] = defaultdict(
            set
        )  # user_id -> set of sids
        self.client_rooms: Dict[str, Set[str]] = defaultdict(
            set
        )  # client_id -> set of sids
        self.project_rooms: Dict[str, Set[str]] = defaultdict(
            set
        )  # project_id -> set of sids

        # Connection statistics with rolling window
        self.connection_stats = {
            "total_connections": 0,
            "peak_connections": 0,
            "rejected_connections": 0,
        }

        # Activity tracking for cleanup
        self.activity_queue = deque()

        # Background cleanup task
        self.cleanup_task: Optional[asyncio.Task] = None

    async def authenticate_token(self, token: str) -> Optional[Dict]:
        """Authenticate JWT token and return user info"""
        try:
            payload = jwt.decode(
                token, settings.secret_key, algorithms=[settings.algorithm]
            )
            user_id: str = payload.get("sub")
            if user_id is None:
                return None
            return {"user_id": user_id, "payload": payload}
        except JWTError:
            return None

    async def connect_user(self, sid: str, user_id: str, user_type: str = "developer"):
        """Register a new user connection with connection limits"""
        # Check connection limits
        if len(self.active_connections) >= self.max_connections:
            logger.warning(f"Connection limit reached, rejecting connection {sid}")
            self.connection_stats["rejected_connections"] += 1
            return False

        # Create connection info (maintain dict structure for compatibility)
        connection_info = {
            "user_id": user_id,
            "user_type": user_type,
            "connected_at": datetime.now(timezone.utc),
            "last_activity": datetime.now(timezone.utc),
            "rooms": set(),  # Track rooms in connection info
        }

        self.active_connections[sid] = connection_info
        self.user_connections[user_id].add(sid)

        # Update statistics
        self.connection_stats["total_connections"] += 1
        if len(self.active_connections) > self.connection_stats["peak_connections"]:
            self.connection_stats["peak_connections"] = len(self.active_connections)

        logger.info(f"User {user_id} connected with session {sid}")

        # Join user to their personal room
        await sio.enter_room(sid, f"user_{user_id}")

        # Add to activity queue for cleanup tracking
        self.activity_queue.append((sid, datetime.now(timezone.utc)))

        # Emit connection success
        await sio.emit(
            "connection_established",
            {
                "status": "connected",
                "user_id": user_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
            room=sid,
        )

        return True

    async def disconnect_user(self, sid: str):
        """Handle user disconnection with resource cleanup"""
        if sid not in self.active_connections:
            return

        connection_info = self.active_connections[sid]
        user_id = connection_info["user_id"]

        # Remove from user connections
        self.user_connections[user_id].discard(sid)
        if not self.user_connections[user_id]:
            del self.user_connections[user_id]

        # Remove from all rooms
        await self._leave_all_rooms(sid, connection_info)

        # Remove connection
        del self.active_connections[sid]

        logger.info(f"User {user_id} disconnected from session {sid}")

    async def join_client_room(self, sid: str, client_id: str):
        """Join a client-specific room for project updates"""
        if sid not in self.active_connections:
            return False

        connection_info = self.active_connections[sid]
        room_name = f"client_{client_id}"
        await sio.enter_room(sid, room_name)

        self.client_rooms[client_id].add(sid)

        # Track room in connection info
        if "rooms" not in connection_info:
            connection_info["rooms"] = set()
        connection_info["rooms"].add(room_name)

        logger.info(f"Session {sid} joined client room {client_id}")
        return True

    async def join_project_room(self, sid: str, project_id: str):
        """Join a project-specific room for real-time updates"""
        if sid not in self.active_connections:
            return False

        connection_info = self.active_connections[sid]
        room_name = f"project_{project_id}"
        await sio.enter_room(sid, room_name)

        self.project_rooms[project_id].add(sid)

        # Track room in connection info
        if "rooms" not in connection_info:
            connection_info["rooms"] = set()
        connection_info["rooms"].add(room_name)

        logger.info(f"Session {sid} joined project room {project_id}")
        return True

    async def leave_client_room(self, sid: str, client_id: str):
        """Leave a client room"""
        if sid not in self.active_connections:
            return

        connection_info = self.active_connections[sid]
        room_name = f"client_{client_id}"
        await sio.leave_room(sid, room_name)

        self.client_rooms[client_id].discard(sid)
        if not self.client_rooms[client_id]:
            del self.client_rooms[client_id]

        # Remove from connection info tracking
        if "rooms" in connection_info:
            connection_info["rooms"].discard(room_name)

    async def leave_project_room(self, sid: str, project_id: str):
        """Leave a project room"""
        if sid not in self.active_connections:
            return

        connection_info = self.active_connections[sid]
        room_name = f"project_{project_id}"
        await sio.leave_room(sid, room_name)

        self.project_rooms[project_id].discard(sid)
        if not self.project_rooms[project_id]:
            del self.project_rooms[project_id]

        # Remove from connection info tracking
        if "rooms" in connection_info:
            connection_info["rooms"].discard(room_name)

    async def _leave_all_rooms(self, sid: str, connection_info: Dict):
        """Leave all rooms for a session efficiently"""
        # Leave rooms tracked in connection info
        if "rooms" in connection_info:
            for room_name in list(connection_info["rooms"]):
                await sio.leave_room(sid, room_name)

                # Remove from room tracking
                if room_name.startswith("client_"):
                    client_id = room_name.split("_", 1)[1]
                    self.client_rooms[client_id].discard(sid)
                    if not self.client_rooms[client_id]:
                        del self.client_rooms[client_id]
                elif room_name.startswith("project_"):
                    project_id = room_name.split("_", 1)[1]
                    self.project_rooms[project_id].discard(sid)
                    if not self.project_rooms[project_id]:
                        del self.project_rooms[project_id]

            # Clear connection rooms
            connection_info["rooms"].clear()

    async def broadcast_to_client(self, client_id: str, event: str, data: Dict):
        """Broadcast event to all connections in a client room"""
        if client_id not in self.client_rooms or not self.client_rooms[client_id]:
            return

        room_name = f"client_{client_id}"
        await sio.emit(event, data, room=room_name)
        logger.debug(f"Broadcasted {event} to client room {client_id}")

    async def broadcast_to_project(self, project_id: str, event: str, data: Dict):
        """Broadcast event to all connections in a project room"""
        if project_id not in self.project_rooms or not self.project_rooms[project_id]:
            return

        room_name = f"project_{project_id}"
        await sio.emit(event, data, room=room_name)
        logger.debug(f"Broadcasted {event} to project room {project_id}")

    async def send_to_user(self, user_id: str, event: str, data: Dict):
        """Send event to all connections of a specific user"""
        if user_id not in self.user_connections or not self.user_connections[user_id]:
            return

        room_name = f"user_{user_id}"
        await sio.emit(event, data, room=room_name)
        logger.debug(f"Sent {event} to user {user_id}")

    async def broadcast_to_developers(self, event: str, data: Dict):
        """Broadcast to all connected developers"""
        developer_sids = [
            sid
            for sid, info in self.active_connections.items()
            if info.get("user_type") == "developer"
        ]

        # Batch emissions for better performance
        batch_size = 100
        for i in range(0, len(developer_sids), batch_size):
            batch = developer_sids[i : i + batch_size]
            tasks = [sio.emit(event, data, room=sid) for sid in batch]
            await asyncio.gather(*tasks, return_exceptions=True)

    def get_connection_stats(self) -> Dict:
        """Get current connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "unique_users": len(self.user_connections),
            "active_client_rooms": len(self.client_rooms),
            "active_project_rooms": len(self.project_rooms),
            "connections_by_type": {
                "developer": len(
                    [
                        info
                        for info in self.active_connections.values()
                        if info.get("user_type") == "developer"
                    ]
                ),
                "client": len(
                    [
                        info
                        for info in self.active_connections.values()
                        if info.get("user_type") == "client"
                    ]
                ),
            },
            "manager_stats": self.connection_stats.copy(),
        }

    async def update_activity(self, sid: str):
        """Update last activity timestamp for a connection"""
        if sid in self.active_connections:
            self.active_connections[sid]["last_activity"] = datetime.now(timezone.utc)

    async def start_cleanup_task(self):
        """Start background cleanup task for inactive connections"""
        if self.cleanup_task and not self.cleanup_task.done():
            return

        self.cleanup_task = asyncio.create_task(self._cleanup_inactive_connections())
        logger.info("WebSocket cleanup task started")

    async def stop_cleanup_task(self):
        """Stop background cleanup task"""
        if self.cleanup_task and not self.cleanup_task.done():
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            logger.info("WebSocket cleanup task stopped")

    async def _cleanup_inactive_connections(self):
        """Background task to clean up inactive connections"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute

                if not self.activity_timeout:
                    continue

                cutoff_time = datetime.now(timezone.utc) - timedelta(
                    seconds=self.activity_timeout
                )
                inactive_sids = [
                    sid
                    for sid, info in self.active_connections.items()
                    if info.get(
                        "last_activity", datetime.min.replace(tzinfo=timezone.utc)
                    )
                    < cutoff_time
                ]

                for sid in inactive_sids:
                    logger.info(f"Disconnecting inactive connection {sid}")
                    await sio.disconnect(sid)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")


# Global WebSocket manager instance
websocket_manager = WebSocketManager()


# Socket.IO Event Handlers
@sio.event
async def connect(sid, environ, auth):
    """Handle new WebSocket connections"""
    try:
        # Extract token from auth
        token = auth.get("token") if auth else None
        if not token:
            logger.warning(f"Connection {sid} rejected: No token provided")
            await sio.disconnect(sid)
            return False

        # Authenticate token
        auth_info = await websocket_manager.authenticate_token(token)
        if not auth_info:
            logger.warning(f"Connection {sid} rejected: Invalid token")
            await sio.disconnect(sid)
            return False

        # Connect user
        user_id = auth_info["user_id"]
        user_type = auth_info["payload"].get("user_type", "developer")

        success = await websocket_manager.connect_user(sid, user_id, user_type)
        if not success:
            await sio.disconnect(sid)
            return False

        logger.info(f"WebSocket connection established for user {user_id}")
        return True

    except Exception as e:
        logger.error(f"Error in WebSocket connect: {e}")
        await sio.disconnect(sid)
        return False


@sio.event
async def disconnect(sid):
    """Handle WebSocket disconnections"""
    try:
        await websocket_manager.disconnect_user(sid)
        logger.info(f"WebSocket disconnection handled for session {sid}")
    except Exception as e:
        logger.error(f"Error in WebSocket disconnect: {e}")


@sio.event
async def join_room(sid, data):
    """Handle room join requests"""
    try:
        await websocket_manager.update_activity(sid)

        room_type = data.get("room_type")
        room_id = data.get("room_id")

        if not room_type or not room_id:
            await sio.emit("error", {"message": "Invalid room join request"}, room=sid)
            return

        success = False
        if room_type == "client":
            success = await websocket_manager.join_client_room(sid, room_id)
        elif room_type == "project":
            success = await websocket_manager.join_project_room(sid, room_id)

        if success:
            await sio.emit(
                "room_joined",
                {
                    "room_type": room_type,
                    "room_id": room_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
                room=sid,
            )
        else:
            await sio.emit("error", {"message": "Failed to join room"}, room=sid)

    except Exception as e:
        logger.error(f"Error in join_room: {e}")
        await sio.emit("error", {"message": "Internal error"}, room=sid)


@sio.event
async def leave_room(sid, data):
    """Handle room leave requests"""
    try:
        await websocket_manager.update_activity(sid)

        room_type = data.get("room_type")
        room_id = data.get("room_id")

        if room_type == "client":
            await websocket_manager.leave_client_room(sid, room_id)
        elif room_type == "project":
            await websocket_manager.leave_project_room(sid, room_id)

        await sio.emit(
            "room_left",
            {
                "room_type": room_type,
                "room_id": room_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
            room=sid,
        )

    except Exception as e:
        logger.error(f"Error in leave_room: {e}")


@sio.event
async def ping(sid, data):
    """Handle ping for connection health check"""
    await websocket_manager.update_activity(sid)
    await sio.emit(
        "pong", {"timestamp": datetime.now(timezone.utc).isoformat()}, room=sid
    )


# Create ASGI app for Socket.IO
socket_app = socketio.ASGIApp(sio, other_asgi_app=None)
