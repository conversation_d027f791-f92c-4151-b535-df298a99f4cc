"""
Phone number validation and normalization service
Uses Google's libphonenumber for accurate validation
"""

import phonenumbers
from phonenumbers import NumberParseException, PhoneNumberFormat
from typing import Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class PhoneValidator:
    """Phone number validation and normalization service"""
    
    @staticmethod
    def validate_and_format(phone_number: str, default_region: str = "US") -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Validate and format a phone number
        
        Args:
            phone_number: Raw phone number string
            default_region: Default country code if not specified
            
        Returns:
            Tuple of (is_valid, formatted_e164, error_message)
        """
        if not phone_number or not phone_number.strip():
            return True, None, None  # Empty phone is valid (optional field)
        
        try:
            # Add + if missing and number looks international
            if phone_number and not phone_number.startswith('+') and len(phone_number.replace(' ', '').replace('-', '')) > 10:
                phone_number = '+' + phone_number
            
            # Parse the phone number
            parsed_number = phonenumbers.parse(phone_number, default_region)
            
            # Check if the number is valid
            if not phonenumbers.is_valid_number(parsed_number):
                return False, None, "Invalid phone number format"
            
            # Format to E.164 (international standard)
            formatted_e164 = phonenumbers.format_number(parsed_number, PhoneNumberFormat.E164)
            
            return True, formatted_e164, None
            
        except NumberParseException as e:
            error_messages = {
                NumberParseException.INVALID_COUNTRY_CODE: "Invalid country code",
                NumberParseException.NOT_A_NUMBER: "Not a valid phone number",
                NumberParseException.TOO_SHORT_NSN: "Phone number too short",
                NumberParseException.TOO_SHORT_AFTER_IDD: "Phone number too short after country code",
                NumberParseException.TOO_LONG: "Phone number too long"
            }
            
            error_msg = error_messages.get(e.error_type, "Invalid phone number")
            logger.warning(f"Phone validation failed: {error_msg} for number: {phone_number}")
            
            return False, None, error_msg
        
        except Exception as e:
            logger.error(f"Unexpected error validating phone number {phone_number}: {e}")
            return False, None, "Phone number validation failed"
    
    @staticmethod
    def get_country_code(phone_number: str) -> Optional[str]:
        """
        Extract country code from phone number
        
        Args:
            phone_number: E.164 formatted phone number
            
        Returns:
            Country code (e.g., 'US', 'NG', 'KE') or None
        """
        try:
            parsed_number = phonenumbers.parse(phone_number, None)
            return phonenumbers.region_code_for_number(parsed_number)
        except:
            return None
    
    @staticmethod
    def format_for_display(phone_number: str, format_type: str = "international") -> Optional[str]:
        """
        Format phone number for display
        
        Args:
            phone_number: E.164 formatted phone number
            format_type: 'international', 'national', or 'e164'
            
        Returns:
            Formatted phone number or None if invalid
        """
        try:
            parsed_number = phonenumbers.parse(phone_number, None)
            
            format_map = {
                "international": PhoneNumberFormat.INTERNATIONAL,
                "national": PhoneNumberFormat.NATIONAL,
                "e164": PhoneNumberFormat.E164
            }
            
            phone_format = format_map.get(format_type, PhoneNumberFormat.INTERNATIONAL)
            return phonenumbers.format_number(parsed_number, phone_format)
            
        except:
            return phone_number  # Return original if formatting fails
    
    @staticmethod
    def is_mobile_number(phone_number: str) -> bool:
        """
        Check if phone number is a mobile number
        
        Args:
            phone_number: E.164 formatted phone number
            
        Returns:
            True if mobile, False otherwise
        """
        try:
            parsed_number = phonenumbers.parse(phone_number, None)
            number_type = phonenumbers.number_type(parsed_number)
            
            mobile_types = [
                phonenumbers.PhoneNumberType.MOBILE,
                phonenumbers.PhoneNumberType.FIXED_LINE_OR_MOBILE
            ]
            
            return number_type in mobile_types
            
        except:
            return False
    
    @staticmethod
    def get_carrier_info(phone_number: str) -> Optional[str]:
        """
        Get carrier information for phone number (if available)
        
        Args:
            phone_number: E.164 formatted phone number
            
        Returns:
            Carrier name or None
        """
        try:
            from phonenumbers import carrier
            parsed_number = phonenumbers.parse(phone_number, None)
            return carrier.name_for_number(parsed_number, "en")
        except:
            return None


# Convenience functions
def validate_phone(phone_number: str, default_region: str = "US") -> Tuple[bool, Optional[str], Optional[str]]:
    """Convenience function for phone validation"""
    return PhoneValidator.validate_and_format(phone_number, default_region)


def normalize_phone(phone_number: str, default_region: str = "US") -> Optional[str]:
    """
    Normalize phone number to E.164 format
    
    Returns:
        E.164 formatted phone number or None if invalid
    """
    is_valid, formatted, _ = PhoneValidator.validate_and_format(phone_number, default_region)
    return formatted if is_valid else None