"""
Authentication utilities for JWT token management with enhanced security
"""

import secrets
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional

from jose import jwt
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.config import settings
from app.core.security_service import SecurityService
from app.core.transaction_manager import db_transaction
from app.models.user import User, UserSession


def create_access_token(data: Dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token with enhanced security
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.access_token_expire_minutes
        )

    to_encode.update(
        {"exp": expire, "type": "access", "iat": datetime.now(timezone.utc)}
    )

    # Add security claims
    to_encode.update(
        {
            "jti": secrets.token_urlsafe(32),  # JWT ID for tracking
            "aud": "devhq-api",  # Audience
            "iss": "devhq-auth-service",  # Issuer
        }
    )

    encoded_jwt = jwt.encode(
        to_encode, settings.secret_key, algorithm=settings.algorithm
    )

    return encoded_jwt


def create_refresh_token(
    user_id: str, db: Session, device_info: Optional[str] = None
) -> str:
    """
    Create JWT refresh token with enhanced security and store session in database
    """
    import uuid

    with db_transaction(db) as tx_db:
        # Generate secure random token using enhanced security service
        security_service = SecurityService(tx_db)
        refresh_token = security_service.create_secure_token(
            48
        )  # Longer token for refresh

        # Calculate expiration
        expires_at = datetime.now(timezone.utc) + timedelta(
            days=settings.refresh_token_expire_days
        )

        # Create session record with additional security
        session = UserSession(
            user_id=uuid.UUID(user_id),
            refresh_token=refresh_token,
            expires_at=expires_at,
            device_info=device_info,
            is_active=True,
        )

        tx_db.add(session)
        tx_db.flush()
        tx_db.refresh(session)

        return refresh_token


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password with enhanced security using bcrypt
    """
    security_service = SecurityService(None)  # No DB needed for password verification
    return security_service.verify_password(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Hash password with enhanced security using bcrypt
    """
    security_service = SecurityService(None)  # No DB needed for password hashing
    return security_service.hash_password(password)


def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """
    Authenticate user with enhanced security including account lockout
    """
    security_service = SecurityService(db)

    # Check if account is locked
    if security_service.is_account_locked(email):
        raise Exception("Account temporarily locked due to multiple failed attempts")

    # Get user from database
    stmt = select(User).where(User.email == email)
    result = db.execute(stmt)
    user = result.scalar_one_or_none()

    if not user or not user.password_hash:
        # Record failed attempt even for non-existent users to prevent enumeration
        security_service.record_failed_login(email)
        return None

    # Verify password
    if not verify_password(password, user.password_hash):
        # Record failed attempt
        if security_service.record_failed_login(email):
            raise Exception(
                "Account temporarily locked due to multiple failed attempts"
            )
        return None

    # Reset failed attempts on successful login
    security_service.reset_failed_login_attempts(email)

    # Check if user is active
    if not user.is_active:
        return None

    return user


def validate_password_strength(password: str) -> Dict:
    """
    Validate password strength using enhanced security service
    """
    security_service = SecurityService(None)  # No DB needed
    return security_service.validate_password_strength(password)


def verify_refresh_token(refresh_token: str, db: Session) -> Optional[User]:
    """
    Verify refresh token with enhanced security checks and return associated user
    """
    # Get session with enhanced security checks
    stmt = select(UserSession).where(
        UserSession.refresh_token == refresh_token,
        UserSession.is_active == True,
        UserSession.expires_at > datetime.now(timezone.utc),  # Double-check expiration
    )
    result = db.execute(stmt)
    session = result.scalar_one_or_none()

    if not session or session.is_expired:
        return None

    # Get user with additional security checks
    stmt = select(User).where(
        User.id == session.user_id,
        User.is_active == True,
        User.deleted_at.is_(None),
    )
    result = db.execute(stmt)
    user = result.scalar_one_or_none()

    # Additional security: Check if user has been deactivated since token was issued
    if user and not user.is_active:
        # Revoke session if user is no longer active
        session.revoke()
        db.commit()
        return None

    return user


async def revoke_refresh_token(refresh_token: str, db: AsyncSession) -> bool:
    """
    Revoke a specific refresh token
    Returns True if token was found and revoked, False if token was not found
    """
    try:
        # Look for the token regardless of expiration status
        stmt = select(UserSession).where(UserSession.refresh_token == refresh_token)
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()

        if session:
            # Revoke the session if it's not already revoked
            if session.is_active:
                session.revoke()
                await db.commit()
            return True

        # Token not found - could be already deleted or never existed
        return False
    except Exception as e:
        await db.rollback()
        raise e


def revoke_all_user_sessions(user_id: str, db: Session) -> int:
    """
    Revoke all active sessions for a user
    Returns number of sessions revoked
    """
    import uuid

    with db_transaction(db) as tx_db:
        stmt = select(UserSession).where(
            UserSession.user_id == uuid.UUID(user_id),
            UserSession.is_active == True
        )
        result = tx_db.execute(stmt)
        sessions = result.scalars().all()

        count = 0
        for session in sessions:
            session.revoke()
            count += 1

        return count


def create_verification_token(user_id: str) -> str:
    """
    Create email verification token
    """
    data = {
        "sub": user_id,
        "type": "email_verification",
        "exp": datetime.now(timezone.utc) + timedelta(hours=24),  # 24 hour expiry
    }

    return jwt.encode(data, settings.secret_key, algorithm=settings.algorithm)


def create_password_reset_token(user_id: str) -> str:
    """
    Create password reset token
    """
    data = {
        "sub": user_id,
        "type": "password_reset",
        "exp": datetime.now(timezone.utc) + timedelta(hours=1),  # 1 hour expiry
    }

    return jwt.encode(data, settings.secret_key, algorithm=settings.algorithm)


def verify_token(token: str, token_type: str) -> Optional[str]:
    """
    Verify a token and return user_id if valid
    """
    try:
        payload = jwt.decode(
            token, settings.secret_key, algorithms=[settings.algorithm]
        )

        # Check token type
        if payload.get("type") != token_type:
            return None

        # Get user ID
        user_id = payload.get("sub")
        return user_id

    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None
