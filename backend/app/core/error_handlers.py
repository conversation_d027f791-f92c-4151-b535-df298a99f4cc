"""Standardized error handling for DevHQ API"""

import logging
import traceback
import uuid
from typing import Any, Dict, List, Optional, Union

from fastapi import HTT<PERSON><PERSON>xception, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

logger = logging.getLogger(__name__)


class DevHQException(Exception):
    """Base exception for DevHQ application"""

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(DevHQException):
    """Validation error exception"""

    def __init__(self, message: str, errors: List[str] = None):
        super().__init__(message, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.errors = errors or []


class NotFoundError(DevHQException):
    """Resource not found exception"""

    def __init__(self, resource: str, identifier: str = None):
        message = f"{resource} not found"
        if identifier:
            message += f" with identifier: {identifier}"
        super().__init__(message, status.HTTP_404_NOT_FOUND)


class ConflictError(DevHQException):
    """Resource conflict exception"""

    def __init__(self, resource: str, field: str = None, value: str = None):
        message = f"{resource} already exists"
        if field and value:
            message += f" with {field}: {value}"
        super().__init__(message, status.HTTP_409_CONFLICT)


class AuthenticationError(DevHQException):
    """Authentication error exception"""

    def __init__(self, message: str = "Authentication required"):
        super().__init__(message, status.HTTP_401_UNAUTHORIZED)


class AuthorizationError(DevHQException):
    """Authorization error exception"""

    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(message, status.HTTP_403_FORBIDDEN)


class RateLimitError(DevHQException):
    """Rate limit exceeded exception"""

    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, status.HTTP_429_TOO_MANY_REQUESTS)


class BusinessLogicError(DevHQException):
    """Business logic error exception"""

    def __init__(self, message: str):
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


def create_error_response(
    detail: str,
    status_code: int = 500,
    errors: Optional[List[Dict[str, Any]]] = None,
    error_code: Optional[str] = None,
    request_id: Optional[str] = None,
) -> Dict[str, Any]:
    """Create standardized error response matching ErrorResponse schema"""
    response = {
        "detail": detail,
    }

    if errors:
        response["errors"] = errors

    if error_code:
        response["error_code"] = error_code

    if request_id:
        response["request_id"] = request_id

    return response


def generate_request_id() -> str:
    """Generate a unique request ID for error tracking"""
    return f"req_{uuid.uuid4().hex[:12]}"


def format_validation_error(error: Dict[str, Any]) -> Dict[str, Any]:
    """Format a single validation error into user-friendly format"""
    field_path = " -> ".join(str(loc) for loc in error["loc"] if loc != "body")
    error_type = error["type"]

    # Create user-friendly error messages
    user_friendly_messages = {
        "missing": "This field is required",
        "string_too_short": "This field is too short",
        "string_too_long": "This field is too long",
        "value_error.email": "Please enter a valid email address",
        "value_error.url": "Please enter a valid URL",
        "type_error.integer": "This field must be a number",
        "type_error.float": "This field must be a decimal number",
        "type_error.bool": "This field must be true or false",
        "value_error.datetime": "Please enter a valid date and time",
        "value_error.date": "Please enter a valid date",
        "value_error.time": "Please enter a valid time",
        "value_error.uuid": "Please enter a valid UUID",
        "value_error.json": "Please enter valid JSON",
    }

    # Get user-friendly message or fall back to original
    message = user_friendly_messages.get(error_type, error["msg"])

    # Generate error codes based on type
    error_codes = {
        "missing": "REQUIRED_FIELD",
        "string_too_short": "FIELD_TOO_SHORT",
        "string_too_long": "FIELD_TOO_LONG",
        "value_error.email": "INVALID_EMAIL",
        "value_error.url": "INVALID_URL",
        "type_error.integer": "INVALID_INTEGER",
        "type_error.float": "INVALID_FLOAT",
        "type_error.bool": "INVALID_BOOLEAN",
        "value_error.datetime": "INVALID_DATETIME",
        "value_error.date": "INVALID_DATE",
        "value_error.time": "INVALID_TIME",
        "value_error.uuid": "INVALID_UUID",
        "value_error.json": "INVALID_JSON",
    }

    return {
        "field": field_path if field_path else None,
        "message": message,
        "code": error_codes.get(error_type, "VALIDATION_ERROR"),
    }


async def devhq_exception_handler(
    request: Request, exc: DevHQException
) -> JSONResponse:
    """Handle DevHQ custom exceptions"""
    request_id = generate_request_id()

    # Map exception types to error codes
    error_code_mapping = {
        ValidationException: "VALIDATION_ERROR",
        NotFoundError: "NOT_FOUND",
        ConflictError: "RESOURCE_CONFLICT",
        AuthenticationError: "INVALID_CREDENTIALS",
        AuthorizationError: "ACCESS_DENIED",
        RateLimitError: "RATE_LIMITED",
        BusinessLogicError: "BUSINESS_LOGIC_ERROR",
    }

    error_code = error_code_mapping.get(type(exc), "INTERNAL_ERROR")

    logger.warning(
        f"DevHQ Exception: {exc.message}",
        extra={
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
            "request_id": request_id,
            "error_code": error_code,
        },
    )

    # Handle validation exceptions with detailed errors
    errors = None
    if isinstance(exc, ValidationException) and hasattr(exc, "errors") and exc.errors:
        errors = [
            {"field": None, "message": error, "code": "VALIDATION_ERROR"}
            for error in exc.errors
        ]

    return JSONResponse(
        status_code=exc.status_code,
        content=create_error_response(
            detail=exc.message,
            errors=errors,
            error_code=error_code,
            request_id=request_id,
        ),
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""
    request_id = generate_request_id()

    # Map HTTP status codes to error codes
    status_code_mapping = {
        400: "BAD_REQUEST",
        401: "UNAUTHORIZED",
        403: "FORBIDDEN",
        404: "NOT_FOUND",
        405: "METHOD_NOT_ALLOWED",
        409: "CONFLICT",
        422: "VALIDATION_ERROR",
        429: "RATE_LIMITED",
        500: "INTERNAL_ERROR",
        502: "BAD_GATEWAY",
        503: "SERVICE_UNAVAILABLE",
        504: "GATEWAY_TIMEOUT",
    }

    error_code = status_code_mapping.get(exc.status_code, "HTTP_ERROR")

    logger.warning(
        f"HTTP Exception: {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "path": request.url.path,
            "request_id": request_id,
            "error_code": error_code,
        },
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=create_error_response(
            detail=str(exc.detail), error_code=error_code, request_id=request_id
        ),
        headers=exc.headers,
    )


async def validation_exception_handler(
    request: Request, exc: RequestValidationError
) -> JSONResponse:
    """Handle Pydantic validation errors with user-friendly messages"""
    request_id = generate_request_id()

    # Format errors using the new user-friendly format
    formatted_errors = [format_validation_error(error) for error in exc.errors()]

    logger.warning(
        f"Validation Error: {len(formatted_errors)} validation errors",
        extra={
            "path": request.url.path,
            "errors": formatted_errors,
            "request_id": request_id,
        },
    )

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=create_error_response(
            detail="Validation failed. Please check the provided data and try again.",
            errors=formatted_errors,
            error_code="VALIDATION_ERROR",
            request_id=request_id,
        ),
    )


async def sqlalchemy_exception_handler(
    request: Request, exc: SQLAlchemyError
) -> JSONResponse:
    """Handle SQLAlchemy database errors with user-friendly messages"""
    request_id = generate_request_id()

    logger.error(
        f"Database Error: {str(exc)}",
        extra={
            "path": request.url.path,
            "exception_type": type(exc).__name__,
            "request_id": request_id,
        },
    )

    # Handle specific SQLAlchemy errors with user-friendly messages
    if isinstance(exc, IntegrityError):
        # Parse common integrity constraint violations
        exc_str = str(exc).lower()
        if "unique constraint" in exc_str or "duplicate key" in exc_str:
            detail = (
                "This record already exists. Please check for duplicates and try again."
            )
            error_code = "DUPLICATE_RECORD"
        elif "foreign key constraint" in exc_str:
            detail = "Referenced record does not exist. Please verify the related data."
            error_code = "INVALID_REFERENCE"
        elif "not null constraint" in exc_str:
            detail = (
                "Required field is missing. Please provide all necessary information."
            )
            error_code = "MISSING_REQUIRED_FIELD"
        else:
            detail = "Data integrity constraint violation. Please check your data and try again."
            error_code = "DATA_INTEGRITY_ERROR"

        status_code = status.HTTP_409_CONFLICT
    else:
        detail = "A database error occurred. Please try again later."
        error_code = "DATABASE_ERROR"
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

    return JSONResponse(
        status_code=status_code,
        content=create_error_response(
            detail=detail, error_code=error_code, request_id=request_id
        ),
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions with proper logging and user-friendly messages"""
    request_id = generate_request_id()

    logger.error(
        f"Unexpected Error: {str(exc)}",
        extra={
            "path": request.url.path,
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc(),
            "request_id": request_id,
        },
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=create_error_response(
            detail="An unexpected error occurred. Our team has been notified and will investigate.",
            error_code="INTERNAL_ERROR",
            request_id=request_id,
        ),
    )


def register_error_handlers(app):
    """Register all error handlers with the FastAPI app"""
    app.add_exception_handler(DevHQException, devhq_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
