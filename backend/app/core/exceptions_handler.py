"""
Standardized exception handling for DevHQ Backend
Provides consistent exception handling across all routers
"""

from typing import Any, Dict, Optional

from fastapi import HTT<PERSON>Exception, Request, status
from fastapi.responses import JSONResponse

from app.core.exceptions import (AuthenticationError, AuthorizationError,
                                 ConflictError, DevHQException, NotFoundError,
                                 ValidationError)


async def devhq_exception_handler(
    request: Request, exc: DevHQException
) -> JSONResponse:
    """Handle custom DevHQ exceptions"""
    # Determine appropriate status code based on exception type
    status_code = status.HTTP_400_BAD_REQUEST
    if isinstance(exc, AuthenticationError):
        status_code = status.HTTP_401_UNAUTHORIZED
    elif isinstance(exc, AuthorizationError):
        status_code = status.HTTP_403_FORBIDDEN
    elif isinstance(exc, ValidationError):
        status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    elif isinstance(exc, NotFoundError):
        status_code = status.HTTP_404_NOT_FOUND
    elif isinstance(exc, ConflictError):
        status_code = status.HTTP_409_CONFLICT

    return JSONResponse(
        status_code=status_code,
        content={
            "detail": exc.message,  # Standard FastAPI format for backward compatibility
            "error": exc.message,  # Custom format for enhanced error handling
            "details": exc.details,
            "type": exc.__class__.__name__,
        },
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions with consistent format"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,  # Standard FastAPI format for backward compatibility
            "error": exc.detail,  # Custom format for enhanced error handling
            "status_code": exc.status_code,
        },
    )


async def validation_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """Handle validation errors with consistent format"""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Validation error",
            "details": str(exc),
        },
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions with consistent format"""
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal server error",
            "details": "An unexpected error occurred",
        },
    )
