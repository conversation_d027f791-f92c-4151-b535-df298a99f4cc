"""
Pagination utility for DevHQ Backend
Provides efficient pagination for database queries
"""

from typing import List, Optional, TypeVar

from sqlalchemy import func
from sqlalchemy.orm import Query, Session

T = TypeVar("T")


class PaginationResult:
    """Result of a paginated query"""

    def __init__(self, items: List[T], total: int, page: int, per_page: int):
        self.items = items
        self.total = total
        self.page = page
        self.per_page = per_page
        self.pages = (total + per_page - 1) // per_page if per_page > 0 else 0

    def to_dict(self):
        """Convert to dictionary format"""
        return {
            "items": self.items,
            "total": self.total,
            "page": self.page,
            "per_page": self.per_page,
            "pages": self.pages,
        }


def paginate_query(
    query: Query, page: int = 1, per_page: int = 25, use_count_query: bool = True
) -> PaginationResult:
    """
    Paginate a SQLAlchemy query efficiently

    Args:
        query: SQLAlchemy query object
        page: Page number (1-indexed)
        per_page: Number of items per page
        use_count_query: Whether to use a separate count query (more efficient for large datasets)

    Returns:
        PaginationResult with items and pagination metadata
    """
    # Validate parameters
    if page < 1:
        page = 1
    if per_page < 1:
        per_page = 25
    if per_page > 100:
        per_page = 100  # Cap at 100 items per page

    # Calculate offset
    offset = (page - 1) * per_page

    # Get total count efficiently
    if use_count_query:
        # Use a separate count query which can be more efficient
        total = query.count()
    else:
        # Count all results (less efficient for large datasets)
        total = query.order_by(None).count()

    # Apply pagination
    items = query.offset(offset).limit(per_page).all()

    return PaginationResult(items, total, page, per_page)


def paginate_with_subquery_count(
    query: Query, page: int = 1, per_page: int = 25
) -> PaginationResult:
    """
    Paginate a query using subquery counting for better performance on large datasets

    Args:
        query: SQLAlchemy query object
        page: Page number (1-indexed)
        per_page: Number of items per page

    Returns:
        PaginationResult with items and pagination metadata
    """
    # Validate parameters
    if page < 1:
        page = 1
    if per_page < 1:
        per_page = 25
    if per_page > 100:
        per_page = 100  # Cap at 100 items per page

    # Calculate offset
    offset = (page - 1) * per_page

    # Use subquery for counting (more efficient for complex queries)
    count_query = query.session.query(func.count("*")).select_from(query.subquery())
    total = count_query.scalar()

    # Apply pagination
    items = query.offset(offset).limit(per_page).all()

    return PaginationResult(items, total, page, per_page)
