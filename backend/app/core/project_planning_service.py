"""
Project Planning Service with templates and intelligent recommendations
"""

from decimal import Decimal
from typing import Dict, List, Optional

from app.schemas.project import MilestoneTemplate, ProjectTemplate


class ProjectPlanningService:
    """Service for intelligent project planning and budgeting"""

    def __init__(self):
        self.templates = self._load_project_templates()

    def _load_project_templates(self) -> Dict[str, ProjectTemplate]:
        """Load predefined project templates"""
        templates = {
            "web_app": ProjectTemplate(
                name="Web Application",
                description="Full-stack web application with modern framework",
                typical_duration_weeks=12,
                recommended_hourly_rate_min=Decimal("75"),
                recommended_hourly_rate_max=Decimal("150"),
                milestones=[
                    MilestoneTemplate(
                        sequence_number=1,
                        title="Discovery & Planning",
                        description="Requirements gathering, technical architecture, and project setup",
                        estimated_hours=40.0,
                        payment_percentage=20.0,
                        typical_deliverables=[
                            "Technical specification document",
                            "Database schema design",
                            "API documentation outline",
                            "Project timeline and milestones",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=2,
                        title="Backend Development",
                        description="API development, database setup, and core business logic",
                        estimated_hours=80.0,
                        payment_percentage=35.0,
                        typical_deliverables=[
                            "RESTful API endpoints",
                            "Database models and migrations",
                            "Authentication system",
                            "Core business logic implementation",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=3,
                        title="Frontend Development",
                        description="User interface development and API integration",
                        estimated_hours=70.0,
                        payment_percentage=30.0,
                        typical_deliverables=[
                            "Responsive user interface",
                            "API integration",
                            "User authentication flows",
                            "Core user features",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=4,
                        title="Testing & Deployment",
                        description="Quality assurance, testing, and production deployment",
                        estimated_hours=30.0,
                        payment_percentage=15.0,
                        typical_deliverables=[
                            "Comprehensive testing suite",
                            "Production deployment",
                            "Documentation and handover",
                            "Performance optimization",
                        ],
                    ),
                ],
            ),
            "mobile_app": ProjectTemplate(
                name="Mobile Application",
                description="Native or cross-platform mobile application",
                typical_duration_weeks=16,
                recommended_hourly_rate_min=Decimal("80"),
                recommended_hourly_rate_max=Decimal("160"),
                milestones=[
                    MilestoneTemplate(
                        sequence_number=1,
                        title="Design & Architecture",
                        description="UI/UX design, technical architecture, and project setup",
                        estimated_hours=50.0,
                        payment_percentage=25.0,
                        typical_deliverables=[
                            "UI/UX design mockups",
                            "Technical architecture document",
                            "Development environment setup",
                            "Project structure and dependencies",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=2,
                        title="Core Features Development",
                        description="Implementation of core app functionality",
                        estimated_hours=100.0,
                        payment_percentage=40.0,
                        typical_deliverables=[
                            "Core app navigation",
                            "Main feature implementations",
                            "Data management and storage",
                            "API integration",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=3,
                        title="Advanced Features & Polish",
                        description="Advanced features, animations, and user experience polish",
                        estimated_hours=60.0,
                        payment_percentage=25.0,
                        typical_deliverables=[
                            "Advanced feature implementations",
                            "Animations and transitions",
                            "Performance optimizations",
                            "User experience enhancements",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=4,
                        title="Testing & App Store Release",
                        description="Testing, app store preparation, and release",
                        estimated_hours=30.0,
                        payment_percentage=10.0,
                        typical_deliverables=[
                            "Comprehensive testing on devices",
                            "App store assets and descriptions",
                            "App store submission",
                            "Documentation and handover",
                        ],
                    ),
                ],
            ),
            "landing_page": ProjectTemplate(
                name="Landing Page",
                description="High-converting landing page with modern design",
                typical_duration_weeks=3,
                recommended_hourly_rate_min=Decimal("60"),
                recommended_hourly_rate_max=Decimal("120"),
                milestones=[
                    MilestoneTemplate(
                        sequence_number=1,
                        title="Design & Content Strategy",
                        description="Visual design, content strategy, and conversion optimization",
                        estimated_hours=15.0,
                        payment_percentage=40.0,
                        typical_deliverables=[
                            "Visual design mockups",
                            "Content strategy and copywriting",
                            "Conversion funnel design",
                            "Technical requirements",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=2,
                        title="Development & Integration",
                        description="Frontend development and third-party integrations",
                        estimated_hours=20.0,
                        payment_percentage=50.0,
                        typical_deliverables=[
                            "Responsive landing page",
                            "Contact form integration",
                            "Analytics setup",
                            "Performance optimization",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=3,
                        title="Launch & Optimization",
                        description="Testing, launch, and initial optimization",
                        estimated_hours=5.0,
                        payment_percentage=10.0,
                        typical_deliverables=[
                            "Cross-browser testing",
                            "Domain setup and deployment",
                            "SEO optimization",
                            "Launch support",
                        ],
                    ),
                ],
            ),
            "api": ProjectTemplate(
                name="API Development",
                description="RESTful API with documentation and testing",
                typical_duration_weeks=8,
                recommended_hourly_rate_min=Decimal("80"),
                recommended_hourly_rate_max=Decimal("150"),
                milestones=[
                    MilestoneTemplate(
                        sequence_number=1,
                        title="API Design & Architecture",
                        description="API specification, database design, and architecture planning",
                        estimated_hours=25.0,
                        payment_percentage=25.0,
                        typical_deliverables=[
                            "API specification document",
                            "Database schema design",
                            "Authentication strategy",
                            "Development environment setup",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=2,
                        title="Core API Development",
                        description="Implementation of core API endpoints and business logic",
                        estimated_hours=50.0,
                        payment_percentage=50.0,
                        typical_deliverables=[
                            "Core API endpoints",
                            "Database models and migrations",
                            "Authentication and authorization",
                            "Input validation and error handling",
                        ],
                    ),
                    MilestoneTemplate(
                        sequence_number=3,
                        title="Testing & Documentation",
                        description="Comprehensive testing, documentation, and deployment",
                        estimated_hours=25.0,
                        payment_percentage=25.0,
                        typical_deliverables=[
                            "Comprehensive test suite",
                            "API documentation",
                            "Deployment configuration",
                            "Performance monitoring setup",
                        ],
                    ),
                ],
            ),
        }
        return templates

    def get_available_templates(self) -> List[Dict]:
        """Get list of available project templates"""
        return [
            {
                "key": key,
                "name": template.name,
                "description": template.description,
                "typical_duration_weeks": template.typical_duration_weeks,
                "recommended_hourly_rate_min": float(
                    template.recommended_hourly_rate_min
                ),
                "recommended_hourly_rate_max": float(
                    template.recommended_hourly_rate_max
                ),
                "milestone_count": len(template.milestones),
            }
            for key, template in self.templates.items()
        ]

    def calculate_project_plan(
        self,
        total_budget: Decimal,
        internal_hourly_rate: Decimal,
        milestone_count: int = 3,
        project_template: str = "custom",
        planning_mode: str = "budget_to_hours",
        estimated_total_hours: Optional[Decimal] = None,
        deadline: Optional[str] = None,
    ) -> Dict:
        """Calculate comprehensive project plan with templates and analysis"""

        # Initialize response structure
        response = {
            "calculated_hours": None,
            "effective_rate": None,
            "suggested_milestones": [],
            "budget_breakdown": {},
            "timeline_analysis": {},
            "warnings": [],
            "template_used": project_template,
            "profitability_analysis": {},
            "risk_assessment": {},
        }

        # Calculate based on planning mode
        if planning_mode == "budget_to_hours":
            calculated_hours = total_budget / internal_hourly_rate
            effective_rate = internal_hourly_rate
            response["calculated_hours"] = calculated_hours
            response["effective_rate"] = effective_rate
        else:  # hours_to_rate
            if not estimated_total_hours:
                response["warnings"].append(
                    "Estimated total hours required for hours_to_rate mode"
                )
                return response

            calculated_hours = estimated_total_hours
            effective_rate = total_budget / estimated_total_hours
            response["calculated_hours"] = calculated_hours
            response["effective_rate"] = effective_rate

        # Generate milestones based on template or custom
        if project_template != "custom" and project_template in self.templates:
            template = self.templates[project_template]
            response["suggested_milestones"] = self._generate_template_milestones(
                template, total_budget, calculated_hours
            )
        else:
            response["suggested_milestones"] = self._generate_custom_milestones(
                milestone_count, total_budget, calculated_hours
            )

        # Budget breakdown
        response["budget_breakdown"] = {
            "total_budget": float(total_budget),
            "hourly_rate": float(internal_hourly_rate),
            "total_hours": float(calculated_hours),
            "milestone_count": len(response["suggested_milestones"]),
            "average_hours_per_milestone": float(
                calculated_hours / len(response["suggested_milestones"])
            ),
            "average_payment_per_milestone": float(
                total_budget / len(response["suggested_milestones"])
            ),
        }

        # Timeline analysis
        response["timeline_analysis"] = self._analyze_timeline(
            calculated_hours, deadline
        )

        # Profitability analysis
        response["profitability_analysis"] = self._analyze_profitability(
            internal_hourly_rate, project_template
        )

        # Risk assessment
        response["risk_assessment"] = self._assess_project_risks(
            total_budget, calculated_hours, internal_hourly_rate, project_template
        )

        # Generate warnings
        response["warnings"].extend(
            self._generate_warnings(
                total_budget, calculated_hours, internal_hourly_rate, project_template
            )
        )

        return response

    def _generate_template_milestones(
        self, template: ProjectTemplate, total_budget: Decimal, total_hours: Decimal
    ) -> List[Dict]:
        """Generate milestones based on project template"""
        milestones = []

        for milestone_template in template.milestones:
            # Calculate hours and payment based on template percentages
            milestone_hours = (
                Decimal(str(milestone_template.estimated_hours))
                / Decimal(str(sum(m.estimated_hours for m in template.milestones)))
            ) * total_hours
            milestone_payment = (
                Decimal(str(milestone_template.payment_percentage)) / Decimal("100")
            ) * total_budget

            milestone = {
                "sequence_number": milestone_template.sequence_number,
                "title": milestone_template.title,
                "description": milestone_template.description,
                "estimated_hours": float(milestone_hours),
                "payment_amount": float(milestone_payment),
                "percentage": milestone_template.payment_percentage,
                "typical_deliverables": milestone_template.typical_deliverables,
            }
            milestones.append(milestone)

        return milestones

    def _generate_custom_milestones(
        self, milestone_count: int, total_budget: Decimal, total_hours: Decimal
    ) -> List[Dict]:
        """Generate custom milestones with equal distribution"""
        milestones = []
        hours_per_milestone = total_hours / milestone_count
        payment_per_milestone = total_budget / milestone_count
        percentage_per_milestone = 100 / milestone_count

        for i in range(milestone_count):
            milestone = {
                "sequence_number": i + 1,
                "title": f"Milestone {i + 1}",
                "description": f"Project milestone {i + 1} - customize as needed",
                "estimated_hours": float(hours_per_milestone),
                "payment_amount": float(payment_per_milestone),
                "percentage": percentage_per_milestone,
                "typical_deliverables": [
                    "Deliverable 1 - customize as needed",
                    "Deliverable 2 - customize as needed",
                ],
            }
            milestones.append(milestone)

        return milestones

    def _analyze_timeline(
        self, total_hours: Decimal, deadline: Optional[str] = None
    ) -> Dict:
        """Analyze project timeline and provide estimates"""
        # Assuming different work intensities
        full_time_days = float(total_hours / 8)  # 8 hours per day
        part_time_days = float(total_hours / 4)  # 4 hours per day
        weekend_days = float(total_hours / 2)  # 2 hours per day

        analysis = {
            "estimated_duration_days_full_time": full_time_days,
            "estimated_duration_days_part_time": part_time_days,
            "estimated_duration_days_weekend": weekend_days,
            "estimated_duration_weeks_full_time": full_time_days / 5,
            "estimated_duration_weeks_part_time": part_time_days / 5,
            "estimated_duration_weeks_weekend": weekend_days / 2,
            "recommended_schedule": "part_time" if total_hours < 100 else "full_time",
        }

        return analysis

    def _analyze_profitability(self, hourly_rate: Decimal, template: str) -> Dict:
        """Analyze project profitability against market rates"""
        # Get template recommendations if available
        template_obj = self.templates.get(template)

        analysis = {
            "hourly_rate": float(hourly_rate),
            "market_position": "unknown",
            "profitability_score": 0.0,
            "recommendations": [],
        }

        if template_obj:
            min_rate = template_obj.recommended_hourly_rate_min
            max_rate = template_obj.recommended_hourly_rate_max

            if hourly_rate < min_rate:
                analysis["market_position"] = "below_market"
                analysis["profitability_score"] = 3.0
                analysis["recommendations"].append(
                    f"Consider increasing rate to ${min_rate}/hour minimum"
                )
            elif hourly_rate > max_rate:
                analysis["market_position"] = "premium"
                analysis["profitability_score"] = 9.0
                analysis["recommendations"].append(
                    "Excellent rate positioning for premium market"
                )
            else:
                # Calculate position within range
                position = (hourly_rate - min_rate) / (max_rate - min_rate)
                analysis["market_position"] = "competitive"
                analysis["profitability_score"] = 5.0 + (float(position) * 3.0)
                analysis["recommendations"].append(
                    "Rate is within competitive market range"
                )

        return analysis

    def _assess_project_risks(
        self, budget: Decimal, hours: Decimal, rate: Decimal, template: str
    ) -> Dict:
        """Assess potential project risks"""
        risks = []
        risk_score = 0.0

        # Budget size risks
        if budget < 1000:
            risks.append("Small budget may indicate scope creep risk")
            risk_score += 2.0
        elif budget > 50000:
            risks.append("Large budget requires careful milestone management")
            risk_score += 1.0

        # Hour estimation risks
        if hours < 10:
            risks.append("Very small project - consider minimum project size")
            risk_score += 2.0
        elif hours > 500:
            risks.append("Large project - consider breaking into phases")
            risk_score += 1.5

        # Rate risks
        if rate < 25:
            risks.append("Low hourly rate may impact project quality expectations")
            risk_score += 2.0

        # Template-specific risks
        template_obj = self.templates.get(template)
        if (
            template_obj
            and hours < sum(m.estimated_hours for m in template_obj.milestones) * 0.8
        ):
            risks.append(f"Hours may be insufficient for {template_obj.name} project")
            risk_score += 1.5

        return {
            "risk_score": min(risk_score, 10.0),  # Cap at 10
            "risk_level": (
                "low" if risk_score < 3 else "medium" if risk_score < 6 else "high"
            ),
            "identified_risks": risks,
            "mitigation_suggestions": self._get_risk_mitigation_suggestions(risks),
        }

    def _get_risk_mitigation_suggestions(self, risks: List[str]) -> List[str]:
        """Get suggestions for mitigating identified risks"""
        suggestions = []

        if any("scope creep" in risk for risk in risks):
            suggestions.append("Define clear project scope and change request process")

        if any("milestone management" in risk for risk in risks):
            suggestions.append(
                "Implement regular milestone reviews and client check-ins"
            )

        if any("minimum project size" in risk for risk in risks):
            suggestions.append(
                "Consider bundling with additional services or maintenance"
            )

        if any("breaking into phases" in risk for risk in risks):
            suggestions.append("Split into multiple projects with separate contracts")

        if any("quality expectations" in risk for risk in risks):
            suggestions.append("Set clear expectations about deliverables and timeline")

        if any("insufficient" in risk for risk in risks):
            suggestions.append("Review scope or increase budget for realistic timeline")

        return suggestions

    def _generate_warnings(
        self, budget: Decimal, hours: Decimal, rate: Decimal, template: str
    ) -> List[str]:
        """Generate warnings based on project parameters"""
        warnings = []

        if hours < 10:
            warnings.append(
                "Project scope may be too small for effective milestone tracking"
            )

        if hours > 1000:
            warnings.append("Large project - consider breaking into multiple phases")

        if rate < 25:
            warnings.append("Hourly rate may be below market standards")

        if budget / hours < rate * Decimal("0.8"):  # Less than 80% of expected
            warnings.append(
                "Budget may be insufficient for estimated hours at this rate"
            )

        # Template-specific warnings
        template_obj = self.templates.get(template)
        if template_obj:
            if rate < template_obj.recommended_hourly_rate_min:
                warnings.append(
                    f"Rate below recommended minimum for {template_obj.name} projects"
                )

        return warnings
