"""Monitoring Decorators for DevHQ Backend

Provides easy-to-use decorators for adding monitoring to services and functions.
"""

import asyncio
import time
from functools import wraps
from typing import Any, Callable, Optional

from app.core.monitoring import monitoring_service


def monitor_endpoint(
    operation_name: Optional[str] = None, record_business_metric: bool = False
):
    """Decorator for monitoring API endpoints."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            name = operation_name or f"endpoint.{func.__name__}"
            start_time = time.time()

            with monitoring_service.trace_operation(
                name, endpoint=func.__name__
            ) as span:
                try:
                    result = await func(*args, **kwargs)

                    if record_business_metric:
                        monitoring_service.record_business_metric(
                            f"{func.__name__}_success", 1, status="success"
                        )

                    return result

                except Exception as e:
                    if record_business_metric:
                        monitoring_service.record_business_metric(
                            f"{func.__name__}_error",
                            1,
                            status="error",
                            error_type=type(e).__name__,
                        )

                    monitoring_service.log_structured(
                        "error",
                        f"Endpoint error: {func.__name__}",
                        endpoint=func.__name__,
                        error=str(e),
                        error_type=type(e).__name__,
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"Endpoint {func.__name__}",
                        duration,
                        threshold=3.0,  # 3 second threshold for endpoints
                        endpoint=func.__name__,
                    )

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            name = operation_name or f"endpoint.{func.__name__}"
            start_time = time.time()

            with monitoring_service.trace_operation(
                name, endpoint=func.__name__
            ) as span:
                try:
                    result = func(*args, **kwargs)

                    if record_business_metric:
                        monitoring_service.record_business_metric(
                            f"{func.__name__}_success", 1, status="success"
                        )

                    return result

                except Exception as e:
                    if record_business_metric:
                        monitoring_service.record_business_metric(
                            f"{func.__name__}_error",
                            1,
                            status="error",
                            error_type=type(e).__name__,
                        )

                    monitoring_service.log_structured(
                        "error",
                        f"Endpoint error: {func.__name__}",
                        endpoint=func.__name__,
                        error=str(e),
                        error_type=type(e).__name__,
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"Endpoint {func.__name__}",
                        duration,
                        threshold=3.0,  # 3 second threshold for endpoints
                        endpoint=func.__name__,
                    )

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


def monitor_service_operation(service_name: str, operation_type: str = "operation"):
    """Decorator for monitoring service operations."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            operation_name = f"{service_name}.{func.__name__}"
            start_time = time.time()

            with monitoring_service.trace_operation(
                operation_name,
                service=service_name,
                operation=func.__name__,
                operation_type=operation_type,
            ) as span:
                try:
                    result = await func(*args, **kwargs)

                    monitoring_service.record_business_metric(
                        f"{service_name}_{operation_type}_success",
                        1,
                        service=service_name,
                        operation=func.__name__,
                    )

                    monitoring_service.log_business_event(
                        f"{operation_type}_completed",
                        service_name,
                        str(getattr(result, "id", "unknown")),
                        operation=func.__name__,
                        status="success",
                    )

                    return result

                except Exception as e:
                    monitoring_service.record_business_metric(
                        f"{service_name}_{operation_type}_error",
                        1,
                        service=service_name,
                        operation=func.__name__,
                        error_type=type(e).__name__,
                    )

                    monitoring_service.log_business_event(
                        f"{operation_type}_failed",
                        service_name,
                        "unknown",
                        operation=func.__name__,
                        status="error",
                        error=str(e),
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"{service_name} {func.__name__}",
                        duration,
                        threshold=5.0,  # 5 second threshold for service operations
                        service=service_name,
                    )

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            operation_name = f"{service_name}.{func.__name__}"
            start_time = time.time()

            with monitoring_service.trace_operation(
                operation_name,
                service=service_name,
                operation=func.__name__,
                operation_type=operation_type,
            ) as span:
                try:
                    result = func(*args, **kwargs)

                    monitoring_service.record_business_metric(
                        f"{service_name}_{operation_type}_success",
                        1,
                        service=service_name,
                        operation=func.__name__,
                    )

                    monitoring_service.log_business_event(
                        f"{operation_type}_completed",
                        service_name,
                        str(getattr(result, "id", "unknown")),
                        operation=func.__name__,
                        status="success",
                    )

                    return result

                except Exception as e:
                    monitoring_service.record_business_metric(
                        f"{service_name}_{operation_type}_error",
                        1,
                        service=service_name,
                        operation=func.__name__,
                        error_type=type(e).__name__,
                    )

                    monitoring_service.log_business_event(
                        f"{operation_type}_failed",
                        service_name,
                        "unknown",
                        operation=func.__name__,
                        status="error",
                        error=str(e),
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"{service_name} {func.__name__}",
                        duration,
                        threshold=5.0,  # 5 second threshold for service operations
                        service=service_name,
                        operation=func.__name__,
                    )

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


def monitor_database_operation(table_name: str, operation_type: str = "query"):
    """Decorator for monitoring database operations."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()

            with monitoring_service.trace_operation(
                f"db.{operation_type}",
                db_table=table_name,
                db_operation=operation_type,
                function=func.__name__,
            ) as span:
                try:
                    result = await func(*args, **kwargs)

                    duration = time.time() - start_time
                    monitoring_service.record_db_query_metrics(
                        operation_type, table_name, duration
                    )

                    return result

                except Exception as e:
                    monitoring_service.log_structured(
                        "error",
                        f"Database operation error: {table_name}.{operation_type}",
                        table=table_name,
                        operation=operation_type,
                        function=func.__name__,
                        error=str(e),
                        error_type=type(e).__name__,
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"Database {operation_type}",
                        duration,
                        threshold=1.0,  # 1 second threshold for DB operations
                        table=table_name,
                        operation=operation_type,
                    )

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()

            with monitoring_service.trace_operation(
                f"db.{operation_type}",
                db_table=table_name,
                db_operation=operation_type,
                function=func.__name__,
            ) as span:
                try:
                    result = func(*args, **kwargs)

                    duration = time.time() - start_time
                    monitoring_service.record_db_query_metrics(
                        operation_type, table_name, duration
                    )

                    return result

                except Exception as e:
                    monitoring_service.log_structured(
                        "error",
                        f"Database operation error: {table_name}.{operation_type}",
                        table=table_name,
                        operation=operation_type,
                        function=func.__name__,
                        error=str(e),
                        error_type=type(e).__name__,
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"Database {operation_type}",
                        duration,
                        threshold=1.0,  # 1 second threshold for DB operations
                        table=table_name,
                        operation=operation_type,
                    )

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


def monitor_cache_operation(cache_key_prefix: str = "cache"):
    """Decorator for monitoring cache operations."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            operation_name = f"cache.{func.__name__}"
            start_time = time.time()

            with monitoring_service.trace_operation(
                operation_name,
                cache_operation=func.__name__,
                cache_key_prefix=cache_key_prefix,
            ) as span:
                try:
                    result = await func(*args, **kwargs)

                    # Determine if it was a hit or miss based on result
                    is_hit = result is not None and func.__name__ in ["get", "mget"]

                    monitoring_service.record_cache_metrics(
                        func.__name__, is_hit, cache_key_prefix=cache_key_prefix
                    )

                    return result

                except Exception as e:
                    monitoring_service.log_structured(
                        "error",
                        f"Cache operation error: {func.__name__}",
                        cache_operation=func.__name__,
                        cache_key_prefix=cache_key_prefix,
                        error=str(e),
                        error_type=type(e).__name__,
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"Cache {func.__name__}",
                        duration,
                        threshold=0.5,  # 500ms threshold for cache operations
                        cache_operation=func.__name__,
                        cache_key_prefix=cache_key_prefix,
                    )

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            operation_name = f"cache.{func.__name__}"
            start_time = time.time()

            with monitoring_service.trace_operation(
                operation_name,
                cache_operation=func.__name__,
                cache_key_prefix=cache_key_prefix,
            ) as span:
                try:
                    result = func(*args, **kwargs)

                    # Determine if it was a hit or miss based on result
                    is_hit = result is not None and func.__name__ in ["get", "mget"]

                    monitoring_service.record_cache_metrics(
                        func.__name__, is_hit, cache_key_prefix=cache_key_prefix
                    )

                    return result

                except Exception as e:
                    monitoring_service.log_structured(
                        "error",
                        f"Cache operation error: {func.__name__}",
                        cache_operation=func.__name__,
                        cache_key_prefix=cache_key_prefix,
                        error=str(e),
                        error_type=type(e).__name__,
                    )

                    if span:
                        span.record_exception(e)

                    raise
                finally:
                    duration = time.time() - start_time
                    monitoring_service.log_performance_warning(
                        f"Cache {func.__name__}",
                        duration,
                        threshold=0.5,  # 500ms threshold for cache operations
                        cache_operation=func.__name__,
                        cache_key_prefix=cache_key_prefix,
                    )

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator
