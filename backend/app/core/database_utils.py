"""Database Utilities for Enhanced Data Integrity

Provides utilities for:
- Foreign key constraint management
- Data validation before database operations
- Bulk operations with transaction safety
- Database health monitoring
- Connection pool management
"""

import logging
from typing import Any, Dict, List, Optional, Type, Union

from sqlalchemy import MetaData, inspect, text
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm import Session, relationship
from sqlalchemy.sql import Select

from app.core.exceptions import DatabaseError, ValidationException
from app.core.transaction_manager import db_transaction
from app.database import Base, engine

logger = logging.getLogger(__name__)


class ForeignKeyManager:
    """Manages foreign key constraints and validation"""

    @staticmethod
    def get_foreign_key_relationships(
        model_class: Type[Base],
    ) -> Dict[str, Dict[str, Any]]:
        """Get all foreign key relationships for a model

        Args:
            model_class: SQLAlchemy model class

        Returns:
            Dictionary mapping relationship names to their details
        """
        mapper = inspect(model_class)
        relationships = {}

        for rel_name, rel in mapper.relationships.items():
            if rel.direction.name == "MANYTOONE":  # Foreign key relationship
                relationships[rel_name] = {
                    "target_class": rel.mapper.class_,
                    "foreign_key": rel.local_columns,
                    "target_column": rel.remote_side,
                    "nullable": any(col.nullable for col in rel.local_columns),
                    "cascade": rel.cascade,
                }

        return relationships

    @staticmethod
    def validate_foreign_key_exists(
        session: Session, target_class: Type[Base], foreign_key_value: Any
    ) -> bool:
        """Validate that a foreign key reference exists

        Args:
            session: Database session
            target_class: Target model class
            foreign_key_value: Value to check

        Returns:
            True if reference exists, False otherwise
        """
        if foreign_key_value is None:
            return True  # Nullable foreign keys are valid

        return (
            session.query(target_class)
            .filter(target_class.id == foreign_key_value)
            .first()
            is not None
        )

    @staticmethod
    def get_referencing_records(
        session: Session, model_class: Type[Base], record_id: Any
    ) -> Dict[str, List[Any]]:
        """Get all records that reference the given record via foreign keys

        Args:
            session: Database session
            model_class: Model class of the record
            record_id: ID of the record to check

        Returns:
            Dictionary mapping model names to lists of referencing records
        """
        referencing_records = {}

        # Get all models that might reference this one
        for model in Base.registry._class_registry.values():
            if hasattr(model, "__tablename__"):
                try:
                    mapper = inspect(model)
                    for rel_name, rel in mapper.relationships.items():
                        if (
                            rel.direction.name == "MANYTOONE"
                            and rel.mapper.class_ == model_class
                        ):
                            # This model has a foreign key to our target model
                            fk_column = list(rel.local_columns)[0]
                            records = (
                                session.query(model)
                                .filter(fk_column == record_id)
                                .all()
                            )

                            if records:
                                referencing_records[model.__name__] = records

                except Exception as e:
                    logger.warning(f"Error checking references for {model}: {e}")

        return referencing_records

    @staticmethod
    def safe_delete_with_references(
        session: Session, model_instance: Any, cascade_delete: bool = False
    ) -> Dict[str, Any]:
        """Safely delete a record, handling foreign key constraints

        Args:
            session: Database session
            model_instance: Instance to delete
            cascade_delete: Whether to cascade delete referencing records

        Returns:
            Dictionary with deletion results

        Raises:
            ValidationException: If record has references and cascade_delete is False
        """
        model_class = model_instance.__class__
        record_id = model_instance.id

        # Check for referencing records
        references = ForeignKeyManager.get_referencing_records(
            session, model_class, record_id
        )

        if references and not cascade_delete:
            ref_summary = {k: len(v) for k, v in references.items()}
            raise ValidationException(
                f"Cannot delete {model_class.__name__} {record_id}: "
                f"Referenced by {ref_summary}"
            )

        deleted_records = {"main": 1, "cascaded": {}}

        # Delete referencing records if cascade is enabled
        if cascade_delete:
            for model_name, records in references.items():
                for record in records:
                    session.delete(record)
                deleted_records["cascaded"][model_name] = len(records)

        # Delete the main record
        session.delete(model_instance)

        return deleted_records


class BulkOperationManager:
    """Manages bulk database operations with transaction safety"""

    @staticmethod
    def bulk_create_with_validation(
        session: Session,
        model_class: Type[Base],
        data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        validate_foreign_keys: bool = True,
    ) -> List[Any]:
        """Bulk create records with validation

        Args:
            session: Database session
            model_class: Model class to create
            data_list: List of dictionaries with record data
            batch_size: Number of records to process per batch
            validate_foreign_keys: Whether to validate foreign keys

        Returns:
            List of created instances
        """
        created_instances = []

        # Process in batches
        for i in range(0, len(data_list), batch_size):
            batch = data_list[i : i + batch_size]
            batch_instances = []

            for data in batch:
                instance = model_class(**data)

                if validate_foreign_keys:
                    ForeignKeyManager.validate_foreign_key_exists(
                        session, model_class, instance
                    )

                batch_instances.append(instance)

            # Add batch to session
            session.add_all(batch_instances)
            session.flush()  # Get IDs without committing

            created_instances.extend(batch_instances)
            logger.info(f"Processed batch {i//batch_size + 1}: {len(batch)} records")

        return created_instances

    @staticmethod
    def bulk_update_with_validation(
        session: Session,
        model_class: Type[Base],
        updates: List[Dict[str, Any]],
        batch_size: int = 1000,
    ) -> int:
        """Bulk update records with validation

        Args:
            session: Database session
            model_class: Model class to update
            updates: List of dictionaries with 'id' and update data
            batch_size: Number of records to process per batch

        Returns:
            Number of updated records
        """
        updated_count = 0

        for i in range(0, len(updates), batch_size):
            batch = updates[i : i + batch_size]

            for update_data in batch:
                record_id = update_data.pop("id")
                instance = (
                    session.query(model_class)
                    .filter(model_class.id == record_id)
                    .first()
                )

                if instance:
                    for key, value in update_data.items():
                        setattr(instance, key, value)
                    updated_count += 1

            session.flush()
            logger.info(f"Updated batch {i//batch_size + 1}: {len(batch)} records")

        return updated_count


class DatabaseHealthMonitor:
    """Monitors database health and performance"""

    @staticmethod
    def get_connection_pool_status() -> Dict[str, Any]:
        """Get connection pool status

        Returns:
            Dictionary with pool statistics
        """
        pool = engine.pool

        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
            "total_connections": pool.size() + pool.overflow(),
        }

    @staticmethod
    def get_active_connections() -> int:
        """Get number of active database connections

        Returns:
            Number of active connections
        """
        try:
            with engine.connect() as conn:
                result = conn.execute(
                    text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
                )
                return result.scalar()
        except Exception as e:
            logger.error(f"Error getting active connections: {e}")
            return -1

    @staticmethod
    def get_table_sizes() -> Dict[str, Dict[str, Any]]:
        """Get size information for all tables

        Returns:
            Dictionary mapping table names to size information
        """
        table_sizes = {}

        try:
            with engine.connect() as conn:
                result = conn.execute(
                    text(
                        """
                    SELECT 
                        schemaname,
                        tablename,
                        attname,
                        n_distinct,
                        correlation
                    FROM pg_stats 
                    WHERE schemaname = 'public'
                    ORDER BY tablename, attname
                """
                    )
                )

                for row in result:
                    table_name = row.tablename
                    if table_name not in table_sizes:
                        table_sizes[table_name] = {
                            "columns": {},
                            "schema": row.schemaname,
                        }

                    table_sizes[table_name]["columns"][row.attname] = {
                        "n_distinct": row.n_distinct,
                        "correlation": row.correlation,
                    }

        except Exception as e:
            logger.error(f"Error getting table sizes: {e}")

        return table_sizes

    @staticmethod
    def analyze_slow_queries() -> List[Dict[str, Any]]:
        """Analyze slow queries from pg_stat_statements

        Returns:
            List of slow query information
        """
        try:
            with engine.connect() as conn:
                result = conn.execute(
                    text(
                        """
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        rows
                    FROM pg_stat_statements 
                    WHERE mean_time > 100  -- queries taking more than 100ms on average
                    ORDER BY mean_time DESC 
                    LIMIT 10
                """
                    )
                )

                return [
                    {
                        "query": row.query[:200] + "..."
                        if len(row.query) > 200
                        else row.query,
                        "calls": row.calls,
                        "total_time": row.total_time,
                        "mean_time": row.mean_time,
                        "rows": row.rows,
                    }
                    for row in result
                ]

        except Exception as e:
            logger.warning(f"Could not analyze slow queries: {e}")
            return []


class DataIntegrityValidator:
    """Validates data integrity across the database"""

    @staticmethod
    def validate_all_foreign_keys(session: Session) -> Dict[str, List[str]]:
        """Validate all foreign key constraints in the database

        Args:
            session: Database session

        Returns:
            Dictionary with validation results
        """
        violations = {"orphaned_records": [], "invalid_references": []}

        # Check each model for foreign key violations
        for model in Base.registry._class_registry.values():
            if hasattr(model, "__tablename__"):
                try:
                    relationships = ForeignKeyManager.get_foreign_key_relationships(
                        model
                    )

                    for rel_name, rel_info in relationships.items():
                        target_class = rel_info["target_class"]

                        # Find records with invalid foreign key references
                        query = (
                            session.query(model)
                            .outerjoin(
                                target_class,
                                getattr(model, rel_name + "_id") == target_class.id,
                            )
                            .filter(
                                getattr(model, rel_name + "_id").isnot(None),
                                target_class.id.is_(None),
                            )
                        )

                        invalid_records = query.all()
                        if invalid_records:
                            violations["invalid_references"].extend(
                                [
                                    f"{model.__name__}.{record.id} -> {target_class.__name__}"
                                    for record in invalid_records
                                ]
                            )

                except Exception as e:
                    logger.error(f"Error validating foreign keys for {model}: {e}")

        return violations

    @staticmethod
    def check_data_consistency(session: Session) -> Dict[str, Any]:
        """Check overall data consistency

        Args:
            session: Database session

        Returns:
            Dictionary with consistency check results
        """
        results = {
            "foreign_key_violations": DataIntegrityValidator.validate_all_foreign_keys(
                session
            ),
            "duplicate_checks": {},
            "null_constraint_violations": {},
            "timestamp_inconsistencies": [],
        }

        # Check for common data inconsistencies
        for model in Base.registry._class_registry.values():
            if hasattr(model, "__tablename__"):
                try:
                    model_name = model.__name__

                    # Check for timestamp inconsistencies (created_at > updated_at)
                    if hasattr(model, "created_at") and hasattr(model, "updated_at"):
                        inconsistent = (
                            session.query(model)
                            .filter(model.created_at > model.updated_at)
                            .count()
                        )

                        if inconsistent > 0:
                            results["timestamp_inconsistencies"].append(
                                f"{model_name}: {inconsistent} records"
                            )

                except Exception as e:
                    logger.error(f"Error checking consistency for {model}: {e}")

        return results


class PerformanceMonitor:
    """Monitors database performance and provides metrics"""

    def __init__(self):
        self.query_times = []
        self.connection_pool_stats = {}

    def start_query_timer(self):
        """Start timing a database query"""
        import time

        return time.perf_counter()

    def end_query_timer(self, start_time: float, query_type: str = "unknown"):
        """End timing a database query and record the duration"""
        import time

        duration = time.perf_counter() - start_time
        self.query_times.append(
            {"duration": duration, "query_type": query_type, "timestamp": time.time()}
        )
        return duration

    def get_average_query_time(self) -> float:
        """Get average query execution time"""
        if not self.query_times:
            return 0.0
        return sum(q["duration"] for q in self.query_times) / len(self.query_times)

    def get_slow_queries(self, threshold: float = 1.0) -> List[Dict[str, Any]]:
        """Get queries that took longer than threshold seconds"""
        return [q for q in self.query_times if q["duration"] > threshold]

    def reset_metrics(self):
        """Reset all collected metrics"""
        self.query_times.clear()
        self.connection_pool_stats.clear()

    def get_connection_pool_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        try:
            pool = engine.pool
            return {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid(),
            }
        except Exception as e:
            logger.error(f"Error getting connection pool stats: {e}")
            return {}

    def log_performance_summary(self):
        """Log a summary of performance metrics"""
        avg_time = self.get_average_query_time()
        slow_queries = self.get_slow_queries()
        pool_stats = self.get_connection_pool_stats()

        logger.info(f"Performance Summary:")
        logger.info(f"  Average query time: {avg_time:.4f}s")
        logger.info(f"  Slow queries (>1s): {len(slow_queries)}")
        logger.info(f"  Connection pool: {pool_stats}")

        return {
            "average_query_time": avg_time,
            "slow_query_count": len(slow_queries),
            "connection_pool": pool_stats,
        }


# Utility functions for common database operations
def safe_get_or_create(
    session: Session,
    model_class: Type[Base],
    defaults: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> tuple[Any, bool]:
    """Safely get or create a record

    Args:
        session: Database session
        model_class: Model class
        defaults: Default values for creation
        **kwargs: Filter criteria

    Returns:
        Tuple of (instance, created_flag)
    """
    instance = session.query(model_class).filter_by(**kwargs).first()

    if instance:
        return instance, False
    else:
        params = dict(kwargs)
        if defaults:
            params.update(defaults)

        instance = model_class(**params)
        session.add(instance)
        session.flush()  # Get ID without committing

        return instance, True


def safe_bulk_insert_or_update(
    session: Session,
    model_class: Type[Base],
    data_list: List[Dict[str, Any]],
    update_on_conflict: bool = True,
) -> Dict[str, int]:
    """Safely bulk insert or update records

    Args:
        session: Database session
        model_class: Model class
        data_list: List of record data
        update_on_conflict: Whether to update on conflict

    Returns:
        Dictionary with operation counts
    """
    results = {"created": 0, "updated": 0, "errors": 0}

    with db_transaction(session) as tx_session:
        for data in data_list:
            try:
                # Try to find existing record
                unique_fields = [
                    col.name
                    for col in inspect(model_class).columns
                    if col.unique and col.name in data
                ]

                if unique_fields:
                    filter_criteria = {field: data[field] for field in unique_fields}
                    existing = (
                        tx_session.query(model_class)
                        .filter_by(**filter_criteria)
                        .first()
                    )

                    if existing:
                        if update_on_conflict:
                            for key, value in data.items():
                                setattr(existing, key, value)
                            results["updated"] += 1
                        # else skip the record
                    else:
                        instance = model_class(**data)
                        tx_session.add(instance)
                        results["created"] += 1
                else:
                    # No unique fields, just create
                    instance = model_class(**data)
                    tx_session.add(instance)
                    results["created"] += 1

            except Exception as e:
                logger.error(f"Error processing record {data}: {e}")
                results["errors"] += 1

    return results
