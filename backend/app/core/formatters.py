"""
Formatting utilities for DevHQ application
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Union


def format_currency(amount: Union[Decimal, float], currency: str = "USD") -> str:
    """Format currency amount"""
    currency_symbols = {
        "USD": "$",
        "EUR": "€",
        "GBP": "£",
        "KES": "KSh",
        "NGN": "₦",
        "GHS": "₵",
        "ZAR": "R",
    }

    symbol = currency_symbols.get(currency.upper(), currency)
    return f"{symbol}{amount:,.2f}"


def format_duration(seconds: int) -> str:
    """Format duration in seconds to human readable format"""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        if remaining_seconds == 0:
            return f"{minutes}m"
        return f"{minutes}m {remaining_seconds}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        if remaining_minutes == 0:
            return f"{hours}h"
        return f"{hours}h {remaining_minutes}m"


def format_date(date: datetime, format_str: str = "%b %d, %Y") -> str:
    """Format date to string"""
    return date.strftime(format_str)


def format_percentage(value: Union[float, Decimal], precision: int = 2) -> str:
    """Format percentage value"""
    return f"{value:.{precision}f}%"
