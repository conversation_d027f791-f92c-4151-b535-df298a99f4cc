"""
Billing Workflow Service for flexible invoice creation scenarios
"""

from decimal import Decimal
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from app.models import (Invoice, InvoiceItem, Project, ProjectMilestone,
                        TimeEntry)


class BillingWorkflowService:
    """Service for managing different billing scenarios and workflows"""

    def __init__(self, db: Session):
        self.db = db

    def get_billing_templates(self) -> List[Dict]:
        """Get available billing workflow templates"""
        return [
            {
                "key": "milestone_payment",
                "name": "Milestone Payment",
                "description": "Invoice for completed project milestone",
                "icon": "milestone",
                "use_cases": [
                    "Client approved a specific milestone",
                    "Phase-based project billing",
                    "Deliverable-based payments",
                ],
                "typical_items": ["Completed milestones", "Associated time entries"],
                "payment_timing": "After milestone approval",
            },
            {
                "key": "deposit_invoice",
                "name": "Project Deposit",
                "description": "Upfront payment before project starts",
                "icon": "deposit",
                "use_cases": [
                    "50% upfront payment",
                    "Project kickoff payment",
                    "Securing project commitment",
                ],
                "typical_items": ["Project deposit", "Setup fees"],
                "payment_timing": "Before work begins",
            },
            {
                "key": "time_materials",
                "name": "Time & Materials",
                "description": "Invoice for tracked time and expenses",
                "icon": "clock",
                "use_cases": [
                    "Monthly time billing",
                    "Hourly rate projects",
                    "Ongoing maintenance work",
                ],
                "typical_items": ["Time entries", "Expenses", "Materials"],
                "payment_timing": "End of billing period",
            },
            {
                "key": "final_payment",
                "name": "Final Payment",
                "description": "Complete project payment on delivery",
                "icon": "completion",
                "use_cases": [
                    "Project completion payment",
                    "Remaining balance after deposits",
                    "Final deliverable payment",
                ],
                "typical_items": ["All remaining work", "Final deliverables"],
                "payment_timing": "Project completion",
            },
            {
                "key": "retainer",
                "name": "Monthly Retainer",
                "description": "Recurring monthly service payment",
                "icon": "recurring",
                "use_cases": [
                    "Ongoing support services",
                    "Monthly maintenance",
                    "Consulting retainer",
                ],
                "typical_items": ["Monthly service fee", "Included hours"],
                "payment_timing": "Monthly recurring",
            },
            {
                "key": "expense_reimbursement",
                "name": "Expense Reimbursement",
                "description": "Client reimbursement for project expenses",
                "icon": "receipt",
                "use_cases": [
                    "Software licenses",
                    "Third-party services",
                    "Travel expenses",
                ],
                "typical_items": ["Software costs", "Service fees", "Travel"],
                "payment_timing": "After expense incurred",
            },
        ]

    def create_deposit_invoice(
        self,
        user_id: UUID,
        project_id: UUID,
        deposit_percentage: Decimal,
        description: Optional[str] = None,
        custom_amount: Optional[Decimal] = None,
    ) -> Dict:
        """Create a deposit invoice for a project"""

        # Get project details
        project = (
            self.db.query(Project)
            .filter(Project.id == project_id, Project.user_id == user_id)
            .first()
        )

        if not project:
            raise ValueError("Project not found or access denied")

        # Calculate deposit amount
        if custom_amount:
            deposit_amount = custom_amount
            percentage_used = (
                (custom_amount / project.total_budget * 100)
                if project.total_budget
                else 0
            )
        else:
            if not project.total_budget:
                raise ValueError(
                    "Project must have a total budget for percentage-based deposits"
                )
            deposit_amount = project.total_budget * (deposit_percentage / 100)
            percentage_used = deposit_percentage

        # Create invoice with a simple invoice number for now
        import uuid
        from datetime import datetime, timedelta, timezone

        invoice_number = f"INV-{str(uuid.uuid4())[:8].upper()}"
        due_date = datetime.now(timezone.utc) + timedelta(days=30)  # 30 days from now

        invoice = Invoice(
            invoice_number=invoice_number,
            client_id=project.client_id,
            project_id=project_id,
            user_id=user_id,
            status="draft",
            currency="USD",  # Default currency - can be enhanced later
            subtotal=deposit_amount,
            total_amount=deposit_amount,
            due_date=due_date,
            notes=f"Project deposit ({percentage_used:.1f}% of total project value)",
        )

        self.db.add(invoice)
        self.db.flush()

        # Create invoice item
        item_description = description or f"Project Deposit - {project.title}"
        invoice_item = InvoiceItem(
            invoice_id=invoice.id,
            description=item_description,
            quantity=Decimal("1.00"),
            unit_price=deposit_amount,
            total_price=deposit_amount,
            item_type="deposit",
            sequence_number=1,
        )

        self.db.add(invoice_item)
        self.db.commit()

        return {
            "invoice_id": invoice.id,
            "invoice_number": invoice.invoice_number,
            "deposit_amount": float(deposit_amount),
            "percentage": float(percentage_used),
            "project_title": project.title,
            "client_name": project.client.name,
        }

    def create_milestone_invoice(
        self,
        user_id: UUID,
        project_id: UUID,
        milestone_ids: List[UUID],
        include_time_entries: bool = True,
    ) -> Dict:
        """Create an invoice for completed milestones"""

        # Get project
        project = (
            self.db.query(Project)
            .filter(Project.id == project_id, Project.user_id == user_id)
            .first()
        )

        if not project:
            raise ValueError("Project not found or access denied")

        # Get milestones
        milestones = (
            self.db.query(ProjectMilestone)
            .filter(
                ProjectMilestone.id.in_(milestone_ids),
                ProjectMilestone.project_id == project_id,
            )
            .all()
        )

        if not milestones:
            raise ValueError("No valid milestones found")

        # Calculate total amount
        total_amount = sum(
            milestone.payment_amount or Decimal("0") for milestone in milestones
        )

        # Create invoice with a simple invoice number
        import uuid
        from datetime import datetime, timedelta, timezone

        invoice_number = f"INV-{str(uuid.uuid4())[:8].upper()}"
        due_date = datetime.now(timezone.utc) + timedelta(days=30)  # 30 days from now

        invoice = Invoice(
            invoice_number=invoice_number,
            client_id=project.client_id,
            project_id=project_id,
            user_id=user_id,
            status="draft",
            currency="USD",  # Default currency - can be enhanced later
            subtotal=total_amount,
            total_amount=total_amount,
            due_date=due_date,
            notes=f"Payment for completed milestones: {', '.join(m.title for m in milestones)}",
        )

        self.db.add(invoice)
        self.db.flush()

        # Create invoice items for milestones
        sequence = 1
        for milestone in milestones:
            invoice_item = InvoiceItem(
                invoice_id=invoice.id,
                description=f"Milestone: {milestone.title}",
                quantity=Decimal("1.00"),
                unit_price=milestone.payment_amount or Decimal("0"),
                total_price=milestone.payment_amount or Decimal("0"),
                item_type="milestone",
                milestone_id=milestone.id,
                sequence_number=sequence,
            )
            self.db.add(invoice_item)
            sequence += 1

        # Add related time entries if requested
        if include_time_entries:
            # Get unbilled time entries for these milestones (simplified - in practice you'd have milestone-time relationships)
            time_entries = (
                self.db.query(TimeEntry)
                .filter(
                    TimeEntry.project_id == project_id,
                    TimeEntry.status == "approved",
                    TimeEntry.is_billable == True,
                    # Add logic to link time entries to milestones
                )
                .all()
            )

            for entry in time_entries:
                if entry.billable_amount and entry.billable_amount > 0:
                    invoice_item = InvoiceItem(
                        invoice_id=invoice.id,
                        description=f"Time Entry: {entry.description or 'Development work'}",
                        quantity=Decimal(str(entry.duration_minutes / 60)),
                        unit_price=entry.hourly_rate or Decimal("0"),
                        total_price=entry.billable_amount,
                        item_type="time",
                        time_entry_id=entry.id,
                        hours_worked=Decimal(str(entry.duration_minutes / 60)),
                        work_date=entry.work_date,
                        sequence_number=sequence,
                    )
                    self.db.add(invoice_item)
                    sequence += 1

        self.db.commit()

        return {
            "invoice_id": invoice.id,
            "invoice_number": invoice.invoice_number,
            "total_amount": float(total_amount),
            "milestone_count": len(milestones),
            "project_title": project.title,
            "client_name": project.client.name,
        }

    def create_time_materials_invoice(
        self,
        user_id: UUID,
        project_id: UUID,
        start_date: str,
        end_date: str,
        include_expenses: bool = False,
    ) -> Dict:
        """Create a time and materials invoice for a billing period"""

        # Get project
        project = (
            self.db.query(Project)
            .filter(Project.id == project_id, Project.user_id == user_id)
            .first()
        )

        if not project:
            raise ValueError("Project not found or access denied")

        # Get unbilled time entries in date range
        time_entries = (
            self.db.query(TimeEntry)
            .filter(
                TimeEntry.project_id == project_id,
                TimeEntry.work_date >= start_date,
                TimeEntry.work_date <= end_date,
                TimeEntry.is_billable == True,
                TimeEntry.status.in_(["approved", "submitted"]),
            )
            .all()
        )

        if not time_entries:
            raise ValueError("No billable time entries found for the specified period")

        # Calculate total
        total_amount = sum(
            entry.billable_amount or Decimal("0") for entry in time_entries
        )

        # Create invoice with a simple invoice number
        import uuid
        from datetime import datetime, timedelta, timezone

        invoice_number = f"INV-{str(uuid.uuid4())[:8].upper()}"
        due_date = datetime.now(timezone.utc) + timedelta(days=30)  # 30 days from now

        invoice = Invoice(
            invoice_number=invoice_number,
            client_id=project.client_id,
            project_id=project_id,
            user_id=user_id,
            status="draft",
            currency="USD",  # Default currency - can be enhanced later
            subtotal=total_amount,
            total_amount=total_amount,
            due_date=due_date,
            notes=f"Time & Materials billing for {start_date} to {end_date}",
        )

        self.db.add(invoice)
        self.db.flush()

        # Group time entries by date or task for better presentation
        grouped_entries = self._group_time_entries(time_entries)

        sequence = 1
        for group_key, entries in grouped_entries.items():
            total_hours = sum(entry.duration_minutes / 60 for entry in entries)
            total_amount_group = sum(
                entry.billable_amount or Decimal("0") for entry in entries
            )

            # Create a single line item for the group
            invoice_item = InvoiceItem(
                invoice_id=invoice.id,
                description=f"Development work - {group_key}",
                quantity=Decimal(str(total_hours)),
                unit_price=entries[0].hourly_rate or Decimal("0"),
                total_price=total_amount_group,
                item_type="time",
                hours_worked=Decimal(str(total_hours)),
                work_date=entries[0].work_date,
                sequence_number=sequence,
            )
            self.db.add(invoice_item)
            sequence += 1

        self.db.commit()

        return {
            "invoice_id": invoice.id,
            "invoice_number": invoice.invoice_number,
            "total_amount": float(total_amount),
            "total_hours": sum(entry.duration_minutes / 60 for entry in time_entries),
            "entry_count": len(time_entries),
            "billing_period": f"{start_date} to {end_date}",
            "project_title": project.title,
            "client_name": project.client.name,
        }

    def create_final_payment_invoice(
        self, user_id: UUID, project_id: UUID, include_all_unbilled: bool = True
    ) -> Dict:
        """Create a final payment invoice for project completion"""

        # Get project
        project = (
            self.db.query(Project)
            .filter(Project.id == project_id, Project.user_id == user_id)
            .first()
        )

        if not project:
            raise ValueError("Project not found or access denied")

        # Calculate total project value and payments made
        total_project_value = project.total_budget or Decimal("0")

        # Get all previous invoices for this project
        previous_invoices = (
            self.db.query(Invoice)
            .filter(
                Invoice.project_id == project_id,
                Invoice.status.in_(["sent", "paid", "viewed"]),
            )
            .all()
        )

        total_invoiced = sum(inv.total_amount for inv in previous_invoices)
        remaining_balance = total_project_value - total_invoiced

        if remaining_balance <= 0:
            raise ValueError("No remaining balance for final payment")

        # Create invoice with a simple invoice number
        import uuid
        from datetime import datetime, timedelta, timezone

        invoice_number = f"INV-{str(uuid.uuid4())[:8].upper()}"
        due_date = datetime.now(timezone.utc) + timedelta(days=30)  # 30 days from now

        invoice = Invoice(
            invoice_number=invoice_number,
            client_id=project.client_id,
            project_id=project_id,
            user_id=user_id,
            status="draft",
            currency="USD",  # Default currency - can be enhanced later
            subtotal=remaining_balance,
            total_amount=remaining_balance,
            due_date=due_date,
            notes=f"Final payment for project completion - {project.title}",
        )

        self.db.add(invoice)
        self.db.flush()

        # Create final payment item
        invoice_item = InvoiceItem(
            invoice_id=invoice.id,
            description=f"Final Payment - {project.title}",
            quantity=Decimal("1.00"),
            unit_price=remaining_balance,
            total_price=remaining_balance,
            item_type="final_payment",
            sequence_number=1,
        )
        self.db.add(invoice_item)

        self.db.commit()

        return {
            "invoice_id": invoice.id,
            "invoice_number": invoice.invoice_number,
            "final_amount": float(remaining_balance),
            "total_project_value": float(total_project_value),
            "previously_invoiced": float(total_invoiced),
            "project_title": project.title,
            "client_name": project.client.name,
        }

    def _group_time_entries(
        self, time_entries: List[TimeEntry]
    ) -> Dict[str, List[TimeEntry]]:
        """Group time entries for better invoice presentation"""
        groups = {}

        for entry in time_entries:
            # Group by date
            date_key = (
                entry.work_date.strftime("%Y-%m-%d")
                if entry.work_date
                else "Unknown Date"
            )

            if date_key not in groups:
                groups[date_key] = []
            groups[date_key].append(entry)

        return groups

    def get_billing_suggestions(self, user_id: UUID, project_id: UUID) -> Dict:
        """Get intelligent billing suggestions for a project"""

        # Get project with related data
        project = (
            self.db.query(Project)
            .filter(Project.id == project_id, Project.user_id == user_id)
            .first()
        )

        if not project:
            return {"suggestions": [], "message": "Project not found"}

        suggestions = []

        # Check for completed milestones
        completed_milestones = [
            m
            for m in project.milestones
            if m.status == "completed" and not m.is_deleted
        ]

        if completed_milestones:
            total_milestone_value = sum(
                m.payment_amount or Decimal("0") for m in completed_milestones
            )
            suggestions.append(
                {
                    "type": "milestone_payment",
                    "priority": "high",
                    "title": f"Invoice {len(completed_milestones)} Completed Milestone(s)",
                    "description": f"${total_milestone_value:.2f} ready to invoice",
                    "action": "Create milestone invoice",
                    "data": {
                        "milestone_ids": [str(m.id) for m in completed_milestones],
                        "total_amount": float(total_milestone_value),
                    },
                }
            )

        # Check for unbilled time entries
        unbilled_time = (
            self.db.query(TimeEntry)
            .filter(
                TimeEntry.project_id == project_id,
                TimeEntry.is_billable == True,
                TimeEntry.status.in_(["approved", "submitted"]),
            )
            .all()
        )

        if unbilled_time:
            total_time_value = sum(
                entry.billable_amount or Decimal("0") for entry in unbilled_time
            )
            total_hours = sum(entry.duration_minutes / 60 for entry in unbilled_time)

            suggestions.append(
                {
                    "type": "time_materials",
                    "priority": "medium",
                    "title": f"Invoice {total_hours:.1f} Hours of Work",
                    "description": f"${total_time_value:.2f} in unbilled time",
                    "action": "Create time & materials invoice",
                    "data": {
                        "total_hours": total_hours,
                        "total_amount": float(total_time_value),
                        "entry_count": len(unbilled_time),
                    },
                }
            )

        # Check if project needs deposit
        if project.status == "draft" and project.total_budget:
            existing_invoices = (
                self.db.query(Invoice).filter(Invoice.project_id == project_id).count()
            )

            if existing_invoices == 0:
                suggestions.append(
                    {
                        "type": "deposit_invoice",
                        "priority": "high",
                        "title": "Request Project Deposit",
                        "description": f"Secure project with upfront payment",
                        "action": "Create deposit invoice",
                        "data": {
                            "suggested_percentage": 50,
                            "total_budget": float(project.total_budget),
                        },
                    }
                )

        return {
            "suggestions": suggestions,
            "project_title": project.title,
            "project_status": project.status,
            "total_suggestions": len(suggestions),
        }
