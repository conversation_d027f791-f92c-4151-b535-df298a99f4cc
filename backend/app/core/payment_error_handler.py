"""Enhanced error handling service for payment gateways"""

import logging
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Tuple, Union

import httpx
from sqlalchemy.orm import Session

from app.database import get_db
from app.models import PaymentErrorLog


class PaymentErrorType(Enum):
    """Payment error types for categorization"""

    NETWORK_ERROR = "network_error"
    AUTHENTICATION_ERROR = "authentication_error"
    VALIDATION_ERROR = "validation_error"
    INSUFFICIENT_FUNDS = "insufficient_funds"
    CARD_DECLINED = "card_declined"
    EXPIRED_CARD = "expired_card"
    INVALID_CARD = "invalid_card"
    GATEWAY_ERROR = "gateway_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    CONFIGURATION_ERROR = "configuration_error"
    WEBHOOK_ERROR = "webhook_error"
    UNKNOWN_ERROR = "unknown_error"


class PaymentErrorSeverity(Enum):
    """Payment error severity levels"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class PaymentError(Exception):
    """Enhanced payment error with categorization and retry logic"""

    def __init__(
        self,
        message: str,
        error_type: PaymentErrorType = PaymentErrorType.UNKNOWN_ERROR,
        severity: PaymentErrorSeverity = PaymentErrorSeverity.MEDIUM,
        gateway: str = None,
        reference: str = None,
        retry_after: int = None,
        retryable: bool = False,
        gateway_response: Dict = None,
    ):
        super().__init__(message)
        self.error_type = error_type
        self.severity = severity
        self.gateway = gateway
        self.reference = reference
        self.retry_after = retry_after
        self.retryable = retryable
        self.gateway_response = gateway_response or {}
        self.timestamp = datetime.now(timezone.utc)


class PaymentErrorHandler:
    """Enhanced error handler for payment processing"""

    def __init__(self, db: Session):
        self.db = db
        self.logger = logging.getLogger(__name__)

        # Error mapping for different gateways
        self.paystack_error_mapping = {
            "Invalid key": (
                PaymentErrorType.AUTHENTICATION_ERROR,
                PaymentErrorSeverity.HIGH,
            ),
            "Invalid amount": (
                PaymentErrorType.VALIDATION_ERROR,
                PaymentErrorSeverity.MEDIUM,
            ),
            "Insufficient funds": (
                PaymentErrorType.INSUFFICIENT_FUNDS,
                PaymentErrorSeverity.LOW,
            ),
            "Card declined": (PaymentErrorType.CARD_DECLINED, PaymentErrorSeverity.LOW),
            "Expired card": (PaymentErrorType.EXPIRED_CARD, PaymentErrorSeverity.LOW),
            "Invalid card number": (
                PaymentErrorType.INVALID_CARD,
                PaymentErrorSeverity.LOW,
            ),
            "Rate limit exceeded": (
                PaymentErrorType.RATE_LIMIT_ERROR,
                PaymentErrorSeverity.MEDIUM,
            ),
        }

        self.dpo_error_mapping = {
            "Invalid company token": (
                PaymentErrorType.AUTHENTICATION_ERROR,
                PaymentErrorSeverity.HIGH,
            ),
            "Transaction not found": (
                PaymentErrorType.VALIDATION_ERROR,
                PaymentErrorSeverity.MEDIUM,
            ),
            "Payment declined": (
                PaymentErrorType.CARD_DECLINED,
                PaymentErrorSeverity.LOW,
            ),
            "Invalid amount": (
                PaymentErrorType.VALIDATION_ERROR,
                PaymentErrorSeverity.MEDIUM,
            ),
            "Service unavailable": (
                PaymentErrorType.GATEWAY_ERROR,
                PaymentErrorSeverity.HIGH,
            ),
        }

    def handle_http_error(
        self, error: httpx.HTTPError, gateway: str, reference: str = None
    ) -> PaymentError:
        """Handle HTTP errors from payment gateways"""

        if isinstance(error, httpx.TimeoutException):
            return PaymentError(
                message=f"Request timeout to {gateway}",
                error_type=PaymentErrorType.NETWORK_ERROR,
                severity=PaymentErrorSeverity.MEDIUM,
                gateway=gateway,
                reference=reference,
                retryable=True,
                retry_after=30,
            )

        elif isinstance(error, httpx.ConnectError):
            return PaymentError(
                message=f"Connection error to {gateway}",
                error_type=PaymentErrorType.NETWORK_ERROR,
                severity=PaymentErrorSeverity.HIGH,
                gateway=gateway,
                reference=reference,
                retryable=True,
                retry_after=60,
            )

        elif hasattr(error, "response") and error.response:
            status_code = error.response.status_code

            if status_code == 401:
                return PaymentError(
                    message=f"Authentication failed with {gateway}",
                    error_type=PaymentErrorType.AUTHENTICATION_ERROR,
                    severity=PaymentErrorSeverity.HIGH,
                    gateway=gateway,
                    reference=reference,
                    retryable=False,
                )

            elif status_code == 429:
                retry_after = int(error.response.headers.get("Retry-After", 300))
                return PaymentError(
                    message=f"Rate limit exceeded for {gateway}",
                    error_type=PaymentErrorType.RATE_LIMIT_ERROR,
                    severity=PaymentErrorSeverity.MEDIUM,
                    gateway=gateway,
                    reference=reference,
                    retryable=True,
                    retry_after=retry_after,
                )

            elif 500 <= status_code < 600:
                return PaymentError(
                    message=f"Gateway error from {gateway}: {status_code}",
                    error_type=PaymentErrorType.GATEWAY_ERROR,
                    severity=PaymentErrorSeverity.HIGH,
                    gateway=gateway,
                    reference=reference,
                    retryable=True,
                    retry_after=120,
                )

        return PaymentError(
            message=f"HTTP error with {gateway}: {str(error)}",
            error_type=PaymentErrorType.NETWORK_ERROR,
            severity=PaymentErrorSeverity.MEDIUM,
            gateway=gateway,
            reference=reference,
            retryable=True,
            retry_after=60,
        )

    def handle_gateway_error(
        self,
        error_message: str,
        gateway: str,
        reference: str = None,
        gateway_response: Dict = None,
    ) -> PaymentError:
        """Handle gateway-specific errors"""

        error_mapping = (
            self.paystack_error_mapping
            if gateway == "paystack"
            else self.dpo_error_mapping
            if gateway == "dpo"
            else {}
        )

        # Find matching error pattern
        error_type = PaymentErrorType.UNKNOWN_ERROR
        severity = PaymentErrorSeverity.MEDIUM
        retryable = False

        for pattern, (mapped_type, mapped_severity) in error_mapping.items():
            if pattern.lower() in error_message.lower():
                error_type = mapped_type
                severity = mapped_severity

                # Determine if error is retryable
                retryable = error_type in [
                    PaymentErrorType.NETWORK_ERROR,
                    PaymentErrorType.GATEWAY_ERROR,
                    PaymentErrorType.RATE_LIMIT_ERROR,
                ]
                break

        retry_after = None
        if retryable:
            if error_type == PaymentErrorType.RATE_LIMIT_ERROR:
                retry_after = 300  # 5 minutes for rate limits
            elif error_type == PaymentErrorType.GATEWAY_ERROR:
                retry_after = 120  # 2 minutes for gateway errors
            else:
                retry_after = 60  # 1 minute for other retryable errors

        return PaymentError(
            message=error_message,
            error_type=error_type,
            severity=severity,
            gateway=gateway,
            reference=reference,
            retryable=retryable,
            retry_after=retry_after,
            gateway_response=gateway_response,
        )

    def handle_validation_error(
        self, error_message: str, gateway: str, reference: str = None
    ) -> PaymentError:
        """Handle validation errors"""

        return PaymentError(
            message=error_message,
            error_type=PaymentErrorType.VALIDATION_ERROR,
            severity=PaymentErrorSeverity.MEDIUM,
            gateway=gateway,
            reference=reference,
            retryable=False,
        )

    def handle_configuration_error(
        self, error_message: str, gateway: str
    ) -> PaymentError:
        """Handle configuration errors"""

        return PaymentError(
            message=error_message,
            error_type=PaymentErrorType.CONFIGURATION_ERROR,
            severity=PaymentErrorSeverity.CRITICAL,
            gateway=gateway,
            retryable=False,
        )

    async def log_error(self, error: PaymentError, context: Dict = None) -> None:
        """Log payment error to database and logger"""

        try:
            # Log to application logger
            log_level = {
                PaymentErrorSeverity.LOW: logging.INFO,
                PaymentErrorSeverity.MEDIUM: logging.WARNING,
                PaymentErrorSeverity.HIGH: logging.ERROR,
                PaymentErrorSeverity.CRITICAL: logging.CRITICAL,
            }.get(error.severity, logging.WARNING)

            self.logger.log(
                log_level,
                f"Payment error - Gateway: {error.gateway}, Type: {error.error_type.value}, "
                f"Reference: {error.reference}, Message: {str(error)}",
            )

            # Log to database
            error_log = PaymentErrorLog(
                gateway=error.gateway,
                error_type=error.error_type.value,
                severity=error.severity.value,
                message=str(error),
                reference=error.reference,
                retryable=error.retryable,
                retry_after=error.retry_after,
                gateway_response=error.gateway_response,
                context=context or {},
                created_at=error.timestamp,
            )

            self.db.add(error_log)
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to log payment error: {str(e)}", exc_info=True)
            # Don't re-raise to avoid masking the original error

    def get_error_statistics(
        self, gateway: str = None, hours: int = 24
    ) -> Dict[str, any]:
        """Get error statistics for monitoring"""

        try:
            from datetime import timedelta

            since = datetime.now(timezone.utc) - timedelta(hours=hours)

            query = self.db.query(PaymentErrorLog).filter(
                PaymentErrorLog.created_at >= since
            )

            if gateway:
                query = query.filter(PaymentErrorLog.gateway == gateway)

            errors = query.all()

            # Calculate statistics
            total_errors = len(errors)
            error_types = {}
            severity_counts = {}
            gateway_counts = {}

            for error in errors:
                # Count by error type
                error_types[error.error_type] = error_types.get(error.error_type, 0) + 1

                # Count by severity
                severity_counts[error.severity] = (
                    severity_counts.get(error.severity, 0) + 1
                )

                # Count by gateway
                gateway_counts[error.gateway] = gateway_counts.get(error.gateway, 0) + 1

            return {
                "total_errors": total_errors,
                "error_types": error_types,
                "severity_counts": severity_counts,
                "gateway_counts": gateway_counts,
                "period_hours": hours,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            self.logger.error(
                f"Failed to get error statistics: {str(e)}", exc_info=True
            )
            return {
                "error": "Failed to retrieve statistics",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    def should_retry(
        self, error: PaymentError, attempt_count: int, max_attempts: int = 3
    ) -> Tuple[bool, int]:
        """Determine if an error should be retried and calculate delay"""

        if not error.retryable or attempt_count >= max_attempts:
            return False, 0

        # Calculate exponential backoff with jitter
        base_delay = error.retry_after or 60
        exponential_delay = base_delay * (2 ** (attempt_count - 1))

        # Add jitter (±25%)
        import random

        jitter = random.uniform(0.75, 1.25)
        final_delay = int(exponential_delay * jitter)

        # Cap maximum delay at 10 minutes
        final_delay = min(final_delay, 600)

        return True, final_delay

    def create_error_summary(self, errors: List[PaymentError]) -> Dict[str, any]:
        """Create a summary of multiple errors"""

        if not errors:
            return {"total_errors": 0}

        error_types = {}
        severities = {}
        gateways = {}
        retryable_count = 0

        for error in errors:
            # Count error types
            error_type = error.error_type.value
            error_types[error_type] = error_types.get(error_type, 0) + 1

            # Count severities
            severity = error.severity.value
            severities[severity] = severities.get(severity, 0) + 1

            # Count gateways
            if error.gateway:
                gateways[error.gateway] = gateways.get(error.gateway, 0) + 1

            # Count retryable errors
            if error.retryable:
                retryable_count += 1

        return {
            "total_errors": len(errors),
            "error_types": error_types,
            "severities": severities,
            "gateways": gateways,
            "retryable_count": retryable_count,
            "most_common_error": (
                max(error_types.items(), key=lambda x: x[1])[0] if error_types else None
            ),
            "highest_severity": max(severities.keys()) if severities else None,
        }
