"""Monitoring Dashboard Configuration for DevHQ Backend"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List


class DashboardType(Enum):
    """Types of monitoring dashboards"""

    OVERVIEW = "overview"
    API_PERFORMANCE = "api_performance"
    BUSINESS_METRICS = "business_metrics"
    INFRASTRUCTURE = "infrastructure"
    ERRORS = "errors"


class ChartType(Enum):
    """Types of charts for dashboard widgets"""

    LINE = "line"
    BAR = "bar"
    GAUGE = "gauge"
    COUNTER = "counter"
    HEATMAP = "heatmap"
    TABLE = "table"


@dataclass
class DashboardWidget:
    """Configuration for a dashboard widget"""

    title: str
    chart_type: ChartType
    metric_query: str
    description: str
    width: int = 6  # Grid width (1-12)
    height: int = 4  # Grid height
    refresh_interval: int = 30  # Seconds
    thresholds: Dict[str, float] = None


@dataclass
class Dashboard:
    """Configuration for a monitoring dashboard"""

    name: str
    dashboard_type: DashboardType
    description: str
    widgets: List[DashboardWidget]
    tags: List[str] = None


class MonitoringDashboards:
    """Pre-configured monitoring dashboards for DevHQ"""

    @staticmethod
    def get_overview_dashboard() -> Dashboard:
        """Main overview dashboard with key metrics"""
        widgets = [
            DashboardWidget(
                title="Request Rate",
                chart_type=ChartType.LINE,
                metric_query="rate(http_requests_total[5m])",
                description="HTTP requests per second",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Response Time P95",
                chart_type=ChartType.LINE,
                metric_query="histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                description="95th percentile response time",
                width=6,
                height=4,
                thresholds={"warning": 0.5, "critical": 1.0},
            ),
            DashboardWidget(
                title="Error Rate",
                chart_type=ChartType.LINE,
                metric_query='rate(http_requests_total{status=~"5.."}[5m])',
                description="5xx error rate per second",
                width=6,
                height=4,
                thresholds={"warning": 0.01, "critical": 0.05},
            ),
            DashboardWidget(
                title="Active Users",
                chart_type=ChartType.GAUGE,
                metric_query="active_websocket_connections",
                description="Currently active WebSocket connections",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Database Connections",
                chart_type=ChartType.GAUGE,
                metric_query="db_connection_pool_active",
                description="Active database connections",
                width=4,
                height=3,
            ),
            DashboardWidget(
                title="Cache Hit Rate",
                chart_type=ChartType.GAUGE,
                metric_query="rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))",
                description="Cache hit rate percentage",
                width=4,
                height=3,
                thresholds={"warning": 0.8, "critical": 0.6},
            ),
            DashboardWidget(
                title="Payment Success Rate",
                chart_type=ChartType.GAUGE,
                metric_query='rate(payment_transactions_total{status="success"}[5m]) / rate(payment_transactions_total[5m])',
                description="Payment transaction success rate",
                width=4,
                height=3,
                thresholds={"warning": 0.95, "critical": 0.9},
            ),
        ]

        return Dashboard(
            name="DevHQ Overview",
            dashboard_type=DashboardType.OVERVIEW,
            description="High-level overview of system health and performance",
            widgets=widgets,
            tags=["overview", "health", "performance"],
        )

    @staticmethod
    def get_api_performance_dashboard() -> Dashboard:
        """Detailed API performance metrics"""
        widgets = [
            DashboardWidget(
                title="Request Duration by Endpoint",
                chart_type=ChartType.HEATMAP,
                metric_query="histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) by (endpoint)",
                description="95th percentile response time by endpoint",
                width=12,
                height=6,
            ),
            DashboardWidget(
                title="Request Volume by Endpoint",
                chart_type=ChartType.BAR,
                metric_query="rate(http_requests_total[5m]) by (endpoint)",
                description="Request rate per endpoint",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Error Rate by Endpoint",
                chart_type=ChartType.BAR,
                metric_query='rate(http_requests_total{status=~"4..|5.."}[5m]) by (endpoint)',
                description="Error rate per endpoint",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Slowest Endpoints",
                chart_type=ChartType.TABLE,
                metric_query="topk(10, histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) by (endpoint))",
                description="Top 10 slowest endpoints by P95 latency",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Database Query Performance",
                chart_type=ChartType.LINE,
                metric_query="histogram_quantile(0.95, rate(database_operations_duration_seconds_bucket[5m]))",
                description="Database query P95 latency",
                width=6,
                height=4,
            ),
        ]

        return Dashboard(
            name="API Performance",
            dashboard_type=DashboardType.API_PERFORMANCE,
            description="Detailed API performance and latency metrics",
            widgets=widgets,
            tags=["api", "performance", "latency"],
        )

    @staticmethod
    def get_business_metrics_dashboard() -> Dashboard:
        """Business-specific metrics dashboard"""
        widgets = [
            DashboardWidget(
                title="Invoices Created",
                chart_type=ChartType.LINE,
                metric_query="rate(invoices_created_total[1h])",
                description="Invoice creation rate per hour",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Payment Volume",
                chart_type=ChartType.LINE,
                metric_query="rate(payment_transactions_total[1h])",
                description="Payment transaction rate per hour",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Timer Sessions",
                chart_type=ChartType.LINE,
                metric_query="rate(timer_sessions_total[1h])",
                description="Timer session creation rate per hour",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Payment Gateway Distribution",
                chart_type=ChartType.BAR,
                metric_query="rate(payment_transactions_total[1h]) by (gateway)",
                description="Payment volume by gateway",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Revenue Trend",
                chart_type=ChartType.LINE,
                metric_query='sum(rate(payment_transactions_total{status="success"}[1h])) * avg(payment_amount)',
                description="Estimated revenue trend",
                width=12,
                height=4,
            ),
        ]

        return Dashboard(
            name="Business Metrics",
            dashboard_type=DashboardType.BUSINESS_METRICS,
            description="Key business metrics and KPIs",
            widgets=widgets,
            tags=["business", "revenue", "kpi"],
        )

    @staticmethod
    def get_infrastructure_dashboard() -> Dashboard:
        """Infrastructure and system metrics"""
        widgets = [
            DashboardWidget(
                title="CPU Usage",
                chart_type=ChartType.LINE,
                metric_query="process_cpu_seconds_total",
                description="CPU usage over time",
                width=6,
                height=4,
                thresholds={"warning": 70, "critical": 90},
            ),
            DashboardWidget(
                title="Memory Usage",
                chart_type=ChartType.LINE,
                metric_query="process_resident_memory_bytes",
                description="Memory usage over time",
                width=6,
                height=4,
                thresholds={"warning": 0.8, "critical": 0.95},
            ),
            DashboardWidget(
                title="Database Connection Pool",
                chart_type=ChartType.LINE,
                metric_query="db_connection_pool_active",
                description="Active database connections",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Cache Performance",
                chart_type=ChartType.LINE,
                metric_query="rate(cache_operations_total[5m]) by (operation)",
                description="Cache operations rate",
                width=6,
                height=4,
            ),
        ]

        return Dashboard(
            name="Infrastructure",
            dashboard_type=DashboardType.INFRASTRUCTURE,
            description="System infrastructure and resource metrics",
            widgets=widgets,
            tags=["infrastructure", "system", "resources"],
        )

    @staticmethod
    def get_errors_dashboard() -> Dashboard:
        """Error tracking and alerting dashboard"""
        widgets = [
            DashboardWidget(
                title="Error Rate Trend",
                chart_type=ChartType.LINE,
                metric_query='rate(http_requests_total{status=~"4..|5.."}[5m])',
                description="Overall error rate trend",
                width=12,
                height=4,
            ),
            DashboardWidget(
                title="Errors by Status Code",
                chart_type=ChartType.BAR,
                metric_query='rate(http_requests_total{status=~"4..|5.."}[5m]) by (status)',
                description="Error distribution by HTTP status code",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Payment Failures",
                chart_type=ChartType.LINE,
                metric_query='rate(payment_transactions_total{status="failed"}[5m])',
                description="Payment failure rate",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Database Errors",
                chart_type=ChartType.LINE,
                metric_query='rate(database_operations_total{status="error"}[5m])',
                description="Database operation error rate",
                width=6,
                height=4,
            ),
            DashboardWidget(
                title="Top Error Endpoints",
                chart_type=ChartType.TABLE,
                metric_query='topk(10, rate(http_requests_total{status=~"4..|5.."}[5m]) by (endpoint))',
                description="Endpoints with highest error rates",
                width=6,
                height=4,
            ),
        ]

        return Dashboard(
            name="Error Tracking",
            dashboard_type=DashboardType.ERRORS,
            description="Error monitoring and alerting dashboard",
            widgets=widgets,
            tags=["errors", "alerts", "troubleshooting"],
        )

    @classmethod
    def get_all_dashboards(cls) -> List[Dashboard]:
        """Get all pre-configured dashboards"""
        return [
            cls.get_overview_dashboard(),
            cls.get_api_performance_dashboard(),
            cls.get_business_metrics_dashboard(),
            cls.get_infrastructure_dashboard(),
            cls.get_errors_dashboard(),
        ]

    @classmethod
    def export_dashboard_config(cls, dashboard: Dashboard) -> Dict[str, Any]:
        """Export dashboard configuration for external tools (Grafana, etc.)"""
        return {
            "dashboard": {
                "title": dashboard.name,
                "description": dashboard.description,
                "tags": dashboard.tags or [],
                "panels": [
                    {
                        "title": widget.title,
                        "type": widget.chart_type.value,
                        "targets": [{"expr": widget.metric_query, "refId": "A"}],
                        "gridPos": {"w": widget.width, "h": widget.height},
                        "options": {"description": widget.description},
                        "alert": (
                            {"conditions": widget.thresholds}
                            if widget.thresholds
                            else None
                        ),
                    }
                    for widget in dashboard.widgets
                ],
            }
        }
