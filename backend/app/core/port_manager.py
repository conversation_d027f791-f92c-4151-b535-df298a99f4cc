"""
Smart port management for DevHQ development server
Automatically finds available ports when the default is in use
"""

import socket
import subprocess
import sys
from typing import List, Optional, Tuple


class PortManager:
    """Manages port allocation for development servers"""

    def __init__(
        self, preferred_port: int = 8000, port_range: Tuple[int, int] = (8000, 8100)
    ):
        self.preferred_port = preferred_port
        self.port_range = port_range

    def is_port_available(self, port: int) -> bool:
        """Check if a port is available for use"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(("localhost", port))
                return result != 0
        except Exception:
            return False

    def get_port_info(self, port: int) -> Optional[str]:
        """Get information about what's using a port"""
        try:
            # Try to get process info using lsof
            result = subprocess.run(
                ["lsof", "-i", f":{port}", "-t"],
                capture_output=True,
                text=True,
                timeout=5,
            )

            if result.returncode == 0 and result.stdout.strip():
                pid = result.stdout.strip().split("\n")[0]

                # Get process name
                proc_result = subprocess.run(
                    ["ps", "-p", pid, "-o", "comm="],
                    capture_output=True,
                    text=True,
                    timeout=5,
                )

                if proc_result.returncode == 0:
                    process_name = proc_result.stdout.strip()
                    return f"{process_name} (PID: {pid})"

            return "Unknown process"

        except (
            subprocess.TimeoutExpired,
            subprocess.SubprocessError,
            FileNotFoundError,
        ):
            return "Unable to determine"

    def find_available_port(self) -> int:
        """Find the next available port starting from preferred_port"""
        # First try the preferred port
        if self.is_port_available(self.preferred_port):
            return self.preferred_port

        # If preferred port is taken, search in range
        start_port, end_port = self.port_range

        for port in range(start_port, end_port + 1):
            if port == self.preferred_port:
                continue  # Already checked

            if self.is_port_available(port):
                return port

        # If no port found in range, raise exception
        raise RuntimeError(f"No available ports found in range {start_port}-{end_port}")

    def get_port_with_info(self) -> Tuple[int, Optional[str]]:
        """Get available port and info about why preferred port wasn't used"""
        if self.is_port_available(self.preferred_port):
            return self.preferred_port, None

        # Preferred port is taken, get info about what's using it
        port_info = self.get_port_info(self.preferred_port)
        available_port = self.find_available_port()

        return available_port, f"Port {self.preferred_port} is in use by: {port_info}"

    def kill_process_on_port(self, port: int) -> bool:
        """Kill the process using a specific port (use with caution!)"""
        try:
            # Get PID of process using the port
            result = subprocess.run(
                ["lsof", "-i", f":{port}", "-t"],
                capture_output=True,
                text=True,
                timeout=5,
            )

            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split("\n")

                for pid in pids:
                    try:
                        subprocess.run(["kill", "-9", pid], timeout=5)
                        print(f"✅ Killed process {pid} on port {port}")
                    except subprocess.SubprocessError:
                        print(f"❌ Failed to kill process {pid}")
                        return False

                return True

            return False

        except (
            subprocess.TimeoutExpired,
            subprocess.SubprocessError,
            FileNotFoundError,
        ):
            return False


def smart_port_selection(
    preferred_port: int = 8000, kill_existing: bool = False
) -> int:
    """
    Smart port selection with user interaction

    Args:
        preferred_port: The port you'd prefer to use
        kill_existing: Whether to automatically kill existing processes

    Returns:
        Available port number
    """
    manager = PortManager(preferred_port)

    if manager.is_port_available(preferred_port):
        print(f"✅ Port {preferred_port} is available!")
        return preferred_port

    # Port is taken, get info
    port_info = manager.get_port_info(preferred_port)
    print(f"⚠️  Port {preferred_port} is already in use by: {port_info}")

    if kill_existing:
        print(f"🔄 Attempting to free port {preferred_port}...")
        if manager.kill_process_on_port(preferred_port):
            if manager.is_port_available(preferred_port):
                print(f"✅ Port {preferred_port} is now available!")
                return preferred_port

    # Find alternative port
    try:
        available_port = manager.find_available_port()
        print(f"🔄 Using alternative port: {available_port}")
        return available_port
    except RuntimeError as e:
        print(f"❌ {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Test the port manager
    port = smart_port_selection(8000)
    print(f"Selected port: {port}")
