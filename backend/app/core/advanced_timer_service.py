"""
Advanced Timer Service for DevHQ Day 9
Handles multi-device timer conflicts, smart suggestions, and webhook integrations
"""

import asyncio
import json
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set
from uuid import UUID

import httpx
from sqlalchemy import and_, desc, func
from sqlalchemy.orm import Session

from ..core.exceptions import TimerConflictError, TimerNotFoundError
from ..models.project import Project, TimeEntry
from ..models.user import User


class AdvancedTimerService:
    """Enhanced timer service with conflict resolution and AI features"""

    def __init__(self, db: Session, redis_client=None):
        self.db = db
        self.redis = redis_client
        self.active_timers_key = "active_timers"
        self.webhook_endpoints: Set[str] = set()

    async def start_smart_timer(
        self,
        user_id: UUID,
        project_id: UUID,
        description: str = "",
        device_id: str = "web",
        auto_resolve_conflicts: bool = True,
        context_data: Optional[Dict] = None,
    ) -> Dict:
        """Start timer with intelligent conflict detection and resolution"""

        # Check for existing active timers
        conflicts = await self.detect_active_conflicts(user_id, device_id)

        if conflicts and auto_resolve_conflicts:
            # Automatically resolve conflicts using AI
            resolution_result = await self.resolve_conflicts_intelligently(conflicts)
        elif conflicts and not auto_resolve_conflicts:
            # Return conflicts for manual resolution
            return {
                "status": "conflict_detected",
                "conflicts": conflicts,
                "suggested_resolutions": [
                    await self._suggest_conflict_resolution(c) for c in conflicts
                ],
            }

        # Start the new timer
        timer_entry = self._create_timer_entry(
            user_id, project_id, description, device_id, context_data
        )

        # Store in Redis for real-time tracking
        if self.redis:
            await self._store_active_timer(timer_entry)

        # Send webhook notifications
        await self._send_timer_webhooks("timer_started", timer_entry)

        # Generate smart suggestions for this session
        suggestions = await self._generate_session_suggestions(user_id, project_id)

        return {
            "status": "started",
            "timer": self._serialize_timer(timer_entry),
            "suggestions": suggestions,
            "estimated_duration": await self._predict_session_duration(
                user_id, project_id
            ),
        }

    async def detect_active_conflicts(
        self, user_id: UUID, device_id: str
    ) -> List[Dict]:
        """Detect conflicts with currently active timers across devices"""
        conflicts = []

        # Check database for active timers
        active_timers = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.end_time.is_(None),
                    TimeEntry.is_timer_active == True,
                )
            )
            .all()
        )

        # Check Redis for real-time active timers
        if self.redis:
            redis_timers = await self._get_redis_active_timers(user_id)
            active_timers.extend(redis_timers)

        # Analyze conflicts
        for timer in active_timers:
            if hasattr(timer, "device_id") and timer.device_id != device_id:
                conflicts.append(
                    {
                        "timer_id": str(timer.id),
                        "device_id": getattr(timer, "device_id", "unknown"),
                        "start_time": timer.start_time.isoformat(),
                        "description": timer.description,
                        "project_id": (
                            str(timer.project_id) if timer.project_id else None
                        ),
                        "conflict_type": "multi_device",
                    }
                )

        return conflicts

    async def resolve_conflicts_intelligently(self, conflicts: List[Dict]) -> Dict:
        """Use AI to automatically resolve timer conflicts"""
        resolution_actions = []

        for conflict in conflicts:
            # Analyze conflict context
            timer_id = UUID(conflict["timer_id"])
            timer_entry = (
                self.db.query(TimeEntry).filter(TimeEntry.id == timer_id).first()
            )

            if not timer_entry:
                continue

            # Determine resolution strategy
            strategy = await self._determine_resolution_strategy(timer_entry, conflict)

            if strategy == "stop_previous":
                # Stop the conflicting timer
                timer_entry.end_time = datetime.now(timezone.utc)
                timer_entry.is_active = False
                self.db.commit()

                # Remove from Redis
                if self.redis:
                    await self._remove_active_timer(timer_id)

                resolution_actions.append(
                    {
                        "action": "stopped_timer",
                        "timer_id": str(timer_id),
                        "reason": "Automatically resolved conflict",
                    }
                )

            elif strategy == "merge_sessions":
                # Mark for manual review but allow new timer
                resolution_actions.append(
                    {
                        "action": "flagged_for_review",
                        "timer_id": str(timer_id),
                        "reason": "Sessions may need merging",
                    }
                )

        return {
            "resolved": True,
            "actions": resolution_actions,
            "strategy": "intelligent_auto_resolution",
        }

    async def _get_user_productivity_patterns(self, user_id: UUID) -> Dict:
        """Get user productivity patterns from historical data"""
        # Get recent time entries for pattern analysis
        recent_entries = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.start_time
                    >= datetime.now(timezone.utc) - timedelta(days=30),
                )
            )
            .all()
        )

        if not recent_entries:
            return {}

        # Analyze hourly productivity patterns
        hourly_productivity = {}
        low_productivity_hours = []

        for entry in recent_entries:
            hour = entry.start_time.hour
            productivity_score = getattr(entry, "productivity_score", 5)  # Default to 5

            if hour not in hourly_productivity:
                hourly_productivity[hour] = []
            hourly_productivity[hour].append(productivity_score)

        # Calculate average productivity per hour and identify low hours
        for hour, scores in hourly_productivity.items():
            avg_score = sum(scores) / len(scores)
            if avg_score < 4:  # Below average productivity
                low_productivity_hours.append(hour)

        return {
            "hourly_productivity": hourly_productivity,
            "low_productivity_hours": low_productivity_hours,
            "total_entries_analyzed": len(recent_entries),
        }

    async def _get_project_specific_suggestions(
        self, user_id: UUID, project_id: UUID
    ) -> List[Dict]:
        """Get project-specific suggestions based on historical data"""
        suggestions = []

        # Get common tasks for this project
        common_tasks = await self._get_common_project_tasks(user_id, project_id)

        for task in common_tasks[:2]:  # Limit to top 2 suggestions
            suggestions.append(
                {
                    "type": "project_task_suggestion",
                    "message": f"Consider working on: {task['description']}",
                    "priority": "low",
                    "action": "suggest_task",
                    "estimated_duration": task.get("avg_duration", 1.0),
                }
            )

        return suggestions

    async def generate_productivity_suggestions(self, user_id: UUID) -> List[Dict]:
        """Generate real-time productivity suggestions during timer sessions"""

        # Get current session context
        current_timer = await self._get_current_active_timer(user_id)
        if not current_timer:
            return []

        suggestions = []

        # Analyze current session duration
        session_duration = (
            datetime.now(timezone.utc) - current_timer.start_time
        ).total_seconds() / 3600

        if session_duration > 2:  # 2+ hours
            suggestions.append(
                {
                    "type": "break_reminder",
                    "message": "You've been working for over 2 hours. Consider taking a 15-minute break.",
                    "priority": "high",
                    "action": "suggest_break",
                }
            )

        # Analyze productivity patterns
        historical_data = await self._get_user_productivity_patterns(user_id)

        if historical_data:
            current_hour = datetime.now().hour
            if current_hour in historical_data.get("low_productivity_hours", []):
                suggestions.append(
                    {
                        "type": "productivity_warning",
                        "message": "This is typically a low-productivity hour for you. Consider scheduling easier tasks.",
                        "priority": "medium",
                        "action": "adjust_expectations",
                    }
                )

        # Project-specific suggestions
        if current_timer.project_id:
            project_suggestions = await self._get_project_specific_suggestions(
                user_id, current_timer.project_id
            )
            suggestions.extend(project_suggestions)

        return suggestions

    async def setup_timer_webhooks(
        self, webhook_url: str, events: List[str] = None
    ) -> bool:
        """Setup webhook endpoints for timer events"""
        if events is None:
            events = [
                "timer_started",
                "timer_stopped",
                "timer_paused",
                "productivity_alert",
            ]

        # Validate webhook URL
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    webhook_url,
                    json={
                        "event": "webhook_test",
                        "timestamp": datetime.now().isoformat(),
                    },
                    timeout=5.0,
                )
                if response.status_code == 200:
                    self.webhook_endpoints.add(webhook_url)
                    return True
        except Exception:
            return False

        return False

    async def _send_timer_webhooks(self, event: str, timer_data: TimeEntry) -> None:
        """Send webhook notifications for timer events"""
        if not self.webhook_endpoints:
            return

        webhook_payload = {
            "event": event,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "timer": self._serialize_timer(timer_data),
            "user_id": str(timer_data.user_id),
        }

        # Send to all registered webhooks
        async with httpx.AsyncClient() as client:
            tasks = []
            for webhook_url in self.webhook_endpoints:
                task = client.post(webhook_url, json=webhook_payload, timeout=5.0)
                tasks.append(task)

            # Send all webhooks concurrently
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

    async def _predict_session_duration(self, user_id: UUID, project_id: UUID) -> float:
        """Predict likely session duration based on historical data"""

        # Get recent sessions for this project
        recent_sessions = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.project_id == project_id,
                    TimeEntry.end_time.isnot(None),
                    TimeEntry.start_time
                    >= datetime.now(timezone.utc) - timedelta(days=30),
                )
            )
            .limit(20)
            .all()
        )

        if not recent_sessions:
            return 2.0  # Default 2 hours

        # Calculate average duration
        durations = [
            (session.end_time - session.start_time).total_seconds() / 3600
            for session in recent_sessions
            if session.end_time is not None
        ]

        if not durations:
            return 2.0  # Default 2 hours if no completed sessions

        # Use median for more robust prediction
        durations.sort()
        median_duration = durations[len(durations) // 2]

        return round(median_duration, 1)

    async def _generate_session_suggestions(
        self, user_id: UUID, project_id: UUID
    ) -> List[Dict]:
        """Generate suggestions for the current timer session"""
        suggestions = []

        # Get project context
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return suggestions

        # Suggest common tasks for this project
        common_tasks = await self._get_common_project_tasks(user_id, project_id)
        for task in common_tasks[:3]:
            suggestions.append(
                {
                    "type": "task_suggestion",
                    "description": task["description"],
                    "estimated_duration": task["avg_duration"],
                    "confidence": task["frequency_score"],
                }
            )

        # Time-based suggestions
        current_hour = datetime.now().hour
        if 9 <= current_hour <= 11:
            suggestions.append(
                {
                    "type": "time_suggestion",
                    "description": "Morning focus time - great for complex tasks",
                    "estimated_duration": 2.0,
                    "confidence": 80,
                }
            )

        return suggestions

    def _create_timer_entry(
        self,
        user_id: UUID,
        project_id: UUID,
        description: str,
        device_id: str,
        context_data: Optional[Dict],
    ) -> TimeEntry:
        """Create a new timer entry with enhanced metadata"""

        timer_entry = TimeEntry(
            id=uuid.uuid4(),
            user_id=user_id,
            project_id=project_id,
            description=description,
            start_time=datetime.now(timezone.utc),
            duration_minutes=0,
            work_date=datetime.now(timezone.utc),
            is_timer_active=True,
        )

        # Add device and context information if available
        if hasattr(timer_entry, "device_id"):
            timer_entry.device_id = device_id

        if hasattr(timer_entry, "context_data") and context_data:
            timer_entry.context_data = context_data

        self.db.add(timer_entry)
        self.db.commit()
        self.db.refresh(timer_entry)

        return timer_entry

    async def _store_active_timer(self, timer_entry: TimeEntry) -> None:
        """Store active timer in Redis for real-time tracking"""
        if not self.redis:
            return

        timer_data = {
            "id": str(timer_entry.id),
            "user_id": str(timer_entry.user_id),
            "project_id": (
                str(timer_entry.project_id) if timer_entry.project_id else None
            ),
            "start_time": timer_entry.start_time.isoformat(),
            "description": timer_entry.description,
            "device_id": getattr(timer_entry, "device_id", "web"),
        }

        # Store with expiration (24 hours)
        await self.redis.hset(
            f"{self.active_timers_key}:{timer_entry.user_id}",
            str(timer_entry.id),
            json.dumps(timer_data),
        )
        await self.redis.expire(
            f"{self.active_timers_key}:{timer_entry.user_id}", 86400
        )

    async def _get_redis_active_timers(self, user_id: UUID) -> List[Dict]:
        """Get active timers from Redis"""
        if not self.redis:
            return []

        timer_data = await self.redis.hgetall(f"{self.active_timers_key}:{user_id}")
        timers = []

        for timer_json in timer_data.values():
            try:
                timer = json.loads(timer_json)
                timers.append(timer)
            except json.JSONDecodeError:
                continue

        return timers

    def _serialize_timer(self, timer_entry: TimeEntry) -> Dict:
        """Serialize timer entry for API responses"""
        return {
            "id": str(timer_entry.id),
            "user_id": str(timer_entry.user_id),
            "project_id": (
                str(timer_entry.project_id) if timer_entry.project_id else None
            ),
            "description": timer_entry.description,
            "start_time": timer_entry.start_time.isoformat(),
            "end_time": (
                timer_entry.end_time.isoformat() if timer_entry.end_time else None
            ),
            "is_timer_active": timer_entry.is_timer_active,
            "device_id": getattr(timer_entry, "device_id", "web"),
        }

    async def _determine_resolution_strategy(
        self, timer_entry: TimeEntry, conflict: Dict
    ) -> str:
        """Determine the best strategy for resolving a timer conflict"""

        # Check session duration
        session_duration = (
            datetime.now(timezone.utc) - timer_entry.start_time
        ).total_seconds() / 60

        if session_duration < 5:  # Less than 5 minutes
            return "stop_previous"
        elif session_duration > 60:  # More than 1 hour
            return "merge_sessions"
        else:
            # Check if same project
            if conflict.get("project_id") == str(timer_entry.project_id):
                return "merge_sessions"
            else:
                return "stop_previous"

    async def _get_common_project_tasks(
        self, user_id: UUID, project_id: UUID
    ) -> List[Dict]:
        """Get common tasks for a project based on historical data"""

        # Get recent time entries for this project
        recent_entries = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.project_id == project_id,
                    TimeEntry.description.isnot(None),
                    TimeEntry.start_time
                    >= datetime.now(timezone.utc) - timedelta(days=60),
                )
            )
            .all()
        )

        # Analyze task patterns
        task_frequency = {}
        task_durations = {}

        for entry in recent_entries:
            if not entry.description:
                continue

            # Normalize task description (first 50 chars)
            task_key = entry.description[:50].strip().lower()

            task_frequency[task_key] = task_frequency.get(task_key, 0) + 1

            if entry.end_time:
                duration = (entry.end_time - entry.start_time).total_seconds() / 3600
                if task_key not in task_durations:
                    task_durations[task_key] = []
                task_durations[task_key].append(duration)

        # Build suggestions
        suggestions = []
        for task, frequency in task_frequency.items():
            if frequency >= 2:  # Appeared at least twice
                avg_duration = sum(task_durations.get(task, [1.0])) / len(
                    task_durations.get(task, [1.0])
                )
                suggestions.append(
                    {
                        "description": task.title(),
                        "frequency_score": min(100, frequency * 20),
                        "avg_duration": round(avg_duration, 1),
                    }
                )

        return sorted(suggestions, key=lambda x: x["frequency_score"], reverse=True)

    async def _remove_active_timer(self, timer_id: UUID) -> None:
        """Remove active timer from Redis"""
        if not self.redis:
            return

        # Find the timer in Redis and remove it
        timer_entry = self.db.query(TimeEntry).filter(TimeEntry.id == timer_id).first()
        if timer_entry:
            await self.redis.hdel(
                f"{self.active_timers_key}:{timer_entry.user_id}", str(timer_id)
            )

    async def _get_current_active_timer(self, user_id: UUID) -> Optional[TimeEntry]:
        """Get the current active timer for a user"""
        return (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.is_timer_active == True,
                    TimeEntry.end_time.is_(None),
                )
            )
            .first()
        )
