"""Monitoring Alerts Configuration for DevHQ Backend"""

import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional

import structlog

logger = structlog.get_logger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels"""

    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class AlertChannel(Enum):
    """Alert notification channels"""

    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"
    SMS = "sms"
    PAGERDUTY = "pagerduty"


@dataclass
class AlertCondition:
    """Configuration for an alert condition"""

    metric_query: str
    operator: str  # >, <, >=, <=, ==, !=
    threshold: float
    duration: str  # e.g., "5m", "1h"
    description: str


@dataclass
class AlertRule:
    """Configuration for an alert rule"""

    name: str
    severity: AlertSeverity
    condition: AlertCondition
    channels: List[AlertChannel]
    message_template: str
    enabled: bool = True
    tags: List[str] = None
    runbook_url: Optional[str] = None
    auto_resolve: bool = True
    resolve_timeout: str = "5m"


class MonitoringAlerts:
    """Pre-configured monitoring alerts for DevHQ"""

    @staticmethod
    def get_critical_alerts() -> List[AlertRule]:
        """Critical system alerts that require immediate attention"""
        return [
            AlertRule(
                name="High Error Rate",
                severity=AlertSeverity.CRITICAL,
                condition=AlertCondition(
                    metric_query='rate(http_requests_total{status=~"5.."}[5m])',
                    operator=">",
                    threshold=0.05,  # 5% error rate
                    duration="2m",
                    description="5xx error rate exceeds 5% for 2 minutes",
                ),
                channels=[
                    AlertChannel.SLACK,
                    AlertChannel.EMAIL,
                    AlertChannel.PAGERDUTY,
                ],
                message_template="🚨 CRITICAL: High error rate detected! Current rate: {{$value}}% - Check logs immediately",
                tags=["critical", "errors", "api"],
                runbook_url="https://docs.devhq.com/runbooks/high-error-rate",
            ),
            AlertRule(
                name="Service Down",
                severity=AlertSeverity.EMERGENCY,
                condition=AlertCondition(
                    metric_query="up",
                    operator="<",
                    threshold=1,
                    duration="1m",
                    description="Service is down for more than 1 minute",
                ),
                channels=[
                    AlertChannel.SLACK,
                    AlertChannel.EMAIL,
                    AlertChannel.PAGERDUTY,
                    AlertChannel.SMS,
                ],
                message_template="🔥 EMERGENCY: DevHQ Backend service is DOWN! Immediate action required!",
                tags=["emergency", "downtime", "service"],
                runbook_url="https://docs.devhq.com/runbooks/service-down",
            ),
            AlertRule(
                name="Database Connection Pool Exhausted",
                severity=AlertSeverity.CRITICAL,
                condition=AlertCondition(
                    metric_query="db_connection_pool_active",
                    operator=">",
                    threshold=90,  # 90% of pool size
                    duration="3m",
                    description="Database connection pool usage exceeds 90%",
                ),
                channels=[AlertChannel.SLACK, AlertChannel.EMAIL],
                message_template="⚠️ CRITICAL: Database connection pool nearly exhausted! Current usage: {{$value}}%",
                tags=["critical", "database", "connections"],
                runbook_url="https://docs.devhq.com/runbooks/db-connections",
            ),
            AlertRule(
                name="Payment Processing Failure Spike",
                severity=AlertSeverity.CRITICAL,
                condition=AlertCondition(
                    metric_query='rate(payment_transactions_total{status="failed"}[5m])',
                    operator=">",
                    threshold=0.1,  # 10% failure rate
                    duration="3m",
                    description="Payment failure rate exceeds 10% for 3 minutes",
                ),
                channels=[AlertChannel.SLACK, AlertChannel.EMAIL, AlertChannel.WEBHOOK],
                message_template="💳 CRITICAL: High payment failure rate detected! Current rate: {{$value}}% - Check payment gateways",
                tags=["critical", "payments", "business"],
                runbook_url="https://docs.devhq.com/runbooks/payment-failures",
            ),
        ]

    @staticmethod
    def get_warning_alerts() -> List[AlertRule]:
        """Warning alerts for performance and capacity issues"""
        return [
            AlertRule(
                name="High Response Time",
                severity=AlertSeverity.WARNING,
                condition=AlertCondition(
                    metric_query="histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                    operator=">",
                    threshold=1.0,  # 1 second P95
                    duration="5m",
                    description="95th percentile response time exceeds 1 second",
                ),
                channels=[AlertChannel.SLACK],
                message_template="⚠️ WARNING: High response time detected! P95 latency: {{$value}}s",
                tags=["warning", "performance", "latency"],
            ),
            AlertRule(
                name="Low Cache Hit Rate",
                severity=AlertSeverity.WARNING,
                condition=AlertCondition(
                    metric_query="rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))",
                    operator="<",
                    threshold=0.8,  # 80% hit rate
                    duration="10m",
                    description="Cache hit rate below 80% for 10 minutes",
                ),
                channels=[AlertChannel.SLACK],
                message_template="📊 WARNING: Low cache hit rate! Current rate: {{$value}}% - Consider cache optimization",
                tags=["warning", "cache", "performance"],
            ),
            AlertRule(
                name="High Memory Usage",
                severity=AlertSeverity.WARNING,
                condition=AlertCondition(
                    metric_query="process_resident_memory_bytes / process_virtual_memory_max_bytes",
                    operator=">",
                    threshold=0.8,  # 80% memory usage
                    duration="5m",
                    description="Memory usage exceeds 80% for 5 minutes",
                ),
                channels=[AlertChannel.SLACK],
                message_template="🧠 WARNING: High memory usage detected! Current usage: {{$value}}%",
                tags=["warning", "memory", "resources"],
            ),
            AlertRule(
                name="Unusual Request Volume",
                severity=AlertSeverity.WARNING,
                condition=AlertCondition(
                    metric_query="rate(http_requests_total[5m])",
                    operator=">",
                    threshold=1000,  # 1000 requests per second
                    duration="3m",
                    description="Request rate exceeds 1000 RPS for 3 minutes",
                ),
                channels=[AlertChannel.SLACK],
                message_template="📈 WARNING: Unusual request volume! Current rate: {{$value}} RPS - Monitor for potential issues",
                tags=["warning", "traffic", "volume"],
            ),
        ]

    @staticmethod
    def get_business_alerts() -> List[AlertRule]:
        """Business-specific alerts for revenue and user experience"""
        return [
            AlertRule(
                name="Low Invoice Creation Rate",
                severity=AlertSeverity.WARNING,
                condition=AlertCondition(
                    metric_query="rate(invoices_created_total[1h])",
                    operator="<",
                    threshold=10,  # Less than 10 invoices per hour
                    duration="30m",
                    description="Invoice creation rate below normal levels",
                ),
                channels=[AlertChannel.SLACK],
                message_template="📄 INFO: Low invoice creation rate! Current rate: {{$value}}/hour - Monitor user activity",
                tags=["business", "invoices", "activity"],
            ),
            AlertRule(
                name="Payment Gateway Timeout",
                severity=AlertSeverity.WARNING,
                condition=AlertCondition(
                    metric_query='rate(payment_processing_duration_seconds_bucket{le="30"}[5m])',
                    operator="<",
                    threshold=0.95,  # Less than 95% of payments complete within 30s
                    duration="5m",
                    description="Payment processing taking longer than expected",
                ),
                channels=[AlertChannel.SLACK, AlertChannel.EMAIL],
                message_template="💳 WARNING: Payment gateway timeouts detected! Success rate: {{$value}}%",
                tags=["warning", "payments", "timeout"],
            ),
            AlertRule(
                name="WebSocket Connection Drop",
                severity=AlertSeverity.INFO,
                condition=AlertCondition(
                    metric_query="active_websocket_connections",
                    operator="<",
                    threshold=5,  # Less than 5 active connections
                    duration="15m",
                    description="Unusually low WebSocket connection count",
                ),
                channels=[AlertChannel.SLACK],
                message_template="🔌 INFO: Low WebSocket activity! Active connections: {{$value}}",
                tags=["info", "websocket", "activity"],
            ),
        ]

    @classmethod
    def get_all_alerts(cls) -> List[AlertRule]:
        """Get all pre-configured alert rules"""
        return (
            cls.get_critical_alerts()
            + cls.get_warning_alerts()
            + cls.get_business_alerts()
        )

    @classmethod
    def get_alerts_by_severity(cls, severity: AlertSeverity) -> List[AlertRule]:
        """Get alerts filtered by severity level"""
        return [alert for alert in cls.get_all_alerts() if alert.severity == severity]

    @classmethod
    def get_alerts_by_channel(cls, channel: AlertChannel) -> List[AlertRule]:
        """Get alerts filtered by notification channel"""
        return [alert for alert in cls.get_all_alerts() if channel in alert.channels]

    @classmethod
    def export_prometheus_rules(cls) -> Dict[str, Any]:
        """Export alert rules in Prometheus format"""
        groups = {
            "critical": cls.get_alerts_by_severity(AlertSeverity.CRITICAL),
            "warning": cls.get_alerts_by_severity(AlertSeverity.WARNING),
            "info": cls.get_alerts_by_severity(AlertSeverity.INFO),
        }

        prometheus_config = {"groups": []}

        for group_name, alerts in groups.items():
            if not alerts:
                continue

            group = {"name": f"devhq_{group_name}", "rules": []}

            for alert in alerts:
                rule = {
                    "alert": alert.name.replace(" ", "_"),
                    "expr": f"{alert.condition.metric_query} {alert.condition.operator} {alert.condition.threshold}",
                    "for": alert.condition.duration,
                    "labels": {
                        "severity": alert.severity.value,
                        "service": "devhq-backend",
                    },
                    "annotations": {
                        "summary": alert.condition.description,
                        "description": alert.message_template,
                        "runbook_url": alert.runbook_url or "",
                    },
                }

                if alert.tags:
                    rule["labels"].update({"tags": ",".join(alert.tags)})

                group["rules"].append(rule)

            prometheus_config["groups"].append(group)

        return prometheus_config

    @classmethod
    def export_alertmanager_config(cls) -> Dict[str, Any]:
        """Export alerting configuration for Alertmanager"""
        return {
            "global": {
                "smtp_smarthost": "localhost:587",
                "smtp_from": "<EMAIL>",
            },
            "route": {
                "group_by": ["alertname"],
                "group_wait": "10s",
                "group_interval": "10s",
                "repeat_interval": "1h",
                "receiver": "web.hook",
                "routes": [
                    {"match": {"severity": "critical"}, "receiver": "critical-alerts"},
                    {"match": {"severity": "warning"}, "receiver": "warning-alerts"},
                ],
            },
            "receivers": [
                {
                    "name": "web.hook",
                    "webhook_configs": [{"url": "http://localhost:5001/webhook"}],
                },
                {
                    "name": "critical-alerts",
                    "email_configs": [
                        {
                            "to": "<EMAIL>",
                            "subject": "DevHQ Critical Alert: {{ .GroupLabels.alertname }}",
                            "body": "{{ range .Alerts }}{{ .Annotations.description }}{{ end }}",
                        }
                    ],
                    "slack_configs": [
                        {
                            "api_url": "YOUR_SLACK_WEBHOOK_URL",
                            "channel": "#alerts",
                            "title": "DevHQ Critical Alert",
                            "text": "{{ range .Alerts }}{{ .Annotations.description }}{{ end }}",
                        }
                    ],
                },
                {
                    "name": "warning-alerts",
                    "slack_configs": [
                        {
                            "api_url": "YOUR_SLACK_WEBHOOK_URL",
                            "channel": "#monitoring",
                            "title": "DevHQ Warning Alert",
                            "text": "{{ range .Alerts }}{{ .Annotations.description }}{{ end }}",
                        }
                    ],
                },
            ],
        }


class AlertManager:
    """Manages alert rules and notifications"""

    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        self.active_alerts: Dict[str, AlertRule] = {}
        self.alert_history: List[Dict[str, Any]] = []

    def register_alerts(self, alerts: List[AlertRule]):
        """Register alert rules"""
        for alert in alerts:
            if alert.enabled:
                self.active_alerts[alert.name] = alert
                self.logger.info(
                    "Alert registered",
                    alert_name=alert.name,
                    severity=alert.severity.value,
                )

    def trigger_alert(
        self, alert_name: str, current_value: float, context: Dict[str, Any] = None
    ):
        """Trigger an alert notification"""
        if alert_name not in self.active_alerts:
            self.logger.warning("Unknown alert triggered", alert_name=alert_name)
            return

        alert = self.active_alerts[alert_name]

        # Format message with current value
        message = alert.message_template.replace("{{$value}}", str(current_value))

        alert_event = {
            "alert_name": alert_name,
            "severity": alert.severity.value,
            "message": message,
            "current_value": current_value,
            "threshold": alert.condition.threshold,
            "timestamp": time.time(),
            "context": context or {},
        }

        self.alert_history.append(alert_event)

        # Log the alert
        self.logger.warning("Alert triggered", **alert_event)

        # Here you would integrate with actual notification systems
        # For now, we'll just log the channels that should be notified
        for channel in alert.channels:
            self.logger.info(
                "Alert notification",
                channel=channel.value,
                alert_name=alert_name,
                message=message,
            )

    def get_alert_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent alert history"""
        return self.alert_history[-limit:]

    def get_active_alerts(self) -> Dict[str, AlertRule]:
        """Get currently active alert rules"""
        return self.active_alerts.copy()


# Global alert manager instance
alert_manager = AlertManager()

# Register all alerts on module import
alert_manager.register_alerts(MonitoringAlerts.get_all_alerts())
