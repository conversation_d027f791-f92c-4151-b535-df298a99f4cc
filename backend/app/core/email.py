"""
Email service for sending verification and notification emails
"""

import smtplib
from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List, Optional

from app.config import settings


async def send_email(
    to_email: str, subject: str, body: str, html_body: Optional[str] = None
) -> bool:
    """
    Send a simple email

    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Plain text body
        html_body: Optional HTML body

    Returns:
        bool: True if email sent successfully
    """
    email_service = EmailService()
    return email_service._send_email(
        to_emails=[to_email],
        subject=subject,
        html_content=html_body or body,
        text_content=body,
    )


class EmailService:
    """Email service for sending emails"""

    def __init__(self):
        self.smtp_host = settings.smtp_host
        self.smtp_port = settings.smtp_port
        self.smtp_user = settings.smtp_user
        self.smtp_password = settings.smtp_password

    def send_email(
        self,
        to_email: str,
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[dict]] = None,
    ) -> bool:
        """Send email - wrapper for _send_email for test compatibility"""
        return self._send_email(
            to_emails=[to_email],
            subject=subject,
            html_content=html_body or body,
            text_content=body,
            attachments=attachments,
        )

    def send_bulk_email(
        self,
        recipients: List[str],
        subject: str,
        body: str,
        html_body: Optional[str] = None,
    ) -> bool:
        """Send bulk email to multiple recipients"""
        return self._send_email(
            to_emails=recipients,
            subject=subject,
            html_content=html_body or body,
            text_content=body,
        )

    def send_template_email(
        self, to_email: str, template_name: str, template_data: dict
    ) -> bool:
        """Send templated email"""
        # Simple template implementation for tests
        subject = template_data.get("subject", "DevHQ Notification")
        body = self._load_template(template_name, template_data)
        return self.send_email(to_email, subject, body)

    def _load_template(self, template_name: str, data: dict) -> str:
        """Load and render email template"""
        # Simple template implementation
        if template_name == "verification":
            return f"Please verify your email: {data.get('verification_link', '#')}"
        elif template_name == "password_reset":
            return f"Reset your password: {data.get('reset_link', '#')}"
        return f"Template: {template_name} with data: {data}"

    def _load_email_template(self, template_name: str, data: dict) -> str:
        """Load and render HTML email template from file"""
        import os
        from pathlib import Path
        
        # Get the template file path
        template_dir = Path(__file__).parent.parent / "templates"
        template_path = template_dir / template_name
        
        try:
            # Read template file
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # Simple template variable replacement
            for key, value in data.items():
                template_content = template_content.replace(f"{{{{{key}}}}}", str(value))
            
            return template_content
            
        except FileNotFoundError:
            # Fallback to simple template if file not found
            print(f"Template file {template_path} not found, using fallback")
            return self._create_fallback_template(template_name, data)
        except Exception as e:
            print(f"Error loading template {template_name}: {e}")
            return self._create_fallback_template(template_name, data)

    def _create_fallback_template(self, template_name: str, data: dict) -> str:
        """Create a simple fallback template"""
        if "verification" in template_name:
            return f"""
            <html>
            <body style="font-family: Arial, sans-serif;">
                <h2>Welcome to DevHQ!</h2>
                <p>Hi {data.get('first_name', 'there')},</p>
                <p>Please verify your email by clicking: <a href="{data.get('verification_url', '#')}">Verify Email</a></p>
                <p>Best regards,<br>DevHQ Team</p>
            </body>
            </html>
            """
        elif "reset" in template_name:
            return f"""
            <html>
            <body style="font-family: Arial, sans-serif;">
                <h2>Password Reset</h2>
                <p>Hi {data.get('first_name', 'there')},</p>
                <p>Reset your password by clicking: <a href="{data.get('reset_url', '#')}">Reset Password</a></p>
                <p>Best regards,<br>DevHQ Team</p>
            </body>
            </html>
            """
        return f"<html><body><p>Email template: {template_name}</p></body></html>"

    def validate_configuration(self) -> bool:
        """Validate email configuration"""
        if not self.smtp_host or not self.smtp_user:
            raise Exception("Email configuration is incomplete")
        return True

    def _send_email(
        self,
        to_emails: List[str],
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        attachments: Optional[List[dict]] = None,
    ) -> bool:
        """
        Send email using SMTP
        """
        if not self.smtp_host or not self.smtp_user or not self.smtp_password:
            # In development, just log the email
            print(f"📧 EMAIL (Development Mode)")
            print(f"To: {', '.join(to_emails)}")
            print(f"Subject: {subject}")
            print(f"Content: {text_content or html_content}")
            print("=" * 50)
            return True

        try:
            # Create message
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = f"{settings.from_name} <{settings.from_email}>" if hasattr(settings, 'from_email') and settings.from_email else self.smtp_user
            msg["To"] = ", ".join(to_emails)

            # Add text content
            if text_content:
                text_part = MIMEText(text_content, "plain")
                msg.attach(text_part)

            # Add HTML content
            html_part = MIMEText(html_content, "html")
            msg.attach(html_part)

            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    part = MIMEBase("application", "octet-stream")
                    part.set_payload(attachment["content"])
                    encoders.encode_base64(part)
                    part.add_header(
                        "Content-Disposition",
                        f'attachment; filename= {attachment["filename"]}',
                    )
                    msg.attach(part)

            # Send email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_user, self.smtp_password)
                server.send_message(msg)

            return True

        except Exception as e:
            print(f"Failed to send email: {e}")
            return False

    def send_verification_email(
        self, email: str, first_name: str, verification_token: str
    ) -> bool:
        """
        Send email verification email with modern template
        """
        subject = "Verify your DevHQ account"

        # Frontend URL for verification
        import urllib.parse
        encoded_token = urllib.parse.quote(verification_token)
        verification_url = (
            f"http://localhost:3000/verify-email?token={encoded_token}"
        )

        # Load and render HTML template
        html_content = self._load_email_template(
            "email_verification.html",
            {
                "first_name": first_name,
                "verification_url": verification_url
            }
        )

        text_content = f"""
        Welcome to DevHQ! 🚀
        
        Hi {first_name},
        
        Thank you for signing up for DevHQ! To complete your registration, please verify your email address by visiting this link:
        
        {verification_url}
        
        This verification link will expire in 24 hours.
        
        If you didn't create an account with DevHQ, you can safely ignore this email.
        
        Best regards,
        The DevHQ Team
        """

        return self._send_email([email], subject, html_content, text_content)

    def send_password_reset_email(
        self, email: str, first_name: str, reset_token: str
    ) -> bool:
        """
        Send password reset email with modern template
        """
        subject = "Reset your DevHQ password"

        # Frontend URL for password reset
        import urllib.parse
        encoded_token = urllib.parse.quote(reset_token)
        reset_url = f"http://localhost:3000/reset-password?token={encoded_token}"

        # Load and render HTML template
        html_content = self._load_email_template(
            "password_reset.html",
            {
                "first_name": first_name,
                "reset_url": reset_url
            }
        )

        text_content = f"""
        Password Reset Request 🔐
        
        Hi {first_name},
        
        We received a request to reset your DevHQ account password. Visit this link to set a new password:
        
        {reset_url}
        
        This password reset link will expire in 1 hour.
        
        If you didn't request a password reset, you can safely ignore this email. Your password will not be changed.
        
        Best regards,
        The DevHQ Team
        """

        return self._send_email([email], subject, html_content, text_content)

    def send_invoice_email(
        self,
        client_email: str,
        client_name: str,
        invoice_number: str,
        invoice_amount: str,
        currency: str,
        due_date: str,
        pdf_attachment: bytes,
        sender_name: str = "DevHQ Team",
    ) -> bool:
        """Send invoice email with PDF attachment"""
        subject = f"Invoice {invoice_number} from {sender_name}"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Invoice {invoice_number}</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #2563eb; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background: #f9f9f9; }}
                .invoice-details {{ background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }}
                .amount {{ font-size: 24px; font-weight: bold; color: #2563eb; }}
                .footer {{ text-align: center; color: #666; font-size: 14px; margin-top: 20px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Invoice {invoice_number}</h1>
                </div>
                <div class="content">
                    <p>Dear {client_name},</p>
                    <p>Thank you for your business! Please find attached your invoice for the services provided.</p>
                    <div class="invoice-details">
                        <h3>Invoice Details:</h3>
                        <p><strong>Invoice Number:</strong> {invoice_number}</p>
                        <p><strong>Amount Due:</strong> <span class="amount">{currency} {invoice_amount}</span></p>
                        <p><strong>Due Date:</strong> {due_date}</p>
                    </div>
                    <p>The invoice is attached as a PDF document. Please review the details and process payment by the due date.</p>
                    <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                    <p>Thank you for choosing our services!</p>
                </div>
                <div class="footer">
                    <p>Best regards,<br>{sender_name}</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Invoice {invoice_number}
        
        Dear {client_name},
        
        Thank you for your business! Please find attached your invoice for the services provided.
        
        Invoice Details:
        - Invoice Number: {invoice_number}
        - Amount Due: {currency} {invoice_amount}
        - Due Date: {due_date}
        
        The invoice is attached as a PDF document. Please review the details and process payment by the due date.
        
        Best regards,
        {sender_name}
        """

        attachments = [
            {"content": pdf_attachment, "filename": f"invoice-{invoice_number}.pdf"}
        ]

        return self._send_email(
            [client_email], subject, html_content, text_content, attachments
        )

    def send_payment_confirmation_email(
        self,
        client_email: str,
        client_name: str,
        invoice_number: str,
        payment_amount: str,
        currency: str,
        transaction_reference: str,
        sender_name: str = "DevHQ Team",
    ) -> bool:
        """Send payment confirmation email"""
        subject = f"Payment Confirmation - Invoice {invoice_number}"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Payment Confirmation</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #10b981; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background: #f9f9f9; }}
                .payment-details {{ background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }}
                .amount {{ font-size: 24px; font-weight: bold; color: #10b981; }}
                .footer {{ text-align: center; color: #666; font-size: 14px; margin-top: 20px; }}
                .success-icon {{ font-size: 48px; color: #10b981; text-align: center; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="success-icon">✓</div>
                    <h1>Payment Received!</h1>
                </div>
                <div class="content">
                    <p>Dear {client_name},</p>
                    <p>We have successfully received your payment. Thank you!</p>
                    <div class="payment-details">
                        <h3>Payment Details:</h3>
                        <p><strong>Invoice Number:</strong> {invoice_number}</p>
                        <p><strong>Amount Paid:</strong> <span class="amount">{currency} {payment_amount}</span></p>
                        <p><strong>Transaction Reference:</strong> {transaction_reference}</p>
                        <p><strong>Status:</strong> <span style="color: #10b981; font-weight: bold;">PAID</span></p>
                    </div>
                    <p>Your invoice has been marked as paid in our system.</p>
                    <p>We appreciate your prompt payment and look forward to continuing our business relationship.</p>
                </div>
                <div class="footer">
                    <p>Best regards,<br>{sender_name}</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Payment Confirmation - Invoice {invoice_number}
        
        Dear {client_name},
        
        We have successfully received your payment. Thank you!
        
        Payment Details:
        - Invoice Number: {invoice_number}
        - Amount Paid: {currency} {payment_amount}
        - Transaction Reference: {transaction_reference}
        - Status: PAID
        
        Your invoice has been marked as paid in our system.
        
        Best regards,
        {sender_name}
        """

        return self._send_email([client_email], subject, html_content, text_content)


# Convenience function for invoice emails
async def send_invoice_email(
    to_email: str, invoice_data: dict, pdf_content: bytes, filename: str = "invoice.pdf"
) -> bool:
    """
    Send invoice email with PDF attachment

    Args:
        to_email: Recipient email address
        invoice_data: Invoice data for email template
        pdf_content: PDF file content as bytes
        filename: PDF filename

    Returns:
        bool: True if email sent successfully
    """
    email_service = EmailService()
    return email_service.send_invoice_email(
        client_email=to_email,
        client_name=invoice_data.get("client_name", "Valued Client"),
        invoice_number=invoice_data.get("number", "N/A"),
        invoice_amount=str(invoice_data.get("total_amount", "0.00")),
        currency=invoice_data.get("currency", "USD"),
        due_date=str(invoice_data.get("due_date", "N/A")),
        pdf_attachment=pdf_content,
        sender_name=invoice_data.get("from_name", "DevHQ Team"),
    )


# Global email service instance
email_service = EmailService()
