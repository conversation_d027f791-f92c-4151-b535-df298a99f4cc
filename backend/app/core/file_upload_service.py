"""
File Upload Service with Cloudinary Integration
Handles secure file uploads, validation, and real-time progress tracking
"""

import asyncio
import hashlib
import logging
import mimetypes
import os
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import cloudinary
import cloudinary.uploader
import cloudinary.utils

try:
    import magic

    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    magic = None

from fastapi import HTTPException, UploadFile
from PIL import Image

from app.services.realtime_service import realtime_service

logger = logging.getLogger(__name__)

# Configure Cloudinary (will be set from environment variables)
cloudinary.config(
    cloud_name=os.getenv("CLOUDINARY_CLOUD_NAME"),
    api_key=os.getenv("CLOUDINARY_API_KEY"),
    api_secret=os.getenv("CLOUDINARY_API_SECRET"),
    secure=True,
)


class FileUploadService:
    """
    Service for handling secure file uploads with real-time progress
    """

    # File type configurations
    ALLOWED_EXTENSIONS = {
        "image": {".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"},
        "document": {".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt"},
        "spreadsheet": {".xls", ".xlsx", ".csv", ".ods"},
        "presentation": {".ppt", ".pptx", ".odp"},
        "archive": {".zip", ".rar", ".7z", ".tar", ".gz"},
        "code": {".py", ".js", ".html", ".css", ".json", ".xml", ".sql"},
        "video": {".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm"},
        "audio": {".mp3", ".wav", ".flac", ".aac", ".ogg"},
    }

    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB for images

    def __init__(self):
        self.upload_progress = {}  # Track upload progress by upload_id

    async def validate_file(self, file: UploadFile) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Validate uploaded file for security and compliance
        Returns: (is_valid, error_message, file_info)
        """
        try:
            # Check file size
            file_size = 0
            content = await file.read()
            file_size = len(content)
            await file.seek(0)  # Reset file pointer

            if file_size > self.MAX_FILE_SIZE:
                return (
                    False,
                    f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size (50MB)",
                    {},
                )

            # Get file extension and MIME type
            file_extension = Path(file.filename).suffix.lower()
            mime_type = mimetypes.guess_type(file.filename)[0]

            # Detect actual file type using python-magic (if available)
            if MAGIC_AVAILABLE:
                try:
                    actual_mime_type = magic.from_buffer(content[:2048], mime=True)
                except:
                    actual_mime_type = mime_type
            else:
                actual_mime_type = mime_type

            # Determine file category
            file_category = self._get_file_category(file_extension)
            if not file_category:
                return False, f"File type '{file_extension}' is not allowed", {}

            # Additional validation for images
            if file_category == "image":
                if file_size > self.MAX_IMAGE_SIZE:
                    return (
                        False,
                        f"Image size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size (10MB)",
                        {},
                    )

                # Validate image integrity
                try:
                    await file.seek(0)
                    image = Image.open(file.file)
                    image.verify()
                    await file.seek(0)
                except Exception as e:
                    return False, "Invalid or corrupted image file", {}

            # Check for malicious content (basic checks)
            if self._contains_malicious_content(content, file_extension):
                return False, "File contains potentially malicious content", {}

            file_info = {
                "filename": file.filename,
                "size": file_size,
                "extension": file_extension,
                "mime_type": mime_type,
                "actual_mime_type": actual_mime_type,
                "category": file_category,
                "hash": hashlib.sha256(content).hexdigest(),
            }

            return True, "", file_info

        except Exception as e:
            logger.error(f"Error validating file: {e}")
            return False, "Error validating file", {}

    def _get_file_category(self, extension: str) -> Optional[str]:
        """Get file category based on extension"""
        for category, extensions in self.ALLOWED_EXTENSIONS.items():
            if extension in extensions:
                return category
        return None

    def _contains_malicious_content(self, content: bytes, extension: str) -> bool:
        """Basic malicious content detection"""
        # Check for common malicious patterns
        malicious_patterns = [
            b"<script",
            b"javascript:",
            b"vbscript:",
            b"onload=",
            b"onerror=",
            b"<?php",
            b"<%",
            b"eval(",
            b"exec(",
            b"system(",
            b"shell_exec(",
        ]

        content_lower = content.lower()
        for pattern in malicious_patterns:
            if pattern in content_lower:
                return True

        return False

    async def upload_file(
        self,
        file: UploadFile,
        project_id: str,
        client_id: str,
        user_id: str,
        upload_context: str = "project_file",
    ) -> Dict[str, Any]:
        """
        Upload file to Cloudinary with real-time progress tracking
        """
        upload_id = str(uuid.uuid4())

        try:
            # Initialize progress tracking
            self.upload_progress[upload_id] = {
                "status": "validating",
                "progress": 0,
                "message": "Validating file...",
            }

            # Emit initial progress
            await realtime_service.emit_event(
                "file_upload_progress",
                {
                    "upload_id": upload_id,
                    "project_id": project_id,
                    "client_id": client_id,
                    **self.upload_progress[upload_id],
                },
            )

            # Validate file
            is_valid, error_message, file_info = await self.validate_file(file)
            if not is_valid:
                self.upload_progress[upload_id] = {
                    "status": "error",
                    "progress": 0,
                    "message": error_message,
                }
                await self._emit_progress(upload_id, project_id, client_id)
                raise HTTPException(status_code=400, detail=error_message)

            # Update progress
            self.upload_progress[upload_id] = {
                "status": "uploading",
                "progress": 25,
                "message": "Uploading to cloud storage...",
            }
            await self._emit_progress(upload_id, project_id, client_id)

            # Prepare upload options
            upload_options = {
                "folder": f"devhq/{upload_context}/{project_id}",
                "public_id": f"{upload_id}_{Path(file.filename).stem}",
                "resource_type": "auto",
                "context": {
                    "project_id": project_id,
                    "client_id": client_id,
                    "user_id": user_id,
                    "upload_context": upload_context,
                    "original_filename": file.filename,
                },
                "tags": [upload_context, project_id, client_id],
            }

            # Add transformation for images
            if file_info["category"] == "image":
                upload_options["transformation"] = [
                    {"quality": "auto:good"},
                    {"fetch_format": "auto"},
                ]

            # Upload to Cloudinary
            await file.seek(0)
            file_content = await file.read()

            # Update progress
            self.upload_progress[upload_id] = {
                "status": "uploading",
                "progress": 50,
                "message": "Processing file...",
            }
            await self._emit_progress(upload_id, project_id, client_id)

            # Perform upload
            upload_result = cloudinary.uploader.upload(file_content, **upload_options)

            # Update progress
            self.upload_progress[upload_id] = {
                "status": "processing",
                "progress": 75,
                "message": "Finalizing upload...",
            }
            await self._emit_progress(upload_id, project_id, client_id)

            # Generate additional URLs and metadata
            file_data = {
                "upload_id": upload_id,
                "cloudinary_id": upload_result["public_id"],
                "url": upload_result["secure_url"],
                "original_filename": file.filename,
                "size": file_info["size"],
                "format": upload_result.get("format"),
                "resource_type": upload_result.get("resource_type"),
                "category": file_info["category"],
                "mime_type": file_info["mime_type"],
                "hash": file_info["hash"],
                "uploaded_at": datetime.now(timezone.utc).isoformat(),
                "project_id": project_id,
                "client_id": client_id,
                "user_id": user_id,
                "upload_context": upload_context,
            }

            # Add image-specific data
            if file_info["category"] == "image":
                file_data.update(
                    {
                        "width": upload_result.get("width"),
                        "height": upload_result.get("height"),
                        "thumbnail_url": cloudinary.utils.cloudinary_url(
                            upload_result["public_id"],
                            width=300,
                            height=300,
                            crop="fill",
                            quality="auto:good",
                        )[0],
                    }
                )

            # Complete upload
            self.upload_progress[upload_id] = {
                "status": "completed",
                "progress": 100,
                "message": "Upload completed successfully",
                "file_data": file_data,
            }
            await self._emit_progress(upload_id, project_id, client_id)

            # Emit file uploaded event
            await realtime_service.emit_file_uploaded(file_data, project_id, client_id)

            # Clean up progress tracking after delay
            asyncio.create_task(self._cleanup_progress(upload_id, delay=30))

            return file_data

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            self.upload_progress[upload_id] = {
                "status": "error",
                "progress": 0,
                "message": f"Upload failed: {str(e)}",
            }
            await self._emit_progress(upload_id, project_id, client_id)
            raise HTTPException(status_code=500, detail="File upload failed")

    async def _emit_progress(self, upload_id: str, project_id: str, client_id: str):
        """Emit upload progress event"""
        progress_data = {
            "upload_id": upload_id,
            "project_id": project_id,
            "client_id": client_id,
            **self.upload_progress[upload_id],
        }
        await realtime_service.emit_event("file_upload_progress", progress_data)

    async def _cleanup_progress(self, upload_id: str, delay: int = 30):
        """Clean up progress tracking after delay"""
        await asyncio.sleep(delay)
        if upload_id in self.upload_progress:
            del self.upload_progress[upload_id]

    async def delete_file(self, cloudinary_id: str) -> bool:
        """Delete file from Cloudinary"""
        try:
            result = cloudinary.uploader.destroy(cloudinary_id)
            return result.get("result") == "ok"
        except Exception as e:
            logger.error(f"Error deleting file {cloudinary_id}: {e}")
            return False

    async def get_file_info(self, cloudinary_id: str) -> Optional[Dict[str, Any]]:
        """Get file information from Cloudinary"""
        try:
            result = cloudinary.api.resource(cloudinary_id)
            return {
                "cloudinary_id": result["public_id"],
                "url": result["secure_url"],
                "format": result.get("format"),
                "size": result.get("bytes"),
                "width": result.get("width"),
                "height": result.get("height"),
                "created_at": result.get("created_at"),
                "resource_type": result.get("resource_type"),
            }
        except Exception as e:
            logger.error(f"Error getting file info for {cloudinary_id}: {e}")
            return None

    def get_upload_progress(self, upload_id: str) -> Optional[Dict[str, Any]]:
        """Get current upload progress"""
        return self.upload_progress.get(upload_id)

    def generate_signed_upload_url(
        self, project_id: str, client_id: str, upload_context: str = "project_file"
    ) -> Dict[str, Any]:
        """Generate signed URL for direct client uploads"""
        timestamp = int(datetime.now().timestamp())

        upload_params = {
            "timestamp": timestamp,
            "folder": f"devhq/{upload_context}/{project_id}",
            "tags": f"{upload_context},{project_id},{client_id}",
            "context": f"project_id={project_id}|client_id={client_id}|upload_context={upload_context}",
        }

        signature = cloudinary.utils.api_sign_request(
            upload_params, cloudinary.config().api_secret
        )

        return {
            "url": f"https://api.cloudinary.com/v1_1/{cloudinary.config().cloud_name}/auto/upload",
            "params": {
                **upload_params,
                "signature": signature,
                "api_key": cloudinary.config().api_key,
            },
        }


# Global file upload service instance
file_upload_service = FileUploadService()
