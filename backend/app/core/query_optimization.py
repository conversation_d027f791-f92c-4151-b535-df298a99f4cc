"""Database query optimization utilities.

This module provides utilities for optimizing database queries,
including efficient pagination, query analysis, and performance monitoring.
"""

import logging
import time
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar

from sqlalchemy import func, text
from sqlalchemy.orm import Query, Session
from sqlalchemy.sql import Select

from app.models.base import Base

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=Base)


class QueryOptimizer:
    """Utility class for database query optimization."""

    @staticmethod
    def paginate_query(
        query: Query, page: int = 1, per_page: int = 20, max_per_page: int = 100
    ) -> Tuple[List[Any], int, Dict[str, Any]]:
        """
        Optimize pagination queries with efficient counting.

        Args:
            query: SQLAlchemy query object
            page: Page number (1-indexed)
            per_page: Items per page
            max_per_page: Maximum allowed items per page

        Returns:
            Tuple of (items, total_count, pagination_info)
        """
        # Validate and sanitize pagination parameters
        page = max(1, page)
        per_page = min(max(1, per_page), max_per_page)

        # Calculate offset
        offset = (page - 1) * per_page

        # Get total count efficiently
        # Use subquery for complex queries to avoid issues with GROUP BY, etc.
        try:
            if hasattr(query.statement, "group_by") and query.statement.group_by:
                # For grouped queries, count the subquery
                subquery = query.statement.alias()
                total_count = query.session.execute(
                    func.count().select_from(subquery)
                ).scalar()
            else:
                # For simple queries, use count() directly
                total_count = query.count()
        except Exception as e:
            logger.warning(f"Fallback to subquery count due to: {e}")
            subquery = query.statement.alias()
            total_count = query.session.execute(
                func.count().select_from(subquery)
            ).scalar()

        # Get paginated items
        items = query.offset(offset).limit(per_page).all()

        # Calculate pagination metadata
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1

        pagination_info = {
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": total_pages,
            "has_next": has_next,
            "has_prev": has_prev,
            "next_page": page + 1 if has_next else None,
            "prev_page": page - 1 if has_prev else None,
        }

        return items, total_count, pagination_info

    @staticmethod
    def add_search_filter(
        query: Query,
        model: Type[T],
        search_term: Optional[str],
        search_fields: List[str],
    ) -> Query:
        """
        Add full-text search filter to query.

        Args:
            query: SQLAlchemy query object
            model: SQLAlchemy model class
            search_term: Search term to filter by
            search_fields: List of field names to search in

        Returns:
            Modified query with search filter
        """
        if not search_term or not search_fields:
            return query

        search_term = search_term.strip()
        if not search_term:
            return query

        # Create ILIKE conditions for each search field
        search_conditions = []
        for field_name in search_fields:
            if hasattr(model, field_name):
                field = getattr(model, field_name)
                search_conditions.append(field.ilike(f"%{search_term}%"))

        if search_conditions:
            # Use OR condition to search across all fields
            from sqlalchemy import or_

            query = query.filter(or_(*search_conditions))

        return query

    @staticmethod
    def add_date_range_filter(
        query: Query,
        date_field: Any,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> Query:
        """
        Add date range filter to query.

        Args:
            query: SQLAlchemy query object
            date_field: SQLAlchemy date field
            start_date: Start date (ISO format)
            end_date: End date (ISO format)

        Returns:
            Modified query with date range filter
        """
        if start_date:
            query = query.filter(date_field >= start_date)

        if end_date:
            query = query.filter(date_field <= end_date)

        return query

    @staticmethod
    def add_soft_delete_filter(query: Query, model: Type[T]) -> Query:
        """
        Add soft delete filter to exclude deleted records.

        Args:
            query: SQLAlchemy query object
            model: SQLAlchemy model class

        Returns:
            Modified query excluding soft-deleted records
        """
        if hasattr(model, "deleted_at"):
            query = query.filter(model.deleted_at.is_(None))

        return query

    @staticmethod
    def add_filters(query: Query, model: Type[T], filters: Dict[str, Any]) -> Query:
        """
        Add multiple filters to query.

        Args:
            query: SQLAlchemy query object
            model: SQLAlchemy model class
            filters: Dictionary of field names and values to filter by

        Returns:
            Modified query with filters applied
        """
        for field_name, value in filters.items():
            if hasattr(model, field_name) and value is not None:
                field = getattr(model, field_name)
                query = query.filter(field == value)

        return query

    @staticmethod
    def optimize_joins(query: Query, eager_load_relations: List[str]) -> Query:
        """
        Optimize query with eager loading for specified relations.

        Args:
            query: SQLAlchemy query object
            eager_load_relations: List of relation names to eager load

        Returns:
            Modified query with optimized joins
        """
        from sqlalchemy.orm import joinedload

        for relation in eager_load_relations:
            query = query.options(joinedload(relation))

        return query

    @staticmethod
    def optimize_pagination(
        query: Query, order_field: Any = None, order_direction: str = "asc"
    ) -> Query:
        """
        Optimize query with ordering for pagination.

        Args:
            query: SQLAlchemy query object
            order_field: Field to order by
            order_direction: Direction to order ("asc" or "desc")

        Returns:
            Modified query with ordering applied
        """
        if order_field is not None:
            if order_direction.lower() == "desc":
                query = query.order_by(order_field.desc())
            else:
                query = query.order_by(order_field.asc())

        return query


class QueryProfiler:
    """Query performance profiling utilities."""

    @staticmethod
    def profile_query(query, query_name: str = "unnamed") -> dict:
        """
        Profile a query's execution time and result count

        Args:
            query: SQLAlchemy query object
            query_name: Name for the query (for logging)

        Returns:
            dict: Profiling results with execution time and result count
        """
        start_time = time.time()

        try:
            results = query.all()
        except Exception as e:
            return {
                "query_name": query_name,
                "execution_time": 0,
                "result_count": 0,
                "error": str(e),
                "timestamp": time.time(),
            }

        end_time = time.time()
        execution_time = end_time - start_time

        profile_data = {
            "query_name": query_name,
            "execution_time": execution_time,
            "result_count": len(results) if results else 0,
            "timestamp": time.time(),
        }

        # Log slow queries
        if execution_time > 1.0:  # Log queries taking more than 1 second
            logger.warning(
                f"Slow query detected: {query_name} took {execution_time:.2f}s"
            )

        return profile_data

    @staticmethod
    @contextmanager
    def profile_query_context(
        query_name: str, log_slow_queries: bool = True, slow_threshold: float = 1.0
    ):
        """
        Context manager for profiling query execution time.

        Args:
            query_name: Name of the query for logging
            log_slow_queries: Whether to log slow queries
            slow_threshold: Threshold in seconds for considering a query slow
        """
        start_time = time.time()
        try:
            yield
        finally:
            execution_time = time.time() - start_time

            if log_slow_queries and execution_time > slow_threshold:
                logger.warning(
                    f"Slow query detected: {query_name} took {execution_time:.3f}s"
                )
            else:
                logger.debug(f"Query executed: {query_name} took {execution_time:.3f}s")

    @staticmethod
    def explain_query(session: Session, query: Query) -> Dict[str, Any]:
        """
        Get query execution plan for analysis.

        Args:
            session: SQLAlchemy session
            query: SQLAlchemy query object

        Returns:
            Dictionary containing query execution plan
        """
        try:
            # Check if query has statement attribute
            if not hasattr(query, "statement"):
                return {
                    "query": str(query),
                    "error": "Query object does not have statement attribute",
                }

            # Get the compiled query
            compiled = query.statement.compile(
                dialect=session.bind.dialect, compile_kwargs={"literal_binds": True}
            )

            # Execute EXPLAIN
            explain_query = text(f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {compiled}")
            result = session.execute(explain_query).fetchone()

            return {
                "query": str(compiled),
                "execution_plan": result[0] if result else None,
            }
        except Exception as e:
            logger.error(f"Failed to explain query: {e}")
            return {"query": str(query), "error": str(e)}


class CacheableQuery:
    """Utilities for making queries cacheable."""

    @staticmethod
    def generate_cache_key(
        model_name: str,
        filters: Dict[str, Any],
        page: int = 1,
        per_page: int = 20,
        order_by: Optional[str] = None,
    ) -> str:
        """
        Generate a consistent cache key for query results.

        Args:
            model_name: Name of the model being queried
            filters: Dictionary of filters applied
            page: Page number
            per_page: Items per page
            order_by: Ordering specification

        Returns:
            Cache key string
        """
        import hashlib
        import json

        # Create a consistent representation of the query parameters
        cache_data = {
            "model": model_name,
            "filters": filters,
            "page": page,
            "per_page": per_page,
            "order_by": order_by,
        }

        # Sort keys for consistency
        cache_string = json.dumps(cache_data, sort_keys=True)

        # Generate hash
        cache_hash = hashlib.md5(cache_string.encode()).hexdigest()

        return f"query:{model_name}:{cache_hash}"

    @staticmethod
    def should_cache_query(
        total_count: int,
        execution_time: float,
        min_count_threshold: int = 100,
        min_time_threshold: float = 0.1,
    ) -> bool:
        """
        Determine if a query result should be cached.

        Args:
            total_count: Total number of records in result
            execution_time: Query execution time in seconds
            min_count_threshold: Minimum record count to consider caching
            min_time_threshold: Minimum execution time to consider caching

        Returns:
            Boolean indicating whether to cache the result
        """
        # Cache if result set is large OR query is slow
        # Also consider caching if both conditions are moderately met
        return (
            total_count >= min_count_threshold
            or execution_time >= min_time_threshold
            or (
                total_count >= min_count_threshold // 2
                and execution_time >= min_time_threshold / 2
            )
        )


# Utility functions for common query patterns
def get_user_projects_optimized(
    session: Session,
    user_id: str,
    page: int = 1,
    per_page: int = 20,
    status: Optional[str] = None,
    client_id: Optional[str] = None,
    search: Optional[str] = None,
) -> Tuple[List[Any], int, Dict[str, Any]]:
    """
    Optimized query for user projects with common filters.

    This function demonstrates the use of query optimization utilities
    for a common use case.
    """
    from app.models.project import Project

    # Start with base query
    query = session.query(Project)

    # Add soft delete filter
    query = QueryOptimizer.add_soft_delete_filter(query, Project)

    # Add user filter
    query = query.filter(Project.user_id == user_id)

    # Add optional filters
    if status:
        query = query.filter(Project.status == status)

    if client_id:
        query = query.filter(Project.client_id == client_id)

    # Add search filter
    if search:
        query = QueryOptimizer.add_search_filter(
            query, Project, search, ["title", "description"]
        )

    # Optimize with eager loading
    query = QueryOptimizer.optimize_joins(query, ["client", "milestones"])

    # Order by most recent
    query = query.order_by(Project.created_at.desc())

    # Apply pagination
    with QueryProfiler.profile_query_context("get_user_projects"):
        return QueryOptimizer.paginate_query(query, page, per_page)
