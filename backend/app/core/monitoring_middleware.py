"""Monitoring Middleware for DevHQ Backend

Integrates monitoring and observability into FastAPI request/response cycle.
"""

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.monitoring import monitoring_service


class MonitoringMiddleware(BaseHTTPMiddleware):
    """Middleware for monitoring HTTP requests and responses."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and record monitoring metrics."""
        start_time = time.time()

        # Extract request information
        method = request.method
        path = request.url.path

        # Normalize endpoint path for metrics (remove IDs)
        endpoint = self._normalize_endpoint(path)

        # Start tracing span
        with monitoring_service.trace_operation(
            "http_request",
            http_method=method,
            http_url=str(request.url),
            http_route=endpoint,
            user_agent=request.headers.get("user-agent", "unknown"),
        ) as span:
            try:
                # Process request
                response = await call_next(request)

                # Calculate duration
                duration = time.time() - start_time

                # Record metrics
                monitoring_service.record_request_metrics(
                    method=method,
                    endpoint=endpoint,
                    status_code=response.status_code,
                    duration=duration,
                )

                # Add span attributes
                if span:
                    span.set_attribute("http.status_code", response.status_code)
                    span.set_attribute(
                        "http.response_size",
                        len(response.body) if hasattr(response, "body") else 0,
                    )

                # Log performance warnings for slow requests
                monitoring_service.log_performance_warning(
                    "HTTP Request",
                    duration,
                    threshold=2.0,  # 2 second threshold
                    method=method,
                    endpoint=endpoint,
                    status_code=response.status_code,
                )

                # Log security events for suspicious activity
                if response.status_code == 401:
                    monitoring_service.log_security_event(
                        "unauthorized_access_attempt",
                        ip_address=request.client.host if request.client else None,
                        endpoint=endpoint,
                        method=method,
                    )
                elif response.status_code == 429:
                    monitoring_service.log_security_event(
                        "rate_limit_exceeded",
                        ip_address=request.client.host if request.client else None,
                        endpoint=endpoint,
                        method=method,
                    )

                return response

            except Exception as e:
                # Calculate duration even for errors
                duration = time.time() - start_time

                # Record error metrics
                monitoring_service.record_request_metrics(
                    method=method, endpoint=endpoint, status_code=500, duration=duration
                )

                # Log error
                monitoring_service.log_structured(
                    "error",
                    "Request processing error",
                    method=method,
                    endpoint=endpoint,
                    duration=duration,
                    error=str(e),
                    error_type=type(e).__name__,
                )

                # Add span error information
                if span:
                    span.record_exception(e)
                    span.set_attribute("http.status_code", 500)

                raise

    def _normalize_endpoint(self, path: str) -> str:
        """Normalize endpoint path for consistent metrics."""
        # Remove common ID patterns to group similar endpoints
        import re

        # Replace UUIDs with placeholder
        path = re.sub(
            r"/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}",
            "/{id}",
            path,
            flags=re.IGNORECASE,
        )

        # Replace numeric IDs with placeholder
        path = re.sub(r"/\d+", "/{id}", path)

        # Replace other common ID patterns
        path = re.sub(r"/[a-zA-Z0-9_-]{20,}", "/{id}", path)

        return path
