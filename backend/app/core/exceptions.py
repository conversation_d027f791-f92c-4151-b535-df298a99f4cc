"""
Custom exception classes for DevHQ Backend
"""

from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class DevHQException(Exception):
    """Base exception class for DevHQ"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(DevHQException):
    """Authentication related errors"""

    pass


class AuthorizationError(DevHQException):
    """Authorization related errors"""

    pass


class ValidationError(DevHQException):
    """Data validation errors"""

    pass


class NotFoundError(DevHQException):
    """Resource not found errors"""

    pass


class ConflictError(DevHQException):
    """Resource conflict errors"""

    pass


class TimerConflictError(ConflictError):
    """Timer conflict errors"""


class DatabaseError(DevHQException):
    """Database operation errors"""

    pass


class ValidationException(DevHQException):
    """Validation exception errors"""

    pass


class BusinessLogicError(DevHQException):
    """Business logic errors"""

    pass


class TimerNotFoundError(NotFoundError):
    """Timer not found errors"""

    pass


class UnauthorizedError(AuthorizationError):
    """Unauthorized access errors"""

    pass


class PermissionError(AuthorizationError):
    """Permission denied errors"""

    pass


class PaymentError(DevHQException):
    """Payment processing errors"""

    pass


class InvoiceError(DevHQException):
    """Invoice processing errors"""

    pass


class EmailError(DevHQException):
    """Email processing errors"""

    pass


class PDFGenerationError(DevHQException):
    """PDF generation errors"""

    pass


class EmailError(DevHQException):
    """Email processing errors"""

    pass


class PDFGenerationError(DevHQException):
    """PDF generation errors"""

    pass


# HTTP Exception helpers
def http_400_bad_request(message: str = "Bad Request") -> HTTPException:
    return HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)


def http_401_unauthorized(message: str = "Unauthorized") -> HTTPException:
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=message,
        headers={"WWW-Authenticate": "Bearer"},
    )


def http_403_forbidden(message: str = "Forbidden") -> HTTPException:
    return HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=message)


def http_404_not_found(message: str = "Not Found") -> HTTPException:
    return HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=message)


def http_409_conflict(message: str = "Conflict") -> HTTPException:
    return HTTPException(status_code=status.HTTP_409_CONFLICT, detail=message)


def http_422_unprocessable_entity(
    message: str = "Unprocessable Entity",
) -> HTTPException:
    return HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=message
    )


def http_500_internal_server_error(
    message: str = "Internal Server Error",
) -> HTTPException:
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=message
    )
