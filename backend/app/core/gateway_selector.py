"""
Gateway selection service for intelligent payment gateway routing
"""

from dataclasses import dataclass
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

from .payment_gateways import DPOGateway, PaymentGateway, PaystackGateway


@dataclass
class GatewayScore:
    """Gateway scoring for selection algorithm"""

    gateway: PaymentGateway
    score: float
    reasons: List[str]
    estimated_fee: Decimal


class GatewaySelector:
    """Intelligent gateway selection based on currency, country, and optimization criteria"""

    def __init__(self, gateways: Dict[str, PaymentGateway]):
        """
        Initialize with available gateways

        Args:
            gateways: Dict mapping gateway names to gateway instances
        """
        self.gateways = gateways

        # Gateway preferences by currency (higher score = better)
        self.currency_preferences = {
            "NGN": {"paystack": 0.9, "dpo": 0.7},
            "GHS": {"paystack": 0.9, "dpo": 0.8},
            "KES": {"paystack": 0.8, "dpo": 0.9},
            "ZAR": {"paystack": 0.8, "dpo": 0.9},
            "UGX": {"dpo": 0.9, "paystack": 0.2},
            "TZS": {"dpo": 0.9, "paystack": 0.2},
            "RWF": {"dpo": 0.9, "paystack": 0.1},
            "ZMW": {"dpo": 0.9, "paystack": 0.1},
            "BWP": {"dpo": 0.9, "paystack": 0.1},
            "MWK": {"dpo": 0.9, "paystack": 0.1},
            "ETB": {"dpo": 0.9, "paystack": 0.1},
            "USD": {"dpo": 0.8, "paystack": 0.7},
            "EUR": {"dpo": 0.8, "paystack": 0.3},
            "GBP": {"dpo": 0.8, "paystack": 0.3},
        }

        # Country preferences (higher score = better)
        self.country_preferences = {
            "NG": {"paystack": 0.9, "dpo": 0.7},  # Nigeria - Paystack home
            "GH": {"paystack": 0.9, "dpo": 0.8},  # Ghana - Paystack strong
            "KE": {"paystack": 0.8, "dpo": 0.9},  # Kenya - DPO strong
            "ZA": {"paystack": 0.8, "dpo": 0.9},  # South Africa - DPO home
            "UG": {"dpo": 0.9, "paystack": 0.2},  # Uganda - DPO only
            "TZ": {"dpo": 0.9, "paystack": 0.2},  # Tanzania - DPO strong
            "RW": {"dpo": 0.9, "paystack": 0.1},  # Rwanda - DPO only
            "ZM": {"dpo": 0.9, "paystack": 0.1},  # Zambia - DPO only
            "MW": {"dpo": 0.9, "paystack": 0.1},  # Malawi - DPO only
            "BW": {"dpo": 0.9, "paystack": 0.1},  # Botswana - DPO only
            "ET": {"dpo": 0.9, "paystack": 0.1},  # Ethiopia - DPO only
            "MU": {"dpo": 0.9, "paystack": 0.1},  # Mauritius - DPO strong
        }

        # Success rate estimates (based on market data)
        self.success_rates = {
            ("paystack", "NGN"): 0.92,
            ("paystack", "GHS"): 0.89,
            ("paystack", "KES"): 0.85,
            ("paystack", "ZAR"): 0.83,
            ("dpo", "NGN"): 0.85,
            ("dpo", "GHS"): 0.87,
            ("dpo", "KES"): 0.90,
            ("dpo", "ZAR"): 0.91,
            ("dpo", "UGX"): 0.88,
            ("dpo", "TZS"): 0.86,
            ("dpo", "RWF"): 0.84,
            ("dpo", "ZMW"): 0.83,
            ("dpo", "BWP"): 0.85,
            ("dpo", "ETB"): 0.82,
            ("dpo", "USD"): 0.89,
            ("dpo", "EUR"): 0.87,
        }

    async def select_gateway(
        self,
        currency: str,
        country: Optional[str] = None,
        amount: Optional[Decimal] = None,
        payment_method: str = "card",
        optimization: str = "balanced",  # "cost", "success_rate", "balanced"
    ) -> PaymentGateway:
        """
        Select the best payment gateway for the given criteria

        Args:
            currency: ISO currency code
            country: ISO country code (optional)
            amount: Transaction amount (optional, for fee calculation)
            payment_method: Preferred payment method
            optimization: Selection optimization strategy

        Returns:
            Selected PaymentGateway instance
        """

        scores = await self._score_gateways(
            currency, country, amount, payment_method, optimization
        )

        if not scores:
            # Fallback to first available gateway
            return next(iter(self.gateways.values()))

        # Return the highest scoring gateway
        best_gateway = max(scores, key=lambda x: x.score)
        return best_gateway.gateway

    async def get_gateway_recommendations(
        self,
        currency: str,
        country: Optional[str] = None,
        amount: Optional[Decimal] = None,
        payment_method: str = "card",
    ) -> List[GatewayScore]:
        """
        Get ranked list of gateway recommendations with scores and reasons

        Returns:
            List of GatewayScore objects sorted by score (highest first)
        """

        scores = await self._score_gateways(currency, country, amount, payment_method)
        return sorted(scores, key=lambda x: x.score, reverse=True)

    async def _score_gateways(
        self,
        currency: str,
        country: Optional[str] = None,
        amount: Optional[Decimal] = None,
        payment_method: str = "card",
        optimization: str = "balanced",
    ) -> List[GatewayScore]:
        """Score all available gateways for the given criteria"""

        scores = []

        for gateway_name, gateway in self.gateways.items():
            # Check basic support
            if not gateway.is_supported_currency(currency):
                continue

            if country and not gateway.is_supported_country(country):
                continue

            score = 0.0
            reasons = []

            # Currency preference scoring
            currency_score = self.currency_preferences.get(currency, {}).get(
                gateway_name, 0.5
            )
            score += currency_score * 0.3
            if currency_score > 0.8:
                reasons.append(f"Excellent {currency} support")
            elif currency_score > 0.6:
                reasons.append(f"Good {currency} support")

            # Country preference scoring
            if country:
                country_score = self.country_preferences.get(country, {}).get(
                    gateway_name, 0.5
                )
                score += country_score * 0.2
                if country_score > 0.8:
                    reasons.append(f"Strong presence in {country}")

            # Success rate scoring
            success_rate = self.success_rates.get((gateway_name, currency), 0.8)
            score += success_rate * 0.3
            if success_rate > 0.9:
                reasons.append("High success rate")
            elif success_rate > 0.85:
                reasons.append("Good success rate")

            # Fee optimization
            estimated_fee = Decimal("0")
            if amount:
                try:
                    fee_info = await gateway.calculate_fees(
                        amount, currency, payment_method
                    )
                    estimated_fee = fee_info["total_fee"]

                    # Lower fees get higher scores
                    fee_percentage = (estimated_fee / amount) * 100
                    if fee_percentage < 2.0:
                        fee_score = 1.0
                        reasons.append("Low fees")
                    elif fee_percentage < 3.0:
                        fee_score = 0.8
                        reasons.append("Competitive fees")
                    elif fee_percentage < 4.0:
                        fee_score = 0.6
                    else:
                        fee_score = 0.4
                        reasons.append("Higher fees")

                    score += fee_score * 0.2
                except Exception:
                    # If fee calculation fails, use neutral score
                    score += 0.5 * 0.2

            # Apply optimization strategy weights
            if optimization == "cost" and amount:
                # Prioritize lower fees
                score = (
                    score * 0.7 + (fee_score if "fee_score" in locals() else 0.5) * 0.3
                )
            elif optimization == "success_rate":
                # Prioritize higher success rates
                score = score * 0.7 + success_rate * 0.3

            scores.append(
                GatewayScore(
                    gateway=gateway,
                    score=score,
                    reasons=reasons,
                    estimated_fee=estimated_fee,
                )
            )

        return scores

    def add_gateway(self, name: str, gateway: PaymentGateway):
        """Add a new gateway to the selector"""
        self.gateways[name] = gateway

    def remove_gateway(self, name: str):
        """Remove a gateway from the selector"""
        if name in self.gateways:
            del self.gateways[name]

    def get_available_gateways(self) -> Dict[str, PaymentGateway]:
        """Get all available gateways"""
        return self.gateways.copy()

    async def get_supported_currencies(self) -> List[str]:
        """Get all currencies supported by any gateway"""
        currencies = set()
        for gateway in self.gateways.values():
            gateway_currencies = await gateway.get_supported_currencies()
            currencies.update(gateway_currencies)
        return sorted(list(currencies))

    async def get_supported_countries(self) -> List[str]:
        """Get all countries supported by any gateway"""
        countries = set()
        for gateway in self.gateways.values():
            gateway_countries = await gateway.get_supported_countries()
            countries.update(gateway_countries)
        return sorted(list(countries))

    async def get_payment_methods_for_currency_country(
        self, currency: str, country: str
    ) -> Dict[str, List[str]]:
        """Get payment methods available from each gateway for currency/country"""
        methods = {}
        for gateway_name, gateway in self.gateways.items():
            if gateway.is_supported_currency(currency) and gateway.is_supported_country(
                country
            ):
                try:
                    gateway_methods = await gateway.get_payment_methods(
                        currency, country
                    )
                    methods[gateway_name] = gateway_methods
                except Exception:
                    methods[gateway_name] = ["card"]  # Fallback
        return methods
