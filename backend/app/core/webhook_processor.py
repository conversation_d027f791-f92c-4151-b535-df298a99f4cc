"""Enhanced webhook processing service with improved error handling and reliability"""

import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

from app.database import get_db
from app.models import Invoice, PaymentTransaction, User

from .payment_service_enhanced import EnhancedPaymentService


class WebhookProcessingError(Exception):
    """Custom exception for webhook processing errors"""

    def __init__(self, message: str, error_code: str = None, retry_after: int = None):
        super().__init__(message)
        self.error_code = error_code
        self.retry_after = retry_after


class WebhookProcessor:
    """Enhanced webhook processor with improved error handling and reliability"""

    def __init__(self, db: Session):
        self.db = db
        self.payment_service = EnhancedPaymentService(db)
        self.logger = logging.getLogger(__name__)

    async def process_paystack_webhook(
        self, payload: str, signature: str, request_id: str = None
    ) -> Dict[str, any]:
        """Process Paystack webhook with enhanced error handling"""

        request_id = request_id or str(uuid.uuid4())

        try:
            # Step 1: Verify webhook signature
            if signature is None:
                self.logger.warning(
                    f"Missing Paystack webhook signature - Request ID: {request_id}"
                )
                raise WebhookProcessingError(
                    "Missing webhook signature",
                    error_code="MISSING_SIGNATURE",
                    retry_after=300,
                )

            if not self.payment_service.verify_webhook_signature(
                payload, signature, "paystack"
            ):
                self.logger.warning(
                    f"Invalid Paystack webhook signature - Request ID: {request_id}"
                )
                raise WebhookProcessingError(
                    "Invalid webhook signature", error_code="INVALID_SIGNATURE"
                )

            # Step 2: Parse webhook data
            try:
                webhook_data = json.loads(payload)
            except json.JSONDecodeError as e:
                self.logger.error(
                    f"Invalid JSON in Paystack webhook - Request ID: {request_id}, Error: {e}"
                )
                raise WebhookProcessingError(
                    "Invalid JSON payload", error_code="INVALID_JSON"
                )

            # Step 3: Extract event type and data
            event_type = webhook_data.get("event")
            event_data = webhook_data.get("data", {})

            if not event_type:
                self.logger.error(
                    f"Missing event type in Paystack webhook - Request ID: {request_id}"
                )
                raise WebhookProcessingError(
                    "Missing event type", error_code="MISSING_EVENT_TYPE"
                )

            # Step 4: Process based on event type
            result = await self._process_paystack_event(
                event_type, event_data, request_id
            )

            self.logger.info(
                f"Successfully processed Paystack webhook - Request ID: {request_id}, "
                f"Event: {event_type}, Reference: {result.get('reference')}"
            )

            return {
                "status": "success",
                "request_id": request_id,
                "event_type": event_type,
                "data": result,
            }

        except WebhookProcessingError:
            raise
        except Exception as e:
            self.logger.error(
                f"Unexpected error processing Paystack webhook - Request ID: {request_id}, "
                f"Error: {str(e)}",
                exc_info=True,
            )
            raise WebhookProcessingError(
                f"Internal processing error: {str(e)}",
                error_code="INTERNAL_ERROR",
                retry_after=300,  # Retry after 5 minutes
            )

    async def process_dpo_webhook(
        self, payload: Dict, request_id: str = None
    ) -> Dict[str, any]:
        """Process DPO webhook with enhanced error handling"""

        request_id = request_id or str(uuid.uuid4())

        try:
            # Step 1: Validate DPO webhook data structure
            if not isinstance(payload, dict):
                self.logger.error(
                    f"Invalid DPO webhook payload format - Request ID: {request_id}"
                )
                raise WebhookProcessingError(
                    "Invalid payload format", error_code="INVALID_FORMAT"
                )

            # Step 2: Extract transaction reference
            reference = self._extract_dpo_reference(payload)
            if not reference:
                self.logger.error(
                    f"Missing transaction reference in DPO webhook - Request ID: {request_id}"
                )
                raise WebhookProcessingError(
                    "Missing transaction reference", error_code="MISSING_REFERENCE"
                )

            # Step 3: Process payment verification
            result = await self.payment_service.process_webhook(
                payload=payload, gateway_name="dpo"
            )

            self.logger.info(
                f"Successfully processed DPO webhook - Request ID: {request_id}, "
                f"Reference: {reference}, Status: {result.get('status')}"
            )

            return {
                "status": "success",
                "request_id": request_id,
                "reference": reference,
                "data": result,
            }

        except WebhookProcessingError:
            raise
        except Exception as e:
            self.logger.error(
                f"Unexpected error processing DPO webhook - Request ID: {request_id}, "
                f"Error: {str(e)}",
                exc_info=True,
            )
            raise WebhookProcessingError(
                f"Internal processing error: {str(e)}",
                error_code="INTERNAL_ERROR",
                retry_after=300,  # Retry after 5 minutes
            )

    async def _process_paystack_event(
        self, event_type: str, event_data: Dict, request_id: str
    ) -> Dict[str, any]:
        """Process specific Paystack event types"""

        if event_type == "charge.success":
            return await self._handle_payment_success(
                event_data, "paystack", request_id
            )
        elif event_type == "charge.failed":
            return await self._handle_payment_failure(
                event_data, "paystack", request_id
            )
        elif event_type == "transfer.success":
            return await self._handle_transfer_success(
                event_data, "paystack", request_id
            )
        elif event_type == "transfer.failed":
            return await self._handle_transfer_failure(
                event_data, "paystack", request_id
            )
        else:
            self.logger.info(
                f"Unhandled Paystack event type: {event_type} - Request ID: {request_id}"
            )
            return {
                "status": "ignored",
                "reason": f"Unhandled event type: {event_type}",
            }

    async def _handle_payment_success(
        self, event_data: Dict, gateway: str, request_id: str
    ) -> Dict[str, any]:
        """Handle successful payment events"""

        reference = event_data.get("reference")
        if not reference:
            raise WebhookProcessingError(
                "Missing payment reference", error_code="MISSING_REFERENCE"
            )

        try:
            # Verify payment with gateway
            payment_result = await self.payment_service.verify_payment(
                reference, gateway
            )
        except ValueError as e:
            # Convert ValueError to WebhookProcessingError with retry
            raise WebhookProcessingError(
                f"Payment verification failed: {str(e)}",
                error_code="PAYMENT_VERIFICATION_ERROR",
                retry_after=300,
            )

        # Update invoice status if payment is successful
        if payment_result.status.value == "success":
            await self._update_invoice_status(reference, "paid", payment_result)

        return {
            "reference": reference,
            "status": payment_result.status.value,
            "amount": str(payment_result.amount),
            "currency": payment_result.currency,
        }

    async def _handle_payment_failure(
        self, event_data: Dict, gateway: str, request_id: str
    ) -> Dict[str, any]:
        """Handle failed payment events"""

        reference = event_data.get("reference")
        if not reference:
            raise WebhookProcessingError(
                "Missing payment reference", error_code="MISSING_REFERENCE"
            )

        # Log payment failure
        self.logger.warning(
            f"Payment failed - Gateway: {gateway}, Reference: {reference}, "
            f"Request ID: {request_id}"
        )

        # Update invoice status
        await self._update_invoice_status(reference, "failed", None)

        return {
            "reference": reference,
            "status": "failed",
            "reason": event_data.get("gateway_response", "Payment failed"),
        }

    async def _handle_transfer_success(
        self, event_data: Dict, gateway: str, request_id: str
    ) -> Dict[str, any]:
        """Handle successful transfer/payout events"""

        transfer_code = event_data.get("transfer_code") or event_data.get("reference")

        self.logger.info(
            f"Transfer successful - Gateway: {gateway}, Transfer Code: {transfer_code}, "
            f"Request ID: {request_id}"
        )

        return {
            "transfer_code": transfer_code,
            "status": "success",
            "amount": event_data.get("amount"),
            "recipient": event_data.get("recipient"),
        }

    async def _handle_transfer_failure(
        self, event_data: Dict, gateway: str, request_id: str
    ) -> Dict[str, any]:
        """Handle failed transfer/payout events"""

        transfer_code = event_data.get("transfer_code") or event_data.get("reference")

        self.logger.error(
            f"Transfer failed - Gateway: {gateway}, Transfer Code: {transfer_code}, "
            f"Request ID: {request_id}, Reason: {event_data.get('reason')}"
        )

        return {
            "transfer_code": transfer_code,
            "status": "failed",
            "reason": event_data.get("reason", "Transfer failed"),
        }

    async def _update_invoice_status(
        self, reference: str, status: str, payment_result: Optional[any] = None
    ) -> None:
        """Update invoice status based on payment result"""

        try:
            # Find invoice by payment link token (reference)
            invoice = (
                self.db.query(Invoice)
                .filter(Invoice.payment_link_token == reference)
                .first()
            )

            if not invoice:
                self.logger.warning(f"Invoice not found for reference: {reference}")
                return

            # Update invoice status
            invoice.status = status

            if payment_result and status == "paid":
                invoice.paid_at = datetime.now(timezone.utc)

                # Create payment transaction record
                payment_transaction = PaymentTransaction(
                    id=uuid.uuid4(),
                    invoice_id=invoice.id,
                    amount=payment_result.amount,
                    currency=payment_result.currency,
                    gateway_reference=payment_result.reference,
                    gateway_response=payment_result.gateway_response,
                    status="completed",
                    created_at=datetime.now(timezone.utc),
                )

                self.db.add(payment_transaction)

            self.db.commit()

            self.logger.info(
                f"Updated invoice {invoice.id} status to {status} for reference {reference}"
            )

        except Exception as e:
            self.db.rollback()
            self.logger.error(
                f"Failed to update invoice status for reference {reference}: {str(e)}",
                exc_info=True,
            )
            raise

    def _extract_dpo_reference(self, payload: Dict) -> Optional[str]:
        """Extract transaction reference from DPO webhook payload"""

        # DPO can send reference in multiple locations
        reference_fields = [
            "TransactionRef",
            "transaction_id",
            "CompanyRef",
            "reference",
        ]

        # Check direct fields
        for field in reference_fields:
            if field in payload and payload[field]:
                return payload[field]

        # Check nested data
        data = payload.get("data", {})
        for field in reference_fields:
            if field in data and data[field]:
                return data[field]

        return None

    def get_webhook_health_status(self) -> Dict[str, any]:
        """Get webhook processing health status"""

        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "gateways": {
                "paystack": {
                    "available": "paystack" in self.payment_service.gateways,
                    "configured": bool(self.payment_service.gateways.get("paystack")),
                },
                "dpo": {
                    "available": "dpo" in self.payment_service.gateways,
                    "configured": bool(self.payment_service.gateways.get("dpo")),
                },
            },
        }
