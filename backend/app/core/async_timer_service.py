"""\nAsync Smart Timer Service for DevHQ\nHandles live timer operations, conflict resolution, and productivity tracking\n"""

import uuid
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.exceptions import TimerConflictError, TimerNotFoundError
from app.models import Project, TimeEntry, User


class AsyncSmartTimerService:
    """Async service for managing live timers with AI insights and conflict resolution."""

    HEARTBEAT_TIMEOUT_MINUTES = (
        5  # Timer considered dead after 5 minutes without heartbeat
    )
    MAX_SESSION_HOURS = 12  # Maximum continuous session length

    def __init__(self, db: AsyncSession):
        self.db = db

    async def start_timer(
        self,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        description: str = "",
        task_name: str = "",
        device_id: str = "web",
        mood_rating: Optional[int] = None,
        focus_level: Optional[int] = None,
    ) -> TimeEntry:
        """Start a new timer, stopping any existing active timers."""

        # Stop any existing active timers for this user
        await self._stop_existing_timers(user_id, device_id)

        # Verify project exists and belongs to user
        project = await self._get_user_project(user_id, project_id)

        # Create new timer entry
        now = datetime.now(timezone.utc)
        timer_entry = TimeEntry(
            project_id=project_id,
            user_id=user_id,
            start_time=now,
            duration_minutes=0,  # Will be calculated in real-time
            description=description,
            task_name=task_name,
            work_date=now,  # Use full datetime for work_date
            is_timer_active=True,
            timer_device_id=device_id,
            last_heartbeat=now,
            timer_started_from=device_id,
            mood_rating=mood_rating,
            focus_level=focus_level,
            hourly_rate=project.hourly_rate,
            is_billable=project.is_billable,
        )

        self.db.add(timer_entry)
        await self.db.commit()
        await self.db.refresh(timer_entry)

        return timer_entry

    async def stop_timer(
        self,
        user_id: uuid.UUID,
        timer_id: Optional[uuid.UUID] = None,
        device_id: str = "web",
    ) -> Optional[TimeEntry]:
        """Stop active timer and calculate final duration."""

        # Find active timer
        if timer_id:
            timer_entry = await self._get_user_timer(user_id, timer_id)
            # Validate device isolation - timer must belong to the specified device
            if timer_entry and timer_entry.timer_device_id != device_id:
                return None
        else:
            timer_entry = await self._get_active_timer(user_id, device_id)

        if not timer_entry or not timer_entry.is_timer_active:
            return None

        # Calculate final duration
        now = datetime.now(timezone.utc)
        if timer_entry.start_time:
            # Ensure both datetimes are timezone-aware for calculation
            start_time = timer_entry.start_time
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            total_duration = (now - start_time).total_seconds() / 60
            # Subtract pause duration
            final_duration = max(
                1, int(total_duration - timer_entry.total_pause_duration)
            )
            timer_entry.duration_minutes = final_duration

        # Stop timer
        timer_entry.is_timer_active = False
        timer_entry.end_time = now
        timer_entry.last_heartbeat = now

        # Calculate productivity score
        timer_entry.productivity_score = self._calculate_productivity_score(timer_entry)

        # Calculate billable amount
        timer_entry.calculate_billable_amount()

        await self.db.commit()
        await self.db.refresh(timer_entry)

        return timer_entry

    async def pause_timer(
        self,
        user_id: uuid.UUID,
        timer_id: Optional[uuid.UUID] = None,
        device_id: str = "web",
    ) -> Optional[TimeEntry]:
        """Pause active timer."""

        if timer_id:
            timer_entry = await self._get_user_timer(user_id, timer_id)
            # Validate device isolation - timer must belong to the specified device
            if timer_entry and timer_entry.timer_device_id != device_id:
                return None
        else:
            timer_entry = await self._get_active_timer(user_id, device_id)

        if (
            not timer_entry
            or not timer_entry.is_timer_active
            or timer_entry.timer_paused_at
        ):
            return None

        timer_entry.timer_paused_at = datetime.now(timezone.utc)
        await self.db.commit()
        await self.db.refresh(timer_entry)

        return timer_entry

    async def resume_timer(
        self,
        user_id: uuid.UUID,
        timer_id: Optional[uuid.UUID] = None,
        device_id: str = "web",
    ) -> Optional[TimeEntry]:
        """Resume paused timer."""

        if timer_id:
            timer_entry = await self._get_user_timer(user_id, timer_id)
            # Validate device isolation - timer must belong to the specified device
            if timer_entry and timer_entry.timer_device_id != device_id:
                return None
        else:
            timer_entry = await self._get_active_timer(user_id, device_id)

        if (
            not timer_entry
            or not timer_entry.is_timer_active
            or not timer_entry.timer_paused_at
        ):
            return None

        # Add pause duration to total
        now = datetime.now(timezone.utc)
        paused_at = timer_entry.timer_paused_at
        if paused_at.tzinfo is None:
            paused_at = paused_at.replace(tzinfo=timezone.utc)
        pause_duration = (now - paused_at).total_seconds() / 60
        timer_entry.total_pause_duration += max(
            1, int(pause_duration)
        )  # Minimum 1 minute for any pause
        timer_entry.timer_paused_at = None
        timer_entry.last_heartbeat = datetime.now(timezone.utc)

        await self.db.commit()
        await self.db.refresh(timer_entry)

        return timer_entry

    async def heartbeat(
        self,
        user_id: uuid.UUID,
        timer_id: Optional[uuid.UUID] = None,
        device_id: str = "web",
        interruption_occurred: bool = False,
    ) -> Optional[Dict]:
        """Update timer heartbeat and return current status."""

        if timer_id:
            timer_entry = await self._get_user_timer(user_id, timer_id)
        else:
            timer_entry = await self._get_active_timer(user_id, device_id)

        if not timer_entry or not timer_entry.is_timer_active:
            return None

        now = datetime.now(timezone.utc)
        timer_entry.last_heartbeat = now

        # Track interruptions
        if interruption_occurred:
            timer_entry.interruption_count += 1

        # Calculate current duration
        if timer_entry.start_time:
            # Ensure both datetimes are timezone-aware for calculation
            start_time = timer_entry.start_time
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            total_duration = (now - start_time).total_seconds() / 60
            current_duration = max(
                0, int(total_duration - timer_entry.total_pause_duration)
            )

            # Auto-stop if session is too long
            if current_duration > (self.MAX_SESSION_HOURS * 60):
                return await self.stop_timer(user_id, timer_entry.id, device_id)
        else:
            current_duration = 0

        await self.db.commit()

        return {
            "timer_id": str(timer_entry.id),
            "is_active": True,
            "is_paused": timer_entry.timer_paused_at is not None,
            "current_duration_minutes": current_duration,
            "start_time": (
                timer_entry.start_time.isoformat() if timer_entry.start_time else None
            ),
            "project_id": str(timer_entry.project_id),
            "description": timer_entry.description,
            "task_name": timer_entry.task_name,
            "interruption_count": timer_entry.interruption_count,
        }

    async def get_timer_status(
        self, user_id: uuid.UUID, device_id: str = "web"
    ) -> Optional[Dict]:
        """Get current timer status for user."""

        timer_entry = await self._get_active_timer(user_id, device_id)

        if not timer_entry:
            return None

        # Check if timer is stale (no heartbeat)
        if self._is_timer_stale(timer_entry):
            # Auto-stop stale timer
            await self.stop_timer(user_id, timer_entry.id, device_id)
            return None

        # Calculate current duration
        now = datetime.now(timezone.utc)
        if timer_entry.start_time:
            # Ensure both datetimes are timezone-aware for calculation
            start_time = timer_entry.start_time
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            total_duration = (now - start_time).total_seconds() / 60
            current_duration = max(
                0, int(total_duration - timer_entry.total_pause_duration)
            )
        else:
            current_duration = 0

        return {
            "timer_id": str(timer_entry.id),
            "is_active": True,
            "is_paused": timer_entry.timer_paused_at is not None,
            "current_duration_minutes": current_duration,
            "start_time": (
                timer_entry.start_time.isoformat() if timer_entry.start_time else None
            ),
            "project_id": str(timer_entry.project_id),
            "description": timer_entry.description,
            "task_name": timer_entry.task_name,
            "interruption_count": timer_entry.interruption_count,
            "mood_rating": timer_entry.mood_rating,
            "focus_level": timer_entry.focus_level,
        }

    async def cleanup_stale_timers(self) -> int:
        """Clean up stale timers that haven't received heartbeats."""

        cutoff_time = datetime.now(timezone.utc) - timedelta(
            minutes=self.HEARTBEAT_TIMEOUT_MINUTES
        )

        stmt = select(TimeEntry).where(
            and_(
                TimeEntry.is_timer_active == True,
                TimeEntry.last_heartbeat < cutoff_time,
            )
        )
        result = await self.db.execute(stmt)
        stale_timers = result.scalars().all()

        count = 0
        for timer in stale_timers:
            # Calculate duration up to last heartbeat
            if timer.start_time and timer.last_heartbeat:
                # Ensure both datetimes are timezone-aware for calculation
                start_time = timer.start_time
                if start_time.tzinfo is None:
                    start_time = start_time.replace(tzinfo=timezone.utc)
                last_heartbeat = timer.last_heartbeat
                if last_heartbeat.tzinfo is None:
                    last_heartbeat = last_heartbeat.replace(tzinfo=timezone.utc)

                total_duration = (last_heartbeat - start_time).total_seconds() / 60
                final_duration = max(
                    1, int(total_duration - timer.total_pause_duration)
                )
                timer.duration_minutes = final_duration

            timer.is_timer_active = False
            timer.end_time = timer.last_heartbeat
            timer.productivity_score = self._calculate_productivity_score(timer)
            timer.calculate_billable_amount()
            count += 1

        if count > 0:
            await self.db.commit()

        return count

    async def _stop_existing_timers(self, user_id: uuid.UUID, device_id: str):
        """Stop any existing active timers for the user."""

        stmt = select(TimeEntry).where(
            and_(TimeEntry.user_id == user_id, TimeEntry.is_timer_active == True)
        )
        result = await self.db.execute(stmt)
        active_timers = result.scalars().all()

        for timer in active_timers:
            await self.stop_timer(user_id, timer.id, device_id)

    async def _get_user_project(
        self, user_id: uuid.UUID, project_id: uuid.UUID
    ) -> Project:
        """Get project and verify user access."""

        stmt = select(Project).where(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.deleted_at.is_(None),
            )
        )
        result = await self.db.execute(stmt)
        project = result.scalar_one_or_none()

        if not project:
            raise ValueError("Project not found or access denied")

        return project

    async def _get_user_timer(
        self, user_id: uuid.UUID, timer_id: uuid.UUID
    ) -> Optional[TimeEntry]:
        """Get timer entry and verify user access."""

        stmt = select(TimeEntry).where(
            and_(
                TimeEntry.id == timer_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_active_timer(
        self, user_id: uuid.UUID, device_id: str = None
    ) -> Optional[TimeEntry]:
        """Get active timer for user, optionally filtered by device."""

        conditions = [
            TimeEntry.user_id == user_id,
            TimeEntry.is_timer_active == True,
            TimeEntry.deleted_at.is_(None),
        ]

        if device_id:
            conditions.append(TimeEntry.timer_device_id == device_id)

        stmt = (
            select(TimeEntry)
            .where(and_(*conditions))
            .order_by(desc(TimeEntry.created_at))
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    def _is_timer_stale(self, timer_entry: TimeEntry) -> bool:
        """Check if timer is stale (no recent heartbeat)."""

        if not timer_entry.last_heartbeat:
            return True

        cutoff_time = datetime.now(timezone.utc) - timedelta(
            minutes=self.HEARTBEAT_TIMEOUT_MINUTES
        )

        # Ensure both datetimes are timezone-aware for comparison
        last_heartbeat = timer_entry.last_heartbeat
        if last_heartbeat.tzinfo is None:
            last_heartbeat = last_heartbeat.replace(tzinfo=timezone.utc)

        return last_heartbeat < cutoff_time

    def _calculate_productivity_score(self, timer_entry: TimeEntry) -> Decimal:
        """Calculate productivity score based on various factors."""

        if not timer_entry.duration_minutes or timer_entry.duration_minutes < 5:
            return Decimal("5.0")  # Neutral score for very short sessions

        score = Decimal("7.0")  # Base score

        # Factor in session length (optimal 25-90 minutes)
        duration = timer_entry.duration_minutes
        if 25 <= duration <= 90:
            score += Decimal("1.0")
        elif duration < 15:
            score -= Decimal("1.0")
        elif duration > 180:
            score -= Decimal("0.5")

        # Factor in interruptions
        if timer_entry.interruption_count:
            interruption_penalty = min(
                Decimal("2.0"),
                Decimal(str(timer_entry.interruption_count)) * Decimal("0.3"),
            )
            score -= interruption_penalty

        # Factor in break ratio (healthy break patterns)
        if timer_entry.total_pause_duration and timer_entry.duration_minutes:
            break_ratio = (
                timer_entry.total_pause_duration / timer_entry.duration_minutes
            )
            if 0.05 <= break_ratio <= 0.15:  # 5-15% break time is healthy
                score += Decimal("0.5")
            elif break_ratio > 0.3:  # Too many breaks
                score -= Decimal("1.0")

        # Factor in mood and focus if provided
        if timer_entry.mood_rating:
            if timer_entry.mood_rating >= 4:
                score += Decimal("0.5")
            elif timer_entry.mood_rating <= 2:
                score -= Decimal("0.5")

        if timer_entry.focus_level:
            if timer_entry.focus_level >= 4:
                score += Decimal("0.5")
            elif timer_entry.focus_level <= 2:
                score -= Decimal("0.5")

        # Ensure score is within bounds
        return max(Decimal("1.0"), min(Decimal("10.0"), score))
