"""Monitoring and Observability Service for DevHQ Backend

Provides comprehensive monitoring capabilities including:
- OpenTelemetry distributed tracing
- Custom metrics collection
- Performance monitoring
- Business metrics tracking
"""

import asyncio
import logging
import time
from contextlib import contextmanager
from datetime import datetime, timezone
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Union

import structlog
from opentelemetry import metrics, trace
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import \
    OTLPMetricExporter
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import \
    OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.psycopg2 import Psycopg2Instrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from sqlalchemy.orm import Session

from app.config import settings
from app.core.monitoring_config import MetricType, monitoring_config

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class MonitoringService:
    """Comprehensive monitoring and observability service."""

    def __init__(self):
        self.settings = settings
        self.config = monitoring_config
        self.logger = structlog.get_logger(__name__)
        self.tracer = None
        self.meter = None
        self.metrics = {}
        self._setup_telemetry()
        self._setup_metrics()

    def _setup_telemetry(self):
        """Initialize OpenTelemetry tracing and metrics."""
        try:
            if not self.config.tracing.enabled:
                self.logger.info("Tracing disabled by configuration")
                return

            # Create resource with service information
            resource = Resource.create(
                {
                    "service.name": self.config.tracing.service_name,
                    "service.version": self.config.tracing.service_version,
                    "deployment.environment": self.config.tracing.environment,
                }
            )

            # Setup tracing
            if self.config.tracing.export_endpoint:
                trace_exporter = OTLPSpanExporter(
                    endpoint=self.config.tracing.export_endpoint,
                    headers=self.config.tracing.export_headers or {},
                )
                trace_provider = TracerProvider(
                    resource=resource,
                    sampler=trace.sampling.TraceIdRatioBasedSampler(
                        self.config.tracing.sample_rate
                    ),
                )
                trace_provider.add_span_processor(BatchSpanProcessor(trace_exporter))
                trace.set_tracer_provider(trace_provider)

                self.logger.info(
                    "OpenTelemetry tracing initialized",
                    sample_rate=self.config.tracing.sample_rate,
                )

            self.tracer = trace.get_tracer(__name__)

            # Setup metrics
            if (
                self.config.tracing.export_endpoint
                and self.config.get_enabled_metrics()
            ):
                metric_exporter = OTLPMetricExporter(
                    endpoint=self.config.tracing.export_endpoint,
                    headers=self.config.tracing.export_headers or {},
                )
                metric_reader = PeriodicExportingMetricReader(
                    exporter=metric_exporter,
                    export_interval_millis=30000,  # Export every 30 seconds
                )
                metric_provider = MeterProvider(
                    resource=resource, metric_readers=[metric_reader]
                )
                metrics.set_meter_provider(metric_provider)

                self.logger.info(
                    "OpenTelemetry metrics initialized",
                    metrics_count=len(self.config.get_enabled_metrics()),
                )

            self.meter = metrics.get_meter(__name__)
            self._setup_metrics()

        except Exception as e:
            self.logger.error("Failed to setup telemetry", error=str(e))

    def _setup_metrics(self):
        """Initialize custom metrics based on configuration."""
        if not self.meter:
            return

        for metric_config in self.config.get_enabled_metrics():
            try:
                if metric_config.metric_type == MetricType.COUNTER:
                    self.metrics[metric_config.name] = self.meter.create_counter(
                        name=metric_config.name,
                        description=metric_config.description,
                        unit="1",
                    )
                elif metric_config.metric_type == MetricType.HISTOGRAM:
                    self.metrics[metric_config.name] = self.meter.create_histogram(
                        name=metric_config.name,
                        description=metric_config.description,
                        unit="s" if "duration" in metric_config.name else "1",
                    )
                elif metric_config.metric_type == MetricType.GAUGE:
                    self.metrics[
                        metric_config.name
                    ] = self.meter.create_up_down_counter(
                        name=metric_config.name,
                        description=metric_config.description,
                        unit="1",
                    )

                self.logger.debug(
                    "Metric created",
                    metric_name=metric_config.name,
                    metric_type=metric_config.metric_type.value,
                )
            except Exception as e:
                self.logger.error(
                    "Failed to create metric",
                    metric_name=metric_config.name,
                    error=str(e),
                )

    def instrument_fastapi(self, app):
        """Instrument FastAPI application with OpenTelemetry."""
        FastAPIInstrumentor.instrument_app(
            app, tracer_provider=trace.get_tracer_provider()
        )
        HTTPXClientInstrumentor().instrument()
        RedisInstrumentor().instrument()
        SQLAlchemyInstrumentor().instrument()
        Psycopg2Instrumentor().instrument()

    @contextmanager
    def trace_operation(self, operation_name: str, **attributes):
        """Context manager for tracing operations."""
        if not self.tracer:
            yield
            return

        with self.tracer.start_as_current_span(operation_name) as span:
            for key, value in attributes.items():
                span.set_attribute(key, value)

            start_time = time.time()
            try:
                yield span
            except Exception as e:
                span.record_exception(e)
                span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                raise
            finally:
                duration = time.time() - start_time
                span.set_attribute("operation.duration", duration)

    def record_request_metrics(
        self, method: str, endpoint: str, status_code: int, duration: float
    ):
        """Record HTTP request metrics."""
        if not self.metrics:
            return

        labels = {
            "method": method,
            "endpoint": endpoint,
            "status_code": str(status_code),
        }

        if "request_count" in self.metrics:
            self.metrics["request_count"].add(1, labels)

        if "request_duration" in self.metrics:
            self.metrics["request_duration"].record(duration, labels)

    def record_db_query_metrics(self, query_type: str, table: str, duration: float):
        """Record database query metrics."""
        if not self.metrics or "db_query_duration" not in self.metrics:
            return

        labels = {"query_type": query_type, "table": table}

        self.metrics["db_query_duration"].record(duration, labels)

    def record_business_metric(
        self, metric_name: str, value: Union[int, float] = 1, **labels
    ):
        """Record business-specific metrics."""
        if not self.metrics or metric_name not in self.metrics:
            return

        metric = self.metrics[metric_name]
        if hasattr(metric, "add"):
            metric.add(value, labels)
        elif hasattr(metric, "record"):
            metric.record(value, labels)

    def record_cache_metrics(self, operation: str, hit: bool, **labels):
        """Record cache operation metrics."""
        if not self.metrics:
            return

        metric_name = "cache_hits" if hit else "cache_misses"
        if metric_name in self.metrics:
            self.metrics[metric_name].add(1, {"operation": operation, **labels})

    def log_structured(self, level: str, message: str, **context):
        """Log structured data with context."""
        log_func = getattr(logger, level.lower(), logger.info)
        log_func(message, **context)

    def log_business_event(
        self, event_type: str, entity_type: str, entity_id: str, **context
    ):
        """Log business events with structured data."""
        self.log_structured(
            "info",
            f"{event_type} {entity_type}",
            event_type=event_type,
            entity_type=entity_type,
            entity_id=entity_id,
            timestamp=datetime.now(timezone.utc).isoformat(),
            **context,
        )

    def log_performance_warning(
        self, operation: str, duration: float, threshold: float, **context
    ):
        """Log performance warnings for slow operations."""
        if duration > threshold:
            self.log_structured(
                "warning",
                f"Slow {operation} detected",
                operation=operation,
                duration=duration,
                threshold=threshold,
                performance_issue=True,
                **context,
            )

    def log_security_event(
        self,
        event_type: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        **context,
    ):
        """Log security-related events."""
        self.log_structured(
            "warning",
            f"Security event: {event_type}",
            event_type=event_type,
            security_event=True,
            user_id=user_id,
            ip_address=ip_address,
            timestamp=datetime.now(timezone.utc).isoformat(),
            **context,
        )


# Global monitoring service instance
monitoring_service = MonitoringService()


def trace_function(operation_name: Optional[str] = None):
    """Decorator for tracing function calls."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            name = operation_name or f"{func.__module__}.{func.__name__}"
            with monitoring_service.trace_operation(name, function=func.__name__):
                return await func(*args, **kwargs)

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            name = operation_name or f"{func.__module__}.{func.__name__}"
            with monitoring_service.trace_operation(name, function=func.__name__):
                return func(*args, **kwargs)

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


@contextmanager
def monitor_db_query(query_type: str, table: str):
    """Context manager for monitoring database queries."""
    start_time = time.time()
    try:
        yield
    finally:
        duration = time.time() - start_time
        monitoring_service.record_db_query_metrics(query_type, table, duration)
        monitoring_service.log_performance_warning(
            f"Database {query_type}",
            duration,
            threshold=1.0,  # 1 second threshold
            table=table,
        )


def monitor_business_operation(operation_type: str, entity_type: str):
    """Decorator for monitoring business operations."""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                monitoring_service.record_business_metric(
                    f"{operation_type}_total",
                    1,
                    entity_type=entity_type,
                    status="success",
                )
                monitoring_service.log_business_event(
                    operation_type,
                    entity_type,
                    str(getattr(result, "id", "unknown")),
                    status="success",
                )
                return result
            except Exception as e:
                monitoring_service.record_business_metric(
                    f"{operation_type}_total",
                    1,
                    entity_type=entity_type,
                    status="error",
                )
                monitoring_service.log_business_event(
                    operation_type, entity_type, "unknown", status="error", error=str(e)
                )
                raise
            finally:
                duration = time.time() - start_time
                monitoring_service.log_performance_warning(
                    f"{operation_type} {entity_type}",
                    duration,
                    threshold=2.0,  # 2 second threshold for business operations
                )

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                monitoring_service.record_business_metric(
                    f"{operation_type}_total",
                    1,
                    entity_type=entity_type,
                    status="success",
                )
                monitoring_service.log_business_event(
                    operation_type,
                    entity_type,
                    str(getattr(result, "id", "unknown")),
                    status="success",
                )
                return result
            except Exception as e:
                monitoring_service.record_business_metric(
                    f"{operation_type}_total",
                    1,
                    entity_type=entity_type,
                    status="error",
                )
                monitoring_service.log_business_event(
                    operation_type, entity_type, "unknown", status="error", error=str(e)
                )
                raise
            finally:
                duration = time.time() - start_time
                monitoring_service.log_performance_warning(
                    f"{operation_type} {entity_type}",
                    duration,
                    threshold=2.0,  # 2 second threshold for business operations
                )

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator
