"""
Caching Layer for DevHQ Backend
Provides Redis-based caching with automatic fallback and performance monitoring
"""

import asyncio
import json
import logging
import pickle
import time
from functools import wraps
from typing import Any, Dict, Optional, Union

import redis
from redis import Redis
from redis.asyncio import Redis as AsyncRedis

from app.config import settings

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Redis-based caching manager with automatic fallback and performance monitoring
    """

    def __init__(self, redis_url: str = None, default_ttl: int = 3600):
        """
        Initialize CacheManager

        Args:
            redis_url (str): Redis connection URL
            default_ttl (int): Default time-to-live in seconds
        """
        self.redis_url = redis_url or settings.redis_url
        self.default_ttl = default_ttl or settings.redis_cache_ttl
        self.redis_client: Optional[AsyncRedis] = None
        self.sync_redis_client: Optional[Redis] = None
        self.connected = False
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "errors": 0,
        }

    async def connect(self):
        """Connect to Redis asynchronously"""
        try:
            if not self.redis_url:
                logger.warning("No Redis URL configured, caching disabled")
                return

            self.redis_client = AsyncRedis.from_url(
                self.redis_url,
                decode_responses=False,  # Use binary responses for better performance
                retry_on_timeout=True,
                socket_keepalive=True,
                health_check_interval=30,
            )

            # Test connection
            await self.redis_client.ping()
            self.connected = True
            logger.info("Redis cache connected successfully")

        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.connected = False
            self.redis_client = None

    def connect_sync(self):
        """Connect to Redis synchronously"""
        try:
            if not self.redis_url:
                logger.warning("No Redis URL configured, caching disabled")
                return

            self.sync_redis_client = Redis.from_url(
                self.redis_url,
                decode_responses=False,
                retry_on_timeout=True,
                socket_keepalive=True,
                health_check_interval=30,
            )

            # Test connection
            self.sync_redis_client.ping()
            self.connected = True
            logger.info("Redis cache (sync) connected successfully")

        except Exception as e:
            logger.error(f"Failed to connect to Redis (sync): {e}")
            self.connected = False
            self.sync_redis_client = None

    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
        if self.sync_redis_client:
            self.sync_redis_client.close()
            self.sync_redis_client = None
        self.connected = False

    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache

        Args:
            key (str): Cache key

        Returns:
            Any: Cached value or None if not found
        """
        if not self.connected or not self.redis_client:
            self.stats["misses"] += 1
            return None

        try:
            value = await self.redis_client.get(key)
            if value is None:
                self.stats["misses"] += 1
                return None

            # Deserialize value
            result = pickle.loads(value)
            self.stats["hits"] += 1
            return result

        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.stats["errors"] += 1
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache

        Args:
            key (str): Cache key
            value (Any): Value to cache
            ttl (int, optional): Time-to-live in seconds

        Returns:
            bool: True if successful
        """
        if not self.connected or not self.redis_client:
            return False

        try:
            # Serialize value
            serialized_value = pickle.dumps(value)

            # Set with TTL
            actual_ttl = ttl if ttl is not None else self.default_ttl
            result = await self.redis_client.setex(key, actual_ttl, serialized_value)

            if result:
                self.stats["sets"] += 1
            return bool(result)

        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            self.stats["errors"] += 1
            return False

    async def delete(self, key: str) -> bool:
        """
        Delete value from cache

        Args:
            key (str): Cache key

        Returns:
            bool: True if successful
        """
        if not self.connected or not self.redis_client:
            return False

        try:
            result = await self.redis_client.delete(key)
            if result:
                self.stats["deletes"] += 1
            return bool(result)

        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            self.stats["errors"] += 1
            return False

    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache

        Args:
            key (str): Cache key

        Returns:
            bool: True if key exists
        """
        if not self.connected or not self.redis_client:
            return False

        try:
            return bool(await self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False

    def get_sync(self, key: str) -> Optional[Any]:
        """
        Get value from cache (synchronous)

        Args:
            key (str): Cache key

        Returns:
            Any: Cached value or None if not found
        """
        if not self.connected or not self.sync_redis_client:
            self.stats["misses"] += 1
            return None

        try:
            value = self.sync_redis_client.get(key)
            if value is None:
                self.stats["misses"] += 1
                return None

            # Deserialize value
            result = pickle.loads(value)
            self.stats["hits"] += 1
            return result

        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.stats["errors"] += 1
            return None

    def set_sync(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache (synchronous)

        Args:
            key (str): Cache key
            value (Any): Value to cache
            ttl (int, optional): Time-to-live in seconds

        Returns:
            bool: True if successful
        """
        if not self.connected or not self.sync_redis_client:
            return False

        try:
            # Serialize value
            serialized_value = pickle.dumps(value)

            # Set with TTL
            actual_ttl = ttl if ttl is not None else self.default_ttl
            result = self.sync_redis_client.setex(key, actual_ttl, serialized_value)

            if result:
                self.stats["sets"] += 1
            return bool(result)

        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            self.stats["errors"] += 1
            return False

    def delete_sync(self, key: str) -> bool:
        """
        Delete value from cache (synchronous)

        Args:
            key (str): Cache key

        Returns:
            bool: True if successful
        """
        if not self.connected or not self.sync_redis_client:
            return False

        try:
            result = self.sync_redis_client.delete(key)
            if result:
                self.stats["deletes"] += 1
            return bool(result)

        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            self.stats["errors"] += 1
            return False

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics

        Returns:
            Dict[str, Any]: Cache statistics
        """
        hit_rate = 0
        if (self.stats["hits"] + self.stats["misses"]) > 0:
            hit_rate = self.stats["hits"] / (self.stats["hits"] + self.stats["misses"])

        return {
            "connected": self.connected,
            "hit_rate": hit_rate,
            "stats": self.stats.copy(),
        }


# Global cache manager instance
cache_manager = CacheManager()


class CacheKeyBuilder:
    """
    Utility class for building consistent cache keys
    """

    @staticmethod
    def user_profile(user_id: str) -> str:
        """Build cache key for user profile"""
        return f"user:profile:{user_id}"

    @staticmethod
    def user_settings(user_id: str) -> str:
        """Build cache key for user settings"""
        return f"user:settings:{user_id}"

    @staticmethod
    def user_projects(
        user_id: str,
        page: int = 1,
        per_page: int = 25,
        client_id: Optional[str] = None,
        workspace_id: Optional[str] = None,
        status: Optional[str] = None,
        billing_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> str:
        """Build cache key for user projects list"""
        # Create a consistent filter string from all parameters
        filters = []
        if client_id:
            filters.append(f"client:{client_id}")
        if workspace_id:
            filters.append(f"workspace:{workspace_id}")
        if status:
            filters.append(f"status:{status}")
        if billing_type:
            filters.append(f"billing:{billing_type}")
        if search:
            filters.append(f"search:{search}")

        filter_str = "|".join(sorted(filters)) if filters else "all"
        filter_hash = hash(filter_str)
        return f"user:projects:{user_id}:page:{page}:per_page:{per_page}:filters:{filter_hash}"

    @staticmethod
    def user_dashboard(user_id: str) -> str:
        """Build cache key for user dashboard data"""
        return f"user:dashboard:{user_id}"

    @staticmethod
    def project_details(project_id: str) -> str:
        """Build cache key for project details"""
        return f"project:details:{project_id}"

    @staticmethod
    def project_milestones(project_id: str, page: int = 1) -> str:
        """Build cache key for project milestones"""
        return f"project:milestones:{project_id}:page:{page}"

    @staticmethod
    def client_projects(client_id: str, page: int = 1) -> str:
        """Build cache key for client projects"""
        return f"client:projects:{client_id}:page:{page}"

    @staticmethod
    def project_stats(project_id: str) -> str:
        """Build cache key for project statistics"""
        return f"project:stats:{project_id}"


def cached(ttl: Optional[int] = None, key_prefix: str = ""):
    """
    Decorator for caching function results

    Args:
        ttl (int, optional): Time-to-live in seconds
        key_prefix (str): Prefix for cache keys

    Example:
        >>> @cached(ttl=300, key_prefix="user_profile")
        >>> async def get_user_profile(user_id: str):
        >>>     # Expensive database query
        >>>     return user_data
        >>>
        >>> # Usage
        >>> profile = await get_user_profile("user123")
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            key_parts = [key_prefix, func.__name__]
            if args:
                key_parts.extend(str(arg) for arg in args)
            if kwargs:
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))

            cache_key = ":".join(key_parts)

            # Try to get from cache
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)

            return result

        return wrapper

    return decorator


def cached_sync(ttl: Optional[int] = None, key_prefix: str = ""):
    """
    Decorator for caching synchronous function results

    Args:
        ttl (int, optional): Time-to-live in seconds
        key_prefix (str): Prefix for cache keys

    Example:
        >>> @cached_sync(ttl=300, key_prefix="project_stats")
        >>> def get_project_statistics(project_id: str):
        >>>     # Expensive calculation
        >>>     return stats
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            key_parts = [key_prefix, func.__name__]
            if args:
                key_parts.extend(str(arg) for arg in args)
            if kwargs:
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))

            cache_key = ":".join(key_parts)

            # Try to get from cache
            cached_result = cache_manager.get_sync(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set_sync(cache_key, result, ttl)

            return result

        return wrapper

    return decorator


# Context manager for cache operations
class CacheContext:
    """Context manager for cache operations with automatic cleanup"""

    def __init__(self, cache_manager: CacheManager, prefix: str = ""):
        self.cache_manager = cache_manager
        self.prefix = prefix
        self.keys_to_delete = []

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Clean up keys on exit
        for key in self.keys_to_delete:
            await self.cache_manager.delete(key)

    def add_key_for_cleanup(self, key: str):
        """Add key to be cleaned up when context exits"""
        self.keys_to_delete.append(key)

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache with prefix"""
        full_key = f"{self.prefix}:{key}" if self.prefix else key
        return await self.cache_manager.get(full_key)

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with prefix"""
        full_key = f"{self.prefix}:{key}" if self.prefix else key
        result = await self.cache_manager.set(full_key, value, ttl)
        if result:
            self.add_key_for_cleanup(full_key)
        return result


# Utility functions for common caching patterns
async def cache_user_profile(user_id: str, profile_data: Dict, ttl: int = 1800) -> bool:
    """
    Cache user profile data

    Args:
        user_id (str): User ID
        profile_data (Dict): User profile data
        ttl (int): Time-to-live in seconds

    Returns:
        bool: True if cached successfully
    """
    key = f"user:profile:{user_id}"
    return await cache_manager.set(key, profile_data, ttl)


async def get_cached_user_profile(user_id: str) -> Optional[Dict]:
    """
    Get cached user profile data

    Args:
        user_id (str): User ID

    Returns:
        Optional[Dict]: Cached user profile or None
    """
    key = f"user:profile:{user_id}"
    return await cache_manager.get(key)


async def invalidate_user_cache(user_id: str) -> bool:
    """
    Invalidate all cache entries for a user

    Args:
        user_id (str): User ID

    Returns:
        bool: True if invalidated successfully
    """
    keys_to_delete = [
        f"user:profile:{user_id}",
        f"user:settings:{user_id}",
        f"user:projects:{user_id}",
    ]

    results = []
    for key in keys_to_delete:
        result = await cache_manager.delete(key)
        results.append(result)

    return all(results)
