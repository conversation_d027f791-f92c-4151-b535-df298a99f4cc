"""
Advanced Time Analytics Service for DevHQ
Provides productivity insights, time distribution analysis, and efficiency metrics
"""

import uuid
from collections import defaultdict
from datetime import date, datetime, timedelta, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, extract, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import Project, TimeEntry, User


class TimeAnalyticsService:
    """Service for generating advanced time tracking analytics and insights."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_advanced_productivity_insights(
        self, user_id: uuid.UUID, days: int = 30
    ) -> Dict:
        """Generate comprehensive productivity insights with AI-powered recommendations"""
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)

        # Get time entries for the period
        time_entries = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.start_time >= start_date,
                    TimeEntry.end_time.isnot(None),
                )
            )
            .all()
        )

        if not time_entries:
            return {
                "total_hours": 0,
                "daily_average": 0,
                "productivity_score": 0,
                "peak_hours": [],
                "project_distribution": {},
                "trends": {},
                "recommendations": [],
                "focus_patterns": {},
                "efficiency_metrics": {},
            }

        # Calculate comprehensive metrics
        total_duration = sum(
            (entry.end_time - entry.start_time).total_seconds() / 3600
            for entry in time_entries
        )
        daily_average = total_duration / days

        # Analyze peak productivity hours
        hourly_distribution = {}
        focus_scores = {}
        for entry in time_entries:
            hour = entry.start_time.hour
            duration = (entry.end_time - entry.start_time).total_seconds() / 3600
            hourly_distribution[hour] = hourly_distribution.get(hour, 0) + duration

            # Track focus levels if available
            if hasattr(entry, "focus_level") and entry.focus_level:
                focus_scores[hour] = focus_scores.get(hour, [])
                focus_scores[hour].append(entry.focus_level)

        peak_hours = sorted(
            hourly_distribution.items(), key=lambda x: x[1], reverse=True
        )[:3]

        # Project time distribution with efficiency analysis
        project_distribution = {}
        project_efficiency = {}
        for entry in time_entries:
            project_name = entry.project.name if entry.project else "No Project"
            duration = (entry.end_time - entry.start_time).total_seconds() / 3600
            project_distribution[project_name] = (
                project_distribution.get(project_name, 0) + duration
            )

            # Calculate efficiency based on productivity score
            if hasattr(entry, "productivity_score") and entry.productivity_score:
                if project_name not in project_efficiency:
                    project_efficiency[project_name] = []
                project_efficiency[project_name].append(entry.productivity_score)

        # Calculate productivity score (0-100) with advanced metrics
        base_score = min(100, int((daily_average / 8) * 100))

        # Adjust based on focus patterns and consistency
        consistency_bonus = self._calculate_consistency_bonus(time_entries)
        focus_bonus = self._calculate_focus_bonus(focus_scores)
        productivity_score = min(100, base_score + consistency_bonus + focus_bonus)

        # Generate trends and patterns
        weekly_hours = self._calculate_weekly_trends(time_entries, days)
        focus_patterns = self._analyze_focus_patterns(focus_scores)
        efficiency_metrics = self._calculate_efficiency_metrics(project_efficiency)

        # AI-powered recommendations
        recommendations = self._generate_advanced_recommendations(
            daily_average,
            peak_hours,
            project_distribution,
            weekly_hours,
            focus_patterns,
            efficiency_metrics,
        )

        return {
            "total_hours": round(total_duration, 2),
            "daily_average": round(daily_average, 2),
            "productivity_score": productivity_score,
            "peak_hours": [
                {"hour": h, "total_hours": round(t, 2)} for h, t in peak_hours
            ],
            "project_distribution": {
                k: round(v, 2) for k, v in project_distribution.items()
            },
            "trends": {
                "weekly_hours": weekly_hours,
                "trend_direction": self._calculate_trend_direction(weekly_hours),
            },
            "focus_patterns": focus_patterns,
            "efficiency_metrics": efficiency_metrics,
            "recommendations": recommendations,
        }

    async def detect_timer_conflicts(self, user_id: uuid.UUID) -> List[Dict]:
        """Detect overlapping timer sessions that need intelligent resolution"""
        # Get active and recent time entries
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)

        time_entries = (
            self.db.query(TimeEntry)
            .filter(
                and_(TimeEntry.user_id == user_id, TimeEntry.start_time >= cutoff_time)
            )
            .order_by(TimeEntry.start_time)
            .all()
        )

        conflicts = []
        for i, entry1 in enumerate(time_entries):
            for entry2 in time_entries[i + 1 :]:
                if self._entries_overlap(entry1, entry2):
                    conflict_data = {
                        "conflict_id": f"{entry1.id}_{entry2.id}",
                        "entry1": self._serialize_time_entry(entry1),
                        "entry2": self._serialize_time_entry(entry2),
                        "overlap_duration": self._calculate_overlap_duration(
                            entry1, entry2
                        ),
                        "suggested_resolution": self._suggest_intelligent_resolution(
                            entry1, entry2
                        ),
                        "confidence_score": self._calculate_resolution_confidence(
                            entry1, entry2
                        ),
                    }
                    conflicts.append(conflict_data)

        return conflicts

    async def generate_ai_time_suggestions(self, user_id: uuid.UUID) -> List[Dict]:
        """Generate AI-powered time entry suggestions based on patterns and context"""
        # Analyze historical patterns (last 90 days)
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=90)

        time_entries = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.start_time >= start_date,
                    TimeEntry.end_time.isnot(None),
                )
            )
            .all()
        )

        if len(time_entries) < 10:  # Need sufficient data
            return []

        suggestions = []

        # Pattern analysis
        daily_patterns = self._analyze_daily_patterns(time_entries)
        project_patterns = self._analyze_project_patterns(time_entries)
        context_patterns = self._analyze_contextual_patterns(time_entries)

        suggestions.extend(daily_patterns)
        suggestions.extend(project_patterns)
        suggestions.extend(context_patterns)

        # Sort by confidence and return top suggestions
        suggestions.sort(key=lambda x: x.get("confidence", 0), reverse=True)
        return suggestions[:10]  # Return top 10 suggestions

    def _calculate_consistency_bonus(self, time_entries: List[TimeEntry]) -> int:
        """Calculate consistency bonus based on regular work patterns."""
        if not time_entries:
            return 0

        # Group entries by day
        daily_hours = defaultdict(float)
        for entry in time_entries:
            if entry.start_time and entry.duration_minutes:
                day = entry.start_time.date()
                daily_hours[day] += entry.duration_minutes / 60

        if len(daily_hours) < 7:  # Need at least a week of data
            return 0

        # Calculate consistency (lower variance = higher bonus)
        hours_list = list(daily_hours.values())
        if not hours_list:
            return 0

        avg_hours = sum(hours_list) / len(hours_list)
        variance = sum((h - avg_hours) ** 2 for h in hours_list) / len(hours_list)

        # Convert variance to bonus (0-10 points)
        if variance < 1:  # Very consistent
            return 10
        elif variance < 4:  # Moderately consistent
            return 5
        else:  # Inconsistent
            return 0

    def _calculate_focus_bonus(self, focus_scores: Dict) -> int:
        """Calculate focus bonus based on focus level scores."""
        if not focus_scores:
            return 0

        # Calculate average focus across all hours
        all_scores = []
        for hour_scores in focus_scores.values():
            all_scores.extend(hour_scores)

        if not all_scores:
            return 0

        avg_focus = sum(all_scores) / len(all_scores)

        # Convert average focus to bonus (0-15 points)
        if avg_focus >= 4.5:
            return 15
        elif avg_focus >= 3.5:
            return 10
        elif avg_focus >= 2.5:
            return 5
        else:
            return 0

    def _calculate_weekly_trends(
        self, time_entries: List[TimeEntry], days: int
    ) -> List[float]:
        """Calculate weekly hour trends."""
        if not time_entries:
            return []

        # Group by week
        weekly_hours = defaultdict(float)
        for entry in time_entries:
            if entry.start_time and entry.end_time:
                duration = (entry.end_time - entry.start_time).total_seconds() / 3600
                # Get week number
                week_start = entry.start_time.date() - timedelta(
                    days=entry.start_time.weekday()
                )
                weekly_hours[week_start] += duration

        # Return last 4 weeks
        sorted_weeks = sorted(weekly_hours.items())
        return [hours for _, hours in sorted_weeks[-4:]]

    def _calculate_trend_direction(self, weekly_hours: List[float]) -> str:
        """Calculate trend direction from weekly hours."""
        if len(weekly_hours) < 2:
            return "neutral"

        recent_avg = sum(weekly_hours[-2:]) / 2
        earlier_avg = (
            sum(weekly_hours[:-2]) / len(weekly_hours[:-2])
            if len(weekly_hours) > 2
            else weekly_hours[0]
        )

        if recent_avg > earlier_avg * 1.1:
            return "increasing"
        elif recent_avg < earlier_avg * 0.9:
            return "decreasing"
        else:
            return "stable"

    def _generate_advanced_recommendations(
        self,
        daily_average: float,
        peak_hours: List,
        project_distribution: Dict,
        weekly_hours: List[float],
        focus_patterns: Dict,
        efficiency_metrics: Dict,
    ) -> List[str]:
        """Generate AI-powered recommendations."""
        recommendations = []

        # Daily average recommendations
        if daily_average < 4:
            recommendations.append(
                "Consider increasing daily work hours for better productivity"
            )
        elif daily_average > 10:
            recommendations.append("Consider reducing daily hours to avoid burnout")

        # Focus pattern recommendations
        if focus_patterns.get("average_focus_level", 0) < 3:
            recommendations.append(
                "Try using focus techniques like Pomodoro to improve concentration"
            )

        # Project distribution recommendations
        if len(project_distribution) > 5:
            recommendations.append(
                "Consider focusing on fewer projects for better efficiency"
            )

        # Weekly trend recommendations
        if len(weekly_hours) >= 2 and weekly_hours[-1] < weekly_hours[-2] * 0.8:
            recommendations.append(
                "Work hours declining - consider reviewing workload balance"
            )

        return recommendations[:5]  # Limit to 5 recommendations

    def _entries_overlap(self, entry1: TimeEntry, entry2: TimeEntry) -> bool:
        """Check if two time entries overlap."""
        if not all(
            [entry1.start_time, entry1.end_time, entry2.start_time, entry2.end_time]
        ):
            return False

        return (
            entry1.start_time < entry2.end_time and entry2.start_time < entry1.end_time
        )

    def _serialize_time_entry(self, entry: TimeEntry) -> Dict:
        """Serialize time entry for conflict resolution."""
        return {
            "id": str(entry.id),
            "start_time": entry.start_time.isoformat() if entry.start_time else None,
            "end_time": entry.end_time.isoformat() if entry.end_time else None,
            "project_name": entry.project.name if entry.project else None,
            "description": entry.description,
        }

    def _calculate_overlap_duration(
        self, entry1: TimeEntry, entry2: TimeEntry
    ) -> float:
        """Calculate overlap duration in hours."""
        if not self._entries_overlap(entry1, entry2):
            return 0.0

        overlap_start = max(entry1.start_time, entry2.start_time)
        overlap_end = min(entry1.end_time, entry2.end_time)

        return (overlap_end - overlap_start).total_seconds() / 3600

    def _suggest_intelligent_resolution(
        self, entry1: TimeEntry, entry2: TimeEntry
    ) -> str:
        """Suggest resolution for overlapping entries."""
        duration1 = (entry1.end_time - entry1.start_time).total_seconds() / 3600
        duration2 = (entry2.end_time - entry2.start_time).total_seconds() / 3600

        if duration1 > duration2:
            return "Keep longer entry, adjust shorter one"
        elif duration2 > duration1:
            return "Keep longer entry, adjust shorter one"
        else:
            return "Split time between entries"

    def _calculate_resolution_confidence(
        self, entry1: TimeEntry, entry2: TimeEntry
    ) -> float:
        """Calculate confidence score for resolution suggestion."""
        # Simple confidence based on entry completeness
        score = 0.5

        if entry1.description and entry2.description:
            score += 0.2
        if entry1.project_id and entry2.project_id:
            score += 0.2
        if entry1.project_id != entry2.project_id:
            score += 0.1

        return min(1.0, score)

    def _analyze_daily_patterns(self, time_entries: List[TimeEntry]) -> List[Dict]:
        """Analyze daily work patterns."""
        patterns = []

        if not time_entries:
            return patterns

        # Group by hour of day
        hourly_activity = defaultdict(int)
        for entry in time_entries:
            if entry.start_time:
                hour = entry.start_time.hour
                duration = (
                    (entry.end_time - entry.start_time).total_seconds() / 60
                    if entry.end_time
                    else 0
                )
                hourly_activity[hour] += duration

        # Find peak hours
        if hourly_activity:
            peak_hour = max(hourly_activity.items(), key=lambda x: x[1])
            patterns.append(
                {
                    "type": "daily_pattern",
                    "description": f"Most active during {peak_hour[0]}:00 hour",
                    "confidence": 80,
                }
            )

        return patterns

    def _analyze_project_patterns(self, time_entries: List[TimeEntry]) -> List[Dict]:
        """Analyze project work patterns."""
        patterns = []

        if not time_entries:
            return patterns

        # Group by project
        project_activity = defaultdict(int)
        for entry in time_entries:
            if entry.project:
                project_activity[entry.project.name] += entry.duration_minutes or 0

        if project_activity:
            top_project = max(project_activity.items(), key=lambda x: x[1])
            patterns.append(
                {
                    "type": "project_pattern",
                    "description": f"Most time spent on {top_project[0]}",
                    "confidence": 75,
                }
            )

        return patterns

    def _analyze_contextual_patterns(self, time_entries: List[TimeEntry]) -> List[Dict]:
        """Analyze contextual work patterns."""
        patterns = []

        if not time_entries:
            return patterns

        # Analyze session lengths
        session_lengths = [
            entry.duration_minutes for entry in time_entries if entry.duration_minutes
        ]
        if session_lengths:
            avg_length = sum(session_lengths) / len(session_lengths)
            if avg_length > 60:
                patterns.append(
                    {
                        "type": "session_pattern",
                        "description": "Tends to work in long sessions",
                        "confidence": 70,
                    }
                )
            elif avg_length < 30:
                patterns.append(
                    {
                        "type": "session_pattern",
                        "description": "Tends to work in short bursts",
                        "confidence": 70,
                    }
                )

        return patterns

    def _analyze_focus_patterns(self, focus_scores: Dict) -> Dict:
        """Analyze focus patterns from focus scores."""
        if not focus_scores:
            return {
                "high_focus_percentage": 0,
                "low_focus_percentage": 0,
                "average_focus_level": 0,
                "focus_trend": "neutral",
            }

        # Calculate focus statistics from hourly focus scores
        all_scores = []
        for hour_scores in focus_scores.values():
            all_scores.extend(hour_scores)

        if not all_scores:
            return {
                "high_focus_percentage": 0,
                "low_focus_percentage": 0,
                "average_focus_level": 0,
                "focus_trend": "neutral",
            }

        high_focus_count = len([s for s in all_scores if s >= 4])
        low_focus_count = len([s for s in all_scores if s <= 2])
        avg_focus = sum(all_scores) / len(all_scores)

        return {
            "high_focus_percentage": round(
                (high_focus_count / len(all_scores)) * 100, 1
            ),
            "low_focus_percentage": round((low_focus_count / len(all_scores)) * 100, 1),
            "average_focus_level": round(avg_focus, 2),
            "focus_trend": (
                "improving"
                if avg_focus > 3.5
                else "declining"
                if avg_focus < 2.5
                else "neutral"
            ),
        }

    def _calculate_efficiency_metrics(self, project_efficiency: Dict) -> Dict:
        """Calculate efficiency metrics from project data."""
        if not project_efficiency:
            return {
                "session_efficiency": 0,
                "task_completion_rate": 0,
                "interruption_impact": 0,
            }

        # Calculate average efficiency across projects
        efficiencies = list(project_efficiency.values())
        avg_efficiency = sum(efficiencies) / len(efficiencies) if efficiencies else 0

        return {
            "session_efficiency": round(avg_efficiency, 2),
            "task_completion_rate": round(min(100, avg_efficiency * 1.2), 1),
            "interruption_impact": round(max(0, 100 - avg_efficiency), 1),
        }

    async def get_productivity_insights(
        self,
        user_id: uuid.UUID,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict:
        """Generate productivity insights for a user."""

        # Default to last 30 days if no date range provided
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        # Get time entries in date range
        time_entries = await self._get_user_time_entries(user_id, start_date, end_date)

        if not time_entries:
            return self._empty_productivity_insights(start_date, end_date)

        # Calculate basic metrics
        total_entries = len(time_entries)
        total_minutes = sum(entry.duration_minutes for entry in time_entries)
        total_hours = total_minutes / 60

        # Productivity score analysis
        scored_entries = [e for e in time_entries if e.productivity_score]
        avg_productivity_score = (
            sum(float(e.productivity_score) for e in scored_entries)
            / len(scored_entries)
            if scored_entries
            else 0
        )

        # Most productive hours analysis
        hourly_productivity = defaultdict(list)
        for entry in time_entries:
            if entry.start_time and entry.productivity_score:
                hour = entry.start_time.hour
                hourly_productivity[hour].append(float(entry.productivity_score))

        most_productive_hours = []
        for hour, scores in hourly_productivity.items():
            avg_score = sum(scores) / len(scores)
            most_productive_hours.append(
                {
                    "hour": hour,
                    "average_score": round(avg_score, 2),
                    "session_count": len(scores),
                }
            )

        most_productive_hours.sort(key=lambda x: x["average_score"], reverse=True)

        # Daily patterns
        daily_stats = defaultdict(lambda: {"minutes": 0, "entries": 0, "scores": []})
        for entry in time_entries:
            day_name = entry.work_date.strftime("%A")
            daily_stats[day_name]["minutes"] += entry.duration_minutes
            daily_stats[day_name]["entries"] += 1
            if entry.productivity_score:
                daily_stats[day_name]["scores"].append(float(entry.productivity_score))

        daily_patterns = []
        for day, stats in daily_stats.items():
            avg_score = (
                sum(stats["scores"]) / len(stats["scores"]) if stats["scores"] else 0
            )
            daily_patterns.append(
                {
                    "day": day,
                    "total_hours": round(stats["minutes"] / 60, 2),
                    "entry_count": stats["entries"],
                    "average_productivity": round(avg_score, 2),
                }
            )

        # Focus time analysis (sessions > 25 minutes with high focus)
        focus_sessions = [
            e
            for e in time_entries
            if e.duration_minutes >= 25 and (e.focus_level or 0) >= 4
        ]
        focus_time_hours = sum(e.duration_minutes for e in focus_sessions) / 60

        # Interruption analysis
        total_interruptions = sum(e.interruption_count or 0 for e in time_entries)
        avg_interruptions_per_session = (
            total_interruptions / total_entries if total_entries > 0 else 0
        )

        # Focus patterns analysis
        focus_patterns = self._analyze_focus_patterns(time_entries)

        # Efficiency metrics
        efficiency_metrics = self._calculate_efficiency_metrics_for_insights(
            time_entries
        )

        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": (end_date - start_date).days + 1,
            },
            "overview": {
                "total_hours": round(total_hours, 2),
                "total_sessions": total_entries,
                "average_session_length": (
                    round(total_minutes / total_entries, 1) if total_entries > 0 else 0
                ),
                "average_productivity_score": round(avg_productivity_score, 2),
                "productivity_score": round(
                    avg_productivity_score, 2
                ),  # Added for compatibility
                "focus_time_hours": round(focus_time_hours, 2),
                "focus_time_percentage": (
                    round((focus_time_hours / total_hours) * 100, 1)
                    if total_hours > 0
                    else 0
                ),
            },
            "patterns": {
                "most_productive_hours": most_productive_hours[:5],
                "daily_patterns": daily_patterns,
                "interruption_analysis": {
                    "total_interruptions": total_interruptions,
                    "average_per_session": round(avg_interruptions_per_session, 2),
                    "sessions_with_interruptions": len(
                        [e for e in time_entries if (e.interruption_count or 0) > 0]
                    ),
                },
            },
            "focus_patterns": focus_patterns,
            "efficiency_metrics": efficiency_metrics,
            "recommendations": self._generate_productivity_recommendations(
                time_entries, daily_patterns, most_productive_hours
            ),
        }

    async def get_time_distribution(
        self,
        user_id: uuid.UUID,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        group_by: str = "project",
    ) -> Dict:
        """Get time distribution analysis across projects, clients, or tasks."""

        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        time_entries = await self._get_user_time_entries(user_id, start_date, end_date)

        if not time_entries:
            return self._empty_time_distribution(start_date, end_date, group_by)

        # Group entries based on group_by parameter
        if group_by == "project":
            distribution = self._group_by_project(time_entries)
        elif group_by == "client":
            distribution = self._group_by_client(time_entries)
        elif group_by == "task":
            distribution = self._group_by_task(time_entries)
        elif group_by == "status":
            distribution = self._group_by_status(time_entries)
        else:
            distribution = self._group_by_project(time_entries)  # Default

        total_minutes = sum(entry.duration_minutes for entry in time_entries)
        total_billable = sum(
            entry.billable_amount or 0 for entry in time_entries if entry.is_billable
        )

        # Calculate percentages and sort by time
        for item in distribution:
            item["percentage"] = (
                round((item["minutes"] / total_minutes) * 100, 1)
                if total_minutes > 0
                else 0
            )
            item["hours"] = round(item["minutes"] / 60, 2)

        distribution.sort(key=lambda x: x["minutes"], reverse=True)

        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
            "summary": {
                "total_hours": round(total_minutes / 60, 2),
                "total_billable_amount": float(total_billable),
                "group_by": group_by,
                "categories_count": len(distribution),
            },
            "distribution": distribution[:20],  # Top 20 categories
        }

    async def get_billable_analysis(
        self,
        user_id: uuid.UUID,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict:
        """Analyze billable vs non-billable time for revenue optimization."""

        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        time_entries = await self._get_user_time_entries(user_id, start_date, end_date)

        billable_entries = [e for e in time_entries if e.is_billable]
        non_billable_entries = [e for e in time_entries if not e.is_billable]

        billable_minutes = sum(e.duration_minutes for e in billable_entries)
        non_billable_minutes = sum(e.duration_minutes for e in non_billable_entries)
        total_minutes = billable_minutes + non_billable_minutes

        total_revenue = sum(e.billable_amount or 0 for e in billable_entries)

        # Rate analysis
        rate_analysis = defaultdict(lambda: {"minutes": 0, "revenue": 0, "entries": 0})
        for entry in billable_entries:
            if entry.hourly_rate:
                rate = float(entry.hourly_rate)
                rate_analysis[rate]["minutes"] += entry.duration_minutes
                rate_analysis[rate]["revenue"] += float(entry.billable_amount or 0)
                rate_analysis[rate]["entries"] += 1

        rate_breakdown = []
        for rate, stats in rate_analysis.items():
            rate_breakdown.append(
                {
                    "hourly_rate": rate,
                    "hours": round(stats["minutes"] / 60, 2),
                    "revenue": round(stats["revenue"], 2),
                    "entry_count": stats["entries"],
                }
            )

        rate_breakdown.sort(key=lambda x: x["revenue"], reverse=True)

        # Project profitability
        project_revenue = defaultdict(
            lambda: {"billable_minutes": 0, "total_minutes": 0, "revenue": 0}
        )
        for entry in time_entries:
            project_id = str(entry.project_id)
            project_revenue[project_id]["total_minutes"] += entry.duration_minutes
            if entry.is_billable:
                project_revenue[project_id][
                    "billable_minutes"
                ] += entry.duration_minutes
                project_revenue[project_id]["revenue"] += float(
                    entry.billable_amount or 0
                )

        project_profitability = []
        for project_id, stats in project_revenue.items():
            billable_ratio = (
                (stats["billable_minutes"] / stats["total_minutes"]) * 100
                if stats["total_minutes"] > 0
                else 0
            )
            project_profitability.append(
                {
                    "project_id": project_id,
                    "total_hours": round(stats["total_minutes"] / 60, 2),
                    "billable_hours": round(stats["billable_minutes"] / 60, 2),
                    "billable_ratio": round(billable_ratio, 1),
                    "revenue": round(stats["revenue"], 2),
                }
            )

        project_profitability.sort(key=lambda x: x["revenue"], reverse=True)

        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
            "overview": {
                "total_hours": round(total_minutes / 60, 2),
                "billable_hours": round(billable_minutes / 60, 2),
                "non_billable_hours": round(non_billable_minutes / 60, 2),
                "billable_percentage": (
                    round((billable_minutes / total_minutes) * 100, 1)
                    if total_minutes > 0
                    else 0
                ),
                "total_revenue": float(total_revenue),
                "average_hourly_rate": (
                    round(float(total_revenue) / (billable_minutes / 60), 2)
                    if billable_minutes > 0
                    else 0
                ),
                "revenue_potential": float(total_revenue),
            },
            "rate_analysis": rate_breakdown,
            "project_profitability": project_profitability[:10],
            "recommendations": self._generate_billing_recommendations(
                billable_entries, non_billable_entries, rate_breakdown
            ),
        }

    async def get_efficiency_metrics(
        self,
        user_id: uuid.UUID,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict:
        """Calculate efficiency metrics and project velocity."""

        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        time_entries = await self._get_user_time_entries(user_id, start_date, end_date)

        # Task completion analysis
        task_analysis = defaultdict(
            lambda: {"total_time": 0, "count": 0, "descriptions": []}
        )
        for entry in time_entries:
            if entry.task_name:
                task = entry.task_name.lower().strip()
                task_analysis[task]["total_time"] += entry.duration_minutes
                task_analysis[task]["count"] += 1
                if entry.description:
                    task_analysis[task]["descriptions"].append(entry.description)

        task_efficiency = []
        for task, stats in task_analysis.items():
            avg_time = stats["total_time"] / stats["count"]
            task_efficiency.append(
                {
                    "task_name": task,
                    "average_time_minutes": round(avg_time, 1),
                    "total_occurrences": stats["count"],
                    "total_time_hours": round(stats["total_time"] / 60, 2),
                }
            )

        task_efficiency.sort(key=lambda x: x["total_time_hours"], reverse=True)

        # Session length optimization
        session_lengths = [e.duration_minutes for e in time_entries]
        optimal_sessions = [
            s for s in session_lengths if 25 <= s <= 90
        ]  # Pomodoro-inspired optimal range

        # Weekly velocity (completed work)
        weekly_stats = defaultdict(lambda: {"minutes": 0, "entries": 0, "completed": 0})
        for entry in time_entries:
            week_start = entry.work_date - timedelta(days=entry.work_date.weekday())
            week_key = week_start.strftime("%Y-W%U")
            weekly_stats[week_key]["minutes"] += entry.duration_minutes
            weekly_stats[week_key]["entries"] += 1
            if entry.status in ["approved", "billed"]:
                weekly_stats[week_key]["completed"] += 1

        weekly_velocity = []
        for week, stats in weekly_stats.items():
            completion_rate = (
                (stats["completed"] / stats["entries"]) * 100
                if stats["entries"] > 0
                else 0
            )
            weekly_velocity.append(
                {
                    "week": week,
                    "hours": round(stats["minutes"] / 60, 2),
                    "entries": stats["entries"],
                    "completion_rate": round(completion_rate, 1),
                }
            )

        weekly_velocity.sort(key=lambda x: x["week"])

        # Calculate additional metrics for the response
        total_days = (end_date - start_date).days + 1
        total_hours = sum(entry.duration_minutes for entry in time_entries) / 60
        average_daily_hours = (
            round(total_hours / total_days, 2) if total_days > 0 else 0
        )

        # Simple consistency score based on daily variance
        daily_hours = {}
        for entry in time_entries:
            day = entry.work_date.isoformat()
            daily_hours[day] = daily_hours.get(day, 0) + entry.duration_minutes / 60

        if len(daily_hours) > 1:
            hours_values = list(daily_hours.values())
            avg_hours = sum(hours_values) / len(hours_values)
            variance = sum((h - avg_hours) ** 2 for h in hours_values) / len(
                hours_values
            )
            consistency_score = max(0, min(100, 100 - (variance * 10)))
        else:
            consistency_score = 100 if daily_hours else 0

        # Velocity trend based on weekly data
        if len(weekly_velocity) >= 2:
            recent_avg = sum(w["hours"] for w in weekly_velocity[-2:]) / 2
            earlier_avg = sum(w["hours"] for w in weekly_velocity[:-2]) / max(
                1, len(weekly_velocity) - 2
            )
            if recent_avg > earlier_avg * 1.1:
                velocity_trend = "increasing"
            elif recent_avg < earlier_avg * 0.9:
                velocity_trend = "decreasing"
            else:
                velocity_trend = "stable"
        else:
            velocity_trend = "stable"

        # Simple efficiency rating based on completion rates and session optimality
        completion_rates = [w["completion_rate"] for w in weekly_velocity]
        avg_completion = (
            sum(completion_rates) / len(completion_rates) if completion_rates else 0
        )
        optimal_percentage = (
            round((len(optimal_sessions) / len(session_lengths)) * 100, 1)
            if session_lengths
            else 0
        )
        efficiency_rating = round((avg_completion + optimal_percentage) / 2, 1)

        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
            "average_daily_hours": average_daily_hours,
            "consistency_score": round(consistency_score, 1),
            "velocity_trend": velocity_trend,
            "efficiency_rating": efficiency_rating,
            "session_analysis": {
                "total_sessions": len(time_entries),
                "average_session_length": (
                    round(sum(session_lengths) / len(session_lengths), 1)
                    if session_lengths
                    else 0
                ),
                "optimal_sessions": len(optimal_sessions),
                "optimal_session_percentage": (
                    round((len(optimal_sessions) / len(session_lengths)) * 100, 1)
                    if session_lengths
                    else 0
                ),
                "shortest_session": min(session_lengths) if session_lengths else 0,
                "longest_session": max(session_lengths) if session_lengths else 0,
            },
            "task_efficiency": task_efficiency[:15],
            "weekly_velocity": weekly_velocity,
            "recommendations": self._generate_efficiency_recommendations(
                task_efficiency, session_lengths, weekly_velocity
            ),
        }

    async def _get_user_time_entries(
        self, user_id: uuid.UUID, start_date: date, end_date: date
    ) -> List[TimeEntry]:
        """Get user's time entries within date range."""

        stmt = (
            select(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == user_id,
                    TimeEntry.deleted_at.is_(None),
                    func.date(TimeEntry.work_date) >= start_date,
                    func.date(TimeEntry.work_date) <= end_date,
                )
            )
            .order_by(TimeEntry.work_date)
        )

        result = await self.db.execute(stmt)
        return result.scalars().all()

    def _group_by_project(self, time_entries: List[TimeEntry]) -> List[Dict]:
        """Group time entries by project."""

        project_stats = defaultdict(lambda: {"minutes": 0, "entries": 0, "revenue": 0})
        project_names = {}

        for entry in time_entries:
            project_id = str(entry.project_id)
            project_stats[project_id]["minutes"] += entry.duration_minutes
            project_stats[project_id]["entries"] += 1
            if entry.is_billable and entry.billable_amount:
                project_stats[project_id]["revenue"] += float(entry.billable_amount)

            if entry.project and project_id not in project_names:
                project_names[project_id] = entry.project.title

        result = []
        for project_id, stats in project_stats.items():
            result.append(
                {
                    "id": project_id,
                    "name": project_names.get(project_id, "Unknown Project"),
                    "minutes": stats["minutes"],
                    "entry_count": stats["entries"],
                    "revenue": round(stats["revenue"], 2),
                }
            )

        return result

    def _group_by_client(self, time_entries: List[TimeEntry]) -> List[Dict]:
        """Group time entries by client."""

        client_stats = defaultdict(lambda: {"minutes": 0, "entries": 0, "revenue": 0})
        client_names = {}

        for entry in time_entries:
            if entry.project and entry.project.client:
                client_id = str(entry.project.client_id)
                client_stats[client_id]["minutes"] += entry.duration_minutes
                client_stats[client_id]["entries"] += 1
                if entry.is_billable and entry.billable_amount:
                    client_stats[client_id]["revenue"] += float(entry.billable_amount)

                if client_id not in client_names:
                    client_names[client_id] = entry.project.client.name

        result = []
        for client_id, stats in client_stats.items():
            result.append(
                {
                    "id": client_id,
                    "name": client_names.get(client_id, "Unknown Client"),
                    "minutes": stats["minutes"],
                    "entry_count": stats["entries"],
                    "revenue": round(stats["revenue"], 2),
                }
            )

        return result

    def _group_by_task(self, time_entries: List[TimeEntry]) -> List[Dict]:
        """Group time entries by task name."""

        task_stats = defaultdict(lambda: {"minutes": 0, "entries": 0, "revenue": 0})

        for entry in time_entries:
            task_name = entry.task_name or "No Task"
            task_stats[task_name]["minutes"] += entry.duration_minutes
            task_stats[task_name]["entries"] += 1
            if entry.is_billable and entry.billable_amount:
                task_stats[task_name]["revenue"] += float(entry.billable_amount)

        result = []
        for task_name, stats in task_stats.items():
            result.append(
                {
                    "id": task_name.lower().replace(" ", "_"),
                    "name": task_name,
                    "minutes": stats["minutes"],
                    "entry_count": stats["entries"],
                    "revenue": round(stats["revenue"], 2),
                }
            )

        return result

    def _group_by_status(self, time_entries: List[TimeEntry]) -> List[Dict]:
        """Group time entries by status."""

        status_stats = defaultdict(lambda: {"minutes": 0, "entries": 0, "revenue": 0})

        for entry in time_entries:
            status = entry.status
            status_stats[status]["minutes"] += entry.duration_minutes
            status_stats[status]["entries"] += 1
            if entry.is_billable and entry.billable_amount:
                status_stats[status]["revenue"] += float(entry.billable_amount)

        result = []
        for status, stats in status_stats.items():
            result.append(
                {
                    "id": status,
                    "name": status.title(),
                    "minutes": stats["minutes"],
                    "entry_count": stats["entries"],
                    "revenue": round(stats["revenue"], 2),
                }
            )

        return result

    def _generate_productivity_recommendations(
        self,
        time_entries: List[TimeEntry],
        daily_patterns: List[Dict],
        productive_hours: List[Dict],
    ) -> List[str]:
        """Generate productivity improvement recommendations."""

        recommendations = []

        # Session length recommendations
        avg_session = (
            sum(e.duration_minutes for e in time_entries) / len(time_entries)
            if time_entries
            else 0
        )
        if avg_session < 25:
            recommendations.append(
                "Consider longer focus sessions (25-90 minutes) for better productivity"
            )
        elif avg_session > 120:
            recommendations.append(
                "Break long sessions into smaller chunks with breaks to maintain focus"
            )

        # Interruption recommendations
        high_interruption_sessions = [
            e for e in time_entries if (e.interruption_count or 0) > 3
        ]
        if len(high_interruption_sessions) > len(time_entries) * 0.3:
            recommendations.append(
                "High interruption rate detected - consider blocking focus time or using 'Do Not Disturb' modes"
            )

        # Time of day recommendations
        if productive_hours:
            best_hour = productive_hours[0]["hour"]
            recommendations.append(
                f"Your most productive time is around {best_hour}:00 - schedule important work during this time"
            )

        # Daily pattern recommendations
        if daily_patterns:
            best_day = max(daily_patterns, key=lambda x: x["average_productivity"])
            if best_day["average_productivity"] > 0:
                recommendations.append(
                    f"{best_day['day']} is your most productive day - consider scheduling challenging tasks then"
                )

        return recommendations

    def _generate_billing_recommendations(
        self,
        billable_entries: List[TimeEntry],
        non_billable_entries: List[TimeEntry],
        rate_breakdown: List[Dict],
    ) -> List[str]:
        """Generate billing optimization recommendations."""

        recommendations = []

        total_entries = len(billable_entries) + len(non_billable_entries)
        billable_ratio = (
            len(billable_entries) / total_entries if total_entries > 0 else 0
        )

        if billable_ratio < 0.7:
            recommendations.append(
                "Consider increasing billable work ratio - currently below 70%"
            )

        if rate_breakdown:
            rates = [r["hourly_rate"] for r in rate_breakdown]
            if len(set(rates)) > 3:
                recommendations.append(
                    "Consider standardizing hourly rates across projects for consistency"
                )

            highest_rate = max(rates)
            lowest_rate = min(rates)
            if highest_rate > lowest_rate * 2:
                recommendations.append(
                    "Large rate variance detected - review pricing strategy"
                )

        # Non-billable time analysis
        if non_billable_entries:
            non_billable_hours = (
                sum(e.duration_minutes for e in non_billable_entries) / 60
            )
            if non_billable_hours > 10:  # More than 10 hours non-billable
                recommendations.append(
                    "High non-billable time - consider if some activities could be billable"
                )

        return recommendations

    def _generate_efficiency_recommendations(
        self,
        task_efficiency: List[Dict],
        session_lengths: List[int],
        weekly_velocity: List[Dict],
    ) -> List[str]:
        """Generate efficiency improvement recommendations."""

        recommendations = []

        # Session optimization
        if session_lengths:
            avg_length = sum(session_lengths) / len(session_lengths)
            optimal_count = len([s for s in session_lengths if 25 <= s <= 90])
            optimal_ratio = optimal_count / len(session_lengths)

            if optimal_ratio < 0.5:
                recommendations.append(
                    "Aim for 25-90 minute focused sessions for optimal productivity"
                )

        # Task time consistency
        if task_efficiency:
            inconsistent_tasks = [
                t for t in task_efficiency if t["total_occurrences"] > 3
            ]
            for task in inconsistent_tasks[:3]:  # Top 3 most frequent tasks
                recommendations.append(
                    f"Track '{task['task_name']}' more consistently - average {task['average_time_minutes']} minutes"
                )

        # Completion rate
        if weekly_velocity:
            recent_weeks = weekly_velocity[-4:]  # Last 4 weeks
            avg_completion = (
                sum(w["completion_rate"] for w in recent_weeks) / len(recent_weeks)
                if recent_weeks
                else 0
            )
            if avg_completion < 80:
                recommendations.append(
                    "Focus on completing more time entries - current completion rate is below 80%"
                )

        return recommendations

    def _analyze_focus_patterns(self, time_entries: List[TimeEntry]) -> Dict:
        """Analyze focus patterns from time entries."""
        if not time_entries:
            return {
                "high_focus_percentage": 0,
                "low_focus_percentage": 0,
                "average_focus_level": 0,
                "focus_trend": "neutral",
            }

        # Count entries by focus level
        focus_counts = defaultdict(int)
        total_with_focus = 0

        for entry in time_entries:
            if hasattr(entry, "focus_level") and entry.focus_level is not None:
                focus_counts[entry.focus_level] += 1
                total_with_focus += 1

        if total_with_focus == 0:
            return {
                "high_focus_percentage": 0,
                "low_focus_percentage": 0,
                "average_focus_level": 0,
                "focus_trend": "neutral",
            }

        # Calculate percentages
        high_focus = sum(focus_counts[i] for i in [4, 5])  # Levels 4-5
        low_focus = sum(focus_counts[i] for i in [1, 2])  # Levels 1-2

        high_focus_pct = (high_focus / total_with_focus) * 100
        low_focus_pct = (low_focus / total_with_focus) * 100

        # Calculate average focus level
        total_focus_points = sum(level * count for level, count in focus_counts.items())
        avg_focus_level = total_focus_points / total_with_focus

        # Determine trend (simplified)
        if high_focus_pct > 60:
            trend = "high"
        elif low_focus_pct > 40:
            trend = "low"
        else:
            trend = "moderate"

        return {
            "high_focus_percentage": round(high_focus_pct, 1),
            "low_focus_percentage": round(low_focus_pct, 1),
            "average_focus_level": round(avg_focus_level, 2),
            "focus_trend": trend,
            "distribution": dict(focus_counts),
        }

    def _calculate_efficiency_metrics_for_insights(
        self, time_entries: List[TimeEntry]
    ) -> Dict:
        """Calculate efficiency metrics for insights endpoint."""
        if not time_entries:
            return {
                "session_efficiency": 0,
                "task_completion_rate": 0,
                "interruption_impact": 0,
            }

        total_entries = len(time_entries)
        total_duration = sum(entry.duration_minutes for entry in time_entries)

        # Count completed/efficient sessions (duration > 25 mins and focus >= 4)
        efficient_sessions = [
            e
            for e in time_entries
            if e.duration_minutes >= 25 and (getattr(e, "focus_level", 0) or 0) >= 4
        ]

        session_efficiency = (
            (len(efficient_sessions) / total_entries) * 100 if total_entries > 0 else 0
        )

        # Task completion analysis (simplified)
        tasks_with_descriptions = [
            e for e in time_entries if e.task_name and e.description
        ]
        task_completion_rate = (
            (len(tasks_with_descriptions) / total_entries) * 100
            if total_entries > 0
            else 0
        )

        # Interruption impact
        total_interruptions = sum(
            getattr(e, "interruption_count", 0) or 0 for e in time_entries
        )
        avg_interruptions = (
            total_interruptions / total_entries if total_entries > 0 else 0
        )
        interruption_impact = min(100, avg_interruptions * 10)  # Scale factor

        return {
            "session_efficiency": round(session_efficiency, 1),
            "task_completion_rate": round(task_completion_rate, 1),
            "interruption_impact": round(interruption_impact, 1),
            "total_sessions": total_entries,
            "total_duration_hours": round(total_duration / 60, 2),
        }

    def _empty_productivity_insights(
        self, start_date: Optional[date] = None, end_date: Optional[date] = None
    ) -> Dict:
        """Return empty productivity insights structure."""
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": (end_date - start_date).days + 1,
            },
            "overview": {
                "total_hours": 0,
                "total_sessions": 0,
                "average_session_length": 0,
                "average_productivity_score": 0,
                "focus_time_hours": 0,
                "focus_time_percentage": 0,
            },
            "patterns": {
                "most_productive_hours": [],
                "daily_patterns": [],
                "interruption_analysis": {
                    "total_interruptions": 0,
                    "average_per_session": 0,
                    "sessions_with_interruptions": 0,
                },
            },
            "focus_patterns": {
                "high_focus_percentage": 0,
                "low_focus_percentage": 0,
                "average_focus_level": 0,
                "focus_trend": "neutral",
            },
            "efficiency_metrics": {
                "session_efficiency": 0,
                "task_completion_rate": 0,
                "interruption_impact": 0,
            },
            "recommendations": ["Start tracking time to generate insights"],
        }

    def _empty_time_distribution(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        group_by: str = "project",
    ) -> Dict:
        """Return empty time distribution structure."""
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
            "summary": {
                "total_hours": 0,
                "total_billable_amount": 0,
                "group_by": group_by,
                "categories_count": 0,
            },
            "distribution": [],
        }
