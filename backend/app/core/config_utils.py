"""
Configuration validation utilities for DevHQ Backend
Provides validation and health checking for application configuration
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional

from app.config import settings

logger = logging.getLogger(__name__)


class ConfigValidator:
    """Utility for validating application configuration"""

    @staticmethod
    def validate_required_settings() -> List[str]:
        """
        Validate that all required settings are present

        Returns:
            List of missing required settings
        """
        missing_settings = []

        # Required settings that must be present
        required_settings = [
            "database_url",
            "secret_key",
        ]

        for setting in required_settings:
            if not getattr(settings, setting, None):
                missing_settings.append(setting)

        return missing_settings

    @staticmethod
    def validate_payment_gateway_config() -> Dict[str, Dict]:
        """
        Validate payment gateway configurations with detailed error reporting

        Returns:
            Dictionary with gateway validation details
        """
        paystack_configured = (
            settings.paystack_secret_key
            and settings.paystack_public_key
            and not settings.paystack_secret_key.startswith("sk_test_placeholder")
            and not settings.paystack_public_key.startswith("pk_test_placeholder")
        )

        dpo_configured = (
            settings.dpo_company_token
            and not settings.dpo_company_token == "DPO_TEST_PLACEHOLDER"
        )

        return {
            "paystack": {
                "configured": bool(paystack_configured),
                "errors": (
                    ConfigValidator._get_paystack_config_errors()
                    if not paystack_configured
                    else []
                ),
            },
            "dpo": {
                "configured": bool(dpo_configured),
                "errors": (
                    ConfigValidator._get_dpo_config_errors()
                    if not dpo_configured
                    else []
                ),
            },
        }

    @staticmethod
    def _get_paystack_config_errors() -> List[str]:
        """Get specific errors for Paystack configuration"""
        errors = []

        if not settings.paystack_secret_key:
            errors.append("Missing PAYSTACK_SECRET_KEY")
        elif settings.paystack_secret_key.startswith("sk_test_placeholder"):
            errors.append(
                "Using placeholder PAYSTACK_SECRET_KEY - please set real key for production"
            )

        if not settings.paystack_public_key:
            errors.append("Missing PAYSTACK_PUBLIC_KEY")
        elif settings.paystack_public_key.startswith("pk_test_placeholder"):
            errors.append(
                "Using placeholder PAYSTACK_PUBLIC_KEY - please set real key for production"
            )

        return errors

    @staticmethod
    def _get_dpo_config_errors() -> List[str]:
        """Get specific errors for DPO configuration"""
        errors = []

        if not settings.dpo_company_token:
            errors.append("Missing DPO_COMPANY_TOKEN")
        elif settings.dpo_company_token == "DPO_TEST_PLACEHOLDER":
            errors.append(
                "Using placeholder DPO_COMPANY_TOKEN - please set real token for production"
            )

        return errors

    @staticmethod
    def validate_external_service_config() -> Dict[str, bool]:
        """
        Validate external service configurations

        Returns:
            Dictionary with service name and whether it's configured
        """
        return {
            "paystack": bool(
                settings.paystack_secret_key
                and settings.paystack_public_key
                and not settings.paystack_secret_key.startswith("sk_test_placeholder")
            ),
            "dpo": bool(
                settings.dpo_company_token
                and not settings.dpo_company_token == "DPO_TEST_PLACEHOLDER"
            ),
            "cloudinary": bool(
                settings.cloudinary_cloud_name
                and settings.cloudinary_api_key
                and settings.cloudinary_api_secret
            ),
            "sentry": bool(settings.sentry_dsn),
            "redis": bool(settings.redis_url),
            "email": bool(
                settings.smtp_host and settings.smtp_user and settings.smtp_password
            ),
        }

    @staticmethod
    def get_configuration_summary() -> Dict:
        """
        Get a summary of the current configuration

        Returns:
            Dictionary with configuration summary
        """
        external_services = ConfigValidator.validate_external_service_config()
        payment_gateways = ConfigValidator.validate_payment_gateway_config()

        return {
            "app_name": settings.app_name,
            "version": settings.version,
            "environment": settings.environment,
            "debug": settings.debug,
            "database_configured": bool(settings.database_url),
            "external_services": external_services,
            "payment_gateways": payment_gateways,
            "cors_origins": settings.allowed_origins,
            "security": {
                "algorithm": settings.algorithm,
                "access_token_expire_minutes": settings.access_token_expire_minutes,
                "refresh_token_expire_days": settings.refresh_token_expire_days,
            },
            "platform_revenue": {
                "platform_fee_rate": settings.platform_fee_rate,
                "instant_settlement_fee_rate": settings.instant_settlement_fee_rate,
                "default_payment_gateway": settings.default_payment_gateway,
                "gateway_selection_strategy": settings.gateway_selection_strategy,
            },
        }

    @staticmethod
    def _get_configuration_warnings() -> List[str]:
        """Get configuration warnings for production environments"""
        warnings = []

        # Warn about test keys in production
        if settings.is_production:
            if (
                settings.paystack_secret_key
                and settings.paystack_secret_key.startswith("sk_test_")
            ):
                warnings.append("Production environment using test Paystack keys")

            if (
                settings.dpo_company_token
                and settings.dpo_company_token == "DPO_TEST_PLACEHOLDER"
            ):
                warnings.append("Production environment using test DPO token")

            if settings.debug:
                warnings.append("Debug mode enabled in production")

        # Warn about missing payment gateways
        payment_gateways = ConfigValidator.validate_payment_gateway_config()
        if not any(gateway["configured"] for gateway in payment_gateways.values()):
            warnings.append("No payment gateways configured")

        return warnings


def get_dynamic_health_info() -> Dict:
    """
    Get dynamic health information including current timestamp

    Returns:
        Dictionary with dynamic health information
    """
    return {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime": datetime.now(timezone.utc).timestamp(),
    }


def format_health_check_response(db_healthy: bool) -> Dict:
    """
    Format the health check response with dynamic information

    Args:
        db_healthy: Whether the database is healthy

    Returns:
        Dictionary with health check response
    """
    health_info = get_dynamic_health_info()

    return {
        "status": "healthy" if db_healthy else "unhealthy",
        "version": settings.version,
        "environment": settings.environment,
        "database": "connected" if db_healthy else "disconnected",
        "timestamp": health_info["timestamp"],
        "uptime": health_info["uptime"],
    }
