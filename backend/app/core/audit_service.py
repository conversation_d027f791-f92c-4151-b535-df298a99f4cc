"""
Enhanced Audit Trail Service for comprehensive activity logging
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from sqlalchemy.orm import Session

from app.models.activity import ActivityLog


class AuditService:
    """Enhanced audit service for comprehensive activity logging"""

    def __init__(self, db: Session):
        self.db = db

    def log_activity(
        self,
        user_id: Optional[str],
        client_id: Optional[str],
        action: str,
        description: str,
        entity_type: str,
        entity_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> ActivityLog:
        """
        Log a comprehensive activity entry

        Args:
            user_id: ID of the user performing the action
            client_id: ID of the client (for portal activities)
            action: Action performed (e.g., 'create', 'update', 'delete', 'view', 'download')
            description: Human-readable description of the action
            entity_type: Type of entity (e.g., 'invoice', 'project', 'payment')
            entity_id: ID of the entity being acted upon
            ip_address: IP address of the request
            user_agent: User agent string
            extra_data: Additional context data
        """
        activity = ActivityLog(
            user_id=user_id,
            client_id=client_id,
            action=action,
            description=description,
            entity_type=entity_type,
            entity_id=entity_id,
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=json.dumps(extra_data) if extra_data else None,
        )

        self.db.add(activity)
        self.db.commit()
        self.db.refresh(activity)

        return activity

    def log_invoice_activity(
        self,
        user_id: str,
        invoice_id: str,
        action: str,
        description: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> ActivityLog:
        """Log invoice-specific activity"""
        return self.log_activity(
            user_id=user_id,
            client_id=None,
            action=action,
            description=description,
            entity_type="invoice",
            entity_id=invoice_id,
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=extra_data,
        )

    def log_client_portal_activity(
        self,
        client_id: str,
        action: str,
        description: str,
        entity_type: str,
        entity_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> ActivityLog:
        """Log client portal activity"""
        return self.log_activity(
            user_id=None,
            client_id=client_id,
            action=action,
            description=description,
            entity_type=entity_type,
            entity_id=entity_id,
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=extra_data,
        )

    def log_payment_activity(
        self,
        user_id: Optional[str],
        client_id: Optional[str],
        action: str,
        description: str,
        transaction_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> ActivityLog:
        """Log payment-related activity"""
        return self.log_activity(
            user_id=user_id,
            client_id=client_id,
            action=action,
            description=description,
            entity_type="payment",
            entity_id=transaction_id,
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=extra_data,
        )

    def log_security_event(
        self,
        action: str,
        description: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> ActivityLog:
        """Log security-related events"""
        return self.log_activity(
            user_id=user_id,
            client_id=None,
            action=action,
            description=description,
            entity_type="security",
            entity_id="system",
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=extra_data,
        )

    def get_user_activities(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0,
        entity_type: Optional[str] = None,
        action: Optional[str] = None,
    ) -> list[ActivityLog]:
        """Get activities for a specific user"""
        query = self.db.query(ActivityLog).filter(ActivityLog.user_id == user_id)

        if entity_type:
            query = query.filter(ActivityLog.entity_type == entity_type)
        if action:
            query = query.filter(ActivityLog.action == action)

        return (
            query.order_by(ActivityLog.created_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    def get_client_activities(
        self,
        client_id: str,
        limit: int = 50,
        offset: int = 0,
        entity_type: Optional[str] = None,
        action: Optional[str] = None,
    ) -> list[ActivityLog]:
        """Get activities for a specific client"""
        query = self.db.query(ActivityLog).filter(ActivityLog.client_id == client_id)

        if entity_type:
            query = query.filter(ActivityLog.entity_type == entity_type)
        if action:
            query = query.filter(ActivityLog.action == action)

        return (
            query.order_by(ActivityLog.created_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    def get_entity_activities(
        self, entity_type: str, entity_id: str, limit: int = 50, offset: int = 0
    ) -> list[ActivityLog]:
        """Get activities for a specific entity"""
        return (
            self.db.query(ActivityLog)
            .filter(
                ActivityLog.entity_type == entity_type,
                ActivityLog.entity_id == entity_id,
            )
            .order_by(ActivityLog.created_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )


def get_audit_service(db: Session) -> AuditService:
    """Get audit service instance"""
    return AuditService(db)
