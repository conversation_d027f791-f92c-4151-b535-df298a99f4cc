"""
Payment service for Paystack integration and African market payment processing
"""

import hashlib
import hmac
import json
import uuid
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Dict, List, Optional

import httpx
from sqlalchemy.orm import Session

from app.config import get_settings
from app.models import Invoice, PaymentTransaction


class PaystackService:
    """Service for Paystack payment gateway integration."""

    def __init__(self, secret_key: str, public_key: str):
        if not secret_key or secret_key.startswith("sk_test_placeholder"):
            raise ValueError("Valid Paystack secret key is required")
        if not public_key or public_key.startswith("pk_test_placeholder"):
            raise ValueError("Valid Paystack public key is required")

        self.secret_key = secret_key
        self.public_key = public_key
        self.base_url = "https://api.paystack.co"
        self.headers = {
            "Authorization": f"Bearer {secret_key}",
            "Content-Type": "application/json",
        }

    async def create_payment_link(
        self,
        invoice: Invoice,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None,
    ) -> Dict[str, str]:
        """Create a payment link for an invoice."""

        # Convert amount to kobo (Paystack uses kobo for NGN)
        amount_in_kobo = int(invoice.total_amount * 100)

        payload = {
            "amount": amount_in_kobo,
            "email": invoice.client.email or "<EMAIL>",
            "currency": invoice.currency,
            "reference": f"inv_{invoice.id}_{uuid.uuid4().hex[:8]}",
            "callback_url": success_url,
            "metadata": {
                "invoice_id": str(invoice.id),
                "invoice_number": invoice.invoice_number,
                "client_name": invoice.client.name,
                "custom_fields": [
                    {
                        "display_name": "Invoice Number",
                        "variable_name": "invoice_number",
                        "value": invoice.invoice_number,
                    }
                ],
            },
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/transaction/initialize",
                headers=self.headers,
                json=payload,
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("status"):
                    return {
                        "payment_link": data["data"]["authorization_url"],
                        "reference": data["data"]["reference"],
                        "access_code": data["data"]["access_code"],
                    }

            raise Exception(f"Failed to create payment link: {response.text}")

    async def verify_payment(self, reference: str) -> Dict:
        """Verify a payment transaction."""

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/transaction/verify/{reference}", headers=self.headers
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("status"):
                    return data["data"]

            raise Exception(f"Failed to verify payment: {response.text}")

    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature from Paystack."""

        computed_signature = hmac.new(
            self.secret_key.encode("utf-8"), payload.encode("utf-8"), hashlib.sha512
        ).hexdigest()

        return hmac.compare_digest(computed_signature, signature)

    async def get_supported_payment_methods(self, currency: str = "NGN") -> List[str]:
        """Get supported payment methods for a currency."""

        # Default supported methods for African markets
        methods = {
            "NGN": ["card", "bank_transfer", "ussd", "mobile_money"],
            "GHS": ["card", "bank_transfer", "mobile_money"],
            "KES": ["card", "bank_transfer", "mobile_money"],
            "ZAR": ["card", "bank_transfer"],
            "USD": ["card", "bank_transfer"],
        }

        return methods.get(currency, ["card", "bank_transfer"])


class PaymentProcessingService:
    """Service for managing payment transactions and invoice updates."""

    def __init__(self, db, paystack_service: PaystackService):
        self.db = db
        self.paystack = paystack_service

    async def generate_payment_link(
        self,
        invoice_id: uuid.UUID,
        user_id: uuid.UUID,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None,
    ) -> Dict[str, str]:
        """Generate payment link for an invoice."""

        # Check if we're using async or sync session
        if hasattr(self.db, "execute"):
            # Async session
            from sqlalchemy import select

            result = await self.db.execute(
                select(Invoice).filter(
                    Invoice.id == invoice_id,
                    Invoice.user_id == user_id,
                    Invoice.status.in_(["sent", "viewed", "overdue"]),
                )
            )
            invoice = result.scalar_one_or_none()
        else:
            # Sync session
            invoice = (
                self.db.query(Invoice)
                .filter(
                    Invoice.id == invoice_id,
                    Invoice.user_id == user_id,
                    Invoice.status.in_(["sent", "viewed", "overdue"]),
                )
                .first()
            )

        if not invoice:
            raise ValueError("Invoice not found, access denied, or not payable")

        # Create payment link
        payment_data = await self.paystack.create_payment_link(
            invoice, success_url, cancel_url
        )

        # Update invoice with payment link
        invoice.payment_link = payment_data["payment_link"]

        # Create payment transaction record
        transaction = PaymentTransaction(
            invoice_id=invoice.id,
            transaction_reference=payment_data["reference"],
            payment_method="online",
            payment_gateway="paystack",
            amount=invoice.total_amount,
            currency=invoice.currency,
            status="pending",
            customer_email=invoice.client.email,
        )

        self.db.add(transaction)

        # Handle commit for both async and sync sessions
        if hasattr(self.db, "commit"):
            # Sync session
            self.db.commit()
        else:
            # Async session
            await self.db.commit()

        return {
            "payment_link": payment_data["payment_link"],
            "payment_token": invoice.payment_token,
            "reference": payment_data["reference"],
        }

    async def process_webhook_payment(self, webhook_data: Dict) -> bool:
        """Process payment webhook from Paystack."""

        event = webhook_data.get("event")
        data = webhook_data.get("data", {})

        if event == "charge.success":
            return await self._handle_successful_payment(data)
        elif event == "charge.failed":
            return await self._handle_failed_payment(data)

        return False

    async def _handle_successful_payment(self, payment_data: Dict) -> bool:
        """Handle successful payment notification."""

        reference = payment_data.get("reference")
        if not reference:
            return False

        # Find transaction
        if hasattr(self.db, "execute"):
            # Async session
            from sqlalchemy import select

            result = await self.db.execute(
                select(PaymentTransaction).filter(
                    PaymentTransaction.transaction_reference == reference
                )
            )
            transaction = result.scalar_one_or_none()
        else:
            # Sync session
            transaction = (
                self.db.query(PaymentTransaction)
                .filter(PaymentTransaction.transaction_reference == reference)
                .first()
            )

        if not transaction:
            return False

        # Verify payment with Paystack
        try:
            verified_data = await self.paystack.verify_payment(reference)

            if verified_data.get("status") == "success":
                # Update transaction
                transaction.mark_as_completed(verified_data)
                transaction.gateway_transaction_id = verified_data.get("id")
                transaction.gateway_fee = (
                    Decimal(str(verified_data.get("fees", 0))) / 100
                )
                transaction.net_amount = transaction.amount - (
                    transaction.gateway_fee or Decimal("0")
                )

                # Update invoice
                invoice = transaction.invoice
                if invoice.is_fully_paid:
                    invoice.mark_as_paid()

                # Handle commit for both async and sync sessions
                if hasattr(self.db, "commit"):
                    # Sync session
                    self.db.commit()
                else:
                    # Async session
                    await self.db.commit()
                return True

        except Exception as e:
            print(f"Payment verification failed: {e}")
            return False

        return False

    async def _handle_failed_payment(self, payment_data: Dict) -> bool:
        """Handle failed payment notification."""

        reference = payment_data.get("reference")
        if not reference:
            return False

        # Find and update transaction
        if hasattr(self.db, "execute"):
            # Async session
            from sqlalchemy import select

            result = await self.db.execute(
                select(PaymentTransaction).filter(
                    PaymentTransaction.transaction_reference == reference
                )
            )
            transaction = result.scalar_one_or_none()
        else:
            # Sync session
            transaction = (
                self.db.query(PaymentTransaction)
                .filter(PaymentTransaction.transaction_reference == reference)
                .first()
            )

        if transaction:
            transaction.mark_as_failed(payment_data)
            # Handle commit for both async and sync sessions
            if hasattr(self.db, "commit"):
                # Sync session
                self.db.commit()
            else:
                # Async session
                await self.db.commit()
            return True

        return False

    async def get_payment_status(
        self, invoice_id: uuid.UUID, user_id: uuid.UUID
    ) -> Dict:
        """Get payment status for an invoice."""

        # Handle both async and sync sessions
        if hasattr(self.db, "execute"):
            # Async session
            from sqlalchemy import select

            result = await self.db.execute(
                select(Invoice).filter(
                    Invoice.id == invoice_id, Invoice.user_id == user_id
                )
            )
            invoice = result.scalar_one_or_none()

            if not invoice:
                raise ValueError("Invoice not found or access denied")

            transactions_result = await self.db.execute(
                select(PaymentTransaction)
                .filter(PaymentTransaction.invoice_id == invoice_id)
                .order_by(PaymentTransaction.created_at.desc())
            )
            transactions = transactions_result.scalars().all()
        else:
            # Sync session
            invoice = (
                self.db.query(Invoice)
                .filter(Invoice.id == invoice_id, Invoice.user_id == user_id)
                .first()
            )

            if not invoice:
                raise ValueError("Invoice not found or access denied")

            transactions = (
                self.db.query(PaymentTransaction)
                .filter(PaymentTransaction.invoice_id == invoice_id)
                .order_by(PaymentTransaction.created_at.desc())
                .all()
            )

        return {
            "invoice_id": invoice.id,
            "status": invoice.status,
            "total_amount": invoice.total_amount,
            "total_paid": invoice.total_paid,
            "balance_due": invoice.balance_due,
            "last_payment_at": max(
                (t.completed_at for t in transactions if t.completed_at), default=None
            ),
            "transactions": transactions,
        }

    async def get_supported_payment_methods(self, currency: str) -> List[str]:
        """Get supported payment methods for a currency."""
        return await self.paystack.get_supported_payment_methods(currency)

    def create_manual_payment(
        self,
        invoice_id: uuid.UUID,
        amount: Decimal,
        payment_method: str,
        reference: str,
        user_id: uuid.UUID,
    ) -> PaymentTransaction:
        """Create manual payment record (for bank transfers, cash, etc.)."""

        invoice = (
            self.db.query(Invoice)
            .filter(Invoice.id == invoice_id, Invoice.user_id == user_id)
            .first()
        )

        if not invoice:
            raise ValueError("Invoice not found or access denied")

        transaction = PaymentTransaction(
            invoice_id=invoice.id,
            transaction_reference=reference,
            payment_method=payment_method,
            payment_gateway="manual",
            amount=amount,
            currency=invoice.currency,
            status="completed",
            completed_at=datetime.now(timezone.utc),
        )

        self.db.add(transaction)

        # Update invoice status if fully paid
        if invoice.is_fully_paid:
            invoice.mark_as_paid()

        self.db.commit()
        return transaction


def get_paystack_service() -> PaystackService:
    """Get configured Paystack service instance."""
    settings = get_settings()

    if not settings.paystack_secret_key or not settings.paystack_public_key:
        raise ValueError(
            "Paystack configuration is missing. Please set PAYSTACK_SECRET_KEY and PAYSTACK_PUBLIC_KEY environment variables."
        )

    return PaystackService(
        secret_key=settings.paystack_secret_key, public_key=settings.paystack_public_key
    )
