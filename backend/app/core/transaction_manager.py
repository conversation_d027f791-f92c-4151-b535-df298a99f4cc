"""Transaction Management System

Provides comprehensive database transaction management with:
- Context managers for transaction boundaries
- Decorators for automatic transaction handling
- Nested transaction support with savepoints
- Proper error handling and rollback mechanisms
- Foreign key constraint validation
"""

import functools
import logging
from contextlib import contextmanager
from typing import Any, Callable, Generator, Optional, TypeVar

from sqlalchemy import event
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm import Session

from app.core.exceptions import (ConflictError, DatabaseError,
                                 ValidationException)
from app.database import SessionLocal

logger = logging.getLogger(__name__)

F = TypeVar("F", bound=Callable[..., Any])


class TransactionManager:
    """Centralized transaction management for database operations"""

    def __init__(self, session: Optional[Session] = None):
        self._session = session
        self._savepoint_counter = 0

    @property
    def session(self) -> Session:
        """Get the current session, creating one if needed"""
        if self._session is None:
            self._session = SessionLocal()
        return self._session

    @contextmanager
    def transaction(
        self, rollback_on_exception: bool = True, close_session: bool = False
    ) -> Generator[Session, None, None]:
        """Context manager for database transactions

        Args:
            rollback_on_exception: Whether to rollback on any exception
            close_session: Whether to close session after transaction

        Yields:
            Database session within transaction boundary

        Example:
            with TransactionManager().transaction() as db:
                user = User(email="<EMAIL>")
                db.add(user)
                # Automatically commits on success, rolls back on exception
        """
        session = self.session
        transaction_started = False

        try:
            # Start transaction if not already in one
            if not session.in_transaction():
                session.begin()
                transaction_started = True
                logger.debug("Started new database transaction")

            yield session

            # Commit if we started the transaction
            if transaction_started:
                session.commit()
                logger.debug("Transaction committed successfully")

        except IntegrityError as e:
            if rollback_on_exception and transaction_started:
                session.rollback()
                logger.warning(f"Transaction rolled back due to integrity error: {e}")

            # Handle common integrity constraint violations
            error_msg = str(e.orig).lower()
            if "unique constraint" in error_msg or "duplicate key" in error_msg:
                raise ConflictError("Resource already exists with these values")
            elif "foreign key constraint" in error_msg:
                raise ValidationException("Referenced resource does not exist")
            elif "not null constraint" in error_msg:
                raise ValidationException("Required field cannot be empty")
            else:
                raise DatabaseError(f"Database integrity error: {str(e)}")

        except SQLAlchemyError as e:
            if rollback_on_exception and transaction_started:
                session.rollback()
                logger.error(f"Transaction rolled back due to database error: {e}")
            raise DatabaseError(f"Database operation failed: {str(e)}")

        except Exception as e:
            if rollback_on_exception and transaction_started:
                session.rollback()
                logger.error(f"Transaction rolled back due to unexpected error: {e}")
            raise

        finally:
            if close_session:
                session.close()
                self._session = None

    @contextmanager
    def savepoint(self, name: Optional[str] = None) -> Generator[Session, None, None]:
        """Context manager for nested transactions using savepoints

        Args:
            name: Optional savepoint name

        Yields:
            Database session within savepoint boundary

        Example:
            with TransactionManager().transaction() as db:
                user = User(email="<EMAIL>")
                db.add(user)

                with tm.savepoint() as sp_db:
                    # This can fail without affecting the outer transaction
                    risky_operation(sp_db)
        """
        session = self.session

        if name is None:
            self._savepoint_counter += 1
            name = f"sp_{self._savepoint_counter}"

        savepoint = session.begin_nested()
        logger.debug(f"Created savepoint: {name}")

        try:
            yield session
            logger.debug(f"Savepoint {name} completed successfully")

        except Exception as e:
            savepoint.rollback()
            logger.warning(f"Savepoint {name} rolled back due to error: {e}")
            raise

    def execute_in_transaction(self, func: Callable[..., Any], *args, **kwargs) -> Any:
        """Execute a function within a transaction

        Args:
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Function result
        """
        with self.transaction() as db:
            # Inject session as first argument if function expects it
            import inspect

            sig = inspect.signature(func)
            params = list(sig.parameters.keys())

            if params and params[0] in ["db", "session"]:
                return func(db, *args, **kwargs)
            else:
                return func(*args, **kwargs)


# Global transaction manager instance
transaction_manager = TransactionManager()


@contextmanager
def db_transaction(
    session: Optional[Session] = None, rollback_on_exception: bool = True
) -> Generator[Session, None, None]:
    """Convenient context manager for database transactions

    Args:
        session: Optional existing session to use
        rollback_on_exception: Whether to rollback on exceptions

    Yields:
        Database session within transaction boundary

    Example:
        with db_transaction() as db:
            user = User(email="<EMAIL>")
            db.add(user)
    """
    if session:
        tm = TransactionManager(session)
    else:
        tm = TransactionManager()

    with tm.transaction(rollback_on_exception=rollback_on_exception) as db:
        yield db


def transactional(rollback_on_exception: bool = True, close_session: bool = False):
    """Decorator for automatic transaction management

    Args:
        rollback_on_exception: Whether to rollback on exceptions
        close_session: Whether to close session after function execution

    Example:
        @transactional()
        def create_user_with_profile(db: Session, user_data: dict):
            user = User(**user_data)
            db.add(user)
            db.flush()  # Get user.id

            profile = UserProfile(user_id=user.id, bio="Default bio")
            db.add(profile)
            return user
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check if first argument is a database session
            import inspect

            sig = inspect.signature(func)
            params = list(sig.parameters.keys())

            if params and params[0] in ["db", "session"] and args:
                # Function expects a session and one was provided
                return func(*args, **kwargs)
            else:
                # Create a new transaction
                tm = TransactionManager()
                with tm.transaction(
                    rollback_on_exception=rollback_on_exception,
                    close_session=close_session,
                ) as db:
                    if params and params[0] in ["db", "session"]:
                        return func(db, *args, **kwargs)
                    else:
                        return func(*args, **kwargs)

        return wrapper

    return decorator


def atomic(func: F) -> F:
    """Decorator for atomic database operations

    Shorthand for @transactional() with default settings

    Example:
        @atomic
        def transfer_funds(db: Session, from_account: str, to_account: str, amount: float):
            # This entire operation is atomic
            debit_account(db, from_account, amount)
            credit_account(db, to_account, amount)
    """
    return transactional()(func)


class DatabaseConstraintValidator:
    """Utility class for validating database constraints before operations"""

    @staticmethod
    def validate_foreign_keys(session: Session, model_instance: Any) -> None:
        """Validate foreign key constraints for a model instance

        Args:
            session: Database session
            model_instance: Model instance to validate

        Raises:
            ValidationException: If foreign key constraints are violated
        """
        from sqlalchemy import inspect

        mapper = inspect(model_instance.__class__)

        for relationship in mapper.relationships:
            if relationship.direction.name == "MANYTOONE":  # Foreign key relationship
                fk_value = getattr(model_instance, relationship.key + "_id", None)
                if fk_value is not None:
                    # Check if referenced record exists
                    related_class = relationship.mapper.class_
                    exists = (
                        session.query(related_class)
                        .filter(related_class.id == fk_value)
                        .first()
                    )

                    if not exists:
                        raise ValidationException(
                            f"Referenced {related_class.__name__} with id {fk_value} does not exist"
                        )

    @staticmethod
    def validate_unique_constraints(session: Session, model_instance: Any) -> None:
        """Validate unique constraints for a model instance

        Args:
            session: Database session
            model_instance: Model instance to validate

        Raises:
            ConflictError: If unique constraints are violated
        """
        from sqlalchemy import inspect

        mapper = inspect(model_instance.__class__)
        model_class = model_instance.__class__

        # Check unique columns
        for column in mapper.columns:
            if column.unique and not column.primary_key:
                value = getattr(model_instance, column.name)
                if value is not None:
                    existing = (
                        session.query(model_class)
                        .filter(getattr(model_class, column.name) == value)
                        .first()
                    )

                    if existing and existing.id != getattr(model_instance, "id", None):
                        raise ConflictError(
                            f"{model_class.__name__} with {column.name}='{value}' already exists"
                        )


# Event listeners for enhanced transaction logging
@event.listens_for(Session, "after_transaction_create")
def log_transaction_create(session, transaction):
    """Log transaction creation"""
    logger.debug(f"Transaction created: {transaction}")


@event.listens_for(Session, "after_transaction_end")
def log_transaction_end(session, transaction):
    """Log transaction completion"""
    logger.debug(f"Transaction ended: {transaction}")


@event.listens_for(Session, "after_commit")
def log_commit(session):
    """Log successful commits"""
    logger.debug("Transaction committed successfully")


@event.listens_for(Session, "after_rollback")
def log_rollback(session):
    """Log rollbacks"""
    logger.warning("Transaction rolled back")
