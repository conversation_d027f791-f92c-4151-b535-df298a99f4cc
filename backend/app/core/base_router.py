"""Base router utilities for consistent API patterns across DevHQ"""

from typing import Any, Dict, List, Optional, Type, TypeVar

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

# Type variables for generic responses
T = TypeVar("T", bound=BaseModel)
CreateT = TypeVar("CreateT", bound=BaseModel)
UpdateT = TypeVar("UpdateT", bound=BaseModel)
ResponseT = TypeVar("ResponseT", bound=BaseModel)


class StandardResponse(BaseModel):
    """Standard API response format"""

    success: bool = True
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None


class PaginationParams(BaseModel):
    """Standard pagination parameters"""

    page: int = Query(1, ge=1, description="Page number")
    per_page: int = Query(25, ge=1, le=100, description="Items per page")


class PaginatedResponse(BaseModel):
    """Standard paginated response format"""

    total: int
    page: int
    per_page: int
    total_pages: int
    has_next: bool
    has_prev: bool
    items: List[Any]

    @classmethod
    def create(cls, items: List[Any], total: int, page: int, per_page: int):
        total_pages = (total + per_page - 1) // per_page
        return cls(
            total=total,
            page=page,
            per_page=per_page,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1,
            items=items,
        )


class BulkOperationRequest(BaseModel):
    """Standard bulk operation request"""

    operation: str  # 'create', 'update', 'delete'
    items: List[Dict[str, Any]]


class BulkOperationResponse(BaseModel):
    """Standard bulk operation response"""

    success_count: int
    error_count: int
    total_count: int
    errors: List[Dict[str, Any]] = []
    successful_items: List[Any] = []


class FilterParams(BaseModel):
    """Standard filtering parameters"""

    search: Optional[str] = Query(None, description="Search term")
    sort_by: Optional[str] = Query("created_at", description="Sort field")
    sort_order: Optional[str] = Query(
        "desc", regex="^(asc|desc)$", description="Sort order"
    )
    created_after: Optional[str] = Query(
        None, description="Filter by creation date (ISO format)"
    )
    created_before: Optional[str] = Query(
        None, description="Filter by creation date (ISO format)"
    )
    is_active: Optional[bool] = Query(None, description="Filter by active status")


class BaseRouterMixin:
    """
    Mixin class providing standardized CRUD operations and response patterns
    """

    def __init__(self, prefix: str, tags: List[str]):
        self.router = APIRouter(prefix=prefix, tags=tags)
        self._setup_standard_responses()

    def _setup_standard_responses(self):
        """Setup standard HTTP response codes and descriptions"""
        self.standard_responses = {
            400: {"description": "Bad Request - Invalid input data"},
            401: {"description": "Unauthorized - Authentication required"},
            403: {"description": "Forbidden - Insufficient permissions"},
            404: {"description": "Not Found - Resource does not exist"},
            409: {"description": "Conflict - Resource already exists"},
            422: {"description": "Validation Error - Invalid data format"},
            429: {"description": "Too Many Requests - Rate limit exceeded"},
            500: {"description": "Internal Server Error"},
        }

    def create_standard_error_response(
        self, status_code: int, message: str, errors: Optional[List[str]] = None
    ):
        """Create standardized error response"""
        return StandardResponse(success=False, message=message, errors=errors or [])

    def create_success_response(self, message: str, data: Any = None):
        """Create standardized success response"""
        return StandardResponse(success=True, message=message, data=data)

    def handle_not_found(self, resource_name: str, resource_id: str = None):
        """Standard not found error handler"""
        message = f"{resource_name} not found"
        if resource_id:
            message += f" with ID: {resource_id}"
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=message)

    def handle_conflict(self, resource_name: str, field: str = None, value: str = None):
        """Standard conflict error handler"""
        message = f"{resource_name} already exists"
        if field and value:
            message += f" with {field}: {value}"
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=message)

    def handle_validation_error(self, message: str, errors: List[str] = None):
        """Standard validation error handler"""
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail={"message": message, "errors": errors or []},
        )

    def validate_uuid(self, uuid_str: str, field_name: str = "ID"):
        """Validate UUID format"""
        try:
            import uuid

            return uuid.UUID(uuid_str)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid {field_name} format",
            )


def create_bulk_endpoint(router: APIRouter, operation_name: str, handler_func):
    """
    Decorator to create standardized bulk operation endpoints
    """

    @router.post(f"/bulk/{operation_name}", response_model=BulkOperationResponse)
    async def bulk_operation(request: BulkOperationRequest, **kwargs):
        return await handler_func(request, **kwargs)

    return bulk_operation


def create_search_endpoint(router: APIRouter, search_handler_func):
    """
    Decorator to create standardized search endpoints
    """

    @router.get("/search", response_model=PaginatedResponse)
    async def search_items(
        q: str = Query(..., description="Search query"),
        filters: FilterParams = Depends(),
        pagination: PaginationParams = Depends(),
        **kwargs,
    ):
        return await search_handler_func(q, filters, pagination, **kwargs)

    return search_items
