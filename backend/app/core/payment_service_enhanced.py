"""
Enhanced payment service with multi-gateway support and platform revenue features
"""

import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.config import get_settings
from app.core.monitoring_decorators import monitor_service_operation
from app.models import Invoice, User

from .gateway_selector import GatewaySelector
from .payment_gateways import (DPOGateway, PaymentGateway, PaymentResult,
                               PaymentStatus, PaystackGateway)
from .payout_strategy_service import PayoutStrategyService
from .platform_fee_service import FeeCalculation, PlatformFeeService


class EnhancedPaymentService:
    """Enhanced payment service with multi-gateway support and revenue features"""

    def __init__(self, db: Session):
        self.db = db
        self.settings = get_settings()
        self.gateways = self._initialize_gateways()
        self.gateway_selector = GatewaySelector(self.gateways)
        self.platform_fee_service = PlatformFeeService(db)
        self.payout_strategy_service = PayoutStrategyService(db)

    def _initialize_gateways(self) -> Dict[str, PaymentGateway]:
        """Initialize available payment gateways with enhanced error handling"""
        gateways = {}

        # Initialize Paystack if configured
        if (
            self.settings.paystack_secret_key
            and not self.settings.paystack_secret_key.startswith("sk_test_placeholder")
        ):
            paystack_config = {
                "secret_key": self.settings.paystack_secret_key,
                "public_key": self.settings.paystack_public_key,
            }
            gateways["paystack"] = PaystackGateway(paystack_config, self.db)

        # Initialize DPO if configured
        if (
            self.settings.dpo_company_token
            and not self.settings.dpo_company_token == "DPO_TEST_PLACEHOLDER"
        ):
            dpo_config = {
                "company_token": self.settings.dpo_company_token,
                "service_type": self.settings.dpo_service_type,
                "test_mode": self.settings.dpo_test_mode,
            }
            gateways["dpo"] = DPOGateway(dpo_config, self.db)

        return gateways

    @monitor_service_operation("payment_link_generation")
    async def generate_payment_link(
        self,
        invoice_id: uuid.UUID,
        user_id: uuid.UUID,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None,
        preferred_gateway: Optional[str] = None,
        instant_settlement: bool = False,
    ) -> Dict[str, any]:
        """Generate payment link with intelligent gateway selection"""

        # Get invoice with access control
        invoice = (
            self.db.query(Invoice)
            .filter(
                Invoice.id == invoice_id,
                Invoice.user_id == user_id,
                Invoice.status.in_(["sent", "viewed", "overdue"]),
            )
            .first()
        )

        if not invoice:
            raise ValueError("Invoice not found or not accessible")

        # Get user's country for gateway selection
        user = self.db.query(User).filter(User.id == user_id).first()
        user_country = getattr(user, "country", None)

        # Select optimal gateway
        if preferred_gateway and preferred_gateway in self.gateways:
            gateway = self.gateways[preferred_gateway]
            gateway_name = preferred_gateway
        else:
            gateway = await self.gateway_selector.select_gateway(
                currency=invoice.currency,
                country=user_country,
                amount=invoice.total_amount,
                optimization=self.settings.gateway_selection_strategy,
            )
            gateway_name = gateway.get_gateway_name()

        # Calculate fees and platform revenue
        gateway_fees = await gateway.calculate_fees(
            invoice.total_amount, invoice.currency
        )

        fee_calculation = self.platform_fee_service.calculate_invoice_payout(
            invoice=invoice,
            gateway_fee=gateway_fees["gateway_fee"],
            instant_settlement=instant_settlement,
        )

        # Generate unique reference
        reference = f"inv_{invoice.id.hex[:8]}_{uuid.uuid4().hex[:8]}"

        # Prepare metadata
        metadata = {
            "invoice_id": str(invoice.id),
            "invoice_number": invoice.invoice_number,
            "client_name": invoice.client.name,
            "user_id": str(user_id),
            "platform_fee": str(fee_calculation.platform_fee),
            "instant_settlement": instant_settlement,
            "custom_fields": [
                {
                    "display_name": "Invoice Number",
                    "variable_name": "invoice_number",
                    "value": invoice.invoice_number,
                }
            ],
        }

        # Create payment link
        payment_data = await gateway.create_payment_link(
            amount=invoice.total_amount,
            currency=invoice.currency,
            customer_email=invoice.client.email or "<EMAIL>",
            reference=reference,
            metadata=metadata,
            success_url=success_url,
            cancel_url=cancel_url,
        )

        # Update invoice with payment details
        invoice.payment_link = payment_data["payment_link"]
        invoice.payment_reference = reference
        invoice.payment_gateway = gateway_name
        invoice.settlement_type = "instant" if instant_settlement else "standard"
        invoice.platform_fee_amount = fee_calculation.platform_fee
        invoice.gateway_fee_amount = fee_calculation.gateway_fee
        invoice.instant_settlement_fee = fee_calculation.instant_settlement_fee
        invoice.net_payout_amount = fee_calculation.final_payout

        self.db.commit()

        return {
            "payment_link": payment_data["payment_link"],
            "reference": reference,
            "gateway": gateway_name,
            "fee_calculation": fee_calculation,
            "expires_at": datetime.now(timezone.utc).isoformat(),
        }

    @monitor_service_operation("payment_verification")
    async def verify_payment(
        self, reference: str, gateway_name: Optional[str] = None
    ) -> PaymentResult:
        """Verify payment with the appropriate gateway"""

        # Find invoice by reference
        invoice = (
            self.db.query(Invoice)
            .filter(Invoice.payment_reference == reference)
            .first()
        )

        if not invoice:
            raise ValueError("Invoice not found for reference")

        # Use specified gateway or invoice's gateway
        gateway_to_use = gateway_name or invoice.payment_gateway
        if gateway_to_use not in self.gateways:
            raise ValueError(f"Gateway {gateway_to_use} not available")

        gateway = self.gateways[gateway_to_use]

        # Verify payment
        payment_result = await gateway.verify_payment(reference)

        # Update invoice if payment successful
        if payment_result.status == PaymentStatus.SUCCESS:
            await self._process_successful_payment(invoice, payment_result)

        return payment_result

    async def _process_successful_payment(
        self, invoice: Invoice, payment_result: PaymentResult
    ):
        """Process successful payment and update invoice"""

        invoice.status = "paid"
        invoice.paid_at = payment_result.paid_at or datetime.now(timezone.utc)
        invoice.settlement_status = "processing"

        # Update actual gateway fee if different from estimate
        if payment_result.gateway_fee:
            invoice.gateway_fee_amount = payment_result.gateway_fee

            # Recalculate net payout with actual fee
            fee_calculation = self.platform_fee_service.calculate_invoice_payout(
                invoice=invoice,
                gateway_fee=payment_result.gateway_fee,
                instant_settlement=(invoice.settlement_type == "instant"),
            )
            invoice.net_payout_amount = fee_calculation.final_payout

        self.db.commit()

        # Trigger settlement processing if auto-settlement enabled
        if invoice.settlement_preference == "auto":
            await self._process_settlement(invoice)

    async def _process_settlement(self, invoice: Invoice):
        """Process settlement for paid invoice using payout strategy service"""

        # Get payout recommendation for this user and amount
        payout_recommendation = self.payout_strategy_service.get_payout_recommendation(
            user_id=invoice.user_id,
            amount=invoice.net_payout_amount or Decimal("0"),
            currency=invoice.currency,
        )

        # Check if user has payout settings
        user = self.db.query(User).filter(User.id == invoice.user_id).first()
        payout_settings = getattr(user, "payout_settings", None)

        # Process based on recommended payout method
        if (
            payout_recommendation.recommended_method.name == "PAYSTACK_SUBACCOUNT"
            and payout_settings
            and payout_settings.payout_bank_account
        ):
            # Check if user already has a Paystack subaccount
            if not payout_settings.paystack_subaccount_id:
                # Create Paystack subaccount
                try:
                    bank_account = payout_settings.payout_bank_account
                    subaccount_data = await self._create_paystack_subaccount(
                        user_id=invoice.user_id,
                        business_name=f"DevHQ Developer - {user.email}",
                        bank_account=bank_account,
                    )

                    # Update user's payout settings with subaccount ID
                    payout_settings.paystack_subaccount_id = subaccount_data[
                        "subaccount_code"
                    ]
                    self.db.commit()

                except Exception as e:
                    # Log error but continue with standard payout
                    print(f"Failed to create Paystack subaccount: {e}")

        # Update invoice with recommended payout method
        # In a real implementation, this would:
        # 1. Create subaccount transfers for Paystack
        # 2. Initiate DPO payouts for mobile money/bank transfers
        # 3. Handle instant vs standard settlement timing based on recommendation
        # 4. Update settlement status
        # 5. Send notifications

        invoice.settlement_status = "completed"
        invoice.settled_at = datetime.now(timezone.utc)
        self.db.commit()

    async def _create_paystack_subaccount(
        self, user_id: uuid.UUID, business_name: str, bank_account: Dict
    ) -> Dict[str, str]:
        """Create a Paystack subaccount for a user"""

        # Check if Paystack gateway is available
        if "paystack" not in self.gateways:
            raise Exception("Paystack gateway not configured")

        paystack_gateway = self.gateways["paystack"]

        # Create subaccount
        subaccount_data = await paystack_gateway.create_subaccount(
            business_name=business_name,
            settlement_bank=bank_account.get("bank_code", ""),
            account_number=bank_account.get("account_number", ""),
            percentage_charge=float(
                self.settings.platform_fee_rate * 100
            ),  # Convert to percentage
            description=f"DevHQ Developer Account - {user_id}",
            primary_contact_email=bank_account.get("email", ""),
            primary_contact_name=bank_account.get("account_name", ""),
        )

        return subaccount_data

    async def get_gateway_recommendations(
        self,
        currency: str,
        country: Optional[str] = None,
        amount: Optional[Decimal] = None,
    ) -> List[Dict]:
        """Get gateway recommendations for currency/country"""

        recommendations = await self.gateway_selector.get_gateway_recommendations(
            currency=currency,
            country=country,
            amount=amount,
        )

        return [
            {
                "gateway": rec.gateway.get_gateway_name(),
                "score": rec.score,
                "reasons": rec.reasons,
                "estimated_fee": rec.estimated_fee,
            }
            for rec in recommendations
        ]

    async def get_supported_currencies(self) -> List[str]:
        """Get all supported currencies across gateways"""
        return await self.gateway_selector.get_supported_currencies()

    async def get_supported_countries(self) -> List[str]:
        """Get all supported countries across gateways"""
        return await self.gateway_selector.get_supported_countries()

    async def get_payment_methods(
        self, currency: str, country: str
    ) -> Dict[str, List[str]]:
        """Get payment methods by gateway for currency/country"""
        return await self.gateway_selector.get_payment_methods_for_currency_country(
            currency, country
        )

    def verify_webhook_signature(
        self, payload: str, signature: str, gateway_name: str
    ) -> bool:
        """Verify webhook signature for specified gateway"""

        if gateway_name not in self.gateways:
            return False

        gateway = self.gateways[gateway_name]

        # For DPO, verification is done via API call rather than signature
        # So we'll return True for now and handle verification in the gateway
        if gateway_name == "dpo":
            # DPO verification is done by calling their verify API
            # The signature verification for DPO is different and typically involves IP checking
            # For now, we'll allow processing and let the gateway handle verification
            return True

        return gateway.verify_webhook_signature(payload, signature)

    @monitor_service_operation("webhook_processing")
    async def process_webhook(
        self,
        payload: Dict,
        gateway_name: str,
    ) -> Dict[str, any]:
        """Process webhook from payment gateway"""

        # Extract reference from webhook payload
        reference = None
        if gateway_name == "paystack":
            reference = payload.get("data", {}).get("reference")
        elif gateway_name == "dpo":
            # For DPO, the reference might be in different locations depending on event type
            reference = (
                payload.get("data", {}).get("TransactionRef")
                or payload.get("TransactionRef")
                or payload.get("transaction_id")
            )

        if not reference:
            raise ValueError("No reference found in webhook payload")

        # Verify and process payment
        payment_result = await self.verify_payment(reference, gateway_name)

        return {
            "reference": reference,
            "status": payment_result.status.value,
            "amount": payment_result.amount,
            "currency": payment_result.currency,
        }

    def get_available_gateways(self) -> List[str]:
        """Get list of available gateway names"""
        return list(self.gateways.keys())

    async def calculate_fees_comparison(
        self,
        amount: Decimal,
        currency: str,
        payment_method: str = "card",
    ) -> Dict[str, Dict]:
        """Compare fees across all available gateways"""

        comparison = {}

        for gateway_name, gateway in self.gateways.items():
            if gateway.is_supported_currency(currency):
                try:
                    fees = await gateway.calculate_fees(
                        amount, currency, payment_method
                    )
                    comparison[gateway_name] = fees
                except Exception as e:
                    comparison[gateway_name] = {"error": str(e)}

        return comparison

    @monitor_service_operation("payment_processing")
    def process_payment(
        self,
        invoice_id: uuid.UUID,
        payment_method: str,
        gateway: str = "paystack",
        metadata: Optional[Dict] = None,
    ) -> Dict[str, any]:
        """Process direct payment for an invoice"""

        # Get invoice
        invoice = self.db.query(Invoice).filter(Invoice.id == invoice_id).first()

        if not invoice:
            raise ValueError("Invoice not found")

        # Validate gateway
        if gateway not in self.gateways:
            raise ValueError(f"Gateway {gateway} not available")

        # Validate payment method
        if not payment_method:
            raise ValueError("Payment method is required")

        # For now, return a mock response since this is a test scenario
        # In a real implementation, this would:
        # 1. Create a payment transaction
        # 2. Process with the selected gateway
        # 3. Update invoice status
        # 4. Handle success/failure scenarios

        return {
            "success": True,
            "payment_id": f"pay_{uuid.uuid4().hex[:8]}",
            "status": "completed",
            "gateway": gateway,
            "payment_method": payment_method,
            "invoice_id": str(invoice_id),
        }
