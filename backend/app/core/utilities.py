"""
Utility functions for common operations
"""

from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union

from app.core.email import EmailService
from app.core.pdf_service import PDFGenerationService
from app.core.security import SecurityUtils
# Import validation functions
from app.core.validators import (validate_currency, validate_email,
                                 validate_password_strength,
                                 validate_phone_number, validate_timezone)

# Initialize services
email_service = EmailService()
pdf_service = PDFGenerationService()


def format_currency(amount: Decimal, currency: str = "USD") -> str:
    """Format amount as currency"""
    if currency == "USD":
        return f"${amount:,.2f}"
    elif currency == "EUR":
        return f"€{amount:,.2f}"
    elif currency == "GBP":
        return f"£{amount:,.2f}"
    elif currency == "NGN":
        return f"₦{amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency}"


def format_date(date_obj: Union[datetime, date], format_str: str = "%b %d, %Y") -> str:
    """Format date as string"""
    return date_obj.strftime(format_str)


def format_percentage(value: Decimal, precision: int = 2) -> str:
    """Format decimal as percentage"""
    # Multiply by 100 to convert decimal to percentage
    percentage_value = value * 100
    return f"{percentage_value:.{precision}f}%"


def format_duration(seconds: int) -> str:
    """Format seconds as duration string"""
    minutes, secs = divmod(seconds, 60)
    hours, minutes = divmod(minutes, 60)

    if hours > 0:
        return f"{hours}h {minutes}m"
    elif minutes > 0:
        return f"{minutes}m"
    else:
        return f"{secs}s"


def send_email(to: str, subject: str, body: str, html: Optional[str] = None) -> bool:
    """Send email using email service"""
    return email_service.send_email(
        to_email=to, subject=subject, body=body, html_body=html
    )


def send_invoice_email(invoice_data: Dict[str, Any]) -> bool:
    """Send invoice email using template"""
    return email_service.send_template_email(
        to_email=invoice_data.get("client_email"),
        template_name="invoice",
        template_data=invoice_data,
    )


def generate_invoice_pdf(invoice_data: Dict[str, Any]) -> bytes:
    """Generate invoice PDF"""
    return pdf_service.generate_invoice_pdf(invoice_data)


def hash_password(password: str) -> str:
    """Hash password using security utils"""
    return SecurityUtils.hash_password(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password using security utils"""
    return SecurityUtils.verify_password(plain_password, hashed_password)


def generate_token(payload: Dict[str, Any]) -> str:
    """Generate JWT token using security utils"""
    return SecurityUtils.generate_token(payload)


def verify_token(token: str) -> Dict[str, Any]:
    """Verify JWT token using security utils"""
    return SecurityUtils.verify_token(token)


def generate_secure_random_string(length: int = 32) -> str:
    """Generate secure random string using security utils"""
    return SecurityUtils.generate_secure_random_string(length)


def encrypt_sensitive_data(data: str) -> str:
    """Encrypt sensitive data using security utils"""
    return SecurityUtils.encrypt_sensitive_data(data)


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """Decrypt sensitive data using security utils"""
    return SecurityUtils.decrypt_sensitive_data(encrypted_data)
