"""
Invoice service for professional billing and payment processing
"""

import uuid
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

from sqlalchemy import and_, func, or_
from sqlalchemy.orm import Session, joinedload

from app.models import (Client, Invoice, InvoiceItem, PaymentTransaction,
                        Project, ProjectMilestone, TimeEntry, User)
from app.schemas.invoice import (BillableItemResponse, BillableItemsResponse,
                                 InvoiceCreate, InvoiceFromProjectRequest,
                                 InvoiceItemCreate)


class BillableItemService:
    """Service for detecting and managing billable items from time tracking."""

    def __init__(self, db: Session):
        self.db = db

    def get_billable_items(
        self,
        user_id: uuid.UUID,
        project_id: Optional[uuid.UUID] = None,
        include_billed: bool = False,
    ) -> BillableItemsResponse:
        """Get all billable items (time entries and milestones) for a user."""

        # Build base queries
        time_query = self.db.query(TimeEntry).filter(
            TimeEntry.user_id == user_id,
            TimeEntry.is_billable == True,
            TimeEntry.deleted_at.is_(None),
        )

        milestone_query = (
            self.db.query(ProjectMilestone)
            .join(Project)
            .filter(
                Project.user_id == user_id,
                ProjectMilestone.status == "completed",
                ProjectMilestone.payment_amount.isnot(None),
                ProjectMilestone.deleted_at.is_(None),
            )
        )

        # Filter by project if specified
        if project_id:
            time_query = time_query.filter(TimeEntry.project_id == project_id)
            milestone_query = milestone_query.filter(
                ProjectMilestone.project_id == project_id
            )

        # Filter by billing status
        if not include_billed:
            time_query = time_query.filter(TimeEntry.status != "billed")
            # For milestones, check if they're already billed via invoice items
            billed_milestone_ids = (
                self.db.query(InvoiceItem.milestone_id)
                .filter(
                    InvoiceItem.milestone_id.isnot(None),
                    InvoiceItem.billing_status.in_(["billed", "paid"]),
                )
                .subquery()
            )
            milestone_query = milestone_query.filter(
                ~ProjectMilestone.id.in_(billed_milestone_ids)
            )

        # Execute queries with relationships
        time_entries = time_query.options(
            joinedload(TimeEntry.project).joinedload(Project.client_record)
        ).all()

        milestones = milestone_query.options(
            joinedload(ProjectMilestone.project).joinedload(Project.client_record)
        ).all()

        # Convert to billable items
        items = []
        total_amount = Decimal("0.00")
        total_hours = Decimal("0.00")

        # Process time entries
        for entry in time_entries:
            suggested_rate = (
                entry.hourly_rate or entry.project.hourly_rate or Decimal("50.00")
            )
            hours = entry.duration_hours
            suggested_total = hours * suggested_rate

            items.append(
                BillableItemResponse(
                    id=entry.id,
                    type="time_entry",
                    description=entry.description or f"Work on {entry.project.title}",
                    quantity=hours,
                    suggested_rate=suggested_rate,
                    suggested_total=suggested_total,
                    work_date=entry.work_date,
                    project_id=entry.project_id,
                    project_title=entry.project.title,
                    client_name=entry.project.client.name,
                    billing_status=entry.status,
                )
            )

            total_amount += suggested_total
            total_hours += hours

        # Process milestones
        for milestone in milestones:
            items.append(
                BillableItemResponse(
                    id=milestone.id,
                    type="milestone",
                    description=f"Milestone: {milestone.title}",
                    quantity=Decimal("1.00"),
                    suggested_rate=milestone.payment_amount,
                    suggested_total=milestone.payment_amount,
                    work_date=milestone.completed_at,
                    project_id=milestone.project_id,
                    project_title=milestone.project.title,
                    client_name=milestone.project.client.name,
                    billing_status="completed",
                )
            )

            total_amount += milestone.payment_amount

        return BillableItemsResponse(
            project_id=project_id,
            total_unbilled_amount=total_amount,
            total_hours=total_hours,
            items=items,
        )

    def get_project_billable_items(
        self, project_id: uuid.UUID, user_id: uuid.UUID
    ) -> BillableItemsResponse:
        """Get billable items for a specific project."""
        return self.get_billable_items(user_id, project_id)

    def mark_items_as_billed(
        self, time_entry_ids: List[uuid.UUID], milestone_ids: List[uuid.UUID]
    ):
        """Mark time entries and milestones as billed to prevent double-billing."""

        # Update time entries
        if time_entry_ids:
            self.db.query(TimeEntry).filter(TimeEntry.id.in_(time_entry_ids)).update(
                {"status": "billed"}, synchronize_session=False
            )

        # Note: Milestones are marked as billed through invoice items, not status change
        self.db.commit()

    def validate_billing_integrity(self, user_id: uuid.UUID) -> Dict[str, List[str]]:
        """Validate billing integrity and detect potential issues."""
        issues = {
            "double_billed_time_entries": [],
            "orphaned_invoice_items": [],
            "inconsistent_billing_status": [],
        }

        # Check for double-billed time entries
        double_billed = (
            self.db.query(TimeEntry.id)
            .join(InvoiceItem)
            .group_by(TimeEntry.id)
            .having(func.count(InvoiceItem.id) > 1)
            .all()
        )

        issues["double_billed_time_entries"] = [str(item[0]) for item in double_billed]

        # Check for orphaned invoice items (pointing to deleted entries)
        orphaned = (
            self.db.query(InvoiceItem.id)
            .outerjoin(TimeEntry)
            .filter(InvoiceItem.time_entry_id.isnot(None), TimeEntry.id.is_(None))
            .all()
        )

        issues["orphaned_invoice_items"] = [str(item[0]) for item in orphaned]

        return issues


class InvoiceGenerationService:
    """Service for intelligent invoice generation and management."""

    def __init__(self, db: Session):
        self.db = db
        self.billable_service = BillableItemService(db)

    def create_invoice(
        self, invoice_data: InvoiceCreate, user_id: uuid.UUID
    ) -> Invoice:
        """Create a new invoice with items."""

        # Validate client belongs to user
        client = (
            self.db.query(Client)
            .filter(Client.id == invoice_data.client_id, Client.user_id == user_id)
            .first()
        )

        if not client:
            raise ValueError("Client not found or access denied")

        # Use provided invoice number or generate unique one
        invoice_number = invoice_data.invoice_number or self._generate_invoice_number(
            user_id
        )

        # Create invoice
        invoice = Invoice(
            invoice_number=invoice_number,
            client_id=invoice_data.client_id,
            project_id=invoice_data.project_id,
            user_id=user_id,
            due_date=invoice_data.due_date,
            tax_rate=invoice_data.tax_rate,
            discount_amount=invoice_data.discount_amount,
            currency=invoice_data.currency,
            notes=invoice_data.notes,
            terms_and_conditions=invoice_data.terms_and_conditions,
            footer_text=invoice_data.footer_text,
            payment_terms_days=invoice_data.payment_terms_days,
        )

        self.db.add(invoice)
        self.db.flush()  # Get invoice ID

        # Create invoice items
        for i, item_data in enumerate(invoice_data.items, 1):
            item = InvoiceItem(
                invoice_id=invoice.id,
                description=item_data.description,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                item_type=item_data.item_type,
                time_entry_id=item_data.time_entry_id,
                milestone_id=item_data.milestone_id,
                hours_worked=item_data.hours_worked,
                hourly_rate=item_data.hourly_rate,
                work_date=item_data.work_date,
                group_name=item_data.group_name,
                sequence_number=item_data.sequence_number or i,
            )
            item.calculate_total()
            invoice.items.append(item)

        # Calculate invoice totals
        invoice.calculate_totals()

        self.db.commit()
        return invoice

    def create_invoice_from_project(
        self, request: InvoiceFromProjectRequest, user_id: uuid.UUID
    ) -> Invoice:
        """Create invoice from project billable items."""

        # Get billable items
        billable_items = self.billable_service.get_billable_items(
            user_id, request.project_id
        )

        # Filter requested items
        selected_items = [
            item for item in billable_items.items if item.id in request.item_ids
        ]

        if not selected_items:
            raise ValueError("No valid billable items found")

        # Get project and client info
        project = (
            self.db.query(Project)
            .filter(Project.id == request.project_id, Project.user_id == user_id)
            .first()
        )

        if not project:
            raise ValueError("Project not found or access denied")

        # Group items if requested
        if request.group_by_date or request.group_by_task:
            selected_items = self._group_billable_items(
                selected_items, request.group_by_date, request.group_by_task
            )

        # Create invoice items
        invoice_items = []
        for i, item in enumerate(selected_items, 1):
            invoice_item = InvoiceItemCreate(
                description=item.description,
                quantity=item.quantity,
                unit_price=item.suggested_rate,
                item_type=item.type.replace("_entry", ""),  # "time_entry" -> "time"
                time_entry_id=item.id if item.type == "time_entry" else None,
                milestone_id=item.id if item.type == "milestone" else None,
                hours_worked=item.quantity if item.type == "time_entry" else None,
                hourly_rate=item.suggested_rate if item.type == "time_entry" else None,
                work_date=item.work_date,
                sequence_number=i,
            )
            invoice_items.append(invoice_item)

        # Create invoice
        invoice_create = InvoiceCreate(
            client_id=project.client_id,
            project_id=request.project_id,
            due_date=request.due_date,
            tax_rate=request.tax_rate,
            discount_amount=request.discount_amount,
            notes=request.notes,
            terms_and_conditions=request.terms_and_conditions,
            items=invoice_items,
        )

        invoice = self.create_invoice(invoice_create, user_id)

        # Mark items as billed
        time_entry_ids = [
            item.id for item in selected_items if item.type == "time_entry"
        ]
        milestone_ids = [item.id for item in selected_items if item.type == "milestone"]

        self.billable_service.mark_items_as_billed(time_entry_ids, milestone_ids)

        return invoice

    def update_invoice(
        self, invoice_id: uuid.UUID, updates: dict, user_id: uuid.UUID
    ) -> Invoice:
        """Update an existing invoice."""

        invoice = (
            self.db.query(Invoice)
            .filter(
                Invoice.id == invoice_id,
                Invoice.user_id == user_id,
                Invoice.status == "draft",  # Only allow updates to draft invoices
            )
            .first()
        )

        if not invoice:
            raise ValueError("Invoice not found, access denied, or not editable")

        # Update fields
        for field, value in updates.items():
            if hasattr(invoice, field) and value is not None:
                setattr(invoice, field, value)

        # Recalculate totals if financial fields changed
        if any(field in updates for field in ["tax_rate", "discount_amount"]):
            invoice.calculate_totals()

        self.db.commit()
        return invoice

    def get_invoice(
        self, invoice_id: uuid.UUID, user_id: uuid.UUID
    ) -> Optional[Invoice]:
        """Get invoice by ID with access control."""
        return (
            self.db.query(Invoice)
            .filter(
                Invoice.id == invoice_id,
                Invoice.user_id == user_id,
                Invoice.deleted_at.is_(None),
            )
            .options(
                joinedload(Invoice.items),
                joinedload(Invoice.client),
                joinedload(Invoice.project),
            )
            .first()
        )

    def list_invoices(
        self,
        user_id: uuid.UUID,
        status: Optional[str] = None,
        client_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 50,
    ) -> Tuple[List[Invoice], int]:
        """List invoices with filtering and pagination."""

        query = self.db.query(Invoice).filter(Invoice.user_id == user_id)

        if status:
            query = query.filter(Invoice.status == status)
        if client_id:
            query = query.filter(Invoice.client_id == client_id)
        if project_id:
            query = query.filter(Invoice.project_id == project_id)

        total = query.count()
        invoices = (
            query.order_by(Invoice.created_at.desc()).offset(skip).limit(limit).all()
        )

        return invoices, total

    def send_invoice(self, invoice_id: uuid.UUID, user_id: uuid.UUID) -> Invoice:
        """Mark invoice as sent and update status."""

        invoice = self.get_invoice(invoice_id, user_id)
        if not invoice:
            raise ValueError("Invoice not found or access denied")

        if invoice.status != "draft":
            raise ValueError("Only draft invoices can be sent")

        invoice.mark_as_sent()
        self.db.commit()

        return invoice

    def _generate_invoice_number(self, user_id: uuid.UUID) -> str:
        """Generate unique invoice number for user."""
        year = datetime.now(timezone.utc).year

        # Get the count of invoices for this user this year
        count = (
            self.db.query(Invoice)
            .filter(
                Invoice.user_id == user_id,
                func.extract("year", Invoice.created_at) == year,
            )
            .count()
        )

        # Format: INV-YYYY-NNNN
        return f"INV-{year}-{(count + 1):04d}"

    def _group_billable_items(
        self,
        items: List[BillableItemResponse],
        group_by_date: bool,
        group_by_task: bool,
    ) -> List[BillableItemResponse]:
        """Group billable items by date and/or task."""

        if not (group_by_date or group_by_task):
            return items

        # Simple grouping logic - in production, this would be more sophisticated
        grouped = {}

        for item in items:
            # Create grouping key
            key_parts = []
            if group_by_date and item.work_date:
                key_parts.append(item.work_date.date().isoformat())
            if group_by_task:
                # Extract task from description (simplified)
                task = (
                    item.description.split(" - ")[0]
                    if " - " in item.description
                    else item.description
                )
                key_parts.append(task)

            key = " | ".join(key_parts) if key_parts else "ungrouped"

            if key not in grouped:
                grouped[key] = {
                    "description": key,
                    "quantity": Decimal("0.00"),
                    "total": Decimal("0.00"),
                    "items": [],
                }

            grouped[key]["quantity"] += item.quantity
            grouped[key]["total"] += item.suggested_total
            grouped[key]["items"].append(item)

        # Convert back to billable items (simplified)
        result = []
        for group_key, group_data in grouped.items():
            if len(group_data["items"]) == 1:
                # Single item, return as-is
                result.append(group_data["items"][0])
            else:
                # Multiple items, create grouped item
                first_item = group_data["items"][0]
                avg_rate = (
                    group_data["total"] / group_data["quantity"]
                    if group_data["quantity"] > 0
                    else Decimal("0.00")
                )

                grouped_item = BillableItemResponse(
                    id=first_item.id,  # Use first item's ID
                    type=first_item.type,
                    description=f"Grouped: {group_data['description']}",
                    quantity=group_data["quantity"],
                    suggested_rate=avg_rate,
                    suggested_total=group_data["total"],
                    work_date=first_item.work_date,
                    project_id=first_item.project_id,
                    project_title=first_item.project_title,
                    client_name=first_item.client_name,
                    billing_status=first_item.billing_status,
                )
                result.append(grouped_item)

        return result
