"""
Rate limiting utilities for enhanced security
"""

from functools import wraps
from typing import Callable, Optional

from slowapi import Limiter
from slowapi.util import get_remote_address

# Global limiter instance
limiter = Limiter(key_func=get_remote_address)


def rate_limit(
    limit: str = "100/minute",
    per_method: bool = False,
    exempt_when: Optional[Callable] = None,
    cost: int = 1,
    override_defaults: bool = True,
):
    """
    Decorator for applying rate limits to API endpoints

    Args:
        limit: Rate limit string (e.g., "100/minute", "10/second")
        per_method: Whether to apply limit per HTTP method
        exempt_when: Function to determine when to exempt from rate limiting
        cost: Cost of each request (for weighted limits)
        override_defaults: Whether to override default limits
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Apply rate limiting
            return await limiter.limit(
                limit,
                per_method=per_method,
                exempt_when=exempt_when,
                cost=cost,
                override_defaults=override_defaults,
            )(func)(*args, **kwargs)

        return wrapper

    return decorator


def ip_rate_limit(limit: str = "100/minute"):
    """
    Rate limit based on IP address

    Args:
        limit: Rate limit string (e.g., "100/minute", "10/second")
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Apply IP-based rate limiting
            return await limiter.limit(limit, key_func=get_remote_address)(func)(
                *args, **kwargs
            )

        return wrapper

    return decorator


def user_rate_limit(limit: str = "1000/hour"):
    """
    Rate limit based on authenticated user

    Args:
        limit: Rate limit string (e.g., "1000/hour", "100/day")
    """

    def get_user_id(request):
        """Extract user ID from request"""
        try:
            # Try to get user from request state
            if hasattr(request, "state") and hasattr(request.state, "user"):
                return str(request.state.user.id)
            # Try to get user ID from auth header or session
            return get_remote_address(request)
        except Exception:
            return get_remote_address(request)

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Apply user-based rate limiting
            return await limiter.limit(limit, key_func=get_user_id)(func)(
                *args, **kwargs
            )

        return wrapper

    return decorator


# Predefined rate limit decorators for common use cases
api_rate_limit = rate_limit("100/minute")  # General API rate limit
auth_rate_limit = rate_limit("10/minute")  # Authentication endpoints
payment_rate_limit = rate_limit("30/minute")  # Payment endpoints
heavy_rate_limit = rate_limit("10/minute")  # Heavy computation endpoints
