"""Monitoring Configuration for DevHQ Backend"""

import os
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional


class MonitoringLevel(Enum):
    """Monitoring levels for different environments"""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class MetricType(Enum):
    """Types of metrics to collect"""

    COUNTER = "counter"
    HISTOGRAM = "histogram"
    GAUGE = "gauge"
    SUMMARY = "summary"


@dataclass
class MetricConfig:
    """Configuration for a specific metric"""

    name: str
    description: str
    metric_type: MetricType
    labels: List[str]
    enabled: bool = True


@dataclass
class TracingConfig:
    """Configuration for distributed tracing"""

    enabled: bool
    sample_rate: float
    service_name: str
    service_version: str
    environment: str
    export_endpoint: Optional[str] = None
    export_headers: Optional[Dict[str, str]] = None


@dataclass
class LoggingConfig:
    """Configuration for structured logging"""

    level: str
    format: str
    include_trace_id: bool
    include_span_id: bool
    performance_threshold_ms: int
    security_events_enabled: bool


class MonitoringConfig:
    """Central monitoring configuration"""

    def __init__(self, level: MonitoringLevel = None):
        self.level = level or self._detect_environment()
        self._setup_configs()

    def _detect_environment(self) -> MonitoringLevel:
        """Detect environment from environment variables"""
        env = os.getenv("ENVIRONMENT", "development").lower()
        if env in ["prod", "production"]:
            return MonitoringLevel.PRODUCTION
        elif env in ["stage", "staging"]:
            return MonitoringLevel.STAGING
        else:
            return MonitoringLevel.DEVELOPMENT

    def _setup_configs(self):
        """Setup configurations based on environment level"""
        if self.level == MonitoringLevel.PRODUCTION:
            self._setup_production_config()
        elif self.level == MonitoringLevel.STAGING:
            self._setup_staging_config()
        else:
            self._setup_development_config()

    def _setup_production_config(self):
        """Production monitoring configuration"""
        self.tracing = TracingConfig(
            enabled=True,
            sample_rate=0.1,  # 10% sampling in production
            service_name="devhq-backend",
            service_version=os.getenv("APP_VERSION", "1.0.0"),
            environment="production",
            export_endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"),
            export_headers=self._parse_headers(
                os.getenv("OTEL_EXPORTER_OTLP_HEADERS", "")
            ),
        )

        self.logging = LoggingConfig(
            level="INFO",
            format="json",
            include_trace_id=True,
            include_span_id=True,
            performance_threshold_ms=1000,
            security_events_enabled=True,
        )

        self.metrics = self._get_production_metrics()

    def _setup_staging_config(self):
        """Staging monitoring configuration"""
        self.tracing = TracingConfig(
            enabled=True,
            sample_rate=0.5,  # 50% sampling in staging
            service_name="devhq-backend-staging",
            service_version=os.getenv("APP_VERSION", "1.0.0-staging"),
            environment="staging",
            export_endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"),
            export_headers=self._parse_headers(
                os.getenv("OTEL_EXPORTER_OTLP_HEADERS", "")
            ),
        )

        self.logging = LoggingConfig(
            level="DEBUG",
            format="json",
            include_trace_id=True,
            include_span_id=True,
            performance_threshold_ms=500,
            security_events_enabled=True,
        )

        self.metrics = self._get_staging_metrics()

    def _setup_development_config(self):
        """Development monitoring configuration"""
        self.tracing = TracingConfig(
            enabled=True,
            sample_rate=1.0,  # 100% sampling in development
            service_name="devhq-backend-dev",
            service_version="dev",
            environment="development",
            export_endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"),
            export_headers=self._parse_headers(
                os.getenv("OTEL_EXPORTER_OTLP_HEADERS", "")
            ),
        )

        self.logging = LoggingConfig(
            level="DEBUG",
            format="console",
            include_trace_id=False,
            include_span_id=False,
            performance_threshold_ms=200,
            security_events_enabled=False,
        )

        self.metrics = self._get_development_metrics()

    def _get_production_metrics(self) -> List[MetricConfig]:
        """Production metrics configuration"""
        return [
            MetricConfig(
                name="http_requests_total",
                description="Total HTTP requests",
                metric_type=MetricType.COUNTER,
                labels=["method", "endpoint", "status_code"],
            ),
            MetricConfig(
                name="http_request_duration_seconds",
                description="HTTP request duration",
                metric_type=MetricType.HISTOGRAM,
                labels=["method", "endpoint"],
            ),
            MetricConfig(
                name="payment_transactions_total",
                description="Total payment transactions",
                metric_type=MetricType.COUNTER,
                labels=["gateway", "status", "currency"],
            ),
            MetricConfig(
                name="payment_processing_duration_seconds",
                description="Payment processing duration",
                metric_type=MetricType.HISTOGRAM,
                labels=["gateway", "payment_method"],
            ),
            MetricConfig(
                name="database_operations_total",
                description="Total database operations",
                metric_type=MetricType.COUNTER,
                labels=["operation", "table"],
            ),
            MetricConfig(
                name="cache_operations_total",
                description="Total cache operations",
                metric_type=MetricType.COUNTER,
                labels=["operation", "hit_miss"],
            ),
            MetricConfig(
                name="active_websocket_connections",
                description="Active WebSocket connections",
                metric_type=MetricType.GAUGE,
                labels=["user_type"],
            ),
        ]

    def _get_staging_metrics(self) -> List[MetricConfig]:
        """Staging metrics configuration (same as production)"""
        return self._get_production_metrics()

    def _get_development_metrics(self) -> List[MetricConfig]:
        """Development metrics configuration (reduced set)"""
        return [
            MetricConfig(
                name="http_requests_total",
                description="Total HTTP requests",
                metric_type=MetricType.COUNTER,
                labels=["method", "endpoint", "status_code"],
            ),
            MetricConfig(
                name="http_request_duration_seconds",
                description="HTTP request duration",
                metric_type=MetricType.HISTOGRAM,
                labels=["method", "endpoint"],
            ),
        ]

    def _parse_headers(self, headers_str: str) -> Dict[str, str]:
        """Parse headers from string format"""
        if not headers_str:
            return {}

        headers = {}
        for header in headers_str.split(","):
            if "=" in header:
                key, value = header.strip().split("=", 1)
                headers[key] = value
        return headers

    def get_metric_config(self, metric_name: str) -> Optional[MetricConfig]:
        """Get configuration for a specific metric"""
        for metric in self.metrics:
            if metric.name == metric_name:
                return metric
        return None

    def is_metric_enabled(self, metric_name: str) -> bool:
        """Check if a metric is enabled"""
        config = self.get_metric_config(metric_name)
        return config.enabled if config else False

    def get_enabled_metrics(self) -> List[MetricConfig]:
        """Get all enabled metrics"""
        return [metric for metric in self.metrics if metric.enabled]


# Global monitoring configuration instance
monitoring_config = MonitoringConfig()
