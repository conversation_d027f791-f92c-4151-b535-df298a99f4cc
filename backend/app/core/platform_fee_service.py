"""
Platform fee service for DevHQ revenue generation and fee management
"""

import uuid
from dataclasses import dataclass
from datetime import datetime, timezone
from decimal import ROUND_HALF_UP, Decimal
from typing import Dict, Optional, Tuple

from sqlalchemy.orm import Session

from app.config import get_settings
from app.models import Invoice, User


@dataclass
class FeeCalculation:
    """Result of platform fee calculation"""

    gross_amount: Decimal
    platform_fee: Decimal
    gateway_fee: Decimal
    net_amount: Decimal
    instant_settlement_fee: Decimal
    final_payout: Decimal
    fee_breakdown: Dict[str, Decimal]


@dataclass
class PayoutDetails:
    """Payout information for user"""

    user_id: uuid.UUID
    amount: Decimal
    currency: str
    payout_method: str
    account_details: Dict
    reference: str
    status: str = "pending"


class PlatformFeeService:
    """Service for calculating and managing platform fees and payouts"""

    def __init__(self, db: Session):
        self.db = db
        self.settings = get_settings()

    def calculate_platform_fees(
        self,
        gross_amount: Decimal,
        currency: str,
        gateway_fee: Decimal = Decimal("0"),
        user_fee_rate: Optional[Decimal] = None,
        instant_settlement: bool = False,
    ) -> FeeCalculation:
        """
        Calculate platform fees and net payout amount

        Args:
            gross_amount: Total payment received
            currency: Currency code
            gateway_fee: Fee charged by payment gateway
            user_fee_rate: Custom fee rate for user (overrides default)
            instant_settlement: Whether user wants instant settlement

        Returns:
            FeeCalculation with detailed breakdown
        """

        # Use custom rate or default platform fee rate
        platform_fee_rate = user_fee_rate or Decimal(
            str(self.settings.platform_fee_rate)
        )

        # Calculate platform fee
        platform_fee = (gross_amount * platform_fee_rate).quantize(
            Decimal("0.01"), rounding=ROUND_HALF_UP
        )

        # Calculate net amount after platform fee
        net_amount = gross_amount - platform_fee

        # Calculate instant settlement fee if applicable
        instant_settlement_fee = Decimal("0")
        if instant_settlement:
            instant_fee_rate = Decimal(str(self.settings.instant_settlement_fee_rate))
            instant_settlement_fee = (net_amount * instant_fee_rate).quantize(
                Decimal("0.01"), rounding=ROUND_HALF_UP
            )

        # Final payout amount
        final_payout = net_amount - gateway_fee - instant_settlement_fee

        # Detailed fee breakdown
        fee_breakdown = {
            "platform_fee": platform_fee,
            "gateway_fee": gateway_fee,
            "instant_settlement_fee": instant_settlement_fee,
            "total_fees": platform_fee + gateway_fee + instant_settlement_fee,
        }

        return FeeCalculation(
            gross_amount=gross_amount,
            platform_fee=platform_fee,
            gateway_fee=gateway_fee,
            net_amount=net_amount,
            instant_settlement_fee=instant_settlement_fee,
            final_payout=final_payout,
            fee_breakdown=fee_breakdown,
        )

    def get_user_fee_rate(self, user_id: uuid.UUID) -> Decimal:
        """Get custom fee rate for user or default rate"""

        user = self.db.query(User).filter(User.id == user_id).first()
        if user and hasattr(user, "platform_fee_rate") and user.platform_fee_rate:
            return user.platform_fee_rate

        return Decimal(str(self.settings.platform_fee_rate))

    def calculate_invoice_payout(
        self,
        invoice: Invoice,
        gateway_fee: Decimal,
        instant_settlement: bool = False,
    ) -> FeeCalculation:
        """Calculate payout for a specific invoice"""

        user_fee_rate = self.get_user_fee_rate(invoice.user_id)

        return self.calculate_platform_fees(
            gross_amount=invoice.total_amount,
            currency=invoice.currency,
            gateway_fee=gateway_fee,
            user_fee_rate=user_fee_rate,
            instant_settlement=instant_settlement,
        )

    def create_payout_request(
        self,
        user_id: uuid.UUID,
        amount: Decimal,
        currency: str,
        payout_method: str = "bank_transfer",
        account_details: Optional[Dict] = None,
    ) -> PayoutDetails:
        """Create a payout request for user"""

        reference = (
            f"payout_{user_id.hex[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )

        return PayoutDetails(
            user_id=user_id,
            amount=amount,
            currency=currency,
            payout_method=payout_method,
            account_details=account_details or {},
            reference=reference,
            status="pending",
        )

    def get_fee_tier(
        self, monthly_volume: Decimal, currency: str = "USD"
    ) -> Tuple[Decimal, str]:
        """
        Get fee tier based on monthly transaction volume

        Returns:
            Tuple of (fee_rate, tier_name)
        """

        # Convert to USD for tier calculation if needed
        if currency != "USD":
            # In a real implementation, you'd use exchange rates
            # For now, using approximate rates
            exchange_rates = {
                "KES": Decimal("0.0067"),
                "NGN": Decimal("0.0013"),
                "GHS": Decimal("0.082"),
                "ZAR": Decimal("0.055"),
            }
            usd_volume = monthly_volume * exchange_rates.get(currency, Decimal("1"))
        else:
            usd_volume = monthly_volume

        # Fee tiers based on monthly volume
        if usd_volume >= Decimal("100000"):  # $100k+
            return Decimal("0.015"), "Enterprise"  # 1.5%
        elif usd_volume >= Decimal("50000"):  # $50k+
            return Decimal("0.020"), "Professional"  # 2.0%
        elif usd_volume >= Decimal("10000"):  # $10k+
            return Decimal("0.025"), "Business"  # 2.5%
        else:
            return Decimal("0.030"), "Starter"  # 3.0%

    def calculate_monthly_volume(
        self, user_id: uuid.UUID, months: int = 1
    ) -> Dict[str, Decimal]:
        """Calculate user's monthly transaction volume by currency"""

        from datetime import timedelta

        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=30 * months)

        # Query paid invoices in the period
        paid_invoices = (
            self.db.query(Invoice)
            .filter(
                Invoice.user_id == user_id,
                Invoice.status == "paid",
                Invoice.paid_at >= start_date,
                Invoice.paid_at <= end_date,
            )
            .all()
        )

        # Group by currency
        volume_by_currency = {}
        for invoice in paid_invoices:
            currency = invoice.currency
            if currency not in volume_by_currency:
                volume_by_currency[currency] = Decimal("0")
            volume_by_currency[currency] += invoice.total_amount

        return volume_by_currency

    def get_revenue_analytics(
        self,
        start_date: datetime,
        end_date: datetime,
    ) -> Dict[str, any]:
        """Get platform revenue analytics for a period"""

        # Query paid invoices in the period
        paid_invoices = (
            self.db.query(Invoice)
            .filter(
                Invoice.status == "paid",
                Invoice.paid_at >= start_date,
                Invoice.paid_at <= end_date,
            )
            .all()
        )

        total_revenue = Decimal("0")
        revenue_by_currency = {}
        transaction_count = 0

        for invoice in paid_invoices:
            transaction_count += 1

            # Calculate platform fee for this invoice
            user_fee_rate = self.get_user_fee_rate(invoice.user_id)
            platform_fee = invoice.total_amount * user_fee_rate

            total_revenue += platform_fee

            currency = invoice.currency
            if currency not in revenue_by_currency:
                revenue_by_currency[currency] = Decimal("0")
            revenue_by_currency[currency] += platform_fee

        return {
            "total_revenue": total_revenue,
            "revenue_by_currency": revenue_by_currency,
            "transaction_count": transaction_count,
            "average_transaction": (
                total_revenue / transaction_count
                if transaction_count > 0
                else Decimal("0")
            ),
            "period_start": start_date,
            "period_end": end_date,
        }

    def estimate_user_savings(
        self,
        user_id: uuid.UUID,
        new_gateway: str,
        current_gateway: str,
        amount: Decimal,
        currency: str,
    ) -> Dict[str, Decimal]:
        """Estimate potential savings by switching gateways"""

        # This would integrate with gateway fee calculations
        # For now, return placeholder data

        return {
            "current_gateway_fee": amount * Decimal("0.029"),
            "new_gateway_fee": amount * Decimal("0.025"),
            "potential_savings": amount * Decimal("0.004"),
            "savings_percentage": Decimal("0.4"),
        }
