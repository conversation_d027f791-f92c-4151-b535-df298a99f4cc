"""
Phone number validation examples for DevHQ
Demonstrates proper usage of the phone validation system
"""

from app.core.phone_validator import PhoneValidator, validate_phone, normalize_phone

def demo_phone_validation():
    """Demonstrate phone validation with various formats"""
    
    print("📱 DevHQ Phone Validation Demo")
    print("=" * 50)
    
    # Test cases for African markets (DevHQ's target audience)
    test_cases = [
        # Valid numbers
        ("+254712345678", "Kenya mobile"),
        ("+234 ************", "Nigeria mobile"),
        ("+233 24 123 4567", "Ghana mobile"),
        ("+27 82 123 4567", "South Africa mobile"),
        ("****** 123 4567", "US number"),
        ("+44 7700 900123", "UK mobile"),
        
        # Invalid numbers
        ("123", "Too short"),
        ("0712345678", "Missing country code"),
        ("+999 123 456 789", "Invalid country code"),
        ("", "Empty (should be valid - optional field)"),
        
        # Edge cases
        ("+254 712 345 678", "Kenya with spaces"),
        ("254712345678", "Kenya without +"),
    ]
    
    for phone, description in test_cases:
        is_valid, formatted, error = validate_phone(phone)
        
        status = "✅" if is_valid else "❌"
        result = formatted if formatted else error
        
        print(f"{status} {phone:<20} ({description:<25}) → {result}")
    
    print("\n" + "=" * 50)
    print("🌍 Supported African Countries:")
    print("  🇳🇬 Nigeria: +234")
    print("  🇰🇪 Kenya: +254") 
    print("  🇬🇭 Ghana: +233")
    print("  🇿🇦 South Africa: +27")
    print("  🇺🇬 Uganda: +256")
    print("  🇹🇿 Tanzania: +255")
    print("  🇷🇼 Rwanda: +250")
    
    print("\n💡 Usage Tips:")
    print("  - Always validate on both frontend (UX) and backend (security)")
    print("  - Store numbers in E.164 format (+254712345678)")
    print("  - Use formatted display for user interfaces")
    print("  - Phone field is optional in DevHQ registration")

if __name__ == "__main__":
    demo_phone_validation()