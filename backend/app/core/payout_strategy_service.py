"""
Payout strategy service for handling developer payments with Paystack + DPO combination
"""

import uuid
from dataclasses import dataclass
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

from app.config import get_settings
from app.models import Invoice, User


class PayoutMethod(Enum):
    """Available payout methods"""

    PAYSTACK_SUBACCOUNT = "paystack_subaccount"
    BANK_TRANSFER = "bank_transfer"
    MOBILE_MONEY = "mobile_money"
    MANUAL_PAYOUT = "manual_payout"


@dataclass
class PayoutOption:
    """Payout option with details"""

    method: PayoutMethod
    gateway: str
    currency: str
    fee_percentage: Decimal
    processing_time: str
    minimum_amount: Decimal
    supported_countries: List[str]
    description: str


@dataclass
class PayoutRecommendation:
    """Payout recommendation for a user"""

    recommended_method: PayoutMethod
    gateway: str
    reason: str
    estimated_fee: Decimal
    processing_time: str
    setup_required: bool
    setup_instructions: List[str]


class PayoutStrategyService:
    """Service for managing developer payouts with Paystack + DPO combination"""

    def __init__(self, db: Session):
        self.db = db
        self.settings = get_settings()

        # Define available payout options
        self.payout_options = [
            PayoutOption(
                method=PayoutMethod.PAYSTACK_SUBACCOUNT,
                gateway="paystack",
                currency="NGN",
                fee_percentage=Decimal("0.015"),  # 1.5% for subaccounts
                processing_time="Instant",
                minimum_amount=Decimal("100"),
                supported_countries=["NG", "GH", "KE", "ZA"],
                description="Automatic payouts via Paystack subaccounts (Nigeria, Ghana, Kenya, South Africa)",
            ),
            PayoutOption(
                method=PayoutMethod.BANK_TRANSFER,
                gateway="paystack",
                currency="NGN",
                fee_percentage=Decimal("0.025"),  # 2.5% for manual transfers
                processing_time="1-3 business days",
                minimum_amount=Decimal("1000"),
                supported_countries=["NG", "GH", "KE", "ZA"],
                description="Bank transfer via Paystack Transfer API",
            ),
            PayoutOption(
                method=PayoutMethod.BANK_TRANSFER,
                gateway="dpo",
                currency="ZAR",
                fee_percentage=Decimal("0.030"),  # 3.0% for DPO transfers
                processing_time="1-5 business days",
                minimum_amount=Decimal("100"),
                supported_countries=[
                    "ZA",
                    "KE",
                    "UG",
                    "TZ",
                    "ZM",
                    "BW",
                    "MW",
                    "RW",
                    "ET",
                    "MU",
                ],
                description="Bank transfer for DPO-supported countries",
            ),
            PayoutOption(
                method=PayoutMethod.MOBILE_MONEY,
                gateway="dpo",
                currency="KES",
                fee_percentage=Decimal("0.025"),  # 2.5% for mobile money
                processing_time="Instant",
                minimum_amount=Decimal("50"),
                supported_countries=["KE", "UG", "TZ", "GH", "RW", "ZM"],
                description="Mobile money payouts (M-Pesa, MTN Mobile Money, etc.)",
            ),
            PayoutOption(
                method=PayoutMethod.MANUAL_PAYOUT,
                gateway="manual",
                currency="USD",
                fee_percentage=Decimal("0.050"),  # 5.0% for manual processing
                processing_time="5-10 business days",
                minimum_amount=Decimal("100"),
                supported_countries=["*"],  # All countries
                description="Manual payout processing for unsupported regions",
            ),
        ]

    def get_payout_recommendation(
        self,
        user_id: uuid.UUID,
        amount: Decimal,
        currency: str,
        country: Optional[str] = None,
    ) -> PayoutRecommendation:
        """Get the best payout recommendation for a user"""

        # Get user's existing payout settings
        user = self.db.query(User).filter(User.id == user_id).first()
        user_country = country or getattr(user, "country", None)

        # Filter options by country and currency
        suitable_options = []
        for option in self.payout_options:
            if option.supported_countries == ["*"] or (
                user_country and user_country in option.supported_countries
            ):
                if amount >= option.minimum_amount:
                    suitable_options.append(option)

        if not suitable_options:
            # Fallback to manual payout
            manual_option = next(
                opt
                for opt in self.payout_options
                if opt.method == PayoutMethod.MANUAL_PAYOUT
            )
            return PayoutRecommendation(
                recommended_method=PayoutMethod.MANUAL_PAYOUT,
                gateway="manual",
                reason="No automatic payout options available for your location",
                estimated_fee=amount * manual_option.fee_percentage,
                processing_time=manual_option.processing_time,
                setup_required=True,
                setup_instructions=[
                    "Contact DevHQ support for manual payout setup",
                    "Provide bank account details",
                    "Complete identity verification",
                ],
            )

        # Prioritize options based on various factors
        def score_option(option: PayoutOption) -> float:
            score = 0.0

            # Lower fees are better
            score += (1 - float(option.fee_percentage)) * 40

            # Faster processing is better
            if "instant" in option.processing_time.lower():
                score += 30
            elif "1-3" in option.processing_time:
                score += 20
            elif "1-5" in option.processing_time:
                score += 10

            # Prefer automated methods
            if option.method == PayoutMethod.PAYSTACK_SUBACCOUNT:
                score += 25
            elif option.method == PayoutMethod.MOBILE_MONEY:
                score += 20
            elif option.method == PayoutMethod.BANK_TRANSFER:
                score += 15

            # Currency preference (local currency is better)
            if user_country:
                local_currencies = {
                    "NG": "NGN",
                    "GH": "GHS",
                    "KE": "KES",
                    "ZA": "ZAR",
                    "UG": "UGX",
                    "TZ": "TZS",
                    "RW": "RWF",
                    "ZM": "ZMW",
                }
                if option.currency == local_currencies.get(user_country):
                    score += 15

            return score

        # Get the best option
        best_option = max(suitable_options, key=score_option)

        # Generate setup instructions
        setup_instructions = self._get_setup_instructions(best_option, user_country)

        return PayoutRecommendation(
            recommended_method=best_option.method,
            gateway=best_option.gateway,
            reason=self._get_recommendation_reason(best_option, user_country),
            estimated_fee=amount * best_option.fee_percentage,
            processing_time=best_option.processing_time,
            setup_required=len(setup_instructions) > 0,
            setup_instructions=setup_instructions,
        )

    def _get_setup_instructions(
        self, option: PayoutOption, country: Optional[str]
    ) -> List[str]:
        """Get setup instructions for a payout option"""

        if option.method == PayoutMethod.PAYSTACK_SUBACCOUNT:
            return [
                "Complete KYC verification with Paystack",
                "Provide business registration documents",
                "Add bank account details in your local currency",
                "DevHQ will create a subaccount for automatic payouts",
            ]

        elif (
            option.method == PayoutMethod.BANK_TRANSFER and option.gateway == "paystack"
        ):
            return [
                "Add your bank account details in DevHQ settings",
                "Verify your bank account with small deposit",
                "Complete identity verification",
            ]

        elif option.method == PayoutMethod.BANK_TRANSFER and option.gateway == "dpo":
            return [
                "Provide bank account details for your country",
                "Complete identity verification",
                "Bank transfers processed manually by DevHQ team",
            ]

        elif option.method == PayoutMethod.MOBILE_MONEY:
            mobile_money_providers = {
                "KE": "M-Pesa",
                "UG": "MTN Mobile Money",
                "TZ": "Tigo Pesa or Airtel Money",
                "GH": "MTN Mobile Money",
                "RW": "MTN Mobile Money",
                "ZM": "MTN Mobile Money",
            }
            provider = mobile_money_providers.get(country, "Mobile Money")

            return [
                f"Add your {provider} phone number",
                "Verify your mobile money account",
                "Ensure your account can receive payments",
            ]

        return []

    def _get_recommendation_reason(
        self, option: PayoutOption, country: Optional[str]
    ) -> str:
        """Get human-readable reason for recommendation"""

        if option.method == PayoutMethod.PAYSTACK_SUBACCOUNT:
            return "Instant automatic payouts with lowest fees via Paystack subaccounts"

        elif option.method == PayoutMethod.MOBILE_MONEY:
            return "Fast and convenient mobile money payouts popular in your region"

        elif (
            option.method == PayoutMethod.BANK_TRANSFER and option.gateway == "paystack"
        ):
            return "Reliable bank transfers via Paystack with competitive fees"

        elif option.method == PayoutMethod.BANK_TRANSFER and option.gateway == "dpo":
            return "Bank transfers available in your country via DPO network"

        return "Best available option for your location and amount"

    def get_all_payout_options(
        self, country: Optional[str] = None
    ) -> List[PayoutOption]:
        """Get all available payout options for a country"""

        if not country:
            return self.payout_options

        return [
            option
            for option in self.payout_options
            if option.supported_countries == ["*"]
            or country in option.supported_countries
        ]

    def calculate_payout_fees(
        self,
        amount: Decimal,
        method: PayoutMethod,
        gateway: str,
        currency: str,
    ) -> Dict[str, Decimal]:
        """Calculate detailed payout fees"""

        # Find the matching option
        option = next(
            (
                opt
                for opt in self.payout_options
                if opt.method == method
                and opt.gateway == gateway
                and opt.currency == currency
            ),
            None,
        )

        if not option:
            # Default to 5% for unknown options
            gateway_fee = amount * Decimal("0.05")
        else:
            gateway_fee = amount * option.fee_percentage

        # Add any additional DevHQ processing fees
        devhq_processing_fee = Decimal("0")  # No additional fee for now

        total_fee = gateway_fee + devhq_processing_fee
        net_amount = amount - total_fee

        return {
            "gross_amount": amount,
            "gateway_fee": gateway_fee,
            "devhq_processing_fee": devhq_processing_fee,
            "total_fee": total_fee,
            "net_amount": net_amount,
            "fee_percentage": (
                (total_fee / amount) * 100 if amount > 0 else Decimal("0")
            ),
        }

    def get_payout_status_explanation(self) -> Dict[str, str]:
        """Get explanations for different payout methods"""

        return {
            "paystack_subaccount": """
                **Paystack Subaccounts (Recommended for NG, GH, KE, ZA)**
                
                • **How it works**: DevHQ creates a Paystack subaccount for you
                • **Payouts**: Automatic, instant after each payment
                • **Fees**: 1.5% (lowest available)
                • **Setup**: Complete KYC with Paystack
                • **Currencies**: NGN, GHS, KES, ZAR
                • **Best for**: High-volume developers in supported countries
            """,
            "bank_transfer": """
                **Bank Transfers**
                
                • **How it works**: DevHQ processes bank transfers weekly/monthly
                • **Payouts**: 1-5 business days depending on country
                • **Fees**: 2.5-3.0% depending on gateway
                • **Setup**: Provide bank account details
                • **Coverage**: Most African countries
                • **Best for**: Developers preferring traditional banking
            """,
            "mobile_money": """
                **Mobile Money (East Africa)**
                
                • **How it works**: Direct payouts to your mobile money account
                • **Payouts**: Instant (M-Pesa, MTN Mobile Money, etc.)
                • **Fees**: 2.5%
                • **Setup**: Verify mobile money account
                • **Coverage**: KE, UG, TZ, GH, RW, ZM
                • **Best for**: Developers preferring mobile payments
            """,
            "manual_payout": """
                **Manual Payouts (Global Fallback)**
                
                • **How it works**: DevHQ team processes manually
                • **Payouts**: 5-10 business days
                • **Fees**: 5.0% (includes processing costs)
                • **Setup**: Contact support with bank details
                • **Coverage**: Worldwide
                • **Best for**: Developers in unsupported regions
            """,
        }

    def get_country_payout_summary(self) -> Dict[str, Dict]:
        """Get payout options summary by country"""

        return {
            "NG": {
                "primary": "Paystack Subaccount (NGN) - Instant, 1.5%",
                "secondary": "Bank Transfer (NGN) - 1-3 days, 2.5%",
                "mobile_money": "Not available",
                "recommendation": "Setup Paystack subaccount for best experience",
            },
            "GH": {
                "primary": "Paystack Subaccount (GHS) - Instant, 1.5%",
                "secondary": "Bank Transfer (GHS) - 1-3 days, 2.5%",
                "mobile_money": "MTN Mobile Money - Instant, 2.5%",
                "recommendation": "Paystack subaccount or mobile money",
            },
            "KE": {
                "primary": "M-Pesa - Instant, 2.5%",
                "secondary": "Paystack Subaccount (KES) - Instant, 1.5%",
                "bank_transfer": "Bank Transfer - 1-5 days, 3.0%",
                "recommendation": "M-Pesa for convenience, Paystack for lower fees",
            },
            "ZA": {
                "primary": "Paystack Subaccount (ZAR) - Instant, 1.5%",
                "secondary": "Bank Transfer (ZAR) - 1-3 days, 2.5%",
                "mobile_money": "Not available",
                "recommendation": "Paystack subaccount recommended",
            },
            "UG": {
                "primary": "MTN Mobile Money - Instant, 2.5%",
                "secondary": "Bank Transfer - 1-5 days, 3.0%",
                "paystack": "Not available",
                "recommendation": "Mobile money for fast payouts",
            },
            "TZ": {
                "primary": "Tigo Pesa/Airtel Money - Instant, 2.5%",
                "secondary": "Bank Transfer - 1-5 days, 3.0%",
                "paystack": "Not available",
                "recommendation": "Mobile money preferred",
            },
            "other": {
                "primary": "Manual Payout - 5-10 days, 5.0%",
                "secondary": "Contact support for options",
                "recommendation": "Manual processing available globally",
            },
        }
