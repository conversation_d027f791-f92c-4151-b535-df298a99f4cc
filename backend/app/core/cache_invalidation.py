"""Advanced Cache Invalidation Service for DevHQ Backend
Provides pattern-based cache invalidation and dependency tracking"""

import asyncio
import logging
from typing import Dict, List, Optional, Set

from app.core.cache import cache_manager

logger = logging.getLogger(__name__)


class CacheInvalidationService:
    """Advanced cache invalidation service with pattern matching and dependency tracking"""

    def __init__(self):
        self.dependency_map: Dict[str, Set[str]] = {}
        self.pattern_map: Dict[str, Set[str]] = {}

    def register_dependency(self, parent_key: str, dependent_key: str):
        """Register a cache dependency relationship

        Args:
            parent_key (str): The parent cache key
            dependent_key (str): The dependent cache key that should be invalidated when parent changes
        """
        if parent_key not in self.dependency_map:
            self.dependency_map[parent_key] = set()
        self.dependency_map[parent_key].add(dependent_key)

    def register_pattern(self, pattern: str, cache_key: str):
        """Register a cache key with a pattern for bulk invalidation

        Args:
            pattern (str): The pattern (e.g., 'user:*', 'project:*')
            cache_key (str): The actual cache key
        """
        if pattern not in self.pattern_map:
            self.pattern_map[pattern] = set()
        self.pattern_map[pattern].add(cache_key)

    async def invalidate_key(self, key: str) -> bool:
        """Invalidate a single cache key and its dependencies

        Args:
            key (str): Cache key to invalidate

        Returns:
            bool: True if successful
        """
        try:
            # Invalidate the key itself
            await cache_manager.delete(key)

            # Invalidate dependent keys
            if key in self.dependency_map:
                for dependent_key in self.dependency_map[key]:
                    await cache_manager.delete(dependent_key)
                    logger.debug(f"Invalidated dependent key: {dependent_key}")

            return True
        except Exception as e:
            logger.error(f"Failed to invalidate cache key {key}: {e}")
            return False

    async def invalidate_pattern(self, pattern: str) -> bool:
        """Invalidate all cache keys matching a pattern

        Args:
            pattern (str): Pattern to match (e.g., 'user:123:*')

        Returns:
            bool: True if successful
        """
        try:
            # Get all keys matching the pattern
            if not cache_manager.connected or not cache_manager.redis_client:
                return False

            keys = await cache_manager.redis_client.keys(pattern)
            if keys:
                await cache_manager.redis_client.delete(*keys)
                logger.info(f"Invalidated {len(keys)} keys matching pattern: {pattern}")

            return True
        except Exception as e:
            logger.error(f"Failed to invalidate pattern {pattern}: {e}")
            return False

    async def invalidate_user_cache(self, user_id: str) -> bool:
        """Invalidate all cache entries for a specific user

        Args:
            user_id (str): User ID

        Returns:
            bool: True if successful
        """
        patterns = [
            f"user:profile:{user_id}",
            f"user:settings:{user_id}",
            f"user:projects:{user_id}*",
            f"user:dashboard:{user_id}*",
            f"user:stats:{user_id}*",
        ]

        results = []
        for pattern in patterns:
            if "*" in pattern:
                result = await self.invalidate_pattern(pattern)
            else:
                result = await self.invalidate_key(pattern)
            results.append(result)

        return all(results)

    async def invalidate_project_cache(
        self, project_id: str, user_id: Optional[str] = None
    ) -> bool:
        """Invalidate all cache entries for a specific project

        Args:
            project_id (str): Project ID
            user_id (str, optional): User ID to also invalidate user-specific caches

        Returns:
            bool: True if successful
        """
        patterns = [
            f"project:{project_id}*",
            f"project:details:{project_id}",
            f"project:milestones:{project_id}*",
            f"project:stats:{project_id}*",
        ]

        # Also invalidate user-specific project caches
        if user_id:
            patterns.extend(
                [
                    f"user:projects:{user_id}*",
                    f"user:dashboard:{user_id}*",
                ]
            )

        results = []
        for pattern in patterns:
            if "*" in pattern:
                result = await self.invalidate_pattern(pattern)
            else:
                result = await self.invalidate_key(pattern)
            results.append(result)

        return all(results)

    async def invalidate_client_cache(
        self, client_id: str, user_id: Optional[str] = None
    ) -> bool:
        """Invalidate all cache entries for a specific client

        Args:
            client_id (str): Client ID
            user_id (str, optional): User ID to also invalidate user-specific caches

        Returns:
            bool: True if successful
        """
        patterns = [
            f"client:{client_id}*",
            f"client:projects:{client_id}*",
        ]

        # Also invalidate user-specific caches
        if user_id:
            patterns.extend(
                [
                    f"user:clients:{user_id}*",
                    f"user:dashboard:{user_id}*",
                ]
            )

        results = []
        for pattern in patterns:
            if "*" in pattern:
                result = await self.invalidate_pattern(pattern)
            else:
                result = await self.invalidate_key(pattern)
            results.append(result)

        return all(results)

    async def bulk_invalidate(self, keys: List[str]) -> Dict[str, bool]:
        """Invalidate multiple cache keys in bulk

        Args:
            keys (List[str]): List of cache keys to invalidate

        Returns:
            Dict[str, bool]: Results for each key
        """
        results = {}

        # Process in batches to avoid overwhelming Redis
        batch_size = 50
        for i in range(0, len(keys), batch_size):
            batch = keys[i : i + batch_size]
            batch_results = await asyncio.gather(
                *[self.invalidate_key(key) for key in batch], return_exceptions=True
            )

            for key, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    results[key] = False
                    logger.error(f"Failed to invalidate {key}: {result}")
                else:
                    results[key] = result

        return results

    def get_dependency_stats(self) -> Dict[str, int]:
        """Get statistics about cache dependencies

        Returns:
            Dict[str, int]: Dependency statistics
        """
        return {
            "total_dependencies": len(self.dependency_map),
            "total_patterns": len(self.pattern_map),
            "avg_dependencies_per_key": (
                sum(len(deps) for deps in self.dependency_map.values())
                / len(self.dependency_map)
                if self.dependency_map
                else 0
            ),
        }


# Global cache invalidation service instance
cache_invalidation_service = CacheInvalidationService()


# Convenience functions for common invalidation patterns
async def invalidate_user_cache(user_id: str) -> bool:
    """Invalidate all cache entries for a user"""
    return await cache_invalidation_service.invalidate_user_cache(user_id)


async def invalidate_project_cache(
    project_id: str, user_id: Optional[str] = None
) -> bool:
    """Invalidate all cache entries for a project"""
    return await cache_invalidation_service.invalidate_project_cache(
        project_id, user_id
    )


async def invalidate_client_cache(
    client_id: str, user_id: Optional[str] = None
) -> bool:
    """Invalidate all cache entries for a client"""
    return await cache_invalidation_service.invalidate_client_cache(client_id, user_id)
