"""
Approval service for intelligent approval workflows and client collaboration
"""

import secrets
import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import Session, joinedload

from app.models import (ApprovalActivity, Client, ClientApproval,
                        ClientFeedback, Invoice, Project, ProjectMilestone,
                        TimeEntry)
from app.schemas.approval import (ApprovalAnalyticsResponse, ApprovalCreate,
                                  ApprovalSuggestionResponse,
                                  ApprovalSuggestionsResponse,
                                  ClientPortalDashboardResponse)


class ApprovalService:
    """
    Comprehensive approval service for managing client approval workflows
    """

    def __init__(self, db: Session):
        self.db = db

    def auto_detect_approvables(
        self, project_id: UUID, user_id: UUID
    ) -> ApprovalSuggestionsResponse:
        """
        Intelligently suggest items ready for approval based on project state
        """
        suggestions = []

        # Get project details
        project = (
            self.db.query(Project)
            .filter(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.deleted_at.is_(None),
            )
            .first()
        )

        if not project:
            return ApprovalSuggestionsResponse(
                suggestions=[],
                total_suggestions=0,
                high_confidence_count=0,
                ready_for_approval_count=0,
            )

        # 1. Detect completed milestones without approval
        completed_milestones = (
            self.db.query(ProjectMilestone)
            .filter(
                ProjectMilestone.project_id == project_id,
                ProjectMilestone.status == "completed",
                ProjectMilestone.deleted_at.is_(None),
            )
            .all()
        )

        for milestone in completed_milestones:
            # Check if already has pending/approved approval
            existing_approval = (
                self.db.query(ClientApproval)
                .filter(
                    ClientApproval.approvable_id == milestone.id,
                    ClientApproval.approvable_type == "milestone",
                    ClientApproval.status.in_(["pending", "approved"]),
                )
                .first()
            )

            if not existing_approval:
                confidence = 0.9 if milestone.completed_at else 0.7
                suggestions.append(
                    ApprovalSuggestionResponse(
                        approvable_id=milestone.id,
                        approvable_type="milestone",
                        suggested_title=f"Milestone Approval: {milestone.title}",
                        suggested_description=f"Please review and approve the completed milestone: {milestone.title}. {milestone.description or ''}",
                        suggested_priority=(
                            "high" if milestone.payment_amount else "medium"
                        ),
                        confidence_score=confidence,
                        reasoning="Milestone marked as completed and ready for client review",
                        project_id=project_id,
                        client_id=project.client_id,
                    )
                )

        # 2. Detect invoices ready for approval
        draft_invoices = (
            self.db.query(Invoice)
            .filter(
                Invoice.project_id == project_id,
                Invoice.status == "draft",
                Invoice.deleted_at.is_(None),
            )
            .all()
        )

        for invoice in draft_invoices:
            # Check if has items and ready for sending
            if invoice.items and invoice.total_amount > 0:
                existing_approval = (
                    self.db.query(ClientApproval)
                    .filter(
                        ClientApproval.approvable_id == invoice.id,
                        ClientApproval.approvable_type == "invoice",
                        ClientApproval.status.in_(["pending", "approved"]),
                    )
                    .first()
                )

                if not existing_approval:
                    suggestions.append(
                        ApprovalSuggestionResponse(
                            approvable_id=invoice.id,
                            approvable_type="invoice",
                            suggested_title=f"Invoice Approval: {invoice.invoice_number}",
                            suggested_description=f"Please review and approve invoice {invoice.invoice_number} for ${invoice.total_amount:.2f}",
                            suggested_priority="high",
                            confidence_score=0.85,
                            reasoning="Invoice is complete with line items and ready for client approval",
                            project_id=project_id,
                            client_id=project.client_id,
                        )
                    )

        # 3. Detect significant time entries that might need approval
        recent_time_entries = (
            self.db.query(TimeEntry)
            .filter(
                TimeEntry.project_id == project_id,
                TimeEntry.status == "submitted",
                TimeEntry.work_date >= datetime.now(timezone.utc) - timedelta(days=7),
                TimeEntry.deleted_at.is_(None),
            )
            .all()
        )

        # Group by week and suggest approval for significant work
        if recent_time_entries:
            total_hours = sum(
                entry.duration_minutes / 60 for entry in recent_time_entries
            )
            total_amount = sum(
                entry.billable_amount or 0 for entry in recent_time_entries
            )

            if total_hours >= 8 or total_amount >= 500:  # Significant work threshold
                # Check if already has approval for this period
                existing_approval = (
                    self.db.query(ClientApproval)
                    .filter(
                        ClientApproval.project_id == project_id,
                        ClientApproval.approvable_type == "time_entries",
                        ClientApproval.status.in_(["pending", "approved"]),
                        ClientApproval.created_at
                        >= datetime.now(timezone.utc) - timedelta(days=7),
                    )
                    .first()
                )

                if not existing_approval:
                    suggestions.append(
                        ApprovalSuggestionResponse(
                            approvable_id=uuid.uuid4(),  # Virtual ID for time entry group
                            approvable_type="time_entries",
                            suggested_title=f"Weekly Work Approval: {total_hours:.1f} hours",
                            suggested_description=f"Please review and approve {total_hours:.1f} hours of work completed this week (${total_amount:.2f})",
                            suggested_priority="medium",
                            confidence_score=0.75,
                            reasoning=f"Significant work completed: {total_hours:.1f} hours worth ${total_amount:.2f}",
                            project_id=project_id,
                            client_id=project.client_id,
                        )
                    )

        # Calculate summary statistics
        high_confidence_count = sum(1 for s in suggestions if s.confidence_score >= 0.8)
        ready_for_approval_count = len(suggestions)

        return ApprovalSuggestionsResponse(
            suggestions=suggestions,
            total_suggestions=len(suggestions),
            high_confidence_count=high_confidence_count,
            ready_for_approval_count=ready_for_approval_count,
        )

    def create_approval_request(
        self, approval_data: ApprovalCreate, user_id: UUID
    ) -> ClientApproval:
        """
        Create formal approval request with smart defaults and security token
        """
        # Generate secure access token
        approval_token = secrets.token_urlsafe(32)

        # Create approval record
        approval = ClientApproval(
            title=approval_data.title,
            description=approval_data.description,
            approvable_id=approval_data.approvable_id,
            approvable_type=approval_data.approvable_type,
            client_id=approval_data.client_id,
            project_id=approval_data.project_id,
            user_id=user_id,
            priority=approval_data.priority,
            due_date=approval_data.due_date,
            approval_token=approval_token,
            client_instructions=approval_data.client_instructions,
            context_data=approval_data.context_data,
            requested_at=datetime.now(timezone.utc),
        )

        self.db.add(approval)
        self.db.flush()  # Get ID for activity log

        # Create initial activity log
        activity = ApprovalActivity(
            approval_id=approval.id,
            action="created",
            actor_type="developer",
            actor_name="Developer",  # TODO: Get actual user name
            details={
                "approvable_type": approval_data.approvable_type,
                "priority": approval_data.priority,
            },
            timestamp=datetime.now(timezone.utc),
        )
        self.db.add(activity)
        self.db.commit()

        return approval

    def get_approval_by_token(
        self, token: str, approval_id: UUID
    ) -> Optional[ClientApproval]:
        """
        Get approval by secure token for client portal access
        """
        return (
            self.db.query(ClientApproval)
            .options(
                joinedload(ClientApproval.client),
                joinedload(ClientApproval.project),
                joinedload(ClientApproval.feedback_items),
                joinedload(ClientApproval.activity_logs),
            )
            .filter(
                ClientApproval.id == approval_id,
                ClientApproval.approval_token == token,
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

    def get_client_portal_dashboard(
        self, client_id: UUID
    ) -> ClientPortalDashboardResponse:
        """
        Generate personalized approval dashboard for client portal
        """
        # Get pending approvals for client
        pending_approvals = (
            self.db.query(ClientApproval)
            .options(joinedload(ClientApproval.project))
            .filter(
                ClientApproval.client_id == client_id,
                ClientApproval.status == "pending",
                ClientApproval.deleted_at.is_(None),
            )
            .order_by(
                desc(ClientApproval.priority == "urgent"),
                desc(ClientApproval.priority == "high"),
                ClientApproval.requested_at,
            )
            .all()
        )

        # Get recent activity
        recent_activity = (
            self.db.query(ApprovalActivity)
            .join(ClientApproval)
            .filter(
                ClientApproval.client_id == client_id,
                ApprovalActivity.timestamp
                >= datetime.now(timezone.utc) - timedelta(days=30),
            )
            .order_by(desc(ApprovalActivity.timestamp))
            .limit(10)
            .all()
        )

        # Calculate statistics
        total_pending = len(pending_approvals)
        overdue_count = sum(1 for approval in pending_approvals if approval.is_overdue)
        urgent_count = sum(
            1 for approval in pending_approvals if approval.priority == "urgent"
        )

        # Get client info
        client = self.db.query(Client).filter(Client.id == client_id).first()
        client_name = client.name if client else "Client"

        # Group approvals by project
        projects_with_approvals = {}
        for approval in pending_approvals:
            project_id = str(approval.project_id)
            if project_id not in projects_with_approvals:
                projects_with_approvals[project_id] = {
                    "project_id": project_id,
                    "project_title": approval.project.title,
                    "approval_count": 0,
                    "urgent_count": 0,
                    "overdue_count": 0,
                }

            projects_with_approvals[project_id]["approval_count"] += 1
            if approval.priority == "urgent":
                projects_with_approvals[project_id]["urgent_count"] += 1
            if approval.is_overdue:
                projects_with_approvals[project_id]["overdue_count"] += 1

        return ClientPortalDashboardResponse(
            pending_approvals=[],  # Will be populated by the API endpoint
            recent_activity=[],  # Will be populated by the API endpoint
            total_pending=total_pending,
            overdue_count=overdue_count,
            urgent_count=urgent_count,
            client_name=client_name,
            projects_with_approvals=list(projects_with_approvals.values()),
        )

    def process_client_decision(
        self,
        approval_id: UUID,
        decision: str,
        client_name: str,
        client_email: str,
        feedback: Optional[str] = None,
        ip_address: Optional[str] = None,
    ) -> ClientApproval:
        """
        Handle client approval decisions with full audit trail
        """
        # Validate decision first
        if decision not in ["approve", "revise"]:
            raise ValueError("Invalid decision. Must be 'approve' or 'revise'")

        approval = (
            self.db.query(ClientApproval)
            .filter(
                ClientApproval.id == approval_id,
                ClientApproval.status == "pending",
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

        if not approval:
            raise ValueError("Approval not found or not in pending status")

        # Mark as viewed if not already
        approval.mark_as_viewed()

        if decision == "approve":
            approval.approve(client_name, client_email, feedback)
        elif decision == "revise":
            if not feedback:
                raise ValueError("Feedback is required for revision requests")
            approval.request_revision(client_name, client_email, feedback)

        # Add IP address to latest activity if provided
        if ip_address and approval.activity_logs:
            approval.activity_logs[-1].ip_address = ip_address

        self.db.commit()
        return approval

    def generate_approval_analytics(self, user_id: UUID) -> ApprovalAnalyticsResponse:
        """
        Provide actionable approval analytics for developer insights
        """
        # Base query for user's approvals
        base_query = self.db.query(ClientApproval).filter(
            ClientApproval.user_id == user_id, ClientApproval.deleted_at.is_(None)
        )

        # Basic counts
        total_approvals = base_query.count()
        pending_approvals = base_query.filter(
            ClientApproval.status == "pending"
        ).count()
        approved_approvals = base_query.filter(
            ClientApproval.status == "approved"
        ).count()
        revision_requests = base_query.filter(
            ClientApproval.status == "revision_requested"
        ).count()
        overdue_approvals = base_query.filter(
            ClientApproval.status == "pending",
            ClientApproval.due_date < datetime.now(timezone.utc),
        ).count()

        # Calculate approval rate
        completed_approvals = approved_approvals + revision_requests
        approval_rate = (
            (approved_approvals / completed_approvals * 100)
            if completed_approvals > 0
            else 0
        )

        # Calculate average response time for approved items
        approved_items = base_query.filter(
            ClientApproval.status == "approved", ClientApproval.approved_at.isnot(None)
        ).all()

        total_response_time = 0
        response_count = 0
        for approval in approved_items:
            if approval.approved_at and approval.requested_at:
                response_time = approval.approved_at - approval.requested_at
                total_response_time += (
                    response_time.total_seconds() / 3600
                )  # Convert to hours
                response_count += 1

        avg_response_time = (
            total_response_time / response_count if response_count > 0 else 0
        )

        # Client response patterns (placeholder for more complex analysis)
        client_patterns = {
            "fastest_responder": "Client A",  # TODO: Implement actual analysis
            "average_response_time_by_client": {},
            "most_collaborative_client": "Client B",
        }

        # Approval trends (last 30 days)
        trends = []
        for i in range(30):
            date = datetime.now(timezone.utc) - timedelta(days=i)
            daily_approvals = base_query.filter(
                func.date(ClientApproval.requested_at) == date.date()
            ).count()
            trends.append(
                {
                    "date": date.date().isoformat(),
                    "approvals_requested": daily_approvals,
                }
            )

        # Top revision categories (placeholder)
        revision_categories = [
            {"category": "Design Changes", "count": 5},
            {"category": "Functionality", "count": 3},
            {"category": "Content Updates", "count": 2},
        ]

        # Project velocity impact (placeholder)
        velocity_impact = {
            "average_delay_days": 2.5,
            "projects_affected": 3,
            "efficiency_score": 85.5,
        }

        return ApprovalAnalyticsResponse(
            total_approvals=total_approvals,
            pending_approvals=pending_approvals,
            approved_approvals=approved_approvals,
            revision_requests=revision_requests,
            average_response_time_hours=avg_response_time,
            overdue_approvals=overdue_approvals,
            approval_rate_percentage=approval_rate,
            client_response_patterns=client_patterns,
            approval_trends=trends,
            top_revision_categories=revision_categories,
            project_velocity_impact=velocity_impact,
        )

    def send_approval_reminder(
        self, approval_id: UUID, user_id: UUID, custom_message: Optional[str] = None
    ) -> bool:
        """
        Send reminder to client for pending approval
        """
        approval = (
            self.db.query(ClientApproval)
            .filter(
                ClientApproval.id == approval_id,
                ClientApproval.user_id == user_id,
                ClientApproval.status == "pending",
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

        if not approval:
            return False

        # Create activity log for reminder
        activity = ApprovalActivity(
            approval_id=approval.id,
            action="reminder_sent",
            actor_type="developer",
            actor_name="Developer",  # TODO: Get actual user name
            details={
                "custom_message": custom_message,
                "reminder_count": self._get_reminder_count(approval_id) + 1,
            },
            timestamp=datetime.now(timezone.utc),
        )
        self.db.add(activity)
        self.db.commit()

        # TODO: Integrate with email service to send actual reminder
        return True

    def _get_reminder_count(self, approval_id: UUID) -> int:
        """Get count of reminders sent for an approval"""
        return (
            self.db.query(ApprovalActivity)
            .filter(
                ApprovalActivity.approval_id == approval_id,
                ApprovalActivity.action == "reminder_sent",
            )
            .count()
        )

    def get_overdue_approvals(self, user_id: UUID) -> List[ClientApproval]:
        """Get all overdue approvals for a user"""
        return (
            self.db.query(ClientApproval)
            .options(
                joinedload(ClientApproval.client), joinedload(ClientApproval.project)
            )
            .filter(
                ClientApproval.user_id == user_id,
                ClientApproval.status == "pending",
                ClientApproval.due_date < datetime.now(timezone.utc),
                ClientApproval.deleted_at.is_(None),
            )
            .order_by(ClientApproval.due_date)
            .all()
        )

    def bulk_send_reminders(
        self,
        approval_ids: List[UUID],
        user_id: UUID,
        custom_message: Optional[str] = None,
    ) -> Dict[str, int]:
        """
        Send reminders for multiple approvals
        """
        success_count = 0
        failed_count = 0

        for approval_id in approval_ids:
            try:
                if self.send_approval_reminder(approval_id, user_id, custom_message):
                    success_count += 1
                else:
                    failed_count += 1
            except Exception:
                failed_count += 1

        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_processed": len(approval_ids),
        }
