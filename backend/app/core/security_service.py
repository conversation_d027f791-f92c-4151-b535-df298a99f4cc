"""
Security service for enhanced authentication and authorization
"""

import hashlib
import secrets
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional, Union
from urllib.parse import urlparse

import bcrypt
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from app.config import settings
from app.core.exceptions import AuthenticationError
from app.models.user import User, UserSession


class SecurityService:
    """Enhanced security service for authentication and authorization"""

    def __init__(self, db: Session):
        self.db = db
        self.failed_login_attempts = {}  # In-memory tracking (use Redis in production)
        self.lockout_duration = 900  # 15 minutes lockout
        self.max_attempts = 5  # Max failed attempts before lockout

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt with proper configuration"""
        # Generate salt with appropriate rounds (12 is recommended for balance of security/performance)
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode("utf-8"), salt)
        return hashed.decode("utf-8")

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        try:
            return bcrypt.checkpw(
                plain_password.encode("utf-8"), hashed_password.encode("utf-8")
            )
        except Exception:
            return False

    def is_account_locked(self, identifier: str) -> bool:
        """Check if account is locked due to failed login attempts"""
        if identifier not in self.failed_login_attempts:
            return False

        attempts, last_attempt = self.failed_login_attempts[identifier]

        # Check if lockout period has expired
        if time.time() - last_attempt > self.lockout_duration:
            # Clear expired lockout
            del self.failed_login_attempts[identifier]
            return False

        return attempts >= self.max_attempts

    def record_failed_login(self, identifier: str) -> bool:
        """Record failed login attempt and return if account should be locked"""
        current_time = time.time()

        if identifier in self.failed_login_attempts:
            attempts, last_attempt = self.failed_login_attempts[identifier]
            # Reset counter if lockout period has expired
            if current_time - last_attempt > self.lockout_duration:
                attempts = 1
            else:
                attempts += 1
        else:
            attempts = 1

        self.failed_login_attempts[identifier] = (attempts, current_time)
        return attempts >= self.max_attempts

    def reset_failed_login_attempts(self, identifier: str) -> None:
        """Reset failed login attempts for identifier"""
        if identifier in self.failed_login_attempts:
            del self.failed_login_attempts[identifier]

    def create_secure_token(self, length: int = 32) -> str:
        """Create cryptographically secure random token"""
        return secrets.token_urlsafe(length)

    def create_secure_pin(self, length: int = 6) -> str:
        """Create secure numeric PIN"""
        return "".join(secrets.choice("0123456789") for _ in range(length))

    def verify_totp_token(self, secret: str, token: str, window: int = 1) -> bool:
        """Verify TOTP token (simplified implementation)"""
        # This is a simplified TOTP implementation
        # In production, use pyotp or similar library
        import hmac
        import struct
        import time

        # Get current time step
        time_step = int(time.time()) // 30

        for i in range(-window, window + 1):
            # Generate HMAC
            counter = struct.pack(">Q", time_step + i)
            hmac_digest = hmac.new(
                secret.encode("utf-8"), counter, hashlib.sha1
            ).digest()

            # Extract 4 bytes starting at offset
            offset = hmac_digest[-1] & 0x0F
            truncated = struct.unpack(">I", hmac_digest[offset : offset + 4])[0]
            truncated &= 0x7FFFFFFF
            truncated %= 1000000

            # Compare with token
            if f"{truncated:06d}" == token:
                return True

        return False

    def validate_password_strength(self, password: str) -> Dict[str, Union[bool, str]]:
        """
        Validate password strength according to security best practices

        Returns:
            Dict with validation results and feedback
        """
        issues = []

        # Length check
        if len(password) < 8:
            issues.append("Password must be at least 8 characters long")

        # Character variety checks
        if not any(c.islower() for c in password):
            issues.append("Password must contain at least one lowercase letter")

        if not any(c.isupper() for c in password):
            issues.append("Password must contain at least one uppercase letter")

        if not any(c.isdigit() for c in password):
            issues.append("Password must contain at least one digit")

        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("Password must contain at least one special character")

        # Common password checks (simplified)
        common_passwords = ["password", "12345678", "qwerty", "admin", "welcome"]
        if password.lower() in common_passwords:
            issues.append("Password is too common")

        # Repeated characters
        if any(password.count(c) > 3 for c in set(password)):
            issues.append("Password contains too many repeated characters")

        is_valid = len(issues) == 0
        feedback = "Password is strong" if is_valid else "; ".join(issues)

        return {
            "valid": is_valid,
            "feedback": feedback,
            "strength_score": max(0, 100 - (len(issues) * 20)),
        }

    def validate_email_format(self, email: str) -> bool:
        """Validate email format"""
        import re

        # RFC 5322 compliant regex (simplified)
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        return bool(re.match(pattern, email))

    def sanitize_input(self, input_str: str, max_length: int = 1000) -> str:
        """Sanitize user input to prevent XSS and injection attacks"""
        if not input_str:
            return ""

        # Truncate to max length
        if len(input_str) > max_length:
            input_str = input_str[:max_length]

        # Remove potentially dangerous characters
        # This is a basic sanitization - use proper libraries like bleach in production
        dangerous_chars = [
            "<",
            ">",
            "&lt;",
            "&gt;",
            "javascript:",
            "vbscript:",
            "script",
        ]
        for char in dangerous_chars:
            input_str = input_str.replace(char, "")

        return input_str.strip()

    def generate_csrf_token(self) -> str:
        """Generate CSRF protection token"""
        return self.create_secure_token(32)

    def verify_csrf_token(self, token: str, expected: str) -> bool:
        """Verify CSRF token"""
        # Use constant time comparison to prevent timing attacks
        return secrets.compare_digest(token, expected)

    def is_valid_origin(self, origin: str, allowed_origins: list) -> bool:
        """Validate request origin against allowed origins"""
        if not origin:
            return False

        try:
            parsed_origin = urlparse(origin)

            # Check if origin matches any allowed origins
            for allowed in allowed_origins:
                if allowed == "*":
                    return True

                try:
                    parsed_allowed = urlparse(allowed)
                    if (
                        parsed_origin.scheme == parsed_allowed.scheme
                        and parsed_origin.hostname == parsed_allowed.hostname
                        and parsed_origin.port == parsed_allowed.port
                    ):
                        return True
                except Exception:
                    continue

            return False
        except Exception:
            return False

    def rotate_refresh_token(self, user_id: str, old_token: str) -> tuple[str, str]:
        """
        Rotate refresh token for enhanced security

        Returns:
            Tuple of (new_refresh_token, new_access_token)
        """
        # Invalidate old session
        old_session = (
            self.db.query(UserSession)
            .filter(
                UserSession.refresh_token == old_token,
                UserSession.user_id == user_id,
                UserSession.is_active == True,
            )
            .first()
        )

        if old_session:
            old_session.revoke()
            self.db.commit()

        # Create new tokens (implementation would depend on auth service)
        # This is a simplified example
        new_refresh_token = self.create_secure_token(32)
        new_access_token = self.create_secure_token(32)  # Simplified

        return new_refresh_token, new_access_token


# Global security service instance
def get_security_service(db: Session) -> SecurityService:
    """Get security service instance"""
    return SecurityService(db)
