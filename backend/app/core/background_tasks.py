"""
Background Task Manager for DevHQ Backend
Handles asynchronous background tasks with retry logic and monitoring
"""

import asyncio
import functools
import logging
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum, IntEnum
from typing import Any, Callable, Dict, List, Optional

logger = logging.getLogger(__name__)


class TaskStatus(str, Enum):
    """Background task status"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


class TaskPriority(IntEnum):
    """Background task priority levels"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class BackgroundTask:
    """Background task with metadata"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    func: Callable = None
    args: tuple = ()
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    error: Optional[str] = None
    result: Any = None


class BackgroundTaskManager:
    """Manages background tasks with priority queues and retry logic"""

    def __init__(self, max_workers: int = 4, max_queue_size: int = 1000):
        """
        Initialize BackgroundTaskManager

        Args:
            max_workers (int): Maximum number of concurrent workers
            max_queue_size (int): Maximum queue size before rejecting new tasks
        """
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        self.workers = []
        self.running = False

        # Priority queues for different task priorities
        self.queues = {
            TaskPriority.LOW: asyncio.PriorityQueue(),
            TaskPriority.NORMAL: asyncio.PriorityQueue(),
            TaskPriority.HIGH: asyncio.PriorityQueue(),
            TaskPriority.CRITICAL: asyncio.PriorityQueue(),
        }

        # Task registry for tracking
        self.tasks: Dict[str, BackgroundTask] = {}
        self.stats = {
            "processed": 0,
            "failed": 0,
            "retried": 0,
        }

    async def start(self):
        """Start the background task manager"""
        if self.running:
            return

        self.running = True

        # Start worker tasks
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)

        logger.info(f"Background task manager started with {self.max_workers} workers")

    async def stop(self):
        """Stop the background task manager"""
        self.running = False

        # Cancel all workers
        for worker in self.workers:
            if not worker.done():
                worker.cancel()

        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()

        logger.info("Background task manager stopped")

    async def submit_task(
        self,
        func: Callable,
        *args,
        name: str = "",
        priority: TaskPriority = TaskPriority.NORMAL,
        max_retries: int = 3,
        **kwargs,
    ) -> str:
        """
        Submit a task for background execution

        Args:
            func (Callable): Function to execute
            *args: Positional arguments for the function
            name (str): Task name for identification
            priority (TaskPriority): Task priority level
            max_retries (int): Maximum number of retry attempts
            **kwargs: Keyword arguments for the function

        Returns:
            str: Task ID

        Raises:
            asyncio.QueueFull: If task queue is full
        """
        # Create task
        task = BackgroundTask(
            name=name or func.__name__,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            max_retries=max_retries,
        )

        # Store task in registry
        self.tasks[task.id] = task

        # Add to appropriate priority queue
        # Lower priority number means higher priority in PriorityQueue
        priority_value = -priority.value  # Negative for correct priority ordering
        await self.queues[priority].put((priority_value, task.id))

        logger.info(
            f"Task {task.id} ({task.name}) submitted with priority {priority.name}"
        )
        return task.id

    async def _worker(self, worker_id: str):
        """Background worker that processes tasks"""
        logger.info(f"Worker {worker_id} started")

        while self.running:
            task = None
            try:
                # Try queues in priority order (critical -> high -> normal -> low)
                for priority in [
                    TaskPriority.CRITICAL,
                    TaskPriority.HIGH,
                    TaskPriority.NORMAL,
                    TaskPriority.LOW,
                ]:
                    try:
                        # Non-blocking get
                        priority_value, task_id = self.queues[priority].get_nowait()
                        task = self.tasks.get(task_id)
                        if task:
                            break
                    except asyncio.QueueEmpty:
                        continue

                # If no task found, wait a bit
                if not task:
                    await asyncio.sleep(0.1)
                    continue

                # Process the task
                await self._process_task(task)

            except asyncio.CancelledError:
                logger.info(f"Worker {worker_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
                if task:
                    task.status = TaskStatus.FAILED
                    task.error = str(e)
                    task.failed_at = datetime.now(timezone.utc)
                    self.stats["failed"] += 1

    async def _process_task(self, task: BackgroundTask):
        """Process a single task"""
        try:
            task.status = TaskStatus.PROCESSING
            task.started_at = datetime.now(timezone.utc)

            logger.info(f"Processing task {task.id} ({task.name})")

            # Execute the task
            if asyncio.iscoroutinefunction(task.func):
                task.result = await task.func(*task.args, **task.kwargs)
            else:
                # Run synchronous functions in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                task.result = await loop.run_in_executor(
                    None, functools.partial(task.func, *task.args, **task.kwargs)
                )

            # Mark as completed
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now(timezone.utc)
            self.stats["processed"] += 1

            logger.info(f"Task {task.id} ({task.name}) completed successfully")

        except Exception as e:
            logger.error(f"Task {task.id} ({task.name}) failed: {e}")

            # Handle retries
            if task.retry_count < task.max_retries:
                await self._retry_task(task, str(e))
            else:
                # Mark as failed
                task.status = TaskStatus.FAILED
                task.error = str(e)
                task.failed_at = datetime.now(timezone.utc)
                self.stats["failed"] += 1

    async def _retry_task(self, task: BackgroundTask, error: str):
        """Retry a failed task"""
        task.status = TaskStatus.RETRYING
        task.retry_count += 1
        task.error = error
        self.stats["retried"] += 1

        logger.info(
            f"Retrying task {task.id} ({task.name}), attempt {task.retry_count}"
        )

        # Add back to queue with same priority
        priority_value = -task.priority.value
        await self.queues[task.priority].put((priority_value, task.id))

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task status by ID

        Args:
            task_id (str): Task ID

        Returns:
            Dict[str, Any]: Task status information or None if not found
        """
        task = self.tasks.get(task_id)
        if not task:
            return None

        return {
            "id": task.id,
            "name": task.name,
            "status": task.status.value,
            "priority": task.priority.name,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": (
                task.completed_at.isoformat() if task.completed_at else None
            ),
            "failed_at": task.failed_at.isoformat() if task.failed_at else None,
            "retry_count": task.retry_count,
            "max_retries": task.max_retries,
            "error": task.error,
        }

    def get_stats(self) -> Dict[str, Any]:
        """
        Get task manager statistics

        Returns:
            Dict[str, Any]: Statistics including queue sizes and processing metrics
        """
        queue_sizes = {
            priority.name.lower(): queue.qsize()
            for priority, queue in self.queues.items()
        }

        return {
            "running": self.running,
            "workers": len(self.workers),
            "queue_sizes": queue_sizes,
            "stats": self.stats.copy(),
            "total_tasks": len(self.tasks),
        }


# Global background task manager instance
background_task_manager = BackgroundTaskManager()


# Decorator for background tasks
def background_task(
    priority: TaskPriority = TaskPriority.NORMAL, max_retries: int = 3, name: str = ""
):
    """
    Decorator to mark a function as a background task

    Args:
        priority (TaskPriority): Task priority
        max_retries (int): Maximum retry attempts
        name (str): Task name

    Example:
        >>> @background_task(priority=TaskPriority.HIGH, max_retries=2)
        >>> async def send_email(recipient: str, subject: str, body: str):
        >>>     # Send email implementation
        >>>     pass
        >>>
        >>> # Usage
        >>> task_id = await send_email("<EMAIL>", "Hello", "World")
    """

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            task_name = name or f"{func.__module__}.{func.__name__}"
            return await background_task_manager.submit_task(
                func,
                *args,
                name=task_name,
                priority=priority,
                max_retries=max_retries,
                **kwargs,
            )

        return wrapper

    return decorator
