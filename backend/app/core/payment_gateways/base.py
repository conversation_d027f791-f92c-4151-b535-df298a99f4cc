"""
Abstract base class for payment gateway implementations
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional


class PaymentStatus(Enum):
    """Payment status enumeration"""

    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PROCESSING = "processing"


@dataclass
class PaymentResult:
    """Standardized payment result across all gateways"""

    status: PaymentStatus
    reference: str
    amount: Decimal
    currency: str
    gateway_response: Dict
    transaction_id: Optional[str] = None
    gateway_fee: Optional[Decimal] = None
    customer_email: Optional[str] = None
    paid_at: Optional[datetime] = None
    failure_reason: Optional[str] = None


class PaymentGateway(ABC):
    """Abstract base class for payment gateway implementations"""

    def __init__(self, config: Dict[str, str]):
        """Initialize gateway with configuration"""
        self.config = config
        self.gateway_name = self.__class__.__name__.replace("Gateway", "").lower()

    @abstractmethod
    async def create_payment_link(
        self,
        amount: Decimal,
        currency: str,
        customer_email: str,
        reference: str,
        metadata: Dict,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None,
    ) -> Dict[str, str]:
        """
        Create a payment link for the specified amount and currency

        Returns:
            Dict containing payment_link, reference, and access_code
        """
        pass

    @abstractmethod
    async def verify_payment(self, reference: str) -> PaymentResult:
        """
        Verify a payment transaction by reference

        Returns:
            PaymentResult with standardized payment information
        """
        pass

    @abstractmethod
    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Verify webhook signature from the payment gateway

        Returns:
            Boolean indicating if signature is valid
        """
        pass

    @abstractmethod
    async def get_supported_currencies(self) -> List[str]:
        """
        Get list of supported currencies for this gateway

        Returns:
            List of ISO currency codes
        """
        pass

    @abstractmethod
    async def get_supported_countries(self) -> List[str]:
        """
        Get list of supported countries for this gateway

        Returns:
            List of ISO country codes
        """
        pass

    @abstractmethod
    async def get_payment_methods(self, currency: str, country: str) -> List[str]:
        """
        Get available payment methods for currency and country

        Returns:
            List of payment method identifiers
        """
        pass

    @abstractmethod
    async def calculate_fees(
        self, amount: Decimal, currency: str, payment_method: str = "card"
    ) -> Dict[str, Decimal]:
        """
        Calculate gateway fees for a transaction

        Returns:
            Dict with gateway_fee, platform_fee, total_fee
        """
        pass

    def get_gateway_name(self) -> str:
        """Get the gateway name"""
        return self.gateway_name

    def is_supported_currency(self, currency: str) -> bool:
        """Check if currency is supported by this gateway"""
        # This will be implemented by calling get_supported_currencies
        # but can be overridden for performance
        return True

    def is_supported_country(self, country: str) -> bool:
        """Check if country is supported by this gateway"""
        # This will be implemented by calling get_supported_countries
        # but can be overridden for performance
        return True
