"""
DPO (Direct Pay Online) payment gateway implementation for African markets
"""

import hashlib
import hmac
import xml.etree.ElementTree as ET
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional

import httpx
from sqlalchemy.orm import Session

from ..payment_error_handler import (PaymentError, PaymentErrorHandler,
                                     PaymentErrorType)
from .base import PaymentGateway, PaymentResult, PaymentStatus


class DPOGateway(PaymentGateway):
    """DPO payment gateway implementation for comprehensive African market coverage"""

    def __init__(self, config: Dict[str, str], db_session: Optional[Session] = None):
        super().__init__(config)

        self.company_token = config.get("company_token")
        self.service_type = config.get("service_type", "3854")  # Default service type
        self.test_mode = config.get("test_mode", "true")

        # Initialize error handler
        self.error_handler = PaymentErrorHandler(db_session) if db_session else None

        if not self.company_token or self.company_token == "DPO_TEST_PLACEHOLDER":
            if self.error_handler:
                error = self.error_handler.handle_configuration_error(
                    "Valid DPO company token is required", "dpo"
                )
                raise error
            raise ValueError("Valid DPO company token is required")

        self.base_url = (
            "https://secure.3gdirectpay.com"
            if self.test_mode == "false"
            else "https://secure1.sandbox.directpay.online"
        )

    async def create_payment_link(
        self,
        amount: Decimal,
        currency: str,
        customer_email: str,
        reference: str,
        metadata: Dict,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None,
    ) -> Dict[str, str]:
        """Create a payment link using DPO"""

        # DPO uses XML for API requests
        xml_data = f"""<?xml version="1.0" encoding="utf-8"?>
        <API3G>
            <CompanyToken>{self.company_token}</CompanyToken>
            <Request>createToken</Request>
            <Transaction>
                <PaymentAmount>{amount}</PaymentAmount>
                <PaymentCurrency>{currency}</PaymentCurrency>
                <CompanyRef>{reference}</CompanyRef>
                <RedirectURL>{success_url or 'https://devhq.co/payment/success'}</RedirectURL>
                <BackURL>{cancel_url or 'https://devhq.co/payment/cancel'}</BackURL>
                <CompanyRefUnique>0</CompanyRefUnique>
                <PTL>5</PTL>
                <PTLtype>minutes</PTLtype>
                <CustomerFirstName>{metadata.get('customer_name', 'Customer').split()[0]}</CustomerFirstName>
                <CustomerLastName>{metadata.get('customer_name', 'Customer').split()[-1]}</CustomerLastName>
                <CustomerEmail>{customer_email}</CustomerEmail>
                <CustomerPhone>{metadata.get('customer_phone', '')}</CustomerPhone>
                <CustomerAddress>{metadata.get('customer_address', '')}</CustomerAddress>
                <CustomerCity>{metadata.get('customer_city', '')}</CustomerCity>
                <CustomerCountry>{metadata.get('customer_country', '')}</CustomerCountry>
                <CustomerZip>{metadata.get('customer_zip', '')}</CustomerZip>
                <MobilePayment>1</MobilePayment>
                <AllowRecurrent>0</AllowRecurrent>
            </Transaction>
            <Services>
                <Service>
                    <ServiceType>{self.service_type}</ServiceType>
                    <ServiceDescription>DevHQ Invoice Payment - {metadata.get('invoice_number', reference)}</ServiceDescription>
                    <ServiceDate>{datetime.now().strftime('%Y/%m/%d %H:%M')}</ServiceDate>
                </Service>
            </Services>
        </API3G>"""

        headers = {
            "Content-Type": "application/xml",
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/API/v6/",
                    headers=headers,
                    content=xml_data,
                )

                if response.status_code == 200:
                    try:
                        # Parse XML response
                        root = ET.fromstring(response.text)
                        result = (
                            root.find("Result").text
                            if root.find("Result") is not None
                            else None
                        )

                        if result == "000":  # Success
                            token = root.find("TransToken").text
                            payment_url = f"{self.base_url}/payv2.php?ID={token}"

                            return {
                                "payment_link": payment_url,
                                "reference": reference,
                                "access_code": token,
                            }
                        else:
                            result_explanation = (
                                root.find("ResultExplanation").text
                                if root.find("ResultExplanation") is not None
                                else "Unknown error"
                            )
                            error_msg = (
                                f"DPO API Error: {result} - {result_explanation}"
                            )
                            if self.error_handler:
                                error = self.error_handler.handle_gateway_error(
                                    error_msg,
                                    "dpo",
                                    reference,
                                    {
                                        "result": result,
                                        "explanation": result_explanation,
                                    },
                                )
                                await self.error_handler.log_error(
                                    error, {"xml_data": xml_data}
                                )
                                raise error
                            raise Exception(error_msg)
                    except ET.ParseError as e:
                        error_msg = f"Invalid XML response from DPO: {str(e)}"
                        if self.error_handler:
                            error = self.error_handler.handle_gateway_error(
                                error_msg,
                                "dpo",
                                reference,
                                {"response_text": response.text},
                            )
                            await self.error_handler.log_error(
                                error, {"xml_data": xml_data}
                            )
                            raise error
                        raise Exception(error_msg)
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                    if self.error_handler:
                        error = self.error_handler.handle_gateway_error(
                            error_msg,
                            "dpo",
                            reference,
                            {"status_code": response.status_code},
                        )
                        await self.error_handler.log_error(
                            error, {"xml_data": xml_data}
                        )
                        raise error
                    raise Exception(error_msg)

        except httpx.HTTPError as e:
            if self.error_handler:
                error = self.error_handler.handle_http_error(e, "dpo", reference)
                await self.error_handler.log_error(error, {"xml_data": xml_data})
                raise error
            raise Exception(f"Network error creating DPO payment link: {str(e)}")
        except PaymentError:
            raise
        except Exception as e:
            if self.error_handler:
                error = self.error_handler.handle_gateway_error(
                    str(e), "dpo", reference
                )
                await self.error_handler.log_error(error, {"xml_data": xml_data})
                raise error
            raise

    async def verify_payment(self, reference: str) -> PaymentResult:
        """Verify a payment transaction with DPO"""

        # DPO verification using XML
        xml_data = f"""<?xml version="1.0" encoding="utf-8"?>
        <API3G>
            <CompanyToken>{self.company_token}</CompanyToken>
            <Request>verifyToken</Request>
            <TransactionToken>{reference}</TransactionToken>
        </API3G>"""

        headers = {
            "Content-Type": "application/xml",
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/API/v6/",
                    headers=headers,
                    content=xml_data,
                )

                if response.status_code == 200:
                    try:
                        root = ET.fromstring(response.text)
                        result = (
                            root.find("Result").text
                            if root.find("Result") is not None
                            else None
                        )

                        if result == "000":  # Success
                            # Parse transaction details
                            transaction_status = (
                                root.find("TransactionStatus").text
                                if root.find("TransactionStatus") is not None
                                else ""
                            )

                            # Map DPO status to our PaymentStatus
                            if transaction_status == "1":  # Paid
                                status = PaymentStatus.SUCCESS
                            elif transaction_status == "2":  # Declined
                                status = PaymentStatus.FAILED
                            elif transaction_status == "4":  # Cancelled
                                status = PaymentStatus.CANCELLED
                            else:
                                status = PaymentStatus.PENDING

                            amount_element = root.find("TransactionAmount")
                            currency_element = root.find("TransactionCurrency")

                            amount = (
                                Decimal(amount_element.text)
                                if amount_element is not None
                                else Decimal("0")
                            )
                            currency = (
                                currency_element.text
                                if currency_element is not None
                                else "USD"
                            )

                            # DPO doesn't provide detailed fee breakdown in verification
                            # Fee calculation would need to be done separately

                            return PaymentResult(
                                status=status,
                                reference=reference,
                                amount=amount,
                                currency=currency,
                                gateway_response={
                                    "transaction_status": transaction_status,
                                    "result": result,
                                    "raw_response": response.text,
                                },
                                transaction_id=(
                                    root.find("TransactionRef").text
                                    if root.find("TransactionRef") is not None
                                    else None
                                ),
                                gateway_fee=None,  # DPO doesn't provide this in verification
                                customer_email=(
                                    root.find("CustomerEmail").text
                                    if root.find("CustomerEmail") is not None
                                    else None
                                ),
                                paid_at=(
                                    datetime.now(timezone.utc)
                                    if status == PaymentStatus.SUCCESS
                                    else None
                                ),
                                failure_reason=(
                                    root.find("ResultExplanation").text
                                    if status == PaymentStatus.FAILED
                                    else None
                                ),
                            )
                        else:
                            result_explanation = (
                                root.find("ResultExplanation").text
                                if root.find("ResultExplanation") is not None
                                else "Unknown error"
                            )
                            error_msg = f"DPO verification failed: {result} - {result_explanation}"
                            if self.error_handler:
                                error = self.error_handler.handle_gateway_error(
                                    error_msg,
                                    "dpo",
                                    reference,
                                    {
                                        "result": result,
                                        "explanation": result_explanation,
                                    },
                                )
                                await self.error_handler.log_error(
                                    error, {"xml_data": xml_data}
                                )
                                raise error
                            raise Exception(error_msg)
                    except ET.ParseError as e:
                        error_msg = f"Invalid XML response from DPO: {str(e)}"
                        if self.error_handler:
                            error = self.error_handler.handle_gateway_error(
                                error_msg,
                                "dpo",
                                reference,
                                {"response_text": response.text},
                            )
                            await self.error_handler.log_error(
                                error, {"xml_data": xml_data}
                            )
                            raise error
                        raise Exception(error_msg)
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                    if self.error_handler:
                        error = self.error_handler.handle_gateway_error(
                            error_msg,
                            "dpo",
                            reference,
                            {"status_code": response.status_code},
                        )
                        await self.error_handler.log_error(
                            error, {"xml_data": xml_data}
                        )
                        raise error
                    raise Exception(error_msg)

        except httpx.HTTPError as e:
            if self.error_handler:
                error = self.error_handler.handle_http_error(e, "dpo", reference)
                await self.error_handler.log_error(error, {"xml_data": xml_data})
                raise error
            raise Exception(f"Network error verifying DPO payment: {str(e)}")
        except PaymentError:
            raise
        except Exception as e:
            if self.error_handler:
                error = self.error_handler.handle_gateway_error(
                    str(e), "dpo", reference
                )
                await self.error_handler.log_error(error, {"xml_data": xml_data})
                raise error
            raise

    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature from DPO"""

        # DPO uses a different webhook verification method
        # They typically send a POST with transaction details
        # Verification is done by calling their verify API rather than signature checking

        # For now, we'll implement a basic check
        # In production, you'd want to verify the source IP and call verify API
        return True  # DPO verification is done via API call, not signature

    async def get_supported_currencies(self) -> List[str]:
        """Get supported currencies for DPO"""
        return [
            # African currencies
            "ZAR",
            "KES",
            "UGX",
            "TZS",
            "GHS",
            "NGN",
            "ZMW",
            "BWP",
            "MWK",
            "RWF",
            "ETB",
            "XAF",
            "XOF",
            "MUR",
            "SCR",
            "SZL",
            "LSL",
            "NAD",
            "AOA",
            "MZN",
            # International currencies
            "USD",
            "EUR",
            "GBP",
            "AUD",
            "CAD",
            "CHF",
            "JPY",
            "CNY",
            "INR",
            "BRL",
        ]

    async def get_supported_countries(self) -> List[str]:
        """Get supported countries for DPO"""
        return [
            # African countries (DPO's strong presence)
            "ZA",
            "KE",
            "UG",
            "TZ",
            "GH",
            "NG",
            "ZM",
            "BW",
            "MW",
            "RW",
            "ET",
            "MU",
            "SC",
            "SZ",
            "LS",
            "NA",
            "AO",
            "MZ",
            "ZW",
            "BF",
            "ML",
            "NE",
            "TD",
            "CF",
            "CG",
            "GA",
            "CM",
            "SN",
            "CI",
            "BJ",
            "TG",
            "GN",
            "SL",
            "LR",
            "GM",
            "GW",
            "CV",
            "ST",
            "DJ",
            "ER",
            "SO",
            "SS",
            "SD",
            "LY",
            "TN",
            "DZ",
            "MA",
            "EG",
            "MR",
            "KM",
            "MG",
            # International presence
            "US",
            "GB",
            "AU",
            "CA",
            "DE",
            "FR",
            "IT",
            "ES",
            "NL",
            "BE",
            "CH",
            "AT",
            "SE",
            "NO",
            "DK",
            "FI",
            "IE",
            "PT",
            "GR",
            "PL",
            "CZ",
            "HU",
            "RO",
            "BG",
            "HR",
            "SI",
            "SK",
            "EE",
            "LV",
            "LT",
            "LU",
            "MT",
            "CY",
            "IN",
            "SG",
            "HK",
            "AE",
            "SA",
            "QA",
            "KW",
            "BH",
            "OM",
            "JO",
            "LB",
            "IL",
            "TR",
            "BR",
            "MX",
            "AR",
            "CL",
            "CO",
            "PE",
            "UY",
            "PY",
            "BO",
            "EC",
            "VE",
            "GY",
            "SR",
            "GF",
        ]

    async def get_payment_methods(self, currency: str, country: str) -> List[str]:
        """Get available payment methods for currency and country"""

        # DPO supports various payment methods based on country
        base_methods = ["card", "bank_transfer"]

        # Country-specific methods
        country_methods = {
            "ZA": [
                "card",
                "bank_transfer",
                "instant_eft",
                "masterpass",
                "visa_checkout",
            ],
            "KE": ["card", "bank_transfer", "mobile_money", "mpesa"],
            "UG": ["card", "bank_transfer", "mobile_money", "mtn_mobile_money"],
            "TZ": [
                "card",
                "bank_transfer",
                "mobile_money",
                "tigo_pesa",
                "airtel_money",
            ],
            "GH": ["card", "bank_transfer", "mobile_money", "mtn_mobile_money"],
            "NG": ["card", "bank_transfer", "ussd", "bank_transfer"],
            "ZM": ["card", "bank_transfer", "mobile_money"],
            "BW": ["card", "bank_transfer", "orange_money"],
            "MW": ["card", "bank_transfer", "airtel_money"],
            "RW": ["card", "bank_transfer", "mobile_money", "mtn_mobile_money"],
            "ET": ["card", "bank_transfer"],
            "MU": ["card", "bank_transfer", "juice"],
        }

        return country_methods.get(country, base_methods)

    async def calculate_fees(
        self, amount: Decimal, currency: str, payment_method: str = "card"
    ) -> Dict[str, Decimal]:
        """Calculate DPO fees for a transaction"""

        # DPO fee structure (as of 2024)
        # Note: DPO fees vary significantly by country and payment method

        if currency == "ZAR":
            if payment_method == "card":
                # 2.9% + R2.50 for South African cards
                fee = (amount * Decimal("0.029")) + Decimal("2.50")
            elif payment_method == "instant_eft":
                # R7.50 flat fee for instant EFT
                fee = Decimal("7.50")
            else:
                fee = amount * Decimal("0.029")

        elif currency in ["KES", "UGX", "TZS"]:
            if payment_method == "mobile_money":
                # 3.5% for mobile money in East Africa
                fee = amount * Decimal("0.035")
            elif payment_method == "card":
                # 3.8% for cards in East Africa
                fee = amount * Decimal("0.038")
            else:
                fee = amount * Decimal("0.035")

        elif currency == "GHS":
            # 3.5% for Ghana
            fee = amount * Decimal("0.035")

        elif currency == "NGN":
            if payment_method == "card":
                # 3.9% for Nigerian cards
                fee = amount * Decimal("0.039")
            elif payment_method == "bank_transfer":
                # NGN 100 flat fee for bank transfers
                fee = Decimal("100")
            else:
                fee = amount * Decimal("0.039")

        elif currency in ["USD", "EUR", "GBP"]:
            # 3.9% for international currencies
            fee = amount * Decimal("0.039")

        else:
            # Default rate for other currencies
            fee = amount * Decimal("0.045")  # 4.5%

        return {
            "gateway_fee": fee,
            "platform_fee": Decimal("0"),  # No additional platform fee at gateway level
            "total_fee": fee,
        }

    def is_supported_currency(self, currency: str) -> bool:
        """Check if currency is supported by DPO"""
        supported = [
            "ZAR",
            "KES",
            "UGX",
            "TZS",
            "GHS",
            "NGN",
            "ZMW",
            "BWP",
            "MWK",
            "RWF",
            "ETB",
            "XAF",
            "XOF",
            "MUR",
            "SCR",
            "SZL",
            "LSL",
            "NAD",
            "AOA",
            "MZN",
            "USD",
            "EUR",
            "GBP",
            "AUD",
            "CAD",
            "CHF",
            "JPY",
            "CNY",
            "INR",
            "BRL",
        ]
        return currency in supported

    def is_supported_country(self, country: str) -> bool:
        """Check if country is supported by DPO"""
        # DPO has very wide coverage, especially in Africa
        # Only a few countries are restricted
        restricted = ["IR", "KP", "SY", "CU", "AF"]  # Sanctioned countries
        return country not in restricted
