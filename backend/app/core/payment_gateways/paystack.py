"""
Paystack payment gateway implementation
"""

import hashlib
import hmac
import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional

import httpx

from ..payment_error_handler import (PaymentError, PaymentErrorHandler,
                                     PaymentErrorType)
from .base import PaymentGateway, PaymentResult, PaymentStatus


class PaystackGateway(PaymentGateway):
    """Paystack payment gateway implementation for African markets"""

    def __init__(self, config: Dict[str, str], db_session=None):
        super().__init__(config)

        self.secret_key = config.get("secret_key")
        self.public_key = config.get("public_key")
        self.db_session = db_session

        # Initialize error handler if database session is available
        self.error_handler = PaymentErrorHandler(db_session) if db_session else None

        if not self.secret_key or self.secret_key.startswith("sk_test_placeholder"):
            error_msg = "Valid Paystack secret key is required"
            if self.error_handler:
                error = self.error_handler.handle_configuration_error(
                    error_msg, "paystack"
                )
                raise error
            raise ValueError(error_msg)

        if not self.public_key or self.public_key.startswith("pk_test_placeholder"):
            error_msg = "Valid Paystack public key is required"
            if self.error_handler:
                error = self.error_handler.handle_configuration_error(
                    error_msg, "paystack"
                )
                raise error
            raise ValueError(error_msg)

        self.base_url = "https://api.paystack.co"
        self.headers = {
            "Authorization": f"Bearer {self.secret_key}",
            "Content-Type": "application/json",
        }

    async def create_payment_link(
        self,
        amount: Decimal,
        currency: str,
        customer_email: str,
        reference: str,
        metadata: Dict,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None,
    ) -> Dict[str, str]:
        """Create a payment link using Paystack"""

        # Convert amount to kobo for NGN, pesewas for GHS, cents for other currencies
        if currency == "NGN":
            amount_in_subunits = int(amount * 100)  # kobo
        elif currency == "GHS":
            amount_in_subunits = int(amount * 100)  # pesewas
        else:
            amount_in_subunits = int(amount * 100)  # cents

        payload = {
            "amount": amount_in_subunits,
            "email": customer_email,
            "currency": currency,
            "reference": reference,
            "callback_url": success_url,
            "metadata": metadata,
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/transaction/initialize",
                    headers=self.headers,
                    json=payload,
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get("status"):
                        return {
                            "payment_link": data["data"]["authorization_url"],
                            "reference": data["data"]["reference"],
                            "access_code": data["data"]["access_code"],
                        }
                    else:
                        error_msg = data.get("message", "Payment link creation failed")
                        if self.error_handler:
                            error = self.error_handler.handle_gateway_error(
                                error_msg, "paystack", reference, data
                            )
                            if self.error_handler:
                                await self.error_handler.log_error(
                                    error, {"payload": payload}
                                )
                            raise error
                        raise Exception(error_msg)
                else:
                    # Handle HTTP error responses
                    try:
                        error_data = response.json()
                        error_msg = error_data.get(
                            "message", f"HTTP {response.status_code} error"
                        )
                    except:
                        error_msg = f"HTTP {response.status_code}: {response.text}"

                    if self.error_handler:
                        error = self.error_handler.handle_gateway_error(
                            error_msg,
                            "paystack",
                            reference,
                            {"status_code": response.status_code},
                        )
                        await self.error_handler.log_error(error, {"payload": payload})
                        raise error
                    raise Exception(error_msg)

        except httpx.HTTPError as e:
            if self.error_handler:
                error = self.error_handler.handle_http_error(e, "paystack", reference)
                await self.error_handler.log_error(error, {"payload": payload})
                raise error
            raise Exception(f"Network error creating Paystack payment link: {str(e)}")
        except PaymentError:
            raise
        except Exception as e:
            if self.error_handler:
                error = self.error_handler.handle_gateway_error(
                    str(e), "paystack", reference
                )
                await self.error_handler.log_error(error, {"payload": payload})
                raise error
            raise

    async def verify_payment(self, reference: str) -> PaymentResult:
        """Verify a payment transaction with Paystack"""

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/transaction/verify/{reference}",
                    headers=self.headers,
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get("status"):
                        transaction_data = data["data"]

                        # Map Paystack status to our PaymentStatus
                        paystack_status = transaction_data.get("status", "").lower()
                        if paystack_status == "success":
                            status = PaymentStatus.SUCCESS
                        elif paystack_status == "failed":
                            status = PaymentStatus.FAILED
                        elif paystack_status == "abandoned":
                            status = PaymentStatus.CANCELLED
                        else:
                            status = PaymentStatus.PENDING

                        # Convert amount back from subunits
                        amount_subunits = transaction_data.get("amount", 0)
                        currency = transaction_data.get("currency", "NGN")
                        amount = Decimal(amount_subunits) / 100

                        # Extract gateway fee
                        gateway_fee = None
                        if "fees" in transaction_data:
                            gateway_fee = Decimal(transaction_data["fees"]) / 100

                        # Parse paid date
                        paid_at = None
                        if transaction_data.get("paid_at"):
                            paid_at = datetime.fromisoformat(
                                transaction_data["paid_at"].replace("Z", "+00:00")
                            )

                        return PaymentResult(
                            status=status,
                            reference=transaction_data.get("reference", reference),
                            amount=amount,
                            currency=currency,
                            gateway_response=transaction_data,
                            transaction_id=str(transaction_data.get("id")),
                            gateway_fee=gateway_fee,
                            customer_email=transaction_data.get("customer", {}).get(
                                "email"
                            ),
                            paid_at=paid_at,
                            failure_reason=(
                                transaction_data.get("gateway_response")
                                if status == PaymentStatus.FAILED
                                else None
                            ),
                        )
                    else:
                        error_msg = data.get("message", "Payment verification failed")
                        if self.error_handler:
                            error = self.error_handler.handle_gateway_error(
                                error_msg, "paystack", reference, data
                            )
                            await self.error_handler.log_error(
                                error, {"reference": reference}
                            )
                            raise error
                        raise Exception(error_msg)
                else:
                    # Handle HTTP error responses
                    try:
                        error_data = response.json()
                        error_msg = error_data.get(
                            "message", f"HTTP {response.status_code} error"
                        )
                    except:
                        error_msg = f"HTTP {response.status_code}: {response.text}"

                    if self.error_handler:
                        error = self.error_handler.handle_gateway_error(
                            error_msg,
                            "paystack",
                            reference,
                            {"status_code": response.status_code},
                        )
                        await self.error_handler.log_error(
                            error, {"reference": reference}
                        )
                        raise error
                    raise Exception(error_msg)

        except httpx.HTTPError as e:
            if self.error_handler:
                error = self.error_handler.handle_http_error(e, "paystack", reference)
                await self.error_handler.log_error(error, {"reference": reference})
                raise error
            raise Exception(f"Network error verifying Paystack payment: {str(e)}")
        except PaymentError:
            raise
        except Exception as e:
            if self.error_handler:
                error = self.error_handler.handle_gateway_error(
                    str(e), "paystack", reference
                )
                await self.error_handler.log_error(error, {"reference": reference})
                raise error
            raise

    async def create_subaccount(
        self,
        business_name: str,
        settlement_bank: str,
        account_number: str,
        percentage_charge: float,
        description: Optional[str] = None,
        primary_contact_email: Optional[str] = None,
        primary_contact_name: Optional[str] = None,
        primary_contact_phone: Optional[str] = None,
    ) -> Dict[str, str]:
        """Create a Paystack subaccount for automatic payouts"""

        payload = {
            "business_name": business_name,
            "settlement_bank": settlement_bank,
            "account_number": account_number,
            "percentage_charge": percentage_charge,
        }

        if description:
            payload["description"] = description
        if primary_contact_email:
            payload["primary_contact_email"] = primary_contact_email
        if primary_contact_name:
            payload["primary_contact_name"] = primary_contact_name
        if primary_contact_phone:
            payload["primary_contact_phone"] = primary_contact_phone

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/subaccount",
                headers=self.headers,
                json=payload,
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("status"):
                    subaccount_data = data["data"]
                    return {
                        "subaccount_code": subaccount_data["subaccount_code"],
                        "bank_name": subaccount_data["settlement_bank"],
                        "account_number": subaccount_data["account_number"],
                        "percentage_charge": subaccount_data["percentage_charge"],
                    }

            raise Exception(f"Failed to create Paystack subaccount: {response.text}")

    async def list_banks(self) -> List[Dict[str, str]]:
        """List available banks for subaccount setup"""

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/bank", headers=self.headers)

            if response.status_code == 200:
                data = response.json()
                if data.get("status"):
                    return data["data"]

            raise Exception(f"Failed to list Paystack banks: {response.text}")

    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature from Paystack"""

        computed_signature = hmac.new(
            self.secret_key.encode("utf-8"), payload.encode("utf-8"), hashlib.sha512
        ).hexdigest()

        return hmac.compare_digest(computed_signature, signature)

    async def get_supported_currencies(self) -> List[str]:
        """Get supported currencies for Paystack"""
        return ["NGN", "GHS", "ZAR", "KES", "USD"]

    async def get_supported_countries(self) -> List[str]:
        """Get supported countries for Paystack"""
        return ["NG", "GH", "ZA", "KE"]  # Nigeria, Ghana, South Africa, Kenya

    async def get_payment_methods(self, currency: str, country: str) -> List[str]:
        """Get available payment methods for currency and country"""

        methods = {
            "NGN": ["card", "bank_transfer", "ussd", "mobile_money", "qr"],
            "GHS": ["card", "bank_transfer", "mobile_money"],
            "KES": ["card", "bank_transfer", "mobile_money"],
            "ZAR": ["card", "bank_transfer"],
            "USD": ["card", "bank_transfer"],
        }

        return methods.get(currency, ["card", "bank_transfer"])

    async def calculate_fees(
        self, amount: Decimal, currency: str, payment_method: str = "card"
    ) -> Dict[str, Decimal]:
        """Calculate Paystack fees for a transaction"""

        # Paystack fee structure (as of 2024)
        if currency == "NGN":
            if payment_method == "card":
                # 1.5% + NGN 100 for local cards, capped at NGN 2000
                fee = min((amount * Decimal("0.015")) + Decimal("100"), Decimal("2000"))
            elif payment_method == "bank_transfer":
                # NGN 50 flat fee
                fee = Decimal("50")
            else:
                # Default to card fees
                fee = min((amount * Decimal("0.015")) + Decimal("100"), Decimal("2000"))

        elif currency == "GHS":
            # 2.9% for Ghana
            fee = amount * Decimal("0.029")

        elif currency == "ZAR":
            # 2.9% for South Africa
            fee = amount * Decimal("0.029")

        elif currency == "KES":
            # 3.5% for Kenya
            fee = amount * Decimal("0.035")

        else:
            # Default international rate
            fee = amount * Decimal("0.039")  # 3.9%

        return {
            "gateway_fee": fee,
            "platform_fee": Decimal("0"),  # No additional platform fee at gateway level
            "total_fee": fee,
        }

    def is_supported_currency(self, currency: str) -> bool:
        """Check if currency is supported by Paystack"""
        supported = ["NGN", "GHS", "ZAR", "KES", "USD"]
        return currency in supported

    def is_supported_country(self, country: str) -> bool:
        """Check if country is supported by Paystack"""
        supported = ["NG", "GH", "ZA", "KE"]
        return country in supported
