"""Cache Warming Service for DevHQ Backend
Proactively populates frequently accessed cache entries"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional

from sqlalchemy import func
from sqlalchemy.orm import Session

from app.core.cache import <PERSON><PERSON><PERSON>ey<PERSON>uilder, cache_manager
from app.core.cache_invalidation import cache_invalidation_service
from app.database import get_db
from app.models.client import Client
from app.models.project import Project
from app.models.user import User

logger = logging.getLogger(__name__)


class CacheWarmingService:
    """Service for warming up cache with frequently accessed data"""

    def __init__(self):
        self.warming_stats = {
            "total_warmed": 0,
            "successful_warms": 0,
            "failed_warms": 0,
            "last_warming_time": None,
        }

    async def warm_user_cache(self, user_id: str, db: Session) -> bool:
        """Warm cache for a specific user

        Args:
            user_id (str): User ID
            db (Session): Database session

        Returns:
            bool: True if successful
        """
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            # Warm user profile cache
            profile_key = CacheKeyBuilder.user_profile(user_id)
            profile_data = {
                "id": str(user.id),
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "full_name": user.full_name,
                "avatar_url": user.avatar_url,
                "phone": user.phone,
                "bio": user.bio,
                "is_active": user.is_active,
                "is_verified": user.is_verified,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            }
            await cache_manager.set(profile_key, profile_data, ttl=1800)  # 30 minutes

            # Warm user projects cache (first page)
            projects = (
                db.query(Project).filter(Project.user_id == user_id).limit(25).all()
            )
            projects_key = CacheKeyBuilder.user_projects(user_id, page=1)
            projects_data = [
                {
                    "id": str(p.id),
                    "name": p.name,
                    "status": p.status,
                    "created_at": p.created_at.isoformat() if p.created_at else None,
                }
                for p in projects
            ]
            await cache_manager.set(projects_key, projects_data, ttl=600)  # 10 minutes

            # Warm dashboard data
            dashboard_key = CacheKeyBuilder.user_dashboard(user_id)
            dashboard_data = await self._build_dashboard_data(user_id, db)
            await cache_manager.set(dashboard_key, dashboard_data, ttl=300)  # 5 minutes

            self.warming_stats["successful_warms"] += 1
            logger.debug(f"Successfully warmed cache for user {user_id}")
            return True

        except Exception as e:
            self.warming_stats["failed_warms"] += 1
            logger.error(f"Failed to warm cache for user {user_id}: {e}")
            return False

    async def warm_project_cache(self, project_id: str, db: Session) -> bool:
        """Warm cache for a specific project

        Args:
            project_id (str): Project ID
            db (Session): Database session

        Returns:
            bool: True if successful
        """
        try:
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                return False

            # Warm project details cache
            details_key = CacheKeyBuilder.project_details(project_id)
            project_data = {
                "id": str(project.id),
                "name": project.name,
                "description": project.description,
                "status": project.status,
                "billing_type": project.billing_type,
                "hourly_rate": (
                    float(project.hourly_rate) if project.hourly_rate else None
                ),
                "fixed_price": (
                    float(project.fixed_price) if project.fixed_price else None
                ),
                "start_date": (
                    project.start_date.isoformat() if project.start_date else None
                ),
                "end_date": project.end_date.isoformat() if project.end_date else None,
                "created_at": (
                    project.created_at.isoformat() if project.created_at else None
                ),
                "updated_at": (
                    project.updated_at.isoformat() if project.updated_at else None
                ),
            }
            await cache_manager.set(details_key, project_data, ttl=1200)  # 20 minutes

            # Warm project statistics
            stats_key = CacheKeyBuilder.project_stats(project_id)
            stats_data = await self._build_project_stats(project_id, db)
            await cache_manager.set(stats_key, stats_data, ttl=600)  # 10 minutes

            self.warming_stats["successful_warms"] += 1
            logger.debug(f"Successfully warmed cache for project {project_id}")
            return True

        except Exception as e:
            self.warming_stats["failed_warms"] += 1
            logger.error(f"Failed to warm cache for project {project_id}: {e}")
            return False

    async def warm_popular_data(
        self, db: Session = None, limit: int = 50
    ) -> Dict[str, int]:
        """Warm cache for most popular/active data

        Args:
            db (Session, optional): Database session
            limit (int): Number of items to warm

        Returns:
            Dict[str, int]: Warming results
        """
        results = {
            "users_warmed": 0,
            "projects_warmed": 0,
            "errors": 0,
        }

        # If no db session provided, get one
        if db is None:
            try:
                db = next(get_db())
            except Exception as e:
                logger.error(f"Failed to get database session: {e}")
                results["errors"] += 1
                return results

        try:
            # Get most active users (by recent login or activity)
            active_users = (
                db.query(User)
                .filter(User.is_active == True)
                .order_by(User.updated_at.desc())
                .limit(limit)
                .all()
            )

            # Warm cache for active users
            for user in active_users:
                success = await self.warm_user_cache(str(user.id), db)
                if success:
                    results["users_warmed"] += 1
                else:
                    results["errors"] += 1

            # Get most recently updated projects
            recent_projects = (
                db.query(Project)
                .filter(Project.status.in_(["active", "in_progress"]))
                .order_by(Project.updated_at.desc())
                .limit(limit)
                .all()
            )

            # Warm cache for recent projects
            for project in recent_projects:
                success = await self.warm_project_cache(str(project.id), db)
                if success:
                    results["projects_warmed"] += 1
                else:
                    results["errors"] += 1

            self.warming_stats["last_warming_time"] = datetime.now(
                timezone.utc
            ).isoformat()
            logger.info(f"Cache warming completed: {results}")

        except Exception as e:
            logger.error(f"Failed to warm popular data: {e}")
            results["errors"] += 1

        return results

    async def _build_dashboard_data(self, user_id: str, db: Session) -> Dict:
        """Build dashboard data for a user"""
        try:
            # Get project counts by status
            project_counts = (
                db.query(Project.status, func.count(Project.id))
                .filter(Project.user_id == user_id)
                .group_by(Project.status)
                .all()
            )

            status_counts = {status: count for status, count in project_counts}

            # Get recent projects
            recent_projects = (
                db.query(Project)
                .filter(Project.user_id == user_id)
                .order_by(Project.updated_at.desc())
                .limit(5)
                .all()
            )

            return {
                "user_id": user_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "project_counts": status_counts,
                "total_projects": sum(status_counts.values()),
                "recent_projects": [
                    {
                        "id": str(p.id),
                        "name": p.name,
                        "status": p.status,
                        "updated_at": (
                            p.updated_at.isoformat() if p.updated_at else None
                        ),
                    }
                    for p in recent_projects
                ],
            }

        except Exception as e:
            logger.error(f"Failed to build dashboard data for user {user_id}: {e}")
            return {
                "user_id": user_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": "Failed to load dashboard data",
            }

    async def _build_project_stats(self, project_id: str, db: Session) -> Dict:
        """Build statistics for a project"""
        try:
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                return {}

            # Calculate basic stats
            stats = {
                "project_id": project_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": project.status,
                "billing_type": project.billing_type,
                "total_value": float(project.fixed_price or project.hourly_rate or 0),
                "days_active": (
                    (datetime.now(timezone.utc).date() - project.created_at.date()).days
                    if project.created_at
                    else 0
                ),
            }

            return stats

        except Exception as e:
            logger.error(f"Failed to build project stats for {project_id}: {e}")
            return {
                "project_id": project_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": "Failed to load project stats",
            }

    def get_warming_stats(self) -> Dict:
        """Get cache warming statistics"""
        return self.warming_stats.copy()


# Global cache warming service instance
cache_warming_service = CacheWarmingService()


# Convenience functions
async def warm_user_cache(user_id: str, db: Session) -> bool:
    """Warm cache for a user"""
    return await cache_warming_service.warm_user_cache(user_id, db)


async def warm_project_cache(project_id: str, db: Session) -> bool:
    """Warm cache for a project"""
    return await cache_warming_service.warm_project_cache(project_id, db)


async def warm_popular_data(db: Session = None, limit: int = 50) -> Dict[str, int]:
    """Warm cache for popular data"""
    return await cache_warming_service.warm_popular_data(db, limit)
