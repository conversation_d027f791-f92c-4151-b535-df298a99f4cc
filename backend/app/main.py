"""
DevHQ Backend - Main FastAPI Application
Enterprise-ready backend for developer business management
"""

from contextlib import asynccontextmanager

import sentry_sdk
import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.config import settings
from app.core.error_handlers import Conflict<PERSON>rror
from app.core.error_handlers import DevH<PERSON>Exception as NewDevHQException
from app.core.error_handlers import (NotFoundError, ValidationException,
                                     register_error_handlers)
from app.core.exceptions import DevHQException

# Initialize Sentry for error monitoring
if settings.sentry_dsn:
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        integrations=[
            FastApiIntegration(),
            SqlalchemyIntegration(),
        ],
        traces_sample_rate=0.1,
        environment=settings.environment,
        release=settings.version,
    )
from app.core.middleware import (RequestLoggingMiddleware,
                                 SecurityHeadersMiddleware)
from app.core.monitoring import monitoring_service
from app.core.monitoring_middleware import MonitoringMiddleware
from app.database import check_database_health

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Create rate limiter with Redis backend for distributed deployments
# Use in-memory storage for single instance or development
if settings.redis_url and settings.environment not in ["development", "testing"]:
    # Use Redis for rate limiting in production
    import redis.asyncio as redis
    from slowapi.storage.redis import RedisStorage

    try:
        redis_client = redis.from_url(settings.redis_url)
        limiter_storage = RedisStorage(redis_client)
        limiter = Limiter(
            key_func=get_remote_address,
            storage_uri=settings.redis_url,
            strategy="fixed-window-elastic-expiry",
            swallow_errors=True,  # Don't break the app if Redis is down
        )
    except Exception as e:
        logger.warning(
            "Failed to initialize Redis rate limiter, falling back to in-memory",
            error=str(e),
        )
        limiter = Limiter(key_func=get_remote_address)
else:
    # Use in-memory storage for development/testing
    limiter = Limiter(key_func=get_remote_address)

# Create rate limiter
# limiter = Limiter(key_func=get_remote_address)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan events
    """
    # Startup
    from app.core.background_tasks import background_task_manager
    from app.core.cache import cache_manager
    from app.core.config_utils import ConfigValidator
    from app.core.websocket_manager import websocket_manager

    logger.info(
        "Starting DevHQ Backend",
        version=settings.version,
        environment=settings.environment,
    )

    # Validate configuration
    validator = ConfigValidator()
    missing_settings = validator.validate_required_settings()

    if missing_settings:
        logger.warning(
            "Missing required configuration settings", missing_settings=missing_settings
        )

    # Initialize cache
    await cache_manager.connect()

    # Initialize cache warming service
    from app.core.cache_warming import cache_warming_service

    await cache_warming_service.warm_popular_data()
    logger.info("Cache warming completed")

    # Initialize background task manager
    await background_task_manager.start()

    # Initialize WebSocket cleanup
    await websocket_manager.start_cleanup_task()

    # Check database connection
    if not check_database_health():
        logger.error("Database connection failed")
        raise Exception("Database connection failed")

    logger.info("Database connection successful")

    # Initialize real-time services (skip in testing)
    if settings.environment != "testing":
        try:
            from app.services.realtime_service import realtime_service

            await realtime_service.start_processing()
            logger.info("Real-time service started successfully")
        except Exception as e:
            logger.error(f"Failed to start real-time service: {e}")
    else:
        logger.info("Skipping real-time service initialization in testing environment")

    yield

    # Shutdown
    # Stop WebSocket cleanup
    await websocket_manager.stop_cleanup_task()

    # Stop background task manager
    await background_task_manager.stop()

    # Disconnect cache
    await cache_manager.disconnect()

    if settings.environment != "testing":
        try:
            from app.services.realtime_service import realtime_service

            await realtime_service.stop_processing()
            logger.info("Real-time service stopped")
        except Exception as e:
            logger.error(f"Error stopping real-time service: {e}")

    logger.info("Shutting down DevHQ Backend")


# OpenAPI tags for better documentation organization
openapi_tags = [
    {
        "name": "Authentication",
        "description": "User authentication and authorization endpoints including registration, login, password reset, and token management.",
    },
    {
        "name": "Users",
        "description": "User profile management, settings, and account operations.",
    },
    {
        "name": "Clients",
        "description": "Client management for tracking business relationships and contact information.",
    },
    {
        "name": "Projects",
        "description": "Project creation, management, and collaboration features.",
    },
    {
        "name": "Time Tracking",
        "description": "Time tracking functionality for projects and tasks.",
    },
    {
        "name": "Financial",
        "description": "Financial management including invoicing, payments, and revenue tracking.",
    },
    {
        "name": "Files",
        "description": "File upload, management, and sharing capabilities.",
    },
    {
        "name": "Analytics",
        "description": "Business analytics, reporting, and performance metrics.",
    },
    {
        "name": "Real-time",
        "description": "WebSocket connections and real-time collaboration features.",
    },
    {"name": "Health", "description": "System health checks and monitoring endpoints."},
]

app = FastAPI(
    title=settings.app_name,
    description="""
    # DevHQ Backend API
    
    **Professional developer business management platform built for African developers and agencies working with international clients.**
    
    ## 🚀 Key Features
    
    ### 👤 User Management
    - Secure user registration and authentication
    - Profile management and settings
    - Email verification and password reset
    
    ### 🔐 Authentication & Security
    - JWT-based authentication with refresh tokens
    - Rate limiting and security headers
    - Device tracking and session management
    
    ### 👥 Client & Project Management
    - Client relationship management
    - Project creation and collaboration
    - Task management and tracking
    
    ### ⏱️ Time & Financial Tracking
    - Comprehensive time tracking
    - Automated invoicing system
    - Multi-gateway payment processing (Paystack + DPO)
    - Multi-currency support (NGN, GHS, KES, ZAR, USD, EUR, GBP)
    
    ### 📊 Analytics & Reporting
    - Business performance metrics
    - Financial reporting and insights
    - Real-time collaboration features
    
    ### 💰 Platform Revenue
    - 2.5% platform fee structure
    - Instant settlement capabilities
    - Revenue sharing and analytics
    
    ## 🔧 Technical Stack
    
    - **Framework**: FastAPI with async/await support
    - **Database**: PostgreSQL with SQLAlchemy ORM
    - **Cache**: Redis for session and rate limiting
    - **File Storage**: Cloudinary integration
    - **Monitoring**: Structured logging with Sentry
    - **Real-time**: WebSocket support for collaboration
    
    ## 📚 API Documentation
    
    This API follows RESTful principles and includes:
    - Comprehensive request/response examples
    - Detailed error documentation
    - Interactive testing interface
    - Rate limiting information
    - Authentication requirements
    
    ## 🌍 Regional Focus
    
    Specifically designed for African developers with:
    - Local payment gateway integrations
    - Multi-currency support for African markets
    - Timezone-aware operations
    - Localized business practices
    """,
    version=settings.version,
    openapi_url="/api/openapi.json" if settings.debug else None,
    docs_url="/api/docs" if settings.debug else None,
    redoc_url="/api/redoc" if settings.debug else None,
    lifespan=lifespan,
    openapi_tags=openapi_tags,
    contact={
        "name": "DevHQ Support",
        "email": "<EMAIL>",
        "url": "https://devhq.com/support",
    },
    license_info={"name": "Proprietary", "url": "https://devhq.com/license"},
    servers=[
        {"url": "http://localhost:8000", "description": "Development server"},
        {"url": "https://api-staging.devhq.com", "description": "Staging server"},
        {"url": "https://api.devhq.com", "description": "Production server"},
    ],
)

# Initialize monitoring and observability
monitoring_service.instrument_fastapi(app)

# Add CORS middleware with security enhancements
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
    # Add security headers
    expose_headers=["X-Request-ID"],
    max_age=600,  # Cache preflight requests for 10 minutes
)

# Add custom middleware
app.add_middleware(MonitoringMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)

# Register rate limiter for enhanced security
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


# Register standardized error handlers
register_error_handlers(app)


# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint for monitoring and load balancers
    """
    from app.database import get_database_info

    db_healthy = check_database_health()
    db_info = get_database_info()

    from app.core.config_utils import format_health_check_response

    health_response = format_health_check_response(db_healthy)

    # Add database information
    health_response["database_info"] = {
        "status": db_info["connection_status"],
        "type": db_info["database_type"],
        "host": db_info["host"],
    }

    return health_response


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """
    Root endpoint with API information
    """
    return {
        "message": "Welcome to DevHQ Backend API",
        "version": settings.version,
        "docs": (
            "/docs" if settings.debug else "Documentation not available in production"
        ),
        "health": "/health",
    }


# API version prefix
@app.get("/api/v1", tags=["API Info"])
async def api_info():
    """
    API version information
    """
    return {
        "api_version": "v1",
        "backend_version": settings.version,
        "environment": settings.environment,
        "features": [
            "User Management",
            "Authentication",
            "Client Management",
            "Project Management",
            "Time Tracking",
            "Financial Management",
            "Invoicing",
            "Payment Processing (Paystack)",
            "File Management (Cloudinary)",
            "Analytics & Reporting",
        ],
    }


@app.get("/config/health", tags=["Configuration"])
async def config_health_check():
    """
    Configuration health check endpoint
    Provides detailed information about application configuration
    """
    from app.core.config_utils import ConfigValidator

    validator = ConfigValidator()

    missing_settings = validator.validate_required_settings()
    external_services = validator.validate_external_service_config()
    payment_gateways = validator.validate_payment_gateway_config()
    config_summary = validator.get_configuration_summary()

    # Determine overall health status
    critical_missing = any(
        setting in missing_settings for setting in ["database_url", "secret_key"]
    )
    payment_configured = any(
        gateway["configured"] for gateway in payment_gateways.values()
    )

    overall_status = "healthy"
    if critical_missing:
        overall_status = "critical"
    elif missing_settings:
        overall_status = "degraded"
    elif not payment_configured and settings.is_production:
        overall_status = "warning"

    return {
        "overall_status": overall_status,
        "configuration_status": "healthy" if not missing_settings else "degraded",
        "missing_required_settings": missing_settings,
        "external_services_configured": external_services,
        "payment_gateways": payment_gateways,
        "config_summary": config_summary,
        "environment": settings.environment,
        "is_production": settings.is_production,
        "is_development": settings.is_development,
        "is_testing": settings.is_testing,
        "warnings": ConfigValidator._get_configuration_warnings(),
    }


# Include routers
from app.routers import (activity, analytics, approvals, auth, clients,
                         currency, dashboard, invoices, milestones, oauth, payouts, portal,
                         projects, time_entries, timer, users, webhooks,
                         websocket, workspaces)

app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(oauth.router, prefix="/api/v1/auth", tags=["OAuth Authentication"])

app.include_router(users.router, tags=["Users"])
app.include_router(workspaces.router, tags=["Workspaces"])
app.include_router(clients.router, prefix="/api/v1/clients", tags=["Clients"])
app.include_router(currency.currency_router.router, prefix="/api/v1", tags=["Currency"])
app.include_router(activity.router, prefix="/api/v1/activity", tags=["Activity Logs"])
app.include_router(projects.router, tags=["Projects"])
app.include_router(milestones.router, tags=["Milestones"])
app.include_router(time_entries.router, tags=["Time Entries"])
app.include_router(timer.router, tags=["Live Timer"])
app.include_router(analytics.router, tags=["Analytics"])
app.include_router(invoices.router, tags=["Invoices"])
app.include_router(payouts.router, prefix="/api/v1", tags=["Payouts"])
app.include_router(approvals.router, tags=["Approvals"])
app.include_router(webhooks.router, tags=["Webhooks"])
app.include_router(portal.router, tags=["Client Portal"])
app.include_router(websocket.router, tags=["WebSocket"])
app.include_router(dashboard.router, tags=["Dashboard"])

# Mount WebSocket application
from app.core.websocket_manager import socket_app

app.mount("/ws", socket_app)

import os

# Serve static files for demo
from fastapi.staticfiles import StaticFiles

static_dir = os.path.join(os.path.dirname(__file__), "..", "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")


# Demo portal endpoint
@app.get("/demo-portal")
async def demo_portal():
    """Serve the demo real-time client portal"""
    from fastapi.responses import FileResponse

    portal_file = os.path.join(static_dir, "client_portal.html")
    if os.path.exists(portal_file):
        return FileResponse(portal_file)
    else:
        return {"message": "Demo portal not found"}
