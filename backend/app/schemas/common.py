"""
Common schemas used across the application
"""

from typing import Generic, List, TypeVar
from pydantic import BaseModel, Field

# Generic type for paginated responses
T = TypeVar('T')


class PaginatedResponse(BaseModel, Generic[T]):
    """Base schema for paginated responses"""
    data: List[T]
    total: int = Field(..., description="Total number of items")
    skip: int = Field(..., description="Number of items skipped")
    limit: int = Field(..., description="Number of items per page")
    
    class Config:
        from_attributes = True


class MessageResponse(BaseModel):
    """Standard message response"""
    message: str
    
    class Config:
        from_attributes = True


class StandardResponse(BaseModel, Generic[T]):
    """Standard API response wrapper"""
    success: bool = True
    message: str
    data: T = None
    
    class Config:
        from_attributes = True
