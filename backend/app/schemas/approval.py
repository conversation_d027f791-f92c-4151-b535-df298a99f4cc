"""
Pydantic schemas for client approval system request/response validation
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class ApprovalCreate(BaseModel):
    """Schema for creating new approval requests"""

    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    approvable_id: UUID
    approvable_type: str = Field(
        ..., pattern="^(milestone|upload|invoice|deliverable)$"
    )
    client_id: UUID
    project_id: UUID
    priority: str = Field(default="medium", pattern="^(low|medium|high|urgent)$")
    due_date: Optional[datetime] = None
    client_instructions: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None


class ApprovalUpdate(BaseModel):
    """Schema for updating approval requests"""

    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    priority: Optional[str] = Field(None, pattern="^(low|medium|high|urgent)$")
    due_date: Optional[datetime] = None
    client_instructions: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None


class ClientFeedbackCreate(BaseModel):
    """Schema for client feedback submission"""

    content: str = Field(..., min_length=1)
    feedback_type: str = Field(
        default="revision", pattern="^(revision|question|approval_note)$"
    )
    client_name: str = Field(..., min_length=1, max_length=200)
    client_email: str = Field(..., pattern=r"^[^@]+@[^@]+\.[^@]+$")
    urgency_level: Optional[str] = Field(None, pattern="^(low|medium|high)$")
    category: Optional[str] = Field(None, max_length=100)


class ClientDecisionRequest(BaseModel):
    """Schema for client approval decisions"""

    decision: str = Field(..., pattern="^(approve|revise)$")
    feedback: Optional[str] = None
    client_name: str = Field(..., min_length=1, max_length=200)
    client_email: str = Field(..., pattern=r"^[^@]+@[^@]+\.[^@]+$")


class DeveloperResponseRequest(BaseModel):
    """Schema for developer responses to feedback"""

    response: str = Field(..., min_length=1)


class ApprovalReminderRequest(BaseModel):
    """Schema for sending approval reminders"""

    message: Optional[str] = None
    include_context: bool = True


class BulkReminderRequest(BaseModel):
    """Schema for bulk reminder operations"""

    approval_ids: List[UUID] = Field(..., min_items=1)
    message: Optional[str] = None
    include_context: bool = True


# Response Schemas


class ClientFeedbackResponse(BaseModel):
    """Response schema for client feedback"""

    id: UUID
    content: str
    feedback_type: str
    submitted_at: datetime
    is_addressed: bool
    developer_response: Optional[str]
    responded_at: Optional[datetime]
    client_name: str
    client_email: str
    urgency_level: Optional[str]
    category: Optional[str]

    class Config:
        from_attributes = True


class ApprovalActivityResponse(BaseModel):
    """Response schema for approval activities"""

    id: UUID
    action: str
    actor_type: str
    actor_name: str
    details: Optional[Dict[str, Any]]
    timestamp: datetime
    ip_address: Optional[str]

    class Config:
        from_attributes = True


class ApprovalResponse(BaseModel):
    """Response schema for approval details"""

    id: UUID
    status: str
    title: str
    description: Optional[str]
    approvable_id: UUID
    approvable_type: str
    client_id: UUID
    project_id: UUID
    user_id: UUID
    requested_at: datetime
    viewed_at: Optional[datetime]
    approved_at: Optional[datetime]
    revision_requested_at: Optional[datetime]
    priority: str
    due_date: Optional[datetime]
    approval_token: str
    version_number: int
    previous_approval_id: Optional[UUID]
    context_data: Optional[Dict[str, Any]]
    client_instructions: Optional[str]
    is_overdue: bool
    days_pending: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ApprovalWithDetailsResponse(ApprovalResponse):
    """Extended approval response with related data"""

    feedback_items: List[ClientFeedbackResponse] = []
    activity_logs: List[ApprovalActivityResponse] = []
    client_name: Optional[str] = None
    client_email: Optional[str] = None
    project_title: Optional[str] = None


class ApprovalListResponse(BaseModel):
    """Response schema for approval lists"""

    approvals: List[ApprovalResponse]
    total_count: int
    pending_count: int
    overdue_count: int
    approved_count: int
    revision_requested_count: int


class PaginatedApprovalsResponse(BaseModel):
    """Paginated approval response"""

    items: List[ApprovalResponse]
    total: int
    page: int
    size: int
    pages: int
    has_next: bool
    has_prev: bool


class ClientPortalApprovalResponse(BaseModel):
    """Client portal specific approval response"""

    id: UUID
    title: str
    description: Optional[str]
    status: str
    priority: str
    requested_at: datetime
    due_date: Optional[datetime]
    is_overdue: bool
    days_pending: int
    project_title: str
    project_description: Optional[str]
    approvable_type: str
    context_data: Optional[Dict[str, Any]]
    client_instructions: Optional[str]
    can_approve: bool
    can_request_revision: bool

    class Config:
        from_attributes = True


class ClientPortalDashboardResponse(BaseModel):
    """Client portal dashboard response"""

    pending_approvals: List[ClientPortalApprovalResponse]
    recent_activity: List[ApprovalActivityResponse]
    total_pending: int
    overdue_count: int
    urgent_count: int
    client_name: str
    projects_with_approvals: List[Dict[str, Any]]


class ApprovalAnalyticsResponse(BaseModel):
    """Approval analytics and insights response"""

    total_approvals: int
    pending_approvals: int
    approved_approvals: int
    revision_requests: int
    average_response_time_hours: float
    overdue_approvals: int
    approval_rate_percentage: float
    client_response_patterns: Dict[str, Any]
    approval_trends: List[Dict[str, Any]]
    top_revision_categories: List[Dict[str, Any]]
    project_velocity_impact: Dict[str, Any]


class ApprovalSuggestionResponse(BaseModel):
    """Smart approval suggestion response"""

    approvable_id: UUID
    approvable_type: str
    suggested_title: str
    suggested_description: str
    suggested_priority: str
    confidence_score: float
    reasoning: str
    project_id: UUID
    client_id: UUID


class ApprovalSuggestionsResponse(BaseModel):
    """Multiple approval suggestions response"""

    suggestions: List[ApprovalSuggestionResponse]
    total_suggestions: int
    high_confidence_count: int
    ready_for_approval_count: int
