"""
User profile and settings schemas
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, HttpUrl, conint, constr


class UserUpdate(BaseModel):
    """User update schema"""

    first_name: Optional[
        constr(strip_whitespace=True, min_length=1, max_length=100)
    ] = None
    last_name: Optional[
        constr(strip_whitespace=True, min_length=1, max_length=100)
    ] = None
    phone: Optional[constr(strip_whitespace=True, max_length=20)] = None
    bio: Optional[constr(strip_whitespace=True, max_length=1000)] = None


class UserProfileUpdate(BaseModel):
    """Profile update request schema"""

    first_name: Optional[
        constr(strip_whitespace=True, min_length=1, max_length=100)
    ] = None
    last_name: Optional[
        constr(strip_whitespace=True, min_length=1, max_length=100)
    ] = None
    phone: Optional[constr(strip_whitespace=True, max_length=20)] = None
    bio: Optional[constr(strip_whitespace=True, max_length=1000)] = None
    company_name: Optional[constr(strip_whitespace=True, max_length=200)] = None


class UserProfileResponse(BaseModel):
    """Profile response schema with computed fields"""

    id: str = Field(..., description="User ID")
    email: EmailStr
    first_name: str
    last_name: str
    full_name: str
    avatar_url: Optional[HttpUrl] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    company_name: Optional[str] = None
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserSettingsUpdate(BaseModel):
    """User settings update request"""

    # All optional to allow partial updates
    theme: Optional[
        constr(strip_whitespace=True, to_lower=True)
    ] = None  # light|dark|system
    language: Optional[
        constr(strip_whitespace=True, min_length=2, max_length=10)
    ] = None
    timezone: Optional[
        constr(strip_whitespace=True, min_length=1, max_length=50)
    ] = None
    date_format: Optional[
        constr(strip_whitespace=True, min_length=1, max_length=20)
    ] = None
    time_format: Optional[
        constr(strip_whitespace=True, min_length=2, max_length=10)
    ] = None

    default_currency: Optional[
        constr(strip_whitespace=True, min_length=3, max_length=3)
    ] = None
    default_hourly_rate: Optional[
        float
    ] = None  # non-negative enforced in service/db if needed

    invoice_prefix: Optional[
        constr(strip_whitespace=True, min_length=1, max_length=10)
    ] = None
    invoice_number_start: Optional[int] = Field(
        None, ge=1, description="Starting invoice number"
    )
    payment_terms_days: Optional[int] = Field(
        None, ge=1, le=365, description="Payment terms in days"
    )

    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    marketing_emails: Optional[bool] = None


class UserSettingsResponse(BaseModel):
    """User settings response"""

    theme: str
    language: str
    timezone: str
    date_format: str
    time_format: str

    default_currency: str
    default_hourly_rate: float

    invoice_prefix: str
    invoice_number_start: int
    payment_terms_days: int

    email_notifications: bool
    push_notifications: bool
    marketing_emails: bool

    class Config:
        from_attributes = True


class PasswordChange(BaseModel):
    """Password change schema"""

    current_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=128)


class AvatarUploadResponse(BaseModel):
    """Avatar upload result"""

    url: HttpUrl
    content_type: str
    width: int
    height: int
    size_bytes: int
