"""
Workspace schemas for API validation and serialization
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .common import PaginatedResponse


class WorkspaceBase(BaseModel):
    """Base workspace schema with common fields"""
    name: str = Field(..., min_length=1, max_length=200, description="Workspace name")
    description: Optional[str] = Field(None, max_length=1000, description="Workspace description")


class WorkspaceCreate(WorkspaceBase):
    """Schema for creating a new workspace"""
    is_default: bool = Field(False, description="Whether this is the default workspace")


class WorkspaceUpdate(BaseModel):
    """Schema for updating a workspace"""
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Workspace name")
    description: Optional[str] = Field(None, max_length=1000, description="Workspace description")
    is_active: Optional[bool] = Field(None, description="Whether the workspace is active")


class WorkspaceResponse(WorkspaceBase):
    """Schema for workspace response"""
    id: UUID
    user_id: UUID
    is_active: bool
    is_default: bool
    status: str
    project_count: int
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class WorkspaceWithProjects(WorkspaceResponse):
    """Workspace response with project details"""
    projects: List[dict] = []  # Use dict instead of ProjectResponse to avoid circular imports

    class Config:
        from_attributes = True


class PaginatedWorkspacesResponse(PaginatedResponse[WorkspaceResponse]):
    """Paginated workspace response"""
    pass


class SetActiveWorkspaceRequest(BaseModel):
    """Schema for setting active workspace"""
    workspace_id: UUID = Field(..., description="ID of the workspace to set as active")


class WorkspaceStatsResponse(BaseModel):
    """Workspace statistics response"""
    total_workspaces: int
    active_workspaces: int
    inactive_workspaces: int
    total_projects: int
    active_projects: int
    default_workspace_id: Optional[UUID] = None

    class Config:
        from_attributes = True
