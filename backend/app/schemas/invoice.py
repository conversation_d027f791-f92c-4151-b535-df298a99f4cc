"""
Pydantic schemas for invoice management and billing workflows
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


# Base schemas for common fields
class InvoiceItemBase(BaseModel):
    """Base schema for invoice items."""

    description: str = Field(..., min_length=1, max_length=1000)
    quantity: Decimal = Field(default=Decimal("1.00"), ge=0)
    unit_price: Decimal = Field(..., ge=0)
    item_type: str = Field(
        default="custom", pattern="^(time|milestone|expense|custom)$"
    )
    hours_worked: Optional[Decimal] = Field(None, ge=0)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    work_date: Optional[datetime] = None
    group_name: Optional[str] = Field(None, max_length=255)


class InvoiceItemCreate(InvoiceItemBase):
    """Schema for creating invoice items."""

    time_entry_id: Optional[UUID] = None
    milestone_id: Optional[UUID] = None
    sequence_number: int = Field(default=1, ge=1)


class InvoiceItemUpdate(BaseModel):
    """Schema for updating invoice items."""

    description: Optional[str] = Field(None, min_length=1, max_length=1000)
    quantity: Optional[Decimal] = Field(None, ge=0)
    unit_price: Optional[Decimal] = Field(None, ge=0)
    group_name: Optional[str] = Field(None, max_length=255)


class InvoiceItemResponse(InvoiceItemBase):
    """Schema for invoice item responses."""

    id: UUID
    invoice_id: UUID
    total_price: Decimal
    billing_status: str
    time_entry_id: Optional[UUID] = None
    milestone_id: Optional[UUID] = None
    sequence_number: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Invoice schemas
class InvoiceBase(BaseModel):
    """Base schema for invoices."""

    invoice_number: Optional[str] = None
    due_date: datetime
    project_id: Optional[UUID] = None
    subtotal: Decimal = Field(default=Decimal("0.00"), ge=0)
    tax_rate: Decimal = Field(default=Decimal("0.00"), ge=0, le=100)
    discount_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    currency: str = Field(default="USD", pattern="^(USD|KES|NGN|GHS|ZAR)$")
    notes: Optional[str] = None
    terms_and_conditions: Optional[str] = None
    footer_text: Optional[str] = None
    payment_terms_days: int = Field(default=30, ge=1, le=365)


class InvoiceCreate(InvoiceBase):
    """Schema for creating invoices."""

    client_id: UUID
    items: List[InvoiceItemCreate] = Field(default_factory=list)

    @validator("items")
    def validate_items(cls, v):
        if not v:
            raise ValueError("Invoice must have at least one item")
        return v


class InvoiceUpdate(BaseModel):
    """Schema for updating invoices."""

    due_date: Optional[datetime] = None
    project_id: Optional[UUID] = None
    tax_rate: Optional[Decimal] = Field(None, ge=0, le=100)
    discount_amount: Optional[Decimal] = Field(None, ge=0)
    notes: Optional[str] = None
    terms_and_conditions: Optional[str] = None
    footer_text: Optional[str] = None
    payment_terms_days: Optional[int] = Field(None, ge=1, le=365)


class InvoiceResponse(InvoiceBase):
    """Schema for invoice responses."""

    id: UUID
    invoice_number: str
    status: str
    issue_date: datetime
    sent_at: Optional[datetime] = None
    viewed_at: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    client_id: UUID
    user_id: UUID
    tax_amount: Decimal
    total_amount: Decimal
    payment_link: Optional[str] = None
    payment_token: str
    pdf_generated: bool
    pdf_url: Optional[str] = None
    is_overdue: bool
    days_overdue: int
    total_paid: Decimal
    balance_due: Decimal
    is_fully_paid: bool
    items: List[InvoiceItemResponse] = Field(default_factory=list)
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class InvoiceListResponse(BaseModel):
    """Schema for invoice list responses."""

    id: UUID
    invoice_number: str
    status: str
    issue_date: datetime
    due_date: datetime
    client_id: UUID
    total_amount: Decimal
    currency: str
    is_overdue: bool
    balance_due: Decimal
    created_at: datetime

    class Config:
        from_attributes = True


# Billing workflow schemas
class BillableItemResponse(BaseModel):
    """Schema for billable items from time tracking."""

    id: UUID
    type: str  # "time_entry" or "milestone"
    description: str
    quantity: Decimal  # Hours for time entries, 1 for milestones
    suggested_rate: Decimal
    suggested_total: Decimal
    work_date: Optional[datetime] = None
    project_id: UUID
    project_title: str
    client_name: str
    billing_status: str

    class Config:
        from_attributes = True


class BillableItemsResponse(BaseModel):
    """Schema for billable items collection."""

    project_id: Optional[UUID] = None
    total_unbilled_amount: Decimal
    total_hours: Decimal
    items: List[BillableItemResponse]


class InvoiceFromProjectRequest(BaseModel):
    """Schema for creating invoice from project billable items."""

    project_id: UUID
    item_ids: List[UUID] = Field(..., min_items=1)
    due_date: datetime
    tax_rate: Decimal = Field(default=Decimal("0.00"), ge=0, le=100)
    discount_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    notes: Optional[str] = None
    terms_and_conditions: Optional[str] = None
    group_by_date: bool = Field(default=True)
    group_by_task: bool = Field(default=False)


class BulkBillRequest(BaseModel):
    """Schema for bulk billing operations."""

    time_entry_ids: List[UUID] = Field(default_factory=list)
    milestone_ids: List[UUID] = Field(default_factory=list)

    @validator("time_entry_ids", "milestone_ids")
    def validate_at_least_one(cls, v, values):
        if (
            not v
            and not values.get("time_entry_ids", [])
            and not values.get("milestone_ids", [])
        ):
            raise ValueError("Must specify at least one time entry or milestone")
        return v


# Payment schemas
class PaymentTransactionResponse(BaseModel):
    """Schema for payment transaction responses."""

    id: UUID
    invoice_id: UUID
    transaction_reference: str
    payment_method: str
    payment_gateway: str
    amount: Decimal
    currency: str
    status: str
    initiated_at: datetime
    completed_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    gateway_fee: Optional[Decimal] = None
    net_amount: Optional[Decimal] = None

    class Config:
        from_attributes = True


class PaymentLinkRequest(BaseModel):
    """Schema for payment link generation."""

    success_url: Optional[str] = None
    cancel_url: Optional[str] = None
    webhook_url: Optional[str] = None


class PaymentLinkResponse(BaseModel):
    """Schema for payment link response."""

    payment_link: str
    payment_token: str
    expires_at: Optional[datetime] = None


class PaymentStatusResponse(BaseModel):
    """Schema for payment status response."""

    invoice_id: UUID
    status: str
    total_amount: Decimal
    total_paid: Decimal
    balance_due: Decimal
    last_payment_at: Optional[datetime] = None
    transactions: List[PaymentTransactionResponse] = Field(default_factory=list)


# Pagination schemas
class PaginatedInvoicesResponse(BaseModel):
    """Schema for paginated invoice responses."""

    items: List[InvoiceListResponse]
    total: int
    page: int
    per_page: int
    pages: int


class PaginatedBillableItemsResponse(BaseModel):
    """Schema for paginated billable items responses."""

    items: List[BillableItemResponse]
    total: int
    page: int
    size: int
    pages: int
    total_unbilled_amount: Decimal


# Invoice generation options
class InvoiceGenerationOptions(BaseModel):
    """Schema for invoice generation options."""

    template_id: Optional[str] = None
    include_company_logo: bool = Field(default=True)
    include_payment_instructions: bool = Field(default=True)
    include_time_summary: bool = Field(default=True)
    include_productivity_insights: bool = Field(default=False)
    language: str = Field(
        default="en", pattern="^(en|sw|fr|pt)$"
    )  # English, Swahili, French, Portuguese


class InvoicePDFResponse(BaseModel):
    """Schema for PDF generation response."""

    pdf_url: str
    generated_at: datetime
    file_size_bytes: int


# Client portal schemas
class ClientInvoicePortalResponse(BaseModel):
    """Schema for client portal invoice view."""

    invoice_number: str
    status: str
    issue_date: datetime
    due_date: datetime
    total_amount: Decimal
    currency: str
    balance_due: Decimal
    is_overdue: bool
    company_details: dict
    billing_address: dict
    items: List[InvoiceItemResponse]
    payment_methods: List[str]
    can_pay_online: bool


class ClientPaymentInitiationRequest(BaseModel):
    """Schema for client payment initiation."""

    payment_method: str = Field(..., pattern="^(card|bank_transfer|mobile_money)$")
    customer_email: Optional[str] = None
    customer_phone: Optional[str] = None
    success_url: Optional[str] = None
    cancel_url: Optional[str] = None
