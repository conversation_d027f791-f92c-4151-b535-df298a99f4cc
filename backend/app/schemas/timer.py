"""
Timer schemas for live timer API requests and responses
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class TimerStartRequest(BaseModel):
    """Request schema for starting a timer."""

    project_id: uuid.UUID
    description: Optional[str] = Field(None, max_length=1000)
    task_name: Optional[str] = Field(None, max_length=255)
    device_id: Optional[str] = Field("web", max_length=50)
    mood_rating: Optional[int] = Field(None, ge=1, le=5)
    focus_level: Optional[int] = Field(None, ge=1, le=5)


class TimerResponse(BaseModel):
    """Response schema for timer operations."""

    timer_id: str
    project_id: str
    description: str
    task_name: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    is_active: bool
    is_paused: bool
    current_duration_minutes: int
    device_id: Optional[str] = None
    productivity_score: Optional[float] = None
    billable_amount: Optional[float] = None
    interruption_count: Optional[int] = 0
    mood_rating: Optional[int] = None
    focus_level: Optional[int] = None


class TimerStatusResponse(BaseModel):
    """Response schema for timer status check."""

    has_active_timer: bool
    timer: Optional[TimerResponse] = None


class TimerHeartbeatRequest(BaseModel):
    """Request schema for timer heartbeat."""

    timer_id: Optional[str] = None
    device_id: Optional[str] = "web"
    interruption_occurred: Optional[bool] = False


class TimerHeartbeatResponse(BaseModel):
    """Response schema for timer heartbeat."""

    success: bool
    message: str
    timer_status: Optional[Dict[str, Any]] = None


# Enhanced Time Entry Schemas with Advanced Features
class TimeEntryCreateAdvanced(BaseModel):
    """Enhanced time entry creation with advanced tracking."""

    project_id: uuid.UUID
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: int = Field(..., ge=1)
    description: Optional[str] = None
    task_name: Optional[str] = Field(None, max_length=255)
    hourly_rate: Optional[float] = Field(None, ge=0)
    is_billable: bool = True
    status: str = Field(default="draft", pattern="^(draft|submitted|approved|billed)$")
    work_date: datetime

    # Advanced tracking fields
    timer_started_from: Optional[str] = Field(None, max_length=50)
    productivity_score: Optional[float] = Field(None, ge=1.0, le=10.0)
    break_duration_minutes: Optional[int] = Field(0, ge=0)
    tags: Optional[str] = None  # JSON string
    mood_rating: Optional[int] = Field(None, ge=1, le=5)
    focus_level: Optional[int] = Field(None, ge=1, le=5)
    interruption_count: Optional[int] = Field(0, ge=0)


class TimeEntryResponseAdvanced(BaseModel):
    """Enhanced time entry response with advanced tracking."""

    id: str
    project_id: str
    user_id: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: int
    description: Optional[str] = None
    task_name: Optional[str] = None
    hourly_rate: Optional[float] = None
    billable_amount: Optional[float] = None
    is_billable: bool
    status: str
    work_date: datetime
    created_at: datetime
    updated_at: datetime

    # Advanced tracking fields
    timer_started_from: Optional[str] = None
    productivity_score: Optional[float] = None
    break_duration_minutes: Optional[int] = 0
    tags: Optional[str] = None
    mood_rating: Optional[int] = None
    focus_level: Optional[int] = None
    interruption_count: Optional[int] = 0

    # Timer state (if active)
    is_timer_active: Optional[bool] = False
    timer_device_id: Optional[str] = None
    last_heartbeat: Optional[datetime] = None
    timer_paused_at: Optional[datetime] = None
    total_pause_duration: Optional[int] = 0

    # Computed fields
    duration_hours: float

    class Config:
        from_attributes = True


class BulkTimeEntryOperation(BaseModel):
    """Schema for bulk time entry operations."""

    operation: str = Field(..., pattern="^(update|delete|approve|submit)$")
    entry_ids: list[str] = Field(..., min_items=1, max_items=100)
    update_data: Optional[dict] = None


class BulkTimeEntryResponse(BaseModel):
    """Response for bulk time entry operations."""

    success_count: int
    failed_count: int
    failed_entries: list[dict] = []
    message: str


class TimeEntryTemplate(BaseModel):
    """Schema for time entry templates."""

    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    task_name: Optional[str] = None
    default_duration_minutes: Optional[int] = Field(None, ge=1)
    tags: Optional[str] = None
    is_billable: bool = True


class TimeEntryTemplateResponse(TimeEntryTemplate):
    """Response schema for time entry templates."""

    id: str
    user_id: str
    created_at: datetime
    updated_at: datetime
    usage_count: int = 0

    class Config:
        from_attributes = True


class TimeEntrySuggestion(BaseModel):
    """Schema for smart time entry suggestions."""

    description: str
    task_name: Optional[str] = None
    estimated_duration_minutes: Optional[int] = None
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    based_on: str  # "history", "project_pattern", "ai_analysis"


class TimeEntrySuggestionsResponse(BaseModel):
    """Response schema for time entry suggestions."""

    suggestions: list[TimeEntrySuggestion]
    project_id: str
    context: dict = {}


class TimeEntryValidationRequest(BaseModel):
    """Request schema for time entry validation."""

    project_id: uuid.UUID
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: int
    work_date: datetime
    user_id: Optional[uuid.UUID] = None  # For admin validation


class TimeEntryValidationResponse(BaseModel):
    """Response schema for time entry validation."""

    is_valid: bool
    warnings: list[str] = []
    errors: list[str] = []
    suggestions: list[str] = []
    overlapping_entries: list[dict] = []


# Day 9 Advanced Timer Schemas
class SmartTimerRequest(BaseModel):
    """Request schema for starting a smart timer"""

    project_id: UUID
    description: str = ""
    device_id: str = "web"
    auto_resolve_conflicts: bool = True
    context_data: Optional[Dict] = None


class SmartTimerResponse(BaseModel):
    """Response schema for smart timer operations"""

    status: str
    timer: Optional[Dict] = None
    conflicts: Optional[List[Dict]] = None
    suggested_resolutions: Optional[List[Dict]] = None
    suggestions: Optional[List[Dict]] = None
    estimated_duration: Optional[float] = None


class TimerConflictResponse(BaseModel):
    """Response schema for timer conflicts"""

    timer_id: str
    device_id: str
    start_time: str
    description: str
    project_id: Optional[str] = None
    conflict_type: str


class ProductivitySuggestionResponse(BaseModel):
    """Response schema for productivity suggestions"""

    type: str
    message: str
    priority: str
    action: str


class WebhookSetupRequest(BaseModel):
    """Request schema for webhook setup"""

    webhook_url: str
    events: List[str] = Field(
        default=["timer_started", "timer_stopped", "timer_paused", "productivity_alert"]
    )


class AdvancedAnalyticsResponse(BaseModel):
    """Response schema for advanced analytics"""

    total_hours: float
    daily_average: float
    productivity_score: int
    peak_hours: List[Dict]
    project_distribution: Dict[str, float]
    trends: Dict
    focus_patterns: Dict
    efficiency_metrics: Dict
    recommendations: List[str]
