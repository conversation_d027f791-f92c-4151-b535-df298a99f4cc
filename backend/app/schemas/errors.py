"""Error response schemas for comprehensive API documentation"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ErrorDetail(BaseModel):
    """Individual error detail

    Example:
        {
            "field": "email",
            "message": "Invalid email format",
            "code": "INVALID_EMAIL"
        }
    """

    field: Optional[str] = Field(
        None,
        description="Field name that caused the error (for validation errors)",
        examples=["email", "password", "first_name"],
    )
    message: str = Field(
        ...,
        description="Human-readable error message",
        examples=["Invalid email format", "Password too weak", "Field is required"],
    )
    code: Optional[str] = Field(
        None,
        description="Machine-readable error code",
        examples=["INVALID_EMAIL", "WEAK_PASSWORD", "REQUIRED_FIELD"],
    )


class ErrorResponse(BaseModel):
    """Standard error response schema

    Examples:
        Validation Error:
        {
            "detail": "Validation failed",
            "errors": [
                {
                    "field": "email",
                    "message": "Invalid email format",
                    "code": "INVALID_EMAIL"
                }
            ],
            "error_code": "VALIDATION_ERROR",
            "request_id": "req_123456789"
        }

        Authentication Error:
        {
            "detail": "Invalid credentials",
            "error_code": "INVALID_CREDENTIALS",
            "request_id": "req_123456789"
        }
    """

    detail: str = Field(
        ...,
        description="Main error message",
        examples=[
            "Validation failed",
            "Invalid credentials",
            "Resource not found",
            "Access denied",
        ],
    )
    errors: Optional[List[ErrorDetail]] = Field(
        None, description="List of specific error details (for validation errors)"
    )
    error_code: Optional[str] = Field(
        None,
        description="Machine-readable error code",
        examples=[
            "VALIDATION_ERROR",
            "INVALID_CREDENTIALS",
            "NOT_FOUND",
            "ACCESS_DENIED",
            "RATE_LIMITED",
            "INTERNAL_ERROR",
        ],
    )
    request_id: Optional[str] = Field(
        None,
        description="Unique request identifier for debugging",
        examples=["req_123456789", "req_abcdef123"],
    )


class ValidationErrorResponse(ErrorResponse):
    """Validation error response (422)

    Example:
        {
            "detail": "Validation failed",
            "errors": [
                {
                    "field": "email",
                    "message": "Invalid email format",
                    "code": "INVALID_EMAIL"
                },
                {
                    "field": "password",
                    "message": "Password must contain at least one uppercase letter",
                    "code": "WEAK_PASSWORD"
                }
            ],
            "error_code": "VALIDATION_ERROR",
            "request_id": "req_123456789"
        }
    """

    pass


class AuthenticationErrorResponse(ErrorResponse):
    """Authentication error response (401)

    Example:
        {
            "detail": "Invalid credentials",
            "error_code": "INVALID_CREDENTIALS",
            "request_id": "req_123456789"
        }
    """

    pass


class AuthorizationErrorResponse(ErrorResponse):
    """Authorization error response (403)

    Example:
        {
            "detail": "Access denied. Insufficient permissions",
            "error_code": "ACCESS_DENIED",
            "request_id": "req_123456789"
        }
    """

    pass


class NotFoundErrorResponse(ErrorResponse):
    """Not found error response (404)

    Example:
        {
            "detail": "User not found",
            "error_code": "NOT_FOUND",
            "request_id": "req_123456789"
        }
    """

    pass


class ConflictErrorResponse(ErrorResponse):
    """Conflict error response (409)

    Example:
        {
            "detail": "User with this email already exists",
            "error_code": "RESOURCE_CONFLICT",
            "request_id": "req_123456789"
        }
    """

    pass


class RateLimitErrorResponse(ErrorResponse):
    """Rate limit error response (429)

    Example:
        {
            "detail": "Rate limit exceeded. Try again in 60 seconds",
            "error_code": "RATE_LIMITED",
            "request_id": "req_123456789",
            "retry_after": 60
        }
    """

    retry_after: Optional[int] = Field(
        None, description="Seconds to wait before retrying", examples=[60, 300, 3600]
    )


class InternalServerErrorResponse(ErrorResponse):
    """Internal server error response (500)

    Example:
        {
            "detail": "An internal server error occurred",
            "error_code": "INTERNAL_ERROR",
            "request_id": "req_123456789"
        }
    """

    pass


# Common error responses for OpenAPI documentation
COMMON_RESPONSES = {
    400: {
        "description": "Bad Request - Invalid request data",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "Invalid request data",
                    "error_code": "BAD_REQUEST",
                    "request_id": "req_123456789",
                }
            }
        },
    },
    401: {
        "description": "Unauthorized - Invalid or missing authentication",
        "model": AuthenticationErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "Invalid credentials",
                    "error_code": "INVALID_CREDENTIALS",
                    "request_id": "req_123456789",
                }
            }
        },
    },
    403: {
        "description": "Forbidden - Insufficient permissions",
        "model": AuthorizationErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "Access denied. Insufficient permissions",
                    "error_code": "ACCESS_DENIED",
                    "request_id": "req_123456789",
                }
            }
        },
    },
    404: {
        "description": "Not Found - Resource does not exist",
        "model": NotFoundErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "Resource not found",
                    "error_code": "NOT_FOUND",
                    "request_id": "req_123456789",
                }
            }
        },
    },
    409: {
        "description": "Conflict - Resource already exists",
        "model": ConflictErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "Resource already exists",
                    "error_code": "RESOURCE_CONFLICT",
                    "request_id": "req_123456789",
                }
            }
        },
    },
    422: {
        "description": "Validation Error - Invalid input data",
        "model": ValidationErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "Validation failed",
                    "errors": [
                        {
                            "field": "email",
                            "message": "Invalid email format",
                            "code": "INVALID_EMAIL",
                        }
                    ],
                    "error_code": "VALIDATION_ERROR",
                    "request_id": "req_123456789",
                }
            }
        },
    },
    429: {
        "description": "Too Many Requests - Rate limit exceeded",
        "model": RateLimitErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "Rate limit exceeded. Try again in 60 seconds",
                    "error_code": "RATE_LIMITED",
                    "request_id": "req_123456789",
                    "retry_after": 60,
                }
            }
        },
    },
    500: {
        "description": "Internal Server Error - Something went wrong",
        "model": InternalServerErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "detail": "An internal server error occurred",
                    "error_code": "INTERNAL_ERROR",
                    "request_id": "req_123456789",
                }
            }
        },
    },
}
