"""
Project management schemas for API requests and responses
"""

import uuid
from datetime import datetime, date
from decimal import Decimal
from typing import List, Optional, Union

from pydantic import BaseModel, ConfigDict, Field, field_validator


# Project Schemas
class ProjectBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    billing_type: str = Field(
        default="time_and_materials", pattern="^(time_and_materials|fixed_price)$"
    )
    total_budget: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    estimated_hours: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    hourly_rate: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    currency: str = Field(default="USD", pattern="^(USD|KES|NGN|GHS|ZAR)$")
    status: str = Field(
        default="active", pattern="^(draft|active|completed|on_hold|cancelled)$"
    )
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    deadline: Optional[datetime] = None
    is_billable: bool = True
    is_active: bool = True

    # Project-specific client information
    client_name: Optional[str] = Field(None, max_length=255)
    contact_person: Optional[str] = Field(None, max_length=255)
    client_email: Optional[str] = Field(None, max_length=255)
    client_phone: Optional[str] = Field(None, max_length=50)
    client_address: Optional[str] = None
    client_industry: Optional[str] = Field(None, max_length=255)
    client_company_size: Optional[str] = Field(None, pattern="^(|Small|Medium|Large|Enterprise)$")
    client_priority: Optional[str] = Field(None, pattern="^(|low|medium|high)$")
    client_status: Optional[str] = Field(None, pattern="^(|active|inactive|completed|archived)$")
    client_notes: Optional[str] = None


class ProjectCreate(ProjectBase):
    client_id: Union[str, uuid.UUID]
    workspace_id: Union[str, uuid.UUID]

    @field_validator("client_id")
    @classmethod
    def validate_client_id(cls, v):
        if isinstance(v, str):
            try:
                return uuid.UUID(v)
            except ValueError:
                raise ValueError("Invalid UUID format for client_id")
        return v

    @field_validator("workspace_id")
    @classmethod
    def validate_workspace_id(cls, v):
        if isinstance(v, str):
            try:
                return uuid.UUID(v)
            except ValueError:
                raise ValueError("Invalid UUID format for workspace_id")
        return v


class ProjectUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    billing_type: Optional[str] = Field(
        None, pattern="^(time_and_materials|fixed_price)$"
    )
    total_budget: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    estimated_hours: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    hourly_rate: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    status: Optional[str] = Field(
        None, pattern="^(|draft|active|completed|on_hold|cancelled)$"
    )
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    deadline: Optional[datetime] = None
    is_billable: Optional[bool] = None
    is_active: Optional[bool] = None

    # Project-specific client information
    client_name: Optional[str] = Field(None, max_length=255)
    contact_person: Optional[str] = Field(None, max_length=255)
    client_email: Optional[str] = Field(None, max_length=255)
    client_phone: Optional[str] = Field(None, max_length=50)
    client_address: Optional[str] = None
    client_industry: Optional[str] = Field(None, max_length=255)
    client_company_size: Optional[str] = Field(None, pattern="^(|Small|Medium|Large|Enterprise)$")
    client_priority: Optional[str] = Field(None, pattern="^(|low|medium|high)$")
    client_status: Optional[str] = Field(None, pattern="^(|active|inactive|completed|archived)$")
    client_notes: Optional[str] = None


class ProjectResponse(ProjectBase):
    id: Union[str, uuid.UUID]
    client_id: Union[str, uuid.UUID]
    user_id: Union[str, uuid.UUID]
    workspace_id: Union[str, uuid.UUID]
    created_at: datetime
    updated_at: datetime
    total_logged_hours: Decimal
    total_billable_amount: Decimal
    completion_percentage: float

    model_config = ConfigDict(from_attributes=True)


class ProjectWithDetails(ProjectResponse):
    milestones: List["MilestoneResponse"] = []
    time_entries_count: int = 0

    model_config = ConfigDict(from_attributes=True)


# Forward declarations to avoid circular imports
class ClientSummary(BaseModel):
    id: Union[str, uuid.UUID]
    name: str
    email: Optional[str] = None
    company: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class TimeEntrySummary(BaseModel):
    id: Union[str, uuid.UUID]
    description: Optional[str] = None
    duration_hours: Decimal
    is_billable: bool
    work_date: datetime

    model_config = ConfigDict(from_attributes=True)


class ProjectDetailResponse(ProjectResponse):
    client: ClientSummary
    milestones: List["MilestoneResponse"] = []
    time_entries: List[TimeEntrySummary] = []
    time_entries_count: int = 0


# Project Milestone Schemas
class MilestoneBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    estimated_hours: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    payment_amount: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    due_date: Optional[date] = None
    status: str = Field(
        default="pending", pattern="^(pending|in_progress|completed|approved)$"
    )
    sequence_number: int = Field(default=1, ge=1)


class MilestoneCreate(MilestoneBase):
    pass


class MilestoneUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    estimated_hours: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    payment_amount: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    due_date: Optional[date] = None
    status: Optional[str] = Field(
        None, pattern="^(pending|in_progress|completed|approved)$"
    )
    sequence_number: Optional[int] = Field(None, ge=1)


class MilestoneResponse(MilestoneBase):
    id: Union[str, uuid.UUID]
    project_id: Union[str, uuid.UUID]
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    is_overdue: bool
    logged_hours: Decimal

    model_config = ConfigDict(from_attributes=True)


# Time Entry Schemas
class TimeEntryBase(BaseModel):
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: int = Field(..., ge=1)
    description: Optional[str] = None
    task_name: Optional[str] = Field(None, max_length=255)
    hourly_rate: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    is_billable: bool = True
    status: str = Field(default="draft", pattern="^(draft|submitted|approved|billed)$")
    work_date: datetime


class TimeEntryCreate(TimeEntryBase):
    project_id: Union[str, uuid.UUID]

    @field_validator("project_id")
    @classmethod
    def validate_project_id(cls, v):
        if isinstance(v, str):
            try:
                return uuid.UUID(v)
            except ValueError:
                raise ValueError("Invalid UUID format for project_id")
        return v


class TimeEntryUpdate(BaseModel):
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: Optional[int] = Field(None, ge=1)
    description: Optional[str] = None
    task_name: Optional[str] = Field(None, max_length=255)
    hourly_rate: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    is_billable: Optional[bool] = None
    status: Optional[str] = Field(None, pattern="^(draft|submitted|approved|billed)$")
    work_date: Optional[datetime] = None


class TimeEntryResponse(TimeEntryBase):
    id: Union[str, uuid.UUID]
    project_id: Union[str, uuid.UUID]
    user_id: Union[str, uuid.UUID]
    billable_amount: Optional[Decimal] = None
    duration_hours: Decimal
    created_at: datetime
    updated_at: datetime
    # Advanced timer fields
    productivity_score: Optional[float] = None
    interruption_count: Optional[int] = 0
    break_duration_minutes: Optional[int] = 0
    focus_level: Optional[int] = None
    mood_rating: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)


# Project Planning Schemas
class ProjectPlanningRequest(BaseModel):
    total_budget: Decimal = Field(..., gt=0, decimal_places=2)
    internal_hourly_rate: Decimal = Field(..., gt=0, decimal_places=2)
    deadline: Optional[datetime] = None
    milestone_count: Optional[int] = Field(default=3, ge=1, le=10)
    project_template: Optional[str] = Field(
        default="custom",
        description="Template type: web_app, mobile_app, landing_page, api, custom",
    )
    planning_mode: str = Field(
        default="budget_to_hours", description="budget_to_hours or hours_to_rate"
    )
    estimated_total_hours: Optional[Decimal] = Field(
        None, gt=0, description="Required for hours_to_rate mode"
    )


class MilestoneTemplate(BaseModel):
    sequence_number: int
    title: str
    description: str
    estimated_hours: float
    payment_percentage: float
    typical_deliverables: List[str]


class ProjectTemplate(BaseModel):
    name: str
    description: str
    typical_duration_weeks: int
    milestones: List[MilestoneTemplate]
    recommended_hourly_rate_min: Decimal
    recommended_hourly_rate_max: Decimal


class ProjectPlanningResponse(BaseModel):
    calculated_hours: Optional[Decimal] = None
    effective_rate: Optional[Decimal] = None
    suggested_milestones: List[dict]
    budget_breakdown: dict
    timeline_analysis: dict
    warnings: List[str] = []
    template_used: Optional[str] = None
    profitability_analysis: dict
    risk_assessment: dict


# Pagination Schemas
class PaginatedProjectsResponse(BaseModel):
    total: int
    page: int
    per_page: int
    items: List[ProjectResponse]


class PaginatedMilestonesResponse(BaseModel):
    total: int
    page: int
    per_page: int
    items: List[MilestoneResponse]


class PaginatedTimeEntriesResponse(BaseModel):
    total: int
    page: int
    per_page: int
    items: List[TimeEntryResponse]


# Forward reference resolution
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from app.schemas.client import ClientResponse

# Rebuild models after all imports
try:
    ProjectWithDetails.model_rebuild()
except Exception:
    pass  # Ignore rebuild errors during import
