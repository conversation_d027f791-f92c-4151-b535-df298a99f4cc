"""
Payment schemas for API serialization
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, Field


class PaymentBase(BaseModel):
    """Base payment schema"""

    amount: Decimal = Field(..., gt=0, description="Payment amount")
    currency: str = Field(..., min_length=3, max_length=3, description="Currency code")
    gateway: str = Field(..., description="Payment gateway used")
    reference: Optional[str] = Field(None, description="Payment reference")
    description: Optional[str] = Field(None, description="Payment description")


class PaymentCreate(PaymentBase):
    """Schema for creating a payment"""

    invoice_id: str = Field(..., description="Invoice ID")


class PaymentUpdate(BaseModel):
    """Schema for updating a payment"""

    status: Optional[str] = Field(None, description="Payment status")
    gateway_response: Optional[dict] = Field(None, description="Gateway response data")


class PaymentResponse(PaymentBase):
    """Schema for payment response"""

    id: str
    invoice_id: str
    status: str
    gateway_response: Optional[dict] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
