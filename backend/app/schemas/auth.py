"""
Authentication-related Pydantic schemas
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator


class UserRegisterRequest(BaseModel):
    """User registration request schema

    Example:
        {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "phone": "+234-************"
        }
    """

    email: EmailStr = Field(
        ...,
        description="User's email address",
        examples=["<EMAIL>", "<EMAIL>"],
    )
    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="User's password (min 8 chars, must contain uppercase, lowercase, digit)",
        examples=["SecurePass123!"],
    )
    first_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="User's first name",
        examples=["<PERSON>", "<PERSON>"],
    )
    last_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="User's last name",
        examples=["<PERSON><PERSON>", "<PERSON>"],
    )
    phone: Optional[str] = Field(
        None,
        max_length=20,
        description="User's phone number (optional)",
        examples=["+234-************", "******-123-4567"],
    )

    @field_validator("password")
    @classmethod
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        return v

    @field_validator("first_name", "last_name")
    @classmethod
    def validate_names(cls, v):
        """Validate name fields"""
        if not v.strip():
            raise ValueError("Name cannot be empty or just whitespace")
        return v.strip().title()


class UserLoginRequest(BaseModel):
    """User login request schema

    Example:
        {
            "email_or_username": "<EMAIL>",
            "password": "SecurePass123!"
        }
    """

    email_or_username: str = Field(
        ...,
        description="User's email address or username",
        examples=["<EMAIL>", "johndoe", "<EMAIL>"],
    )
    password: str = Field(
        ..., description="User's password", examples=["SecurePass123!"]
    )


class TokenResponse(BaseModel):
    """JWT token response schema

    Example:
        {
            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "token_type": "bearer",
            "expires_in": 3600
        }
    """

    access_token: str = Field(
        ...,
        description="JWT access token for API authentication",
        examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."],
    )
    refresh_token: str = Field(
        ...,
        description="JWT refresh token for obtaining new access tokens",
        examples=["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."],
    )
    token_type: str = Field(
        default="bearer",
        description="Token type (always 'bearer')",
        examples=["bearer"],
    )
    expires_in: int = Field(
        ...,
        description="Access token expiration time in seconds",
        examples=[3600, 7200],
    )


class RefreshTokenRequest(BaseModel):
    """Refresh token request schema"""

    refresh_token: str = Field(..., description="Refresh token")


class PasswordResetRequest(BaseModel):
    """Password reset request schema"""

    email: EmailStr = Field(..., description="User's email address")


class PasswordResetConfirm(BaseModel):
    """Password reset confirmation schema"""

    token: str = Field(..., description="Password reset token")
    new_password: str = Field(
        ..., min_length=8, max_length=128, description="New password"
    )

    @field_validator("new_password")
    @classmethod
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        return v


class EmailVerificationRequest(BaseModel):
    """Email verification request schema"""

    token: str = Field(..., description="Email verification token")


class UserResponse(BaseModel):
    """User response schema"""

    id: str = Field(..., description="User's unique identifier")
    email: str = Field(..., description="User's email address")
    first_name: str = Field(..., description="User's first name")
    last_name: str = Field(..., description="User's last name")
    full_name: str = Field(..., description="User's full name")
    avatar_url: Optional[str] = Field(None, description="User's avatar URL")
    phone: Optional[str] = Field(None, description="User's phone number")
    bio: Optional[str] = Field(None, description="User's bio")
    is_active: bool = Field(..., description="Whether user account is active")
    is_verified: bool = Field(..., description="Whether user email is verified")
    oauth_provider: Optional[str] = Field(None, description="OAuth provider (google, github, etc.)")
    oauth_id: Optional[str] = Field(None, description="OAuth provider user ID")
    created_at: datetime = Field(..., description="Account creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class AuthResponse(BaseModel):
    """Authentication response schema"""

    user: UserResponse = Field(..., description="User information")
    tokens: TokenResponse = Field(..., description="Authentication tokens")


class MessageResponse(BaseModel):
    """Generic message response schema"""

    message: str = Field(..., description="Response message")
    success: bool = Field(default=True, description="Operation success status")


class OAuthCallbackRequest(BaseModel):
    """OAuth callback request schema"""
    code: str = Field(..., description="Authorization code from OAuth provider")
    state: Optional[str] = Field(None, description="State parameter for CSRF protection")


class OAuthUserInfo(BaseModel):
    """OAuth user information schema"""
    id: str = Field(..., description="OAuth provider user ID")
    email: str = Field(..., description="User email")
    name: str = Field(..., description="User full name")
    first_name: Optional[str] = Field(None, description="User first name")
    last_name: Optional[str] = Field(None, description="User last name")
    avatar_url: Optional[str] = Field(None, description="User avatar URL")
    provider: str = Field(..., description="OAuth provider (google, github)")


class OAuthLoginResponse(BaseModel):
    """OAuth login response schema"""
    authorization_url: str = Field(..., description="URL to redirect user for OAuth authorization")
