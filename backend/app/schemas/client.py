"""
Client schemas for basic CRM operations
"""

import uuid
from datetime import datetime
from typing import List, Optional, Union

from pydantic import BaseModel, ConfigDict, EmailStr, Field, constr, field_validator


class ClientBase(BaseModel):
    name: constr(strip_whitespace=True, min_length=1, max_length=200)
    email: Optional[EmailStr] = None
    phone: Optional[constr(strip_whitespace=True, max_length=50)] = None
    company: Optional[constr(strip_whitespace=True, max_length=255)] = None
    position: Optional[constr(strip_whitespace=True, max_length=100)] = None
    address_line1: Optional[constr(strip_whitespace=True, max_length=255)] = None
    address_line2: Optional[constr(strip_whitespace=True, max_length=255)] = None
    city: Optional[constr(strip_whitespace=True, max_length=100)] = None
    state: Optional[constr(strip_whitespace=True, max_length=100)] = None
    postal_code: Optional[constr(strip_whitespace=True, max_length=20)] = None
    country: Optional[constr(strip_whitespace=True, max_length=100)] = None
    notes: Optional[constr(strip_whitespace=True, max_length=2000)] = None
    is_active: Optional[bool] = True


class ClientCreate(ClientBase):
    workspace_id: Union[str, uuid.UUID]

    @field_validator("workspace_id")
    @classmethod
    def validate_workspace_id(cls, v):
        if isinstance(v, str):
            try:
                return uuid.UUID(v)
            except ValueError:
                raise ValueError("Invalid UUID format for workspace_id")
        return v


class ClientUpdate(BaseModel):
    name: Optional[constr(strip_whitespace=True, min_length=1, max_length=200)] = None
    email: Optional[EmailStr] = None
    phone: Optional[constr(strip_whitespace=True, max_length=50)] = None
    company: Optional[constr(strip_whitespace=True, max_length=255)] = None
    position: Optional[constr(strip_whitespace=True, max_length=100)] = None
    address_line1: Optional[constr(strip_whitespace=True, max_length=255)] = None
    address_line2: Optional[constr(strip_whitespace=True, max_length=255)] = None
    city: Optional[constr(strip_whitespace=True, max_length=100)] = None
    state: Optional[constr(strip_whitespace=True, max_length=100)] = None
    postal_code: Optional[constr(strip_whitespace=True, max_length=20)] = None
    country: Optional[constr(strip_whitespace=True, max_length=100)] = None
    notes: Optional[constr(strip_whitespace=True, max_length=2000)] = None
    is_active: Optional[bool] = None


class ClientResponse(ClientBase):
    id: Union[str, uuid.UUID]
    user_id: Union[str, uuid.UUID]
    workspace_id: Union[str, uuid.UUID]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Forward declarations to avoid circular imports
class ProjectSummary(BaseModel):
    id: Union[str, uuid.UUID]
    name: str
    status: str
    billing_type: str

    model_config = ConfigDict(from_attributes=True)


class InvoiceSummary(BaseModel):
    id: Union[str, uuid.UUID]
    invoice_number: str
    status: str
    total_amount: str

    model_config = ConfigDict(from_attributes=True)


class ClientDetailResponse(ClientResponse):
    projects: List[ProjectSummary] = []
    invoices: List[InvoiceSummary] = []


class PaginatedClientsResponse(BaseModel):
    items: List[ClientResponse]
    total: int
    page: int
    per_page: int
    pages: int
