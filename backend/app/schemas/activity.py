"""
Activity Log schemas for API responses
"""

import uuid
from datetime import datetime
from typing import List, Optional, Union

from pydantic import BaseModel, ConfigDict, Field


class ActivityLogResponse(BaseModel):
    """Response schema for activity log entries"""

    id: Union[str, uuid.UUID]
    user_id: Union[str, uuid.UUID]
    entity_type: str
    entity_id: str
    action: str
    details: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class PaginatedActivityResponse(BaseModel):
    """Paginated response for activity logs"""

    total: int
    page: int
    per_page: int
    items: List[ActivityLogResponse]
