"""
Time entry schemas for API serialization
"""

from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field


class TimeEntryBase(BaseModel):
    """Base time entry schema"""

    description: str = Field(..., min_length=1, max_length=500)
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_minutes: Optional[int] = Field(None, ge=0)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    is_billable: bool = True
    tags: Optional[List[str]] = Field(default_factory=list)


class TimeEntryCreate(TimeEntryBase):
    """Schema for creating a time entry"""

    project_id: str = Field(..., description="Project ID")


class TimeEntryUpdate(BaseModel):
    """Schema for updating a time entry"""

    description: Optional[str] = Field(None, min_length=1, max_length=500)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: Optional[int] = Field(None, ge=0)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    is_billable: Optional[bool] = None
    tags: Optional[List[str]] = None


class TimeEntryResponse(TimeEntryBase):
    """Schema for time entry response"""

    id: str
    project_id: str
    user_id: str
    total_amount: Optional[Decimal] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
