#!/usr/bin/env python3
"""
Scheduled tasks for DevHQ Backend
Handles periodic cleanup and maintenance tasks
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from app.config import settings
from app.core.background_tasks import background_task_manager

logger = logging.getLogger(__name__)


async def cleanup_unverified_accounts():
    """Remove unverified accounts older than 24 hours"""
    logger.info("🧹 Starting cleanup of unverified accounts")
    
    try:
        # Create database engine
        engine = create_engine(settings.database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # Calculate cutoff time (24 hours ago)
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            # Delete unverified accounts older than 24 hours
            result = db.execute(
                text("""
                    DELETE FROM users 
                    WHERE is_verified = false 
                    AND created_at < :cutoff_time
                """),
                {"cutoff_time": cutoff_time}
            )
            
            deleted_count = result.rowcount
            db.commit()
            
            logger.info(f"✅ Cleaned up {deleted_count} unverified accounts older than 24 hours")
            return deleted_count
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            db.rollback()
            raise
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"💥 Cleanup task failed: {e}")
        raise


# Schedule the cleanup task to run daily
@background_task(name="daily_cleanup", priority=2)  # NORMAL priority
async def schedule_daily_cleanup():
    """Schedule daily cleanup tasks"""
    logger.info("⏰ Running daily cleanup tasks")
    
    # Run cleanup of unverified accounts
    await cleanup_unverified_accounts()
    
    logger.info("✅ Daily cleanup tasks completed")


# Function to start scheduled tasks
async def start_scheduled_tasks():
    """Start all scheduled maintenance tasks"""
    logger.info("🚀 Starting scheduled maintenance tasks")
    
    # Schedule daily cleanup (runs every 24 hours)
    while True:
        try:
            await schedule_daily_cleanup()
        except Exception as e:
            logger.error(f"❌ Scheduled task error: {e}")
        
        # Wait 24 hours before next run
        await asyncio.sleep(24 * 60 * 60)  # 24 hours in seconds


if __name__ == "__main__":
    # For testing the cleanup function directly
    asyncio.run(cleanup_unverified_accounts())