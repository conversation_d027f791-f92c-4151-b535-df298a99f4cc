"""
Dependency injection for FastAPI
Common dependencies used across the application
"""

import uuid
from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JWTError, jwt
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.config import settings
from app.core.exceptions import http_401_unauthorized
from app.database import get_async_db
from app.models.user import User

# Security scheme with auto_error=False to handle missing tokens manually
security = HTTPBearer(auto_error=False)


async def get_current_user(
    db: AsyncSession = Depends(get_async_db),
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> User:
    """
    Get current authenticated user from JWT token
    """
    # Handle missing authorization header
    if credentials is None:
        raise http_401_unauthorized("Authentication required")

    try:
        # Decode JWT token
        payload = jwt.decode(
            credentials.credentials,
            settings.secret_key,
            algorithms=[settings.algorithm],
            audience="devhq-api",
            issuer="devhq-auth-service",
        )

        # Extract user ID from token
        user_id_str: str = payload.get("sub")
        if user_id_str is None:
            raise http_401_unauthorized("Invalid authentication credentials")

        # Convert string UUID back to UUID object for database query
        try:
            user_id = uuid.UUID(user_id_str)
        except ValueError:
            raise http_401_unauthorized("Invalid user ID format")

    except JWTError:
        raise http_401_unauthorized("Invalid authentication credentials")

    # Get user from database
    result = await db.execute(
        select(User).where(
            User.id == user_id, User.is_active == True, User.deleted_at.is_(None)
        )
    )
    user = result.scalar_one_or_none()

    if user is None:
        raise http_401_unauthorized("User not found")

    return user


def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Get current active user (additional check for user status)
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )
    return current_user


async def get_optional_current_user(
    db: AsyncSession = Depends(get_async_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
) -> Optional[User]:
    """
    Get current user if authenticated, None otherwise
    Useful for endpoints that work with or without authentication
    """
    if credentials is None:
        return None

    try:
        return await get_current_user(db, credentials)
    except HTTPException:
        return None
