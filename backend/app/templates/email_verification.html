<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your DevHQ Account</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #f1f5f9;
            background: #0d0d0d;
            min-height: 100vh;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(20, 20, 20, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(142, 202, 230, 0.2);
        }

        .header {
            background: linear-gradient(135deg, hsl(142.1, 70.6%, 45.3%) 0%, hsl(142.1, 70.6%, 35.3%) 100%);
            padding: 40px 30px;
            text-align: center;
            color: #0d0d0d;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 10px rgba(13, 13, 13, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            border: 2px solid #0d0d0d;
            background: transparent;
            display: inline-block;
            animation: blink 1.5s infinite alternate;
        }

        @keyframes blink {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px 30px;
            background: rgba(30, 41, 59, 0.5);
        }

        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: hsl(142.1, 70.6%, 45.3%);
            margin-bottom: 20px;
            text-shadow: 0 0 10px hsla(142.1, 70.6%, 45.3%, 0.3);
        }

        .message {
            font-size: 16px;
            color: #cbd5e1;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .cta-container {
            text-align: center;
            margin: 40px 0;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, hsl(142.1, 70.6%, 45.3%) 0%, hsl(142.1, 70.6%, 35.3%) 100%);
            color: #0d0d0d;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px hsla(142.1, 70.6%, 45.3%, 0.3);
            border: 1px solid hsla(142.1, 70.6%, 45.3%, 0.5);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
            text-shadow: 0 0 5px rgba(15, 23, 42, 0.5);
        }

        .backup-link {
            background: rgba(51, 65, 85, 0.5);
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            border-left: 4px solid #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .backup-link p {
            font-size: 14px;
            color: #94a3b8;
            margin-bottom: 10px;
        }

        .backup-url {
            word-break: break-all;
            color: #22c55e;
            font-family: monospace;
            font-size: 13px;
            background: rgba(15, 23, 42, 0.5);
            padding: 8px;
            border-radius: 4px;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .expiry-notice {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .expiry-notice p {
            color: #92400e;
            font-size: 14px;
            margin: 0;
        }

        .footer {
            background: rgba(15, 23, 42, 0.5);
            padding: 30px;
            text-align: center;
            border-top: 1px solid rgba(34, 197, 94, 0.2);
        }

        .footer p {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #22c55e;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            color: #16a34a;
            text-shadow: 0 0 5px rgba(34, 197, 94, 0.5);
        }

        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(34, 197, 94, 0.3), transparent);
            margin: 30px 0;
        }

        .feature-list {
            color: #cbd5e1;
            margin-left: 20px;
            margin-bottom: 20px;
        }

        .feature-list li {
            margin-bottom: 8px;
            position: relative;
        }

        .feature-list li::before {
            content: '✨';
            position: absolute;
            left: -20px;
            color: #22c55e;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }

            .content {
                padding: 30px 20px;
            }

            .header {
                padding: 30px 20px;
            }

            .greeting {
                font-size: 20px;
            }

            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon"></div>
                <span>DevHQ</span>
            </div>
            <div class="header-subtitle">Professional Developer Business Management</div>
        </div>

        <div class="content">
            <div class="greeting">Welcome to DevHQ! 🚀</div>

            <p class="message">
                Hi <strong>{{first_name}}</strong>,
            </p>

            <p class="message">
                Thank you for joining DevHQ, the professional business management platform designed specifically for
                African developers and agencies working with international clients.
            </p>

            <p class="message">
                To complete your registration and start managing your developer business like a pro, please verify your
                email address by clicking the button below:
            </p>

            <div class="cta-container">
                <a href="{{verification_url}}" class="cta-button">
                    Verify Email Address
                </a>
            </div>

            <div class="backup-link">
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <div class="backup-url">{{verification_url}}</div>
            </div>

            <div class="expiry-notice">
                <p>⏰ This verification link will expire in 24 hours for security reasons.</p>
            </div>

            <div class="divider"></div>

            <p class="message">
                Once verified, you'll have access to:
            </p>

            <ul class="feature-list">
                <li>Professional time tracking and project management</li>
                <li>Automated invoicing with multi-currency support</li>
                <li>Integrated payment processing (Paystack & DPO)</li>
                <li>Client portal for seamless collaboration</li>
                <li>Business analytics and financial reporting</li>
            </ul>
        </div>

        <div class="footer">
            <p>If you didn't create an account with DevHQ, you can safely ignore this email.</p>
            <p>
                <strong>Best regards,</strong><br>
                The DevHQ Team
            </p>

            <div class="social-links">
                <a href="https://devhq.com">Website</a>
                <a href="https://devhq.com/support">Support</a>
                <a href="https://devhq.com/docs">Documentation</a>
            </div>
        </div>
    </div>
</body>

</html>