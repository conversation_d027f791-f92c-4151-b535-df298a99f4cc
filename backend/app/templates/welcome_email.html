<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to DevHQ</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #f1f5f9;
            background: #0d0d0d;
            min-height: 100vh;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(20, 20, 20, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(142, 202, 230, 0.2);
        }

        .header {
            background: linear-gradient(135deg, hsl(142.1, 70.6%, 45.3%) 0%, hsl(142.1, 70.6%, 35.3%) 100%);
            padding: 40px 30px;
            text-align: center;
            color: #0d0d0d;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(13,13,13,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 10px rgba(13, 13, 13, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            border: 2px solid #0d0d0d;
            background: transparent;
            display: inline-block;
            animation: blink 1.5s infinite alternate;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: hsl(142.1, 70.6%, 45.3%);
            margin-bottom: 20px;
            text-shadow: 0 0 10px hsla(142.1, 70.6%, 45.3%, 0.3);
        }

        .message {
            font-size: 16px;
            margin-bottom: 30px;
            color: #e2e8f0;
        }

        .features {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }

        .features h3 {
            color: hsl(142.1, 70.6%, 45.3%);
            margin-bottom: 15px;
            font-size: 18px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #cbd5e1;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: '✓';
            color: hsl(142.1, 70.6%, 45.3%);
            font-weight: bold;
            margin-right: 10px;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, hsl(142.1, 70.6%, 45.3%) 0%, hsl(142.1, 70.6%, 35.3%) 100%);
            color: #0d0d0d;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px hsla(142.1, 70.6%, 45.3%, 0.3);
            border: 1px solid hsla(142.1, 70.6%, 45.3%, 0.5);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px hsla(142.1, 70.6%, 45.3%, 0.4);
        }

        .footer {
            background: rgba(255, 255, 255, 0.05);
            padding: 30px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-text {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            color: hsl(142.1, 70.6%, 45.3%);
            text-decoration: none;
            margin: 0 10px;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: hsl(142.1, 70.6%, 55.3%);
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .logo {
                font-size: 28px;
            }
            
            .greeting {
                font-size: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon"></div>
                <span>DevHQ</span>
            </div>
            <div class="header-subtitle">Professional Developer Business Management</div>
        </div>

        <div class="content">
            <div class="greeting">Welcome to DevHQ! 🚀</div>
            
            <div class="message">
                Hi {{ user_name }},
                <br><br>
                Welcome to DevHQ - your all-in-one platform for managing your developer business! We're excited to have you on board and help you streamline your professional workflow.
            </div>

            <div class="features">
                <h3>🎯 What you can do with DevHQ:</h3>
                <ul class="feature-list">
                    <li>Manage clients and projects efficiently</li>
                    <li>Track time and generate professional invoices</li>
                    <li>Handle multi-currency transactions globally</li>
                    <li>Monitor your business performance with analytics</li>
                    <li>Customize your professional profile and settings</li>
                </ul>
            </div>

            <div class="message">
                Your account is now active and ready to use. Click the button below to access your dashboard and start building your developer business empire!
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ dashboard_url }}" class="cta-button">Access Your Dashboard</a>
            </div>

            <div class="message">
                If you have any questions or need assistance, our support team is here to help. Just reply to this email or visit our help center.
                <br><br>
                Happy coding!<br>
                <strong>The DevHQ Team</strong>
            </div>
        </div>

        <div class="footer">
            <div class="footer-text">
                This email was sent to {{ user_email }}. If you didn't create a DevHQ account, please ignore this email.
            </div>
            <div class="social-links">
                <a href="#">Help Center</a> |
                <a href="#">Privacy Policy</a> |
                <a href="#">Terms of Service</a>
            </div>
        </div>
    </div>
</body>

</html>
