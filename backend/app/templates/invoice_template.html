<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ invoice.invoice_number }}</title>
    <style>
        @page {
            size: A4;
            margin: 1in;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            margin: 0;
            padding: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            border-bottom: 3px solid hsl(142.1, 70.6%, 45.3%);
            padding-bottom: 1rem;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 2rem;
            font-weight: bold;
            color: hsl(142.1, 70.6%, 45.3%);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 32px;
            height: 32px;
            border: 2px solid hsl(142.1, 70.6%, 45.3%);
            background: transparent;
            display: inline-block;
        }

        .company-details {
            color: #666;
            font-size: 0.9rem;
        }

        .invoice-info {
            text-align: right;
            flex: 1;
        }

        .invoice-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .invoice-number {
            font-size: 1.1rem;
            color: hsl(142.1, 70.6%, 45.3%);
            font-weight: 600;
        }

        .billing-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .bill-to,
        .invoice-details {
            flex: 1;
            margin-right: 2rem;
        }

        .bill-to h3,
        .invoice-details h3 {
            color: #1f2937;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 0.25rem;
        }

        .client-info {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid hsl(142.1, 70.6%, 45.3%);
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .items-table th {
            background: hsl(142.1, 70.6%, 45.3%);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .items-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .items-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .items-table tr:hover {
            background: #f3f4f6;
        }

        .text-right {
            text-align: right;
        }

        .totals-section {
            margin-left: auto;
            width: 300px;
            background: #f9fafb;
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }

        .total-row.final {
            border-top: 2px solid hsl(142.1, 70.6%, 45.3%);
            padding-top: 0.75rem;
            margin-top: 0.75rem;
            font-weight: bold;
            font-size: 1.1rem;
            color: #1f2937;
        }

        .payment-info {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #fef3c7;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
        }

        .payment-info h3 {
            color: #92400e;
            margin-bottom: 1rem;
        }

        .footer {
            margin-top: 3rem;
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            border-top: 1px solid #e5e7eb;
            padding-top: 1rem;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-draft {
            background: #fef3c7;
            color: #92400e;
        }

        .status-sent {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .status-paid {
            background: #d1fae5;
            color: #065f46;
        }

        .status-overdue {
            background: #fee2e2;
            color: #991b1b;
        }

        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">
                <div class="company-logo"></div>
                <span>{{ company_name }}</span>
            </div>
            <div class="company-details">
                {{ company_address }}<br>
                Email: {{ company_email }}<br>
                Phone: {{ company_phone }}
            </div>
        </div>
        <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number">#{{ invoice.invoice_number }}</div>
            <div class="status-badge status-{{ invoice.status }}">{{ invoice.status.title() }}</div>
        </div>
    </div>

    <div class="billing-section">
        <div class="bill-to">
            <h3>Bill To:</h3>
            <div class="client-info">
                <strong>{{ invoice.client.company_name or invoice.client.name }}</strong><br>
                {% if invoice.client.email %}{{ invoice.client.email }}<br>{% endif %}
                {% if invoice.client.phone %}{{ invoice.client.phone }}<br>{% endif %}
                {% if invoice.client.address %}{{ invoice.client.address }}{% endif %}
            </div>
        </div>

        <div class="invoice-details">
            <h3>Invoice Details:</h3>
            <table style="width: 100%;">
                <tr>
                    <td><strong>Issue Date:</strong></td>
                    <td>{{ issue_date_formatted }}</td>
                </tr>
                <tr>
                    <td><strong>Due Date:</strong></td>
                    <td>{{ due_date_formatted }}</td>
                </tr>
                <tr>
                    <td><strong>Currency:</strong></td>
                    <td>{{ invoice.currency }}</td>
                </tr>
                {% if invoice.project %}
                <tr>
                    <td><strong>Project:</strong></td>
                    <td>{{ invoice.project.name }}</td>
                </tr>
                {% endif %}
            </table>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th class="text-right">Quantity</th>
                <th class="text-right">Rate</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for item in invoice.items %}
            <tr>
                <td>
                    <strong>{{ item.description }}</strong>
                    {% if item.notes %}
                    <br><small style="color: #666;">{{ item.notes }}</small>
                    {% endif %}
                </td>
                <td class="text-right">{{ "%.2f"|format(item.quantity) }}</td>
                <td class="text-right">{{ currency_symbol }}{{ "%.2f"|format(item.rate) }}</td>
                <td class="text-right">{{ currency_symbol }}{{ "%.2f"|format(item.total_amount) }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="totals-section">
        <div class="total-row">
            <span>Subtotal:</span>
            <span>{{ currency_symbol }}{{ "%.2f"|format(subtotal) }}</span>
        </div>
        {% if tax_amount > 0 %}
        <div class="total-row">
            <span>Tax ({{ invoice.tax_rate }}%):</span>
            <span>{{ currency_symbol }}{{ "%.2f"|format(tax_amount) }}</span>
        </div>
        {% endif %}
        <div class="total-row final">
            <span>Total:</span>
            <span>{{ currency_symbol }}{{ "%.2f"|format(total_amount) }}</span>
        </div>
    </div>

    {% if invoice.notes %}
    <div class="payment-info">
        <h3>Notes:</h3>
        <p>{{ invoice.notes }}</p>
    </div>
    {% endif %}

    <div class="payment-info">
        <h3>Payment Information:</h3>
        <p>
            <strong>Payment is due by {{ due_date_formatted }}.</strong><br>
            Please reference invoice number <strong>{{ invoice.invoice_number }}</strong> when making payment.
        </p>
        {% if invoice.status == 'draft' %}
        <p><em>This is a draft invoice. Payment is not yet due.</em></p>
        {% elif invoice.status == 'paid' %}
        <p style="color: #065f46;"><strong>✓ This invoice has been paid. Thank you!</strong></p>
        {% endif %}
    </div>

    <div class="footer">
        <p>Generated on {{ generated_date }} | {{ company_name }} - Professional Developer Services</p>
        <p>Thank you for your business!</p>
    </div>
</body>

</html>