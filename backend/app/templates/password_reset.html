<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your DevHQ Password</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #f1f5f9;
            background: #0d0d0d;
            min-height: 100vh;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(20, 20, 20, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .header {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            padding: 40px 30px;
            text-align: center;
            color: #0d0d0d;
            position: relative;
            overflow: hidden;
        }

        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 10px rgba(13, 13, 13, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            border: 2px solid #0d0d0d;
            background: transparent;
            display: inline-block;
            animation: blink 1.5s infinite alternate;
        }

        @keyframes blink {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }

        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .cta-container {
            text-align: center;
            margin: 40px 0;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
        }

        .backup-link {
            background-color: #f3f4f6;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            border-left: 4px solid #dc2626;
        }

        .backup-link p {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 10px;
        }

        .backup-url {
            word-break: break-all;
            color: #dc2626;
            font-family: monospace;
            font-size: 13px;
            background-color: #ffffff;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }

        .security-notice {
            background-color: #fef2f2;
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .security-notice p {
            color: #991b1b;
            font-size: 14px;
            margin: 0;
        }

        .expiry-notice {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .expiry-notice p {
            color: #92400e;
            font-size: 14px;
            margin: 0;
        }

        .footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .footer p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
        }

        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e5e7eb, transparent);
            margin: 30px 0;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }

            .content {
                padding: 30px 20px;
            }

            .header {
                padding: 30px 20px;
            }

            .greeting {
                font-size: 20px;
            }

            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon"></div>
                <span>DevHQ</span>
            </div>
            <div class="header-subtitle">Password Reset Request</div>
        </div>

        <div class="content">
            <div class="greeting">Password Reset Request 🔐</div>

            <p class="message">
                Hi <strong>{{first_name}}</strong>,
            </p>

            <p class="message">
                We received a request to reset the password for your DevHQ account. If you made this request, click the
                button below to set a new password:
            </p>

            <div class="cta-container">
                <a href="{{reset_url}}" class="cta-button">
                    Reset Password
                </a>
            </div>

            <div class="backup-link">
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <div class="backup-url">{{reset_url}}</div>
            </div>

            <div class="expiry-notice">
                <p>⏰ This password reset link will expire in 1 hour for security reasons.</p>
            </div>

            <div class="security-notice">
                <p>🔒 If you didn't request a password reset, you can safely ignore this email. Your password will not
                    be changed.</p>
            </div>

            <div class="divider"></div>

            <p class="message">
                <strong>Security Tips:</strong>
            </p>

            <ul style="color: #4b5563; margin-left: 20px; margin-bottom: 20px;">
                <li>Choose a strong password with at least 8 characters</li>
                <li>Include uppercase, lowercase, numbers, and special characters</li>
                <li>Don't reuse passwords from other accounts</li>
                <li>Consider using a password manager</li>
            </ul>
        </div>

        <div class="footer">
            <p>If you have any questions or concerns, please contact our support team.</p>
            <p>
                <strong>Best regards,</strong><br>
                The DevHQ Team
            </p>

            <div class="social-links">
                <a href="https://devhq.com">Website</a>
                <a href="https://devhq.com/support">Support</a>
                <a href="https://devhq.com/security">Security</a>
            </div>
        </div>
    </div>
</body>

</html>