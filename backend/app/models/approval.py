"""
Client approval system models for formal project feedback and scope management
"""

import enum
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import (<PERSON><PERSON>an, Column, DateTime, ForeignKey, Index, Integer,
                        String, Text)
from sqlalchemy.dialects.postgresql import JSON, UUID
from sqlalchemy.orm import relationship

from .base import Base


class ApprovalStatus(str, enum.Enum):
    """Approval status enumeration for state machine logic"""

    PENDING = "pending"
    APPROVED = "approved"
    REVISION_REQUESTED = "revision_requested"
    SUPERSEDED = "superseded"  # For new versions


class ApprovalPriority(str, enum.Enum):
    """Approval priority levels for client attention management"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class ClientApproval(Base):
    """
    Polymorphic approval model supporting milestones, uploads, invoices, and future items.
    Provides formal approval workflows with state machine logic and audit trails.
    """

    __tablename__ = "client_approvals"
    __table_args__ = (
        Index("ix_client_approvals_user_id_status", "user_id", "status"),
        Index("ix_client_approvals_client_id_status", "client_id", "status"),
        Index("ix_client_approvals_project_id_status", "project_id", "status"),
        Index(
            "ix_client_approvals_approvable_type_id", "approvable_type", "approvable_id"
        ),
    )

    # Core approval fields
    status = Column(
        String(50), nullable=False, default=ApprovalStatus.PENDING.value, index=True
    )
    title = Column(String(255), nullable=False)  # "Milestone: Phase 1 Complete"
    description = Column(Text, nullable=True)  # What needs approval

    # Polymorphic relationship to any approvable item
    approvable_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    approvable_type = Column(
        String(50), nullable=False, index=True
    )  # "milestone", "upload", "invoice", etc.

    # Client and project relationships
    client_id = Column(
        UUID(as_uuid=True), ForeignKey("clients.id"), nullable=False, index=True
    )
    project_id = Column(
        UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True
    )
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )  # Developer who requested approval

    # Approval lifecycle timestamps
    requested_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
    )
    viewed_at = Column(
        DateTime(timezone=True), nullable=True
    )  # When client first viewed
    approved_at = Column(DateTime(timezone=True), nullable=True)
    revision_requested_at = Column(DateTime(timezone=True), nullable=True)

    # Approval metadata
    priority = Column(
        String(20), nullable=False, default=ApprovalPriority.MEDIUM.value, index=True
    )
    due_date = Column(DateTime(timezone=True), nullable=True)  # Optional deadline
    approval_token = Column(
        String(255), nullable=False, unique=True, index=True
    )  # Secure access for client portal

    # Versioning support
    version_number = Column(Integer, nullable=False, default=1)
    previous_approval_id = Column(
        UUID(as_uuid=True), ForeignKey("client_approvals.id"), nullable=True
    )  # Links to superseded version

    # Additional context and metadata
    context_data = Column(JSON, nullable=True)  # Additional context for the approval
    client_instructions = Column(Text, nullable=True)  # Special instructions for client

    # Relationships
    client = relationship("Client", backref="approvals")
    project = relationship("Project", backref="approvals")
    user = relationship("User", backref="approval_requests")
    previous_approval = relationship("ClientApproval", remote_side="ClientApproval.id")
    feedback_items = relationship(
        "ClientFeedback", back_populates="approval", cascade="all, delete-orphan"
    )
    activity_logs = relationship(
        "ApprovalActivity", back_populates="approval", cascade="all, delete-orphan"
    )

    @property
    def is_overdue(self) -> bool:
        """Check if approval is overdue based on due date"""
        if not self.due_date or self.status in [
            ApprovalStatus.APPROVED,
            ApprovalStatus.SUPERSEDED,
        ]:
            return False
        # Ensure both datetimes are timezone-aware for comparison
        now = datetime.now(timezone.utc)
        due_date = self.due_date
        if due_date.tzinfo is None:
            due_date = due_date.replace(tzinfo=timezone.utc)
        return now > due_date

    @property
    def days_pending(self) -> int:
        """Calculate days since approval was requested"""
        if self.status != ApprovalStatus.PENDING:
            return 0
        # Ensure both datetimes are timezone-aware for calculation
        now = datetime.now(timezone.utc)
        requested_at = self.requested_at
        if requested_at.tzinfo is None:
            requested_at = requested_at.replace(tzinfo=timezone.utc)
        return (now - requested_at).days

    def mark_as_viewed(self):
        """Mark approval as viewed by client"""
        if not self.viewed_at:
            self.viewed_at = datetime.now(timezone.utc)

    def approve(self, client_name: str, client_email: str, notes: Optional[str] = None):
        """Mark approval as approved with audit trail"""
        self.status = ApprovalStatus.APPROVED.value
        self.approved_at = datetime.now(timezone.utc)

        # Create activity log
        activity = ApprovalActivity(
            approval_id=self.id,
            action="approved",
            actor_type="client",
            actor_name=client_name,
            details={"notes": notes, "email": client_email},
            timestamp=datetime.now(timezone.utc),
        )
        self.activity_logs.append(activity)

    def request_revision(self, client_name: str, client_email: str, feedback: str):
        """Mark approval as needing revision with feedback"""
        self.status = ApprovalStatus.REVISION_REQUESTED.value
        self.revision_requested_at = datetime.now(timezone.utc)

        # Create feedback record
        feedback_item = ClientFeedback(
            approval_id=self.id,
            content=feedback,
            feedback_type="revision",
            client_name=client_name,
            client_email=client_email,
            submitted_at=datetime.now(timezone.utc),
        )
        self.feedback_items.append(feedback_item)

        # Create activity log
        activity = ApprovalActivity(
            approval_id=self.id,
            action="revision_requested",
            actor_type="client",
            actor_name=client_name,
            details={"feedback": feedback, "email": client_email},
            timestamp=datetime.now(timezone.utc),
        )
        self.activity_logs.append(activity)

    def supersede_with_new_version(self, new_approval_id: UUID):
        """Mark this approval as superseded by a new version"""
        self.status = ApprovalStatus.SUPERSEDED.value

        # Create activity log
        activity = ApprovalActivity(
            approval_id=self.id,
            action="superseded",
            actor_type="system",
            actor_name="System",
            details={"new_approval_id": str(new_approval_id)},
            timestamp=datetime.now(timezone.utc),
        )
        self.activity_logs.append(activity)

    def __repr__(self):
        return f"<ClientApproval(title={self.title}, status={self.status}, client_id={self.client_id})>"


class ClientFeedback(Base):
    """
    Client feedback model for structured revision requests and approval notes
    """

    __tablename__ = "client_feedback"

    # Feedback details
    approval_id = Column(
        UUID(as_uuid=True),
        ForeignKey("client_approvals.id"),
        nullable=False,
        index=True,
    )
    content = Column(Text, nullable=False)  # Client's feedback/revision requests
    feedback_type = Column(
        String(50), nullable=False, default="revision", index=True
    )  # "revision", "question", "approval_note"

    # Feedback metadata
    submitted_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
    )
    is_addressed = Column(Boolean, default=False, nullable=False)
    developer_response = Column(Text, nullable=True)
    responded_at = Column(DateTime(timezone=True), nullable=True)

    # Client information for audit trail
    client_name = Column(String(200), nullable=False)
    client_email = Column(String(255), nullable=False)

    # Additional metadata
    urgency_level = Column(String(20), nullable=True)  # "low", "medium", "high"
    category = Column(
        String(100), nullable=True
    )  # "design", "functionality", "content", etc.

    # Relationships
    approval = relationship("ClientApproval", back_populates="feedback_items")

    def mark_as_addressed(self, developer_response: str):
        """Mark feedback as addressed with developer response"""
        self.is_addressed = True
        self.developer_response = developer_response
        self.responded_at = datetime.now(timezone.utc)

    def __repr__(self):
        return f"<ClientFeedback(approval_id={self.approval_id}, type={self.feedback_type}, addressed={self.is_addressed})>"


class ApprovalActivity(Base):
    """
    Activity tracking for approvals providing complete audit trail
    """

    __tablename__ = "approval_activities"

    # Activity tracking for approvals
    approval_id = Column(
        UUID(as_uuid=True),
        ForeignKey("client_approvals.id"),
        nullable=False,
        index=True,
    )
    action = Column(
        String(50), nullable=False, index=True
    )  # "created", "viewed", "approved", "revision_requested", "superseded"
    actor_type = Column(String(20), nullable=False)  # "developer", "client", "system"
    actor_name = Column(String(200), nullable=False)
    details = Column(JSON, nullable=True)  # Additional context
    timestamp = Column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
    )

    # Optional IP tracking for security
    ip_address = Column(String(45), nullable=True)  # IPv4/IPv6 support
    user_agent = Column(String(500), nullable=True)

    # Relationships
    approval = relationship("ClientApproval", back_populates="activity_logs")

    def __repr__(self):
        return f"<ApprovalActivity(approval_id={self.approval_id}, action={self.action}, actor={self.actor_name})>"
