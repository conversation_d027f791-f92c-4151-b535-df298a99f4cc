"""
Project management models for project tracking, milestones, and time entries
"""

from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional

from sqlalchemy import (Boolean, Column, DateTime, ForeignKey, Index, Integer,
                        Numeric, String, Text)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .base import Base


class Project(Base):
    """Project model for managing client projects with billing and time tracking."""

    __tablename__ = "projects"
    __table_args__ = (
        Index("ix_projects_user_id_status", "user_id", "status"),
        Index("ix_projects_client_id_status", "client_id", "status"),
        Index("ix_projects_user_id_billing_type", "user_id", "billing_type"),
    )

    # Basic project information
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Client relationship
    client_id = Column(
        UUID(as_uuid=True), ForeignKey("clients.id"), nullable=False, index=True
    )

    # User/owner relationship
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )

    # Workspace relationship
    workspace_id = Column(
        UUID(as_uuid=True), ForeignKey("workspaces.id"), nullable=False, index=True
    )

    # Billing configuration
    billing_type = Column(
        String(50), nullable=False, default="time_and_materials", index=True
    )  # time_and_materials, fixed_price

    total_budget = Column(Numeric(12, 2), nullable=True)  # For fixed price projects
    estimated_hours = Column(Numeric(8, 2), nullable=True)  # Estimated total hours
    hourly_rate = Column(Numeric(10, 2), nullable=True)  # Default hourly rate
    currency = Column(String(3), nullable=False, default="USD")  # Project currency

    # Project status and timeline
    status = Column(
        String(50), nullable=False, default="draft", index=True
    )  # draft, active, completed, on_hold, cancelled

    start_date = Column(DateTime(timezone=True), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)
    deadline = Column(DateTime(timezone=True), nullable=True)

    # Project settings
    is_billable = Column(Boolean, default=True, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Project-specific client information (can override client record)
    client_name = Column(String(255), nullable=True)  # Client name override
    contact_person = Column(String(255), nullable=True)
    client_email = Column(String(255), nullable=True)
    client_phone = Column(String(50), nullable=True)
    client_address = Column(Text, nullable=True)
    client_industry = Column(String(255), nullable=True)
    client_company_size = Column(String(50), nullable=True)  # Small, Medium, Large, Enterprise
    client_priority = Column(String(20), nullable=True)  # low, medium, high
    client_status = Column(String(50), nullable=True)  # active, inactive, completed, archived
    client_notes = Column(Text, nullable=True)

    # Relationships
    client_record = relationship("Client", backref="projects")
    user = relationship("User", backref="projects")
    workspace = relationship("Workspace", back_populates="projects")
    milestones = relationship(
        "ProjectMilestone", back_populates="project", cascade="all, delete-orphan"
    )
    time_entries = relationship(
        "TimeEntry", back_populates="project", cascade="all, delete-orphan"
    )

    @property
    def total_logged_hours(self) -> Decimal:
        """Calculate total hours logged across all time entries."""
        if not self.time_entries:
            return Decimal("0.00")
        return sum(
            (entry.duration_minutes or 0) / 60
            for entry in self.time_entries
            if not entry.is_deleted
        )

    @property
    def total_billable_amount(self) -> Decimal:
        """Calculate total billable amount from time entries."""
        if not self.time_entries:
            return Decimal("0.00")
        return sum(
            entry.billable_amount or Decimal("0.00")
            for entry in self.time_entries
            if not entry.is_deleted
        )

    @property
    def completion_percentage(self) -> float:
        """Calculate project completion based on milestones."""
        if not self.milestones:
            return 0.0

        completed_milestones = sum(
            1
            for milestone in self.milestones
            if milestone.status == "completed" and not milestone.is_deleted
        )
        total_milestones = sum(
            1 for milestone in self.milestones if not milestone.is_deleted
        )

        if total_milestones == 0:
            return 0.0

        return (completed_milestones / total_milestones) * 100

    def __repr__(self):
        return f"<Project(title={self.title}, status={self.status}, client_id={self.client_id})>"


class ProjectMilestone(Base):
    """Project milestone model for tracking project phases and payments."""

    __tablename__ = "project_milestones"

    # Project relationship
    project_id = Column(
        UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True
    )

    # Milestone details
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Planning and estimation
    estimated_hours = Column(Numeric(8, 2), nullable=True)
    payment_amount = Column(Numeric(12, 2), nullable=True)  # Payment for this milestone

    # Timeline
    due_date = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Status and ordering
    status = Column(
        String(50), nullable=False, default="pending", index=True
    )  # pending, in_progress, completed, approved

    sequence_number = Column(
        Integer, nullable=False, default=1
    )  # For ordering milestones

    # Client portal and billing
    is_client_visible = Column(Boolean, default=False, nullable=False)
    billing_status = Column(
        String(50), default="unbilled", nullable=False, index=True
    )  # unbilled, invoiced, paid

    # Relationships
    project = relationship("Project", back_populates="milestones")

    @property
    def is_overdue(self) -> bool:
        """Check if milestone is overdue."""
        if not self.due_date or self.status == "completed":
            return False
        # Ensure both datetimes are timezone-aware for comparison
        now = datetime.now(timezone.utc)
        due_date = self.due_date
        if due_date.tzinfo is None:
            due_date = due_date.replace(tzinfo=timezone.utc)
        return now > due_date

    @property
    def logged_hours(self) -> Decimal:
        """Calculate hours logged for this milestone from time entries."""
        # For now, return 0.00 as a placeholder
        # In a more advanced system, time entries could be linked to specific milestones
        # Note: Accessing relationships in properties can cause greenlet issues in async contexts
        return Decimal("0.00")  # Placeholder for milestone-specific time tracking

    def mark_completed(self):
        """Mark milestone as completed with timestamp."""
        self.status = "completed"
        self.completed_at = datetime.now(timezone.utc)

    def __repr__(self):
        return f"<ProjectMilestone(title={self.title}, status={self.status}, project_id={self.project_id})>"


class TimeEntry(Base):
    """Time entry model for tracking work time on projects."""

    __tablename__ = "time_entries"
    __table_args__ = (
        Index("ix_time_entries_project_id_work_date", "project_id", "work_date"),
        Index("ix_time_entries_user_id_work_date", "user_id", "work_date"),
        Index("ix_time_entries_project_id_status", "project_id", "status"),
        Index("ix_time_entries_user_id_status", "user_id", "status"),
    )

    # Project relationship
    project_id = Column(
        UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, index=True
    )

    # User who logged the time
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )

    # Time tracking
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    duration_minutes = Column(Integer, nullable=False)  # Duration in minutes

    # Work description
    description = Column(Text, nullable=True)
    task_name = Column(String(255), nullable=True)  # Optional task/activity name

    # Billing information
    hourly_rate = Column(Numeric(10, 2), nullable=True)  # Rate for this entry
    billable_amount = Column(
        Numeric(12, 2), nullable=True
    )  # Calculated billable amount
    is_billable = Column(Boolean, default=True, nullable=False)

    # Entry status
    status = Column(
        String(50), nullable=False, default="draft", index=True
    )  # draft, submitted, approved, billed

    # Billing status
    billing_status = Column(
        String(50), default="unbilled", nullable=False, index=True
    )  # unbilled, invoiced, paid

    # Date for reporting (extracted from start_time or manually set)
    work_date = Column(DateTime(timezone=True), nullable=False, index=True)

    # Advanced tracking fields for revolutionary features
    timer_started_from = Column(String(50), nullable=True)  # "web", "mobile", "api"
    productivity_score = Column(
        Numeric(3, 1), nullable=True
    )  # 1-10 based on focus time
    break_duration_minutes = Column(Integer, default=0)  # Tracked break time
    tags = Column(Text, nullable=True)  # JSON array for categorization
    mood_rating = Column(Integer, nullable=True)  # 1-5 developer mood during work
    focus_level = Column(Integer, nullable=True)  # 1-5 concentration level
    interruption_count = Column(Integer, default=0)  # Number of interruptions

    # Timer state management
    is_timer_active = Column(Boolean, default=False, nullable=False, index=True)
    timer_device_id = Column(String(255), nullable=True)  # Device identifier
    last_heartbeat = Column(DateTime(timezone=True), nullable=True)  # Keep timer alive
    timer_paused_at = Column(DateTime(timezone=True), nullable=True)  # Pause timestamp
    total_pause_duration = Column(Integer, default=0)  # Total paused time in minutes

    # Relationships
    project = relationship("Project", back_populates="time_entries")
    user = relationship("User", backref="time_entries")

    def calculate_billable_amount(self):
        """Calculate and set billable amount based on duration and rate."""
        if not self.is_billable or not self.hourly_rate or not self.duration_minutes:
            self.billable_amount = Decimal("0.00")
            return

        hours = Decimal(str(self.duration_minutes)) / Decimal("60")
        self.billable_amount = hours * self.hourly_rate

    @property
    def duration_hours(self) -> Decimal:
        """Get duration in hours as decimal."""
        if not self.duration_minutes:
            return Decimal("0.00")
        return Decimal(str(self.duration_minutes)) / Decimal("60")

    def __repr__(self):
        return f"<TimeEntry(project_id={self.project_id}, duration={self.duration_minutes}min, status={self.status})>"
