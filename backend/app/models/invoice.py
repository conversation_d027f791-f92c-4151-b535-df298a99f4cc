"""Invoice models for professional billing and payment processing"""

import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional

from sqlalchemy import (<PERSON><PERSON>an, Column, DateTime, ForeignKey, Index, Integer,
                        Numeric, String, Text)
from sqlalchemy.dialects.postgresql import JSON, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.monitoring_decorators import monitor_database_operation

from .base import Base


class Invoice(Base):
    """Professional invoice model with complete lifecycle management."""

    __tablename__ = "invoices"
    __table_args__ = (
        Index("ix_invoices_user_id_status", "user_id", "status"),
        Index("ix_invoices_client_id_status", "client_id", "status"),
        Index("ix_invoices_project_id_status", "project_id", "status"),
        Index("ix_invoices_user_id_due_date", "user_id", "due_date"),
        # Payment gateway indexes
        Index("ix_invoices_payment_gateway", "payment_gateway"),
        Index("ix_invoices_settlement_status", "settlement_status"),
        Index("ix_invoices_settled_at", "settled_at"),
        Index("ix_invoices_payment_reference", "payment_reference"),
        Index("ix_invoices_currency", "currency"),
        # Composite indexes for common queries
        Index("ix_invoices_user_id_created_at", "user_id", "created_at"),
        Index("ix_invoices_status_created_at", "status", "created_at"),
        Index("ix_invoices_currency_created_at", "currency", "created_at"),
    )

    # Core invoice identification
    invoice_number = Column(String(50), nullable=False, unique=True, index=True)

    # Invoice status lifecycle
    status = Column(
        String(50), nullable=False, default="draft", index=True
    )  # draft, sent, viewed, paid, overdue, cancelled

    # Dates and timeline
    issue_date = Column(DateTime(timezone=True), nullable=False, default=func.now())
    due_date = Column(DateTime(timezone=True), nullable=False)
    sent_at = Column(DateTime(timezone=True), nullable=True)
    viewed_at = Column(DateTime(timezone=True), nullable=True)
    paid_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    client_id = Column(
        UUID(as_uuid=True), ForeignKey("clients.id"), nullable=False, index=True
    )
    project_id = Column(
        UUID(as_uuid=True), ForeignKey("projects.id"), nullable=True, index=True
    )  # Optional - can be multi-project invoice
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )

    # Financial calculations
    subtotal = Column(Numeric(12, 2), nullable=False, default=Decimal("0.00"))
    tax_rate = Column(
        Numeric(5, 2), nullable=False, default=Decimal("0.00")
    )  # Percentage
    tax_amount = Column(Numeric(12, 2), nullable=False, default=Decimal("0.00"))
    discount_amount = Column(Numeric(12, 2), nullable=False, default=Decimal("0.00"))
    total_amount = Column(Numeric(12, 2), nullable=False, default=Decimal("0.00"))

    # Multi-currency support for African markets
    currency = Column(
        String(3), nullable=False, default="USD"
    )  # KES, NGN, GHS, ZAR, USD
    exchange_rate = Column(Numeric(10, 4), nullable=True)  # For currency conversion

    # Payment integration
    payment_link = Column(String(500), nullable=True)  # Payment gateway link
    payment_token = Column(
        String(255), nullable=True, unique=True
    )  # Secure access token
    payment_reference = Column(String(255), nullable=True)  # Payment gateway reference
    payment_gateway = Column(String(50), nullable=True)  # paystack, dpo, etc.

    # Settlement and revenue sharing
    settlement_type = Column(
        String(20), default="standard", nullable=False
    )  # standard, instant
    settlement_preference = Column(
        String(20), default="auto", nullable=False
    )  # auto, manual
    instant_settlement_fee = Column(Numeric(10, 2), nullable=True)
    platform_fee_amount = Column(Numeric(10, 2), nullable=True)
    gateway_fee_amount = Column(Numeric(10, 2), nullable=True)
    net_payout_amount = Column(Numeric(10, 2), nullable=True)
    settlement_status = Column(
        String(20), default="pending", nullable=False
    )  # pending, processing, completed, failed
    settled_at = Column(DateTime(timezone=True), nullable=True)

    # Billing addresses and details
    billing_address = Column(JSON, nullable=True)  # Client billing address
    company_details = Column(JSON, nullable=True)  # Invoice issuer details

    # Content and customization
    notes = Column(Text, nullable=True)  # Internal notes
    terms_and_conditions = Column(Text, nullable=True)
    footer_text = Column(Text, nullable=True)

    # PDF and branding
    pdf_generated = Column(Boolean, default=False, nullable=False)
    pdf_url = Column(String(500), nullable=True)
    template_id = Column(String(50), nullable=True)  # For custom templates

    # Business intelligence
    payment_terms_days = Column(Integer, default=30, nullable=False)  # Net 30, etc.
    late_fee_rate = Column(Numeric(5, 2), nullable=True)  # Late fee percentage

    # Relationships
    client = relationship("Client", backref="invoices")
    project = relationship("Project", backref="invoices")
    user = relationship("User", backref="invoices")
    items = relationship(
        "InvoiceItem", back_populates="invoice", cascade="all, delete-orphan"
    )
    transactions = relationship(
        "PaymentTransaction", back_populates="invoice", cascade="all, delete-orphan"
    )

    def __init__(self, **kwargs):
        # Set due_date before calling super() if not provided
        if "due_date" not in kwargs and "issue_date" in kwargs:
            from datetime import timedelta

            payment_terms_days = kwargs.get("payment_terms_days", 30)
            kwargs["due_date"] = kwargs["issue_date"] + timedelta(
                days=payment_terms_days
            )
        elif "due_date" not in kwargs:
            # If no issue_date provided either, set due_date to 30 days from now
            from datetime import timedelta

            kwargs["due_date"] = datetime.now(timezone.utc) + timedelta(
                days=kwargs.get("payment_terms_days", 30)
            )

        super().__init__(**kwargs)
        if not self.payment_token:
            self.payment_token = str(uuid.uuid4())

    @property
    def is_overdue(self) -> bool:
        """Check if invoice is overdue."""
        if self.status in ["paid", "cancelled"]:
            return False
        # Ensure both datetimes are timezone-aware for comparison
        now = datetime.now(timezone.utc)
        due_date = self.due_date
        if due_date.tzinfo is None:
            due_date = due_date.replace(tzinfo=timezone.utc)
        return now > due_date

    @property
    def days_overdue(self) -> int:
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        # Ensure both datetimes are timezone-aware for calculation
        now = datetime.now(timezone.utc)
        due_date = self.due_date
        if due_date.tzinfo is None:
            due_date = due_date.replace(tzinfo=timezone.utc)
        delta = now - due_date
        return delta.days

    @property
    def total_paid(self) -> Decimal:
        """Calculate total amount paid from transactions."""
        try:
            if not hasattr(self, "_transactions_loaded"):
                # If transactions aren't loaded, return 0 to avoid lazy loading issues
                if not self.transactions:
                    return Decimal("0.00")
            return sum(
                transaction.amount
                for transaction in self.transactions
                if transaction.status == "completed"
            )
        except Exception:
            # Fallback to 0 if there's any issue accessing transactions
            return Decimal("0.00")

    @property
    def balance_due(self) -> Decimal:
        """Calculate remaining balance due."""
        return self.total_amount - self.total_paid

    @property
    def is_fully_paid(self) -> bool:
        """Check if invoice is fully paid."""
        return self.balance_due <= Decimal("0.00")

    def calculate_totals(self):
        """Calculate subtotal, tax, and total from items."""
        if not self.items:
            self.subtotal = Decimal("0.00")
            self.tax_amount = Decimal("0.00")
            self.total_amount = Decimal("0.00")
            return

        # Calculate subtotal
        self.subtotal = sum(item.total_price for item in self.items)

        # Calculate tax
        self.tax_amount = (self.subtotal * self.tax_rate) / Decimal("100")

        # Calculate total
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount

    def generate_invoice_number(self, user_id: uuid.UUID) -> str:
        """Generate unique invoice number."""
        from sqlalchemy.orm import Session

        from app.database import get_db

        # Format: INV-YYYY-NNNN (e.g., INV-2025-0001)
        year = datetime.now(timezone.utc).year

        # This would need to be called within a database session context
        # For now, return a basic format - will be improved in service layer
        return f"INV-{year}-{str(uuid.uuid4())[:8].upper()}"

    @monitor_database_operation("invoice_mark_sent")
    def mark_as_sent(self):
        """Mark invoice as sent."""
        self.status = "sent"
        self.sent_at = datetime.now(timezone.utc)

    @monitor_database_operation("invoice_mark_viewed")
    def mark_as_viewed(self):
        """Mark invoice as viewed by client."""
        if self.status == "sent":
            self.status = "viewed"
        if not self.viewed_at:
            self.viewed_at = datetime.now(timezone.utc)

    @monitor_database_operation("invoice_mark_paid")
    def mark_as_paid(self):
        """Mark invoice as paid."""
        self.status = "paid"
        self.paid_at = datetime.now(timezone.utc)

    def __repr__(self):
        return f"<Invoice(number={self.invoice_number}, status={self.status}, total={self.total_amount})>"


class InvoiceItem(Base):
    """Individual line items within an invoice."""

    __tablename__ = "invoice_items"

    # Invoice relationship
    invoice_id = Column(
        UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False, index=True
    )

    # Item details
    description = Column(Text, nullable=False)
    quantity = Column(Numeric(10, 2), nullable=False, default=Decimal("1.00"))
    unit_price = Column(Numeric(12, 2), nullable=False)
    total_price = Column(Numeric(12, 2), nullable=False)

    # Item categorization
    item_type = Column(
        String(50), nullable=False, default="custom", index=True
    )  # "time", "milestone", "expense", "custom"

    # Source tracking for billing integrity
    time_entry_id = Column(
        UUID(as_uuid=True), ForeignKey("time_entries.id"), nullable=True, index=True
    )
    milestone_id = Column(
        UUID(as_uuid=True),
        ForeignKey("project_milestones.id"),
        nullable=True,
        index=True,
    )

    # Billing status management
    billing_status = Column(
        String(50), nullable=False, default="billed", index=True
    )  # "unbilled", "billed", "paid"

    # Time-based item details
    hours_worked = Column(Numeric(8, 2), nullable=True)  # For time entries
    hourly_rate = Column(Numeric(10, 2), nullable=True)  # Rate used
    work_date = Column(DateTime(timezone=True), nullable=True)  # When work was done

    # Ordering and grouping
    sequence_number = Column(Integer, nullable=False, default=1)
    group_name = Column(String(255), nullable=True)  # For grouping similar items

    # Relationships
    invoice = relationship("Invoice", back_populates="items")
    time_entry = relationship("TimeEntry", backref="invoice_items")
    milestone = relationship("ProjectMilestone", backref="invoice_items")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Calculate total_price if not provided
        if (
            self.total_price is None
            and self.quantity is not None
            and self.unit_price is not None
        ):
            self.calculate_total()

    def calculate_total(self):
        """Calculate total price from quantity and unit price."""
        self.total_price = self.quantity * self.unit_price

    @property
    def is_time_based(self) -> bool:
        """Check if item is based on time tracking."""
        return self.item_type == "time" and self.time_entry_id is not None

    @property
    def is_milestone_based(self) -> bool:
        """Check if item is based on project milestone."""
        return self.item_type == "milestone" and self.milestone_id is not None

    def __repr__(self):
        return f"<InvoiceItem(description={self.description[:50]}, total={self.total_price})>"


class PaymentTransaction(Base):
    """Payment transaction records for invoice payments."""

    __tablename__ = "payment_transactions"

    # Invoice relationship
    invoice_id = Column(
        UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False, index=True
    )

    # Transaction details
    transaction_reference = Column(String(255), nullable=False, unique=True, index=True)
    payment_method = Column(
        String(50), nullable=False
    )  # "card", "bank_transfer", "mobile_money"
    payment_gateway = Column(String(50), nullable=False, default="paystack")

    # Financial details
    amount = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), nullable=False)
    exchange_rate = Column(Numeric(10, 4), nullable=True)

    # Transaction status
    status = Column(
        String(50), nullable=False, default="pending", index=True
    )  # pending, processing, completed, failed, cancelled, refunded

    # Gateway response data
    gateway_response = Column(JSON, nullable=True)  # Full gateway response
    gateway_transaction_id = Column(String(255), nullable=True, index=True)

    # Timestamps
    initiated_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)

    # Fees and charges
    gateway_fee = Column(Numeric(12, 2), nullable=True)
    net_amount = Column(Numeric(12, 2), nullable=True)  # Amount after fees

    # Customer details
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(50), nullable=True)

    # Relationships
    invoice = relationship("Invoice", back_populates="transactions")

    @monitor_database_operation("transaction_mark_completed")
    def mark_as_completed(self, gateway_response: dict = None):
        """Mark transaction as completed."""
        self.status = "completed"
        self.completed_at = datetime.now(timezone.utc)
        if gateway_response:
            self.gateway_response = gateway_response

    @monitor_database_operation("transaction_mark_failed")
    def mark_as_failed(self, gateway_response: dict = None):
        """Mark transaction as failed."""
        self.status = "failed"
        self.failed_at = datetime.now(timezone.utc)
        if gateway_response:
            self.gateway_response = gateway_response

    @property
    def is_successful(self) -> bool:
        """Check if transaction was successful."""
        return self.status == "completed"

    def __repr__(self):
        return f"<PaymentTransaction(reference={self.transaction_reference}, status={self.status}, amount={self.amount})>"
