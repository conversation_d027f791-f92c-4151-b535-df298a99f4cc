"""
Workspace model for organizing projects and data isolation
"""

from datetime import datetime, timezone
from uuid import uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .base import Base


class Workspace(Base):
    """
    Workspace model for organizing projects and providing data isolation
    """
    __tablename__ = "workspaces"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="workspaces")
    projects = relationship("Project", back_populates="workspace", cascade="all, delete-orphan")
    clients = relationship("Client", back_populates="workspace", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Workspace(id={self.id}, name='{self.name}', user_id={self.user_id})>"

    def get_project_count(self) -> int:
        """Get the number of active projects in this workspace"""
        if hasattr(self, '_project_count'):
            return self._project_count
        return 0

    @property
    def status(self) -> str:
        """Get workspace status based on is_active flag"""
        return "Active" if self.is_active else "Inactive"

    def soft_delete(self):
        """Soft delete the workspace"""
        self.deleted_at = datetime.now(timezone.utc)
        self.is_active = False

    def restore(self):
        """Restore a soft-deleted workspace"""
        self.deleted_at = None
        self.is_active = True
