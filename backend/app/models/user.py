"""
User-related models for authentication and user management
"""

from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Optional

from passlib.context import CryptContext
from sqlalchemy import (Boolean, Column, DateTime, ForeignKey, Index, Integer,
                        Numeric, String, Text)
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON>, UUID
from sqlalchemy.orm import relationship, foreign

from .base import Base


class UserRole(str, Enum):
    """User role enumeration"""

    USER = "user"
    ADMIN = "admin"
    MODERATOR = "moderator"


# Password hashing context
pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")


class User(Base):
    """
    User model for authentication and profile management
    """

    __tablename__ = "users"

    # Basic profile information
    email = Column(String(255), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)

    # Optional profile fields
    avatar_url = Column(String(500), nullable=True)
    phone = Column(String(20), nullable=True)
    bio = Column(Text, nullable=True)
    company_name = Column(String(200), nullable=True)

    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    email_verified_at = Column(DateTime(timezone=True), nullable=True)

    # OAuth fields
    oauth_provider = Column(String(50), nullable=True)  # 'google', 'github', etc.
    oauth_id = Column(String(255), nullable=True)  # OAuth provider user ID

    # Workspace management (will be added as FK after workspace table creation)
    active_workspace_id = Column(UUID(as_uuid=True), nullable=True)

    # Relationships
    settings = relationship("UserSettings", back_populates="user", uselist=False)
    sessions = relationship("UserSession", back_populates="user")
    workspaces = relationship("Workspace", back_populates="user", cascade="all, delete-orphan", foreign_keys="Workspace.user_id")
    active_workspace = relationship("Workspace", primaryjoin="User.active_workspace_id == foreign(Workspace.id)", post_update=True)

    def set_password(self, password: str) -> None:
        """Hash and set user password"""
        self.password_hash = pwd_context.hash(password)

    def verify_password(self, password: str) -> bool:
        """Verify password against stored hash"""
        return pwd_context.verify(password, self.password_hash)

    @property
    def full_name(self) -> str:
        """Get user's full name"""
        return f"{self.first_name} {self.last_name}"

    def __repr__(self):
        return f"<User(email={self.email}, name={self.full_name})>"


class UserSettings(Base):
    """
    User preferences and settings
    """

    __tablename__ = "user_settings"
    __table_args__ = (Index("ix_user_settings_user_id", "user_id"),)

    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # UI Preferences
    theme = Column(String(20), default="system", nullable=False)  # light, dark, system
    language = Column(String(10), default="en", nullable=False)
    timezone = Column(String(50), default="UTC", nullable=False)
    date_format = Column(String(20), default="YYYY-MM-DD", nullable=False)
    time_format = Column(String(10), default="24h", nullable=False)  # 12h, 24h

    # Business Preferences
    default_currency = Column(
        String(3), default="KES", nullable=False
    )  # ISO currency code
    default_hourly_rate = Column(Numeric(10, 2), default=0.00, nullable=False)

    # Invoice Settings
    invoice_prefix = Column(String(10), default="INV", nullable=False)
    invoice_number_start = Column(Integer, default=1, nullable=False)
    payment_terms_days = Column(Integer, default=30, nullable=False)

    # Notification Preferences
    email_notifications = Column(Boolean, default=True, nullable=False)
    push_notifications = Column(Boolean, default=True, nullable=False)
    marketing_emails = Column(Boolean, default=False, nullable=False)

    # Relationships
    user = relationship("User", back_populates="settings")

    def __repr__(self):
        return (
            f"<UserSettings(user_id={self.user_id}, currency={self.default_currency})>"
        )


class UserPayoutSettings(Base):
    """
    User payout and revenue sharing settings
    """

    __tablename__ = "user_payout_settings"
    __table_args__ = (
        Index("ix_user_payout_settings_user_id", "user_id"),
        Index("ix_user_payout_settings_fee_tier", "fee_tier"),
        Index("ix_user_payout_settings_payout_method", "payout_method"),
        Index("ix_user_payout_settings_last_payout_date", "last_payout_date"),
        Index("ix_user_payout_settings_total_revenue_earned", "total_revenue_earned"),
    )

    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # Platform fee settings
    platform_fee_rate = Column(
        Numeric(5, 4), default=Decimal("0.025"), nullable=False
    )  # 2.5% default
    fee_tier = Column(
        String(20), default="starter", nullable=False
    )  # starter, business, professional, enterprise

    # Payout preferences
    payout_method = Column(String(50), default="bank_transfer", nullable=False)
    payout_frequency = Column(
        String(20), default="weekly", nullable=False
    )  # daily, weekly, monthly
    minimum_payout_amount = Column(
        Numeric(10, 2), default=Decimal("100.00"), nullable=False
    )
    instant_settlement_enabled = Column(Boolean, default=False, nullable=False)

    # Payment gateway accounts
    paystack_subaccount_id = Column(String(100), nullable=True)

    # Bank account details (encrypted in production)
    payout_bank_account = Column(
        JSON, nullable=True
    )  # {bank_name, account_number, account_name, bank_code}
    payout_mobile_money = Column(JSON, nullable=True)  # {provider, phone_number, name}

    # Settlement preferences
    auto_settlement = Column(Boolean, default=True, nullable=False)
    settlement_currency = Column(
        String(3), nullable=True
    )  # Convert to specific currency

    # Revenue tracking
    total_revenue_earned = Column(
        Numeric(15, 2), default=Decimal("0.00"), nullable=False
    )
    total_fees_paid = Column(Numeric(15, 2), default=Decimal("0.00"), nullable=False)
    last_payout_date = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", backref="payout_settings")

    def __repr__(self):
        return f"<UserPayoutSettings(user_id={self.user_id}, fee_rate={self.platform_fee_rate})>"


class UserSession(Base):
    """
    User session management for JWT refresh tokens
    """

    __tablename__ = "user_sessions"
    __table_args__ = (
        Index("ix_user_sessions_user_id", "user_id"),
        Index("ix_user_sessions_is_active", "is_active"),
    )

    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # Session data
    refresh_token = Column(String(500), unique=True, index=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    device_info = Column(Text, nullable=True)  # User agent, IP, etc.
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    user = relationship("User", back_populates="sessions")

    @property
    def is_expired(self) -> bool:
        """Check if session is expired"""
        if not self.expires_at:
            return True

        # Ensure both datetimes are timezone-aware for comparison
        now = datetime.now(timezone.utc)
        expires_at = self.expires_at
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=timezone.utc)
        return now > expires_at

    def revoke(self):
        """Revoke the session"""
        self.is_active = False

    def __repr__(self):
        return f"<UserSession(user_id={self.user_id}, active={self.is_active})>"
