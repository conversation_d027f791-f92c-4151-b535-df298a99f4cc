"""
Activity log model for tracking user actions on entities
"""

from sqlalchemy import <PERSON>SO<PERSON>, Column, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import UUID

from .base import Base


class ActivityLog(Base):
    """A simple activity log capturing CRUD actions on resources."""

    __tablename__ = "activity_logs"
    __table_args__ = (
        Index("ix_activity_logs_user_id_action", "user_id", "action"),
        Index("ix_activity_logs_entity_type_entity_id", "entity_type", "entity_id"),
        Index("ix_activity_logs_user_id_entity_type", "user_id", "entity_type"),
    )

    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, index=True
    )
    client_id = Column(
        UUID(as_uuid=True), ForeignKey("clients.id"), nullable=True, index=True
    )
    entity_type = Column(
        String(50), nullable=False, index=True
    )  # e.g., 'invoice', 'project', 'payment', 'security'
    entity_id = Column(String(64), nullable=False, index=True)
    action = Column(
        String(100), nullable=False
    )  # e.g., 'create', 'update', 'delete', 'view', 'download'
    description = Column(Text, nullable=True)  # Human-readable description
    ip_address = Column(String(45), nullable=True)  # IPv4/IPv6 address
    user_agent = Column(String(500), nullable=True)  # Browser/client info
    extra_data = Column(JSON, nullable=True)  # Additional context data
    details = Column(
        Text, nullable=True
    )  # Legacy field - JSON string or free text with change summary

    def __repr__(self) -> str:
        return f"<ActivityLog(user_id={self.user_id}, entity={self.entity_type}:{self.entity_id}, action={self.action})>"
