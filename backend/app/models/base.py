"""
Base model class with common fields and functionality
"""

import uuid
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func

from app.database import Base as SQLAlchemyBase


class Base(SQLAlchemyBase):
    """
    Base model class with common fields for all entities
    Includes UUID primary key, timestamps, and soft delete functionality
    """

    __abstract__ = True

    # Primary key as UUID
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Audit trail fields
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Soft delete support
    deleted_at = Column(DateTime(timezone=True), nullable=True, default=None)

    @declared_attr
    def __tablename__(cls):
        """Generate table name from class name"""
        return cls.__name__.lower() + "s"

    def soft_delete(self):
        """Mark record as deleted without removing from database"""
        self.deleted_at = datetime.now(timezone.utc)

    def restore(self):
        """Restore soft-deleted record"""
        self.deleted_at = None

    @property
    def is_deleted(self) -> bool:
        """Check if record is soft-deleted"""
        return self.deleted_at is not None

    def to_dict(self) -> dict:
        """Convert model instance to dictionary"""
        return {
            column.name: getattr(self, column.name) for column in self.__table__.columns
        }

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"
