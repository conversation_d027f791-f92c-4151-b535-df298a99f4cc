"""Payment error logging model"""

import uuid
from datetime import datetime, timezone
from typing import Dict, Optional

from sqlalchemy import JSO<PERSON>, Boolean, Column, DateTime, Integer, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

from app.database import Base


class PaymentErrorLog(Base):
    """Model for logging payment processing errors"""

    __tablename__ = "payment_error_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    gateway = Column(String(50), nullable=False, index=True)
    error_type = Column(String(50), nullable=False, index=True)
    severity = Column(String(20), nullable=False, index=True)
    message = Column(Text, nullable=False)
    reference = Column(String(255), nullable=True, index=True)
    retryable = Column(Boolean, default=False, nullable=False)
    retry_after = Column(Integer, nullable=True)
    gateway_response = Column(JSON, nullable=True)
    context = Column(JSON, nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False,
        index=True,
    )

    def __repr__(self):
        return (
            f"<PaymentErrorLog(id={self.id}, gateway={self.gateway}, "
            f"error_type={self.error_type}, severity={self.severity})>"
        )

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        return {
            "id": str(self.id),
            "gateway": self.gateway,
            "error_type": self.error_type,
            "severity": self.severity,
            "message": self.message,
            "reference": self.reference,
            "retryable": self.retryable,
            "retry_after": self.retry_after,
            "gateway_response": self.gateway_response,
            "context": self.context,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
