"""
Client model for CRM foundation
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, ForeignKey, Index, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .base import Base


class Client(Base):
    """Client entity with contact information and relationship to User."""

    __tablename__ = "clients"
    __table_args__ = (
        Index("ix_clients_user_id_name", "user_id", "name"),
        Index("ix_clients_user_id_email", "user_id", "email"),
        Index("ix_clients_user_id_company", "user_id", "company"),
        Index("ix_clients_user_id_is_active", "user_id", "is_active"),
    )

    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )

    # Workspace relationship
    workspace_id = Column(
        UUID(as_uuid=True), ForeignKey("workspaces.id"), nullable=False, index=True
    )

    # Contact info
    name = Column(String(200), nullable=False, index=True)
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(50), nullable=True)
    company = Column(String(255), nullable=True, index=True)
    position = Column(String(100), nullable=True)

    # Address
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True)

    # Meta
    notes = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)

    # Portal access
    portal_enabled = Column(Boolean, default=False, nullable=False)
    portal_access_token = Column(String(255), unique=True, nullable=True, index=True)
    portal_passcode_hash = Column(String(255), nullable=True)
    portal_passcode_enabled = Column(Boolean, default=False, nullable=False)

    # Relationships
    user = relationship("User", backref="clients")
    workspace = relationship("Workspace", back_populates="clients")

    def __repr__(self) -> str:
        return f"<Client(name={self.name}, email={self.email})>"
