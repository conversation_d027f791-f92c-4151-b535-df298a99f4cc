"""
Live Timer API endpoints for DevHQ
Revolutionary time tracking with real-time updates and smart features
"""

import uuid
from datetime import datetime, timezone
from typing import Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.async_timer_service import AsyncSmartTimerService
from app.database import get_async_db
from app.dependencies import get_current_user
from app.models import User
from app.schemas.timer import (TimerHeartbeatRequest, TimerHeartbeatResponse,
                               TimerResponse, TimerStartRequest,
                               TimerStatusResponse)

router = APIRouter(prefix="/api/v1/timer", tags=["timer"])


@router.post(
    "/start", response_model=TimerResponse, status_code=status.HTTP_201_CREATED
)
async def start_timer(
    timer_data: TimerStartRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Start a new timer for a project/task."""

    timer_service = AsyncSmartTimerService(db)

    try:
        timer_entry = await timer_service.start_timer(
            user_id=current_user.id,
            project_id=timer_data.project_id,
            description=timer_data.description or "",
            task_name=timer_data.task_name or "",
            device_id=timer_data.device_id or "web",
            mood_rating=timer_data.mood_rating,
            focus_level=timer_data.focus_level,
        )

        # Schedule cleanup of stale timers in background
        background_tasks.add_task(timer_service.cleanup_stale_timers)

        return TimerResponse(
            timer_id=str(timer_entry.id),
            project_id=str(timer_entry.project_id),
            description=timer_entry.description or "",
            task_name=timer_entry.task_name or "",
            start_time=timer_entry.start_time,
            is_active=True,
            is_paused=False,
            current_duration_minutes=0,
            device_id=timer_entry.timer_device_id,
            mood_rating=timer_entry.mood_rating,
            focus_level=timer_entry.focus_level,
        )

    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start timer",
        )


@router.post("/stop", response_model=TimerResponse)
async def stop_timer(
    timer_id: Optional[str] = None,
    device_id: str = "web",
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Stop the active timer."""

    timer_service = AsyncSmartTimerService(db)

    try:
        timer_uuid = uuid.UUID(timer_id) if timer_id else None
        timer_entry = await timer_service.stop_timer(
            user_id=current_user.id, timer_id=timer_uuid, device_id=device_id
        )

        if not timer_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No active timer found"
            )

        return TimerResponse(
            timer_id=str(timer_entry.id),
            project_id=str(timer_entry.project_id),
            description=timer_entry.description or "",
            task_name=timer_entry.task_name or "",
            start_time=timer_entry.start_time,
            end_time=timer_entry.end_time,
            is_active=False,
            is_paused=False,
            current_duration_minutes=timer_entry.duration_minutes,
            device_id=timer_entry.timer_device_id,
            productivity_score=(
                float(timer_entry.productivity_score)
                if timer_entry.productivity_score
                else None
            ),
            billable_amount=(
                float(timer_entry.billable_amount)
                if timer_entry.billable_amount
                else None
            ),
            interruption_count=timer_entry.interruption_count,
        )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid timer ID format"
        )


@router.post("/pause", response_model=TimerResponse)
async def pause_timer(
    timer_id: Optional[str] = None,
    device_id: str = "web",
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Pause the active timer."""

    timer_service = AsyncSmartTimerService(db)

    try:
        timer_uuid = uuid.UUID(timer_id) if timer_id else None
        timer_entry = await timer_service.pause_timer(
            user_id=current_user.id, timer_id=timer_uuid, device_id=device_id
        )

        if not timer_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active timer found or timer already paused",
            )

        # Calculate current duration for response
        current_duration = 0
        if timer_entry.start_time:
            # Ensure both datetimes are timezone-aware for calculation
            now = datetime.now(timezone.utc)
            start_time = timer_entry.start_time
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            total_duration = (now - start_time).total_seconds() / 60
            current_duration = max(
                0, int(total_duration - timer_entry.total_pause_duration)
            )

        return TimerResponse(
            timer_id=str(timer_entry.id),
            project_id=str(timer_entry.project_id),
            description=timer_entry.description or "",
            task_name=timer_entry.task_name or "",
            start_time=timer_entry.start_time,
            is_active=True,
            is_paused=True,
            current_duration_minutes=current_duration,
            device_id=timer_entry.timer_device_id,
        )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid timer ID format"
        )


@router.post("/resume", response_model=TimerResponse)
async def resume_timer(
    timer_id: Optional[str] = None,
    device_id: str = "web",
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Resume a paused timer."""

    timer_service = AsyncSmartTimerService(db)

    try:
        timer_uuid = uuid.UUID(timer_id) if timer_id else None
        timer_entry = await timer_service.resume_timer(
            user_id=current_user.id, timer_id=timer_uuid, device_id=device_id
        )

        if not timer_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No paused timer found"
            )

        # Calculate current duration for response
        current_duration = 0
        if timer_entry.start_time:
            # Ensure both datetimes are timezone-aware for calculation
            now = datetime.now(timezone.utc)
            start_time = timer_entry.start_time
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            total_duration = (now - start_time).total_seconds() / 60
            current_duration = max(
                0, int(total_duration - timer_entry.total_pause_duration)
            )

        return TimerResponse(
            timer_id=str(timer_entry.id),
            project_id=str(timer_entry.project_id),
            description=timer_entry.description or "",
            task_name=timer_entry.task_name or "",
            start_time=timer_entry.start_time,
            is_active=True,
            is_paused=False,
            current_duration_minutes=current_duration,
            device_id=timer_entry.timer_device_id,
        )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid timer ID format"
        )


@router.get("/status", response_model=TimerStatusResponse)
async def get_timer_status(
    device_id: str = "web",
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get current timer status for the user."""

    timer_service = AsyncSmartTimerService(db)
    timer_status = await timer_service.get_timer_status(
        user_id=current_user.id, device_id=device_id
    )

    if not timer_status:
        return TimerStatusResponse(has_active_timer=False, timer=None)

    return TimerStatusResponse(
        has_active_timer=True,
        timer=TimerResponse(
            timer_id=timer_status["timer_id"],
            project_id=timer_status["project_id"],
            description=timer_status["description"],
            task_name=timer_status["task_name"],
            start_time=timer_status["start_time"],
            is_active=timer_status["is_active"],
            is_paused=timer_status["is_paused"],
            current_duration_minutes=timer_status["current_duration_minutes"],
            device_id=device_id,
            interruption_count=timer_status.get("interruption_count", 0),
            mood_rating=timer_status.get("mood_rating"),
            focus_level=timer_status.get("focus_level"),
        ),
    )


@router.post("/heartbeat", response_model=TimerHeartbeatResponse)
async def timer_heartbeat(
    heartbeat_data: TimerHeartbeatRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Send heartbeat to keep timer alive and get current status."""

    timer_service = AsyncSmartTimerService(db)

    try:
        timer_uuid = (
            uuid.UUID(heartbeat_data.timer_id) if heartbeat_data.timer_id else None
        )
        timer_status = await timer_service.heartbeat(
            user_id=current_user.id,
            timer_id=timer_uuid,
            device_id=heartbeat_data.device_id or "web",
            interruption_occurred=heartbeat_data.interruption_occurred or False,
        )

        if not timer_status:
            return TimerHeartbeatResponse(
                success=False, message="No active timer found", timer_status=None
            )

        return TimerHeartbeatResponse(
            success=True, message="Heartbeat received", timer_status=timer_status
        )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid timer ID format"
        )


@router.post("/cleanup")
async def cleanup_stale_timers(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Cleanup stale timers (admin endpoint)."""

    timer_service = AsyncSmartTimerService(db)
    cleaned_count = await timer_service.cleanup_stale_timers()

    return {
        "message": f"Cleaned up {cleaned_count} stale timers",
        "cleaned_count": cleaned_count,
    }
