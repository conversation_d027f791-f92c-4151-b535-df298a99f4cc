"""
Activity Logs API router for tracking user actions
"""

from typing import Optional

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from app.database import get_db
from app.dependencies import get_current_active_user
from app.models.activity import ActivityLog
from app.models.user import User
from app.schemas.activity import ActivityLogResponse, PaginatedActivityResponse

router = APIRouter()


@router.get("/", response_model=PaginatedActivityResponse)
async def list_activity_logs(
    entity_type: Optional[str] = Query(
        None, description="Filter by entity type (e.g., 'client', 'user')"
    ),
    action: Optional[str] = Query(
        None, description="Filter by action (e.g., 'create', 'update', 'delete')"
    ),
    q: Optional[str] = Query(None, description="Search in details field"),
    sort_order: Optional[str] = Query(
        "desc", description="Sort order: asc|desc (default desc)"
    ),
    page: int = Query(1, ge=1, description="Page number (starts from 1)"),
    per_page: int = Query(
        25, ge=1, le=100, description="Items per page (1-100, default 25)"
    ),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    Get activity logs for the current authenticated user.

    Returns a paginated list of activity logs with optional filtering and sorting.
    Activity logs track user actions on various entities like clients, users, etc.
    """
    # Base query - only logs for the current user
    query = db.query(ActivityLog).filter(ActivityLog.user_id == current_user.id)

    # Apply filters
    if entity_type:
        query = query.filter(ActivityLog.entity_type == entity_type)

    if action:
        query = query.filter(ActivityLog.action == action)

    if q:
        # Search in details field (case insensitive)
        query = query.filter(ActivityLog.details.ilike(f"%{q}%"))

    # Apply sorting (by created_at only for now)
    if (sort_order or "desc").lower() == "asc":
        query = query.order_by(ActivityLog.created_at.asc())
    else:
        query = query.order_by(ActivityLog.created_at.desc())

    # Get total count
    total = query.count()

    # Apply pagination
    items = query.offset((page - 1) * per_page).limit(per_page).all()

    return PaginatedActivityResponse(
        total=total, page=page, per_page=per_page, items=items
    )
