"""
Payout management endpoints for platform revenue features
"""

import uuid
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.platform_fee_service import PlatformFeeService
from app.database import get_db
from app.dependencies import get_current_user
from app.models import Invoice, User

router = APIRouter(prefix="/payouts", tags=["payouts"])


@router.get("/settings")
async def get_payout_settings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get user's payout settings"""

    # Get or create payout settings
    payout_settings = getattr(current_user, "payout_settings", None)

    if not payout_settings:
        # Return default settings
        return {
            "platform_fee_rate": 0.025,
            "fee_tier": "starter",
            "payout_method": "bank_transfer",
            "payout_frequency": "weekly",
            "minimum_payout_amount": 100.00,
            "instant_settlement_enabled": False,
            "auto_settlement": True,
            "settlement_currency": None,
            "total_revenue_earned": 0.00,
            "total_fees_paid": 0.00,
            "last_payout_date": None,
        }

    return {
        "platform_fee_rate": float(payout_settings.platform_fee_rate),
        "fee_tier": payout_settings.fee_tier,
        "payout_method": payout_settings.payout_method,
        "payout_frequency": payout_settings.payout_frequency,
        "minimum_payout_amount": float(payout_settings.minimum_payout_amount),
        "instant_settlement_enabled": payout_settings.instant_settlement_enabled,
        "auto_settlement": payout_settings.auto_settlement,
        "settlement_currency": payout_settings.settlement_currency,
        "total_revenue_earned": float(payout_settings.total_revenue_earned),
        "total_fees_paid": float(payout_settings.total_fees_paid),
        "last_payout_date": payout_settings.last_payout_date,
        "payout_bank_account": payout_settings.payout_bank_account,
        "payout_mobile_money": payout_settings.payout_mobile_money,
    }


@router.put("/settings")
async def update_payout_settings(
    settings_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Update user's payout settings"""

    from app.models.user import UserPayoutSettings

    # Get or create payout settings
    payout_settings = (
        db.query(UserPayoutSettings)
        .filter(UserPayoutSettings.user_id == current_user.id)
        .first()
    )

    if not payout_settings:
        payout_settings = UserPayoutSettings(user_id=current_user.id)
        db.add(payout_settings)

    # Update allowed fields
    allowed_fields = [
        "payout_method",
        "payout_frequency",
        "minimum_payout_amount",
        "instant_settlement_enabled",
        "auto_settlement",
        "settlement_currency",
        "payout_bank_account",
        "payout_mobile_money",
    ]

    for field in allowed_fields:
        if field in settings_data:
            setattr(payout_settings, field, settings_data[field])

    db.commit()

    return {"message": "Payout settings updated successfully"}


@router.get("/fee-calculation")
async def calculate_fees(
    amount: Decimal = Query(..., description="Amount to calculate fees for"),
    currency: str = Query("USD", description="Currency code"),
    instant_settlement: bool = Query(
        False, description="Include instant settlement fee"
    ),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Calculate platform fees for a given amount"""

    platform_fee_service = PlatformFeeService(db)

    # Get user's custom fee rate
    user_fee_rate = platform_fee_service.get_user_fee_rate(current_user.id)

    # Calculate fees (assuming no gateway fee for estimation)
    fee_calculation = platform_fee_service.calculate_platform_fees(
        gross_amount=amount,
        currency=currency,
        gateway_fee=Decimal("0"),  # Will be calculated at payment time
        user_fee_rate=user_fee_rate,
        instant_settlement=instant_settlement,
    )

    return {
        "gross_amount": float(fee_calculation.gross_amount),
        "platform_fee": float(fee_calculation.platform_fee),
        "platform_fee_rate": float(user_fee_rate),
        "instant_settlement_fee": float(fee_calculation.instant_settlement_fee),
        "estimated_net_payout": float(
            fee_calculation.net_amount - fee_calculation.instant_settlement_fee
        ),
        "fee_breakdown": {
            k: float(v) for k, v in fee_calculation.fee_breakdown.items()
        },
    }


@router.get("/revenue-analytics")
async def get_revenue_analytics(
    days: int = Query(30, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get user's revenue analytics"""

    platform_fee_service = PlatformFeeService(db)

    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days)

    # Get paid invoices in the period
    paid_invoices = (
        db.query(Invoice)
        .filter(
            Invoice.user_id == current_user.id,
            Invoice.status == "paid",
            Invoice.paid_at >= start_date,
            Invoice.paid_at <= end_date,
        )
        .all()
    )

    # Calculate analytics
    total_gross_revenue = Decimal("0")
    total_platform_fees = Decimal("0")
    total_gateway_fees = Decimal("0")
    total_net_revenue = Decimal("0")
    revenue_by_currency = {}
    transaction_count = len(paid_invoices)

    for invoice in paid_invoices:
        total_gross_revenue += invoice.total_amount

        if invoice.platform_fee_amount:
            total_platform_fees += invoice.platform_fee_amount

        if invoice.gateway_fee_amount:
            total_gateway_fees += invoice.gateway_fee_amount

        if invoice.net_payout_amount:
            total_net_revenue += invoice.net_payout_amount

        # Group by currency
        currency = invoice.currency
        if currency not in revenue_by_currency:
            revenue_by_currency[currency] = {
                "gross": Decimal("0"),
                "net": Decimal("0"),
                "count": 0,
            }

        revenue_by_currency[currency]["gross"] += invoice.total_amount
        revenue_by_currency[currency]["net"] += invoice.net_payout_amount or Decimal(
            "0"
        )
        revenue_by_currency[currency]["count"] += 1

    # Calculate monthly volume for fee tier
    monthly_volume = platform_fee_service.calculate_monthly_volume(current_user.id)

    # Get fee tier recommendation
    total_monthly_usd = sum(
        volume * Decimal("0.8")  # Rough USD conversion
        for volume in monthly_volume.values()
    )
    recommended_fee_rate, fee_tier = platform_fee_service.get_fee_tier(
        total_monthly_usd
    )

    return {
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "days": days,
        },
        "summary": {
            "total_gross_revenue": float(total_gross_revenue),
            "total_platform_fees": float(total_platform_fees),
            "total_gateway_fees": float(total_gateway_fees),
            "total_net_revenue": float(total_net_revenue),
            "transaction_count": transaction_count,
            "average_transaction": (
                float(total_gross_revenue / transaction_count)
                if transaction_count > 0
                else 0
            ),
        },
        "revenue_by_currency": {
            currency: {
                "gross": float(data["gross"]),
                "net": float(data["net"]),
                "count": data["count"],
            }
            for currency, data in revenue_by_currency.items()
        },
        "fee_tier": {
            "current_tier": fee_tier,
            "current_rate": float(recommended_fee_rate),
            "monthly_volume_usd": float(total_monthly_usd),
        },
        "monthly_volume": {
            currency: float(volume) for currency, volume in monthly_volume.items()
        },
    }


@router.get("/settlement-history")
async def get_settlement_history(
    limit: int = Query(50, description="Number of settlements to return"),
    offset: int = Query(0, description="Offset for pagination"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get user's settlement history"""

    # Get settled invoices
    settled_invoices = (
        db.query(Invoice)
        .filter(
            Invoice.user_id == current_user.id,
            Invoice.settlement_status == "completed",
        )
        .order_by(Invoice.settled_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )

    settlements = []
    for invoice in settled_invoices:
        settlements.append(
            {
                "id": str(invoice.id),
                "invoice_number": invoice.invoice_number,
                "gross_amount": float(invoice.total_amount),
                "platform_fee": float(invoice.platform_fee_amount or 0),
                "gateway_fee": float(invoice.gateway_fee_amount or 0),
                "instant_settlement_fee": float(invoice.instant_settlement_fee or 0),
                "net_payout": float(invoice.net_payout_amount or 0),
                "currency": invoice.currency,
                "settlement_type": invoice.settlement_type,
                "payment_gateway": invoice.payment_gateway,
                "settled_at": (
                    invoice.settled_at.isoformat() if invoice.settled_at else None
                ),
                "paid_at": invoice.paid_at.isoformat() if invoice.paid_at else None,
            }
        )

    return {
        "settlements": settlements,
        "pagination": {
            "limit": limit,
            "offset": offset,
            "total": len(settlements),
        },
    }


@router.post("/request-instant-settlement/{invoice_id}")
async def request_instant_settlement(
    invoice_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Request instant settlement for a paid invoice"""

    # Get invoice
    invoice = (
        db.query(Invoice)
        .filter(
            Invoice.id == invoice_id,
            Invoice.user_id == current_user.id,
            Invoice.status == "paid",
            Invoice.settlement_status.in_(["pending", "processing"]),
        )
        .first()
    )

    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found or not eligible for instant settlement",
        )

    # Check if user has instant settlement enabled
    payout_settings = getattr(current_user, "payout_settings", None)
    if not payout_settings or not payout_settings.instant_settlement_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Instant settlement not enabled for this account",
        )

    # Calculate instant settlement fee
    platform_fee_service = PlatformFeeService(db)
    fee_calculation = platform_fee_service.calculate_invoice_payout(
        invoice=invoice,
        gateway_fee=invoice.gateway_fee_amount or Decimal("0"),
        instant_settlement=True,
    )

    # Update invoice
    invoice.settlement_type = "instant"
    invoice.instant_settlement_fee = fee_calculation.instant_settlement_fee
    invoice.net_payout_amount = fee_calculation.final_payout
    invoice.settlement_status = "processing"

    db.commit()

    return {
        "message": "Instant settlement requested successfully",
        "instant_settlement_fee": float(fee_calculation.instant_settlement_fee),
        "new_net_payout": float(fee_calculation.final_payout),
        "estimated_settlement_time": "Within 1 hour",
    }


@router.get("/fee-tiers")
async def get_fee_tiers():
    """Get available fee tiers and requirements"""

    return {
        "tiers": [
            {
                "name": "Starter",
                "fee_rate": 0.030,
                "monthly_volume_min": 0,
                "monthly_volume_max": 10000,
                "features": ["Standard settlement", "Email support"],
            },
            {
                "name": "Business",
                "fee_rate": 0.025,
                "monthly_volume_min": 10000,
                "monthly_volume_max": 50000,
                "features": [
                    "Standard settlement",
                    "Priority support",
                    "Analytics dashboard",
                ],
            },
            {
                "name": "Professional",
                "fee_rate": 0.020,
                "monthly_volume_min": 50000,
                "monthly_volume_max": 100000,
                "features": [
                    "Instant settlement",
                    "Dedicated support",
                    "Advanced analytics",
                    "Custom branding",
                ],
            },
            {
                "name": "Enterprise",
                "fee_rate": 0.015,
                "monthly_volume_min": 100000,
                "monthly_volume_max": None,
                "features": [
                    "Instant settlement",
                    "24/7 support",
                    "Custom integrations",
                    "Volume discounts",
                ],
            },
        ],
        "currency": "USD",
        "note": "Volume requirements are in USD equivalent per month",
    }
