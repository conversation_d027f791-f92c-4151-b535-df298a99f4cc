"""
WebSocket Router for Real-time Communication
Handles WebSocket endpoints and real-time API integration
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.websocket_manager import websocket_manager
from app.database import get_db
from app.dependencies import get_current_user
from app.models.user import User
from app.services.realtime_service import EventType, realtime_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/websocket", tags=["websocket"])


@router.get("/stats")
async def get_websocket_stats(current_user: User = Depends(get_current_user)):
    """Get WebSocket connection statistics"""
    try:
        websocket_stats = websocket_manager.get_connection_stats()
        realtime_stats = realtime_service.get_stats()

        return {
            "websocket": websocket_stats,
            "realtime_service": realtime_stats,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error(f"Error getting WebSocket stats: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get WebSocket statistics"
        )


@router.post("/broadcast")
async def broadcast_message(
    message_data: Dict[str, Any], current_user: User = Depends(get_current_user)
):
    """Broadcast message to specific rooms or users"""
    try:
        message_type = message_data.get("type")
        target_type = message_data.get(
            "target_type"
        )  # "user", "client", "project", "all_developers"
        target_id = message_data.get("target_id")
        event_type = message_data.get("event_type", "notification")
        data = message_data.get("data", {})

        # Add sender information
        data["sender_id"] = str(current_user.id)
        data["sender_name"] = current_user.full_name or current_user.email
        data["timestamp"] = datetime.now(timezone.utc).isoformat()

        # Route message based on target type
        if target_type == "user" and target_id:
            await websocket_manager.send_to_user(target_id, event_type, data)
        elif target_type == "client" and target_id:
            await websocket_manager.broadcast_to_client(target_id, event_type, data)
        elif target_type == "project" and target_id:
            await websocket_manager.broadcast_to_project(target_id, event_type, data)
        elif target_type == "all_developers":
            await websocket_manager.broadcast_to_developers(event_type, data)
        else:
            raise HTTPException(
                status_code=400, detail="Invalid target type or missing target ID"
            )

        return {
            "status": "sent",
            "target_type": target_type,
            "target_id": target_id,
            "event_type": event_type,
            "timestamp": data["timestamp"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error broadcasting message: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast message")


@router.post("/emit-event")
async def emit_realtime_event(
    event_data: Dict[str, Any], current_user: User = Depends(get_current_user)
):
    """Emit a real-time event through the event system"""
    try:
        event_type = event_data.get("event_type")
        data = event_data.get("data", {})

        # Validate event type
        try:
            event_enum = EventType(event_type)
        except ValueError:
            raise HTTPException(
                status_code=400, detail=f"Invalid event type: {event_type}"
            )

        # Add user context
        data["triggered_by"] = str(current_user.id)
        data["triggered_at"] = datetime.now(timezone.utc).isoformat()

        # Emit event
        await realtime_service.emit_event(event_enum, data)

        return {
            "status": "emitted",
            "event_type": event_type,
            "timestamp": data["triggered_at"],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error emitting event: {e}")
        raise HTTPException(status_code=500, detail="Failed to emit event")


@router.post("/notify-user")
async def notify_user(
    notification_data: Dict[str, Any], current_user: User = Depends(get_current_user)
):
    """Send notification to a specific user"""
    try:
        user_id = notification_data.get("user_id")
        title = notification_data.get("title")
        message = notification_data.get("message")
        notification_type = notification_data.get("type", "info")
        action_url = notification_data.get("action_url")

        if not user_id or not title or not message:
            raise HTTPException(
                status_code=400, detail="Missing required notification fields"
            )

        await realtime_service.emit_notification(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=notification_type,
            action_url=action_url,
        )

        return {
            "status": "sent",
            "user_id": user_id,
            "title": title,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending notification: {e}")
        raise HTTPException(status_code=500, detail="Failed to send notification")


@router.get("/active-connections")
async def get_active_connections(current_user: User = Depends(get_current_user)):
    """Get list of active WebSocket connections"""
    try:
        stats = websocket_manager.get_connection_stats()

        # Get detailed connection info (without sensitive data)
        connections = []
        for sid, info in websocket_manager.active_connections.items():
            connections.append(
                {
                    "session_id": sid[:8] + "...",  # Truncated for privacy
                    "user_type": info.get("user_type"),
                    "connected_at": (
                        info.get("connected_at").isoformat()
                        if info.get("connected_at")
                        else None
                    ),
                    "last_activity": (
                        info.get("last_activity").isoformat()
                        if info.get("last_activity")
                        else None
                    ),
                }
            )

        return {
            "stats": stats,
            "connections": connections,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting active connections: {e}")
        raise HTTPException(status_code=500, detail="Failed to get active connections")


@router.post("/start-realtime-service")
async def start_realtime_service(current_user: User = Depends(get_current_user)):
    """Start the real-time event processing service"""
    try:
        await realtime_service.start_processing()
        return {
            "status": "started",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error(f"Error starting real-time service: {e}")
        raise HTTPException(status_code=500, detail="Failed to start real-time service")


@router.post("/stop-realtime-service")
async def stop_realtime_service(current_user: User = Depends(get_current_user)):
    """Stop the real-time event processing service"""
    try:
        await realtime_service.stop_processing()
        return {
            "status": "stopped",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error(f"Error stopping real-time service: {e}")
        raise HTTPException(status_code=500, detail="Failed to stop real-time service")


@router.get("/health")
async def websocket_health_check():
    """Health check for WebSocket services"""
    try:
        websocket_stats = websocket_manager.get_connection_stats()
        realtime_stats = realtime_service.get_stats()

        health_status = {
            "websocket_manager": "healthy",
            "realtime_service": (
                "healthy" if realtime_stats["processing"] else "stopped"
            ),
            "active_connections": websocket_stats["total_connections"],
            "queue_size": realtime_stats["queue_size"],
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        return health_status

    except Exception as e:
        logger.error(f"Error in WebSocket health check: {e}")
        return {
            "websocket_manager": "error",
            "realtime_service": "error",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# Event emission helpers for other services
class WebSocketEventEmitter:
    """Helper class for emitting WebSocket events from other services"""

    @staticmethod
    async def emit_approval_created(
        approval_id: str, project_id: str, client_id: str, user_id: str
    ):
        """Emit approval created event"""
        await realtime_service.emit_event(
            EventType.APPROVAL_CREATED,
            {
                "approval_id": approval_id,
                "project_id": project_id,
                "client_id": client_id,
                "user_id": user_id,
            },
        )

    @staticmethod
    async def emit_project_updated(
        project_id: str, client_id: str, user_id: str, changes: Dict[str, Any]
    ):
        """Emit project updated event"""
        await realtime_service.emit_event(
            EventType.PROJECT_UPDATED,
            {
                "project_id": project_id,
                "client_id": client_id,
                "user_id": user_id,
                "changes": changes,
            },
        )

    @staticmethod
    async def emit_invoice_created(
        invoice_id: str, client_id: str, project_id: str, user_id: str
    ):
        """Emit invoice created event"""
        await realtime_service.emit_event(
            EventType.INVOICE_CREATED,
            {
                "invoice_id": invoice_id,
                "client_id": client_id,
                "project_id": project_id,
                "user_id": user_id,
            },
        )

    @staticmethod
    async def emit_client_activity(
        client_id: str, activity_type: str, details: Dict[str, Any]
    ):
        """Emit client activity event"""
        await realtime_service.emit_client_activity(client_id, activity_type, details)

    @staticmethod
    async def emit_notification(
        user_id: str,
        title: str,
        message: str,
        notification_type: str = "info",
        action_url: Optional[str] = None,
    ):
        """Emit notification"""
        await realtime_service.emit_notification(
            user_id, title, message, notification_type, action_url
        )


# Export the event emitter for use in other modules
websocket_events = WebSocketEventEmitter()
