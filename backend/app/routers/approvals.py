"""
Approval management endpoints for developer-side approval workflows
"""

import uuid
from datetime import datetime, timezone
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session, joinedload

from app.core.approval_service import ApprovalService
from app.database import get_db
from app.dependencies import get_current_user
from app.models import ClientApproval, ClientFeedback, User
from app.schemas.approval import (ApprovalAnalyticsResponse, ApprovalCreate,
                                  ApprovalListResponse,
                                  ApprovalReminderRequest, ApprovalResponse,
                                  ApprovalSuggestionsResponse, ApprovalUpdate,
                                  ApprovalWithDetailsResponse,
                                  BulkReminderRequest,
                                  DeveloperResponseRequest,
                                  PaginatedApprovalsResponse)

router = APIRouter(prefix="/api/v1/approvals", tags=["approvals"])


@router.post("/", response_model=ApprovalResponse, status_code=status.HTTP_201_CREATED)
async def create_approval_request(
    approval_data: ApprovalCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Create a new approval request for client review."""
    try:
        approval_service = ApprovalService(db)
        approval = approval_service.create_approval_request(
            approval_data, current_user.id
        )

        return ApprovalResponse.model_validate(approval)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create approval request: {str(e)}",
        )


@router.get("/", response_model=PaginatedApprovalsResponse)
async def list_approval_requests(
    page: int = Query(1, ge=1),
    size: int = Query(25, ge=1, le=100),
    status_filter: Optional[str] = Query(
        None, pattern="^(pending|approved|revision_requested|superseded)$"
    ),
    priority_filter: Optional[str] = Query(None, pattern="^(low|medium|high|urgent)$"),
    project_id: Optional[uuid.UUID] = Query(None),
    client_id: Optional[uuid.UUID] = Query(None),
    overdue_only: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """List user's approval requests with filtering and pagination."""
    try:
        # Build query
        query = (
            db.query(ClientApproval)
            .options(
                joinedload(ClientApproval.client), joinedload(ClientApproval.project)
            )
            .filter(
                ClientApproval.user_id == current_user.id,
                ClientApproval.deleted_at.is_(None),
            )
        )

        # Apply filters
        if status_filter:
            query = query.filter(ClientApproval.status == status_filter)

        if priority_filter:
            query = query.filter(ClientApproval.priority == priority_filter)

        if project_id:
            query = query.filter(ClientApproval.project_id == project_id)

        if client_id:
            query = query.filter(ClientApproval.client_id == client_id)

        if overdue_only:
            query = query.filter(
                ClientApproval.status == "pending",
                ClientApproval.due_date < datetime.now(timezone.utc),
            )

        # Get total count
        total = query.count()

        # Apply pagination
        offset = (page - 1) * size
        approvals = (
            query.order_by(
                ClientApproval.priority == "urgent",
                ClientApproval.priority == "high",
                ClientApproval.requested_at.desc(),
            )
            .offset(offset)
            .limit(size)
            .all()
        )

        # Calculate pagination info
        pages = (total + size - 1) // size
        has_next = page < pages
        has_prev = page > 1

        return PaginatedApprovalsResponse(
            items=[ApprovalResponse.model_validate(approval) for approval in approvals],
            total=total,
            page=page,
            size=size,
            pages=pages,
            has_next=has_next,
            has_prev=has_prev,
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve approvals: {str(e)}",
        )


@router.get("/{approval_id}", response_model=ApprovalWithDetailsResponse)
async def get_approval_details(
    approval_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get detailed approval information including feedback and activity."""
    try:
        approval = (
            db.query(ClientApproval)
            .options(
                joinedload(ClientApproval.client),
                joinedload(ClientApproval.project),
                joinedload(ClientApproval.feedback_items),
                joinedload(ClientApproval.activity_logs),
            )
            .filter(
                ClientApproval.id == approval_id,
                ClientApproval.user_id == current_user.id,
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

        if not approval:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Approval not found"
            )

        # Convert to response with additional details
        response_data = ApprovalResponse.model_validate(approval).model_dump()
        response_data.update(
            {
                "feedback_items": approval.feedback_items,
                "activity_logs": approval.activity_logs,
                "client_name": approval.client.name if approval.client else None,
                "client_email": approval.client.email if approval.client else None,
                "project_title": approval.project.title if approval.project else None,
            }
        )

        return ApprovalWithDetailsResponse(**response_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve approval details: {str(e)}",
        )


@router.put("/{approval_id}", response_model=ApprovalResponse)
async def update_approval_request(
    approval_id: uuid.UUID,
    approval_update: ApprovalUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Update approval request details (title, description, priority, etc.)."""
    try:
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.id == approval_id,
                ClientApproval.user_id == current_user.id,
                ClientApproval.status
                == "pending",  # Only allow updates to pending approvals
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

        if not approval:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Approval not found or cannot be updated",
            )

        # Update fields
        update_data = approval_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(approval, field, value)

        db.commit()
        db.refresh(approval)

        return ApprovalResponse.model_validate(approval)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update approval: {str(e)}",
        )


@router.delete("/{approval_id}", status_code=status.HTTP_204_NO_CONTENT)
async def cancel_approval_request(
    approval_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Cancel a pending approval request."""
    try:
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.id == approval_id,
                ClientApproval.user_id == current_user.id,
                ClientApproval.status == "pending",
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

        if not approval:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Approval not found or cannot be cancelled",
            )

        # Soft delete the approval
        approval.soft_delete()
        db.commit()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to cancel approval: {str(e)}",
        )


@router.post("/{approval_id}/remind", status_code=status.HTTP_200_OK)
async def send_approval_reminder(
    approval_id: uuid.UUID,
    reminder_request: ApprovalReminderRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Send reminder to client for pending approval."""
    try:
        approval_service = ApprovalService(db)
        success = approval_service.send_approval_reminder(
            approval_id, current_user.id, reminder_request.message
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Approval not found or not in pending status",
            )

        return {"message": "Reminder sent successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send reminder: {str(e)}",
        )


@router.get("/analytics/insights", response_model=ApprovalAnalyticsResponse)
async def get_approval_analytics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get approval analytics and insights for the current user."""
    try:
        approval_service = ApprovalService(db)
        analytics = approval_service.generate_approval_analytics(current_user.id)
        return analytics

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate analytics: {str(e)}",
        )


@router.get("/overdue/list", response_model=List[ApprovalResponse])
async def get_overdue_approvals(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get all overdue approvals for the current user."""
    try:
        approval_service = ApprovalService(db)
        overdue_approvals = approval_service.get_overdue_approvals(current_user.id)
        return [
            ApprovalResponse.model_validate(approval) for approval in overdue_approvals
        ]

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve overdue approvals: {str(e)}",
        )


@router.post("/bulk-remind", status_code=status.HTTP_200_OK)
async def bulk_send_reminders(
    bulk_request: BulkReminderRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Send reminders for multiple approvals."""
    try:
        approval_service = ApprovalService(db)
        result = approval_service.bulk_send_reminders(
            bulk_request.approval_ids, current_user.id, bulk_request.message
        )
        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send bulk reminders: {str(e)}",
        )


@router.get("/suggestions/detect", response_model=ApprovalSuggestionsResponse)
async def detect_approval_suggestions(
    project_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get intelligent suggestions for items ready for approval."""
    try:
        approval_service = ApprovalService(db)
        suggestions = approval_service.auto_detect_approvables(
            project_id, current_user.id
        )
        return suggestions

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to detect approval suggestions: {str(e)}",
        )


@router.put(
    "/{approval_id}/feedback/{feedback_id}/respond", status_code=status.HTTP_200_OK
)
async def respond_to_feedback(
    approval_id: uuid.UUID,
    feedback_id: uuid.UUID,
    response_request: DeveloperResponseRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Respond to client feedback on an approval."""
    try:
        # Verify approval ownership
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.id == approval_id,
                ClientApproval.user_id == current_user.id,
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

        if not approval:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Approval not found"
            )

        # Get feedback item
        feedback = (
            db.query(ClientFeedback)
            .filter(
                ClientFeedback.id == feedback_id,
                ClientFeedback.approval_id == approval_id,
            )
            .first()
        )

        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Feedback not found"
            )

        # Mark feedback as addressed
        feedback.mark_as_addressed(response_request.response)
        db.commit()

        return {"message": "Response submitted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to respond to feedback: {str(e)}",
        )
