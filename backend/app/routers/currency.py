"""
Currency conversion and exchange rate endpoints
"""

from decimal import Decimal
from typing import Dict, Optional

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_async_db
from app.core.error_handlers import ValidationException
from app.models.user import User
from app.core.base_router import BaseRouterMixin, StandardResponse
from app.services.currency import CurrencyService
from app.dependencies import get_current_active_user
from pydantic import BaseModel, Field


class CurrencyConversionRequest(BaseModel):
    """Request schema for currency conversion"""
    
    amount: Decimal = Field(..., gt=0, description="Amount to convert")
    from_currency: str = Field(..., min_length=3, max_length=3, description="Source currency code")
    to_currency: str = Field(..., min_length=3, max_length=3, description="Target currency code")


class CurrencyConversionResponse(BaseModel):
    """Response schema for currency conversion"""
    
    original_amount: Decimal
    converted_amount: Decimal
    from_currency: str
    to_currency: str
    exchange_rate: Decimal
    conversion_date: str


class ExchangeRatesResponse(BaseModel):
    """Response schema for exchange rates"""
    
    base_currency: str
    rates: Dict[str, float]
    last_updated: str


class SupportedCurrenciesResponse(BaseModel):
    """Response schema for supported currencies"""
    
    currencies: Dict[str, str]


class CurrencySymbolResponse(BaseModel):
    """Response schema for currency symbol"""
    
    currency_code: str
    symbol: str


class UserCurrencyUpdateRequest(BaseModel):
    """Request schema for updating user's default currency"""
    
    new_currency: str = Field(..., min_length=3, max_length=3, description="New default currency code")
    convert_existing_data: bool = Field(default=False, description="Whether to convert existing financial data")


class CurrencyRouter(BaseRouterMixin):
    """Currency conversion and exchange rate router"""

    def __init__(self):
        super().__init__(prefix="/currency", tags=["Currency"])
        self._register_routes()

    def _register_routes(self):
        """Register all currency-related routes"""

        @self.router.get(
            "/rates/{base_currency}",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def get_exchange_rates(
            base_currency: str,
            db: AsyncSession = Depends(get_async_db),
        ):
            """Get current exchange rates for a base currency"""
            try:
                currency_service = CurrencyService(db)
                rates = await currency_service.get_exchange_rates(base_currency.upper())
                
                response_data = ExchangeRatesResponse(
                    base_currency=base_currency.upper(),
                    rates=rates,
                    last_updated=currency_service._rate_cache.get(f"rates_{base_currency.upper()}", (None, None))[1].isoformat() if f"rates_{base_currency.upper()}" in currency_service._rate_cache else None
                )
                
                return self.create_success_response(
                    message="Exchange rates retrieved successfully",
                    data=response_data.model_dump()
                )
            except Exception as e:
                return self.handle_error(e)

        @self.router.post(
            "/convert",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def convert_currency(
            conversion_request: CurrencyConversionRequest,
            db: AsyncSession = Depends(get_async_db),
        ):
            """Convert amount between currencies"""
            try:
                currency_service = CurrencyService(db)
                
                # Validate currency codes
                from_currency = conversion_request.from_currency.upper()
                to_currency = conversion_request.to_currency.upper()
                
                converted_amount = await currency_service.convert_currency(
                    conversion_request.amount,
                    from_currency,
                    to_currency
                )
                
                # Calculate exchange rate
                if from_currency == to_currency:
                    exchange_rate = Decimal("1.0")
                else:
                    exchange_rate = converted_amount / conversion_request.amount
                
                response_data = CurrencyConversionResponse(
                    original_amount=conversion_request.amount,
                    converted_amount=converted_amount,
                    from_currency=from_currency,
                    to_currency=to_currency,
                    exchange_rate=exchange_rate,
                    conversion_date=currency_service._rate_cache.get(f"rates_{from_currency}", (None, None))[1].isoformat() if f"rates_{from_currency}" in currency_service._rate_cache else None
                )
                
                return self.create_success_response(
                    message="Currency converted successfully",
                    data=response_data.model_dump()
                )
            except Exception as e:
                return self.handle_error(e)

        @self.router.get(
            "/supported",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def get_supported_currencies(
            db: AsyncSession = Depends(get_async_db),
        ):
            """Get list of supported currencies"""
            try:
                currency_service = CurrencyService(db)
                currencies = await currency_service.get_supported_currencies()
                
                response_data = SupportedCurrenciesResponse(currencies=currencies)
                
                return self.create_success_response(
                    message="Supported currencies retrieved successfully",
                    data=response_data.model_dump()
                )
            except Exception as e:
                return self.handle_error(e)

        @self.router.get(
            "/symbol/{currency_code}",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def get_currency_symbol(
            currency_code: str,
            db: AsyncSession = Depends(get_async_db),
        ):
            """Get currency symbol for a currency code"""
            try:
                currency_service = CurrencyService(db)
                symbol = currency_service.get_currency_symbol(currency_code.upper())
                
                response_data = CurrencySymbolResponse(
                    currency_code=currency_code.upper(),
                    symbol=symbol
                )
                
                return self.create_success_response(
                    message="Currency symbol retrieved successfully",
                    data=response_data.model_dump()
                )
            except Exception as e:
                return self.handle_error(e)

        @self.router.put(
            "/user/default",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def update_user_default_currency(
            currency_update: UserCurrencyUpdateRequest,
            current_user: User = Depends(get_current_active_user),
            db: AsyncSession = Depends(get_async_db),
        ):
            """Update user's default currency with optional data conversion"""
            try:
                currency_service = CurrencyService(db)
                
                # Get current user settings to find old currency
                from app.services.user import UserService
                user_service = UserService(db)
                settings = await user_service.get_user_settings(current_user.id)
                
                old_currency = settings.default_currency if settings else "USD"
                new_currency = currency_update.new_currency.upper()
                
                conversion_result = None
                if currency_update.convert_existing_data and old_currency != new_currency:
                    conversion_result = await currency_service.convert_user_balances(
                        str(current_user.id),
                        old_currency,
                        new_currency
                    )
                
                # Update user settings
                from app.schemas.user import UserSettingsUpdate
                settings_update = UserSettingsUpdate(default_currency=new_currency)
                await user_service.update_user_settings(current_user.id, settings_update)
                
                response_data = {
                    "old_currency": old_currency,
                    "new_currency": new_currency,
                    "conversion_applied": currency_update.convert_existing_data,
                    "conversion_result": conversion_result
                }
                
                return self.create_success_response(
                    message="Default currency updated successfully",
                    data=response_data
                )
            except Exception as e:
                return self.handle_error(e)


# Create router instance
currency_router = CurrencyRouter()
