"""
Client portal endpoints for secure invoice access and approval workflows without authentication
"""

import uuid
from datetime import datetime, timed<PERSON>ta
from typing import List

from fastapi import (APIRouter, Depends, File, Form, HTTPException, Query,
                     Request, UploadFile, status)
from slowapi import Limiter
from slowapi.util import get_remote_address
from sqlalchemy.orm import Session, joinedload

from app.core.approval_service import ApprovalService
from app.core.file_upload_service import file_upload_service
from app.core.payment_service import (PaymentProcessingService,
                                      get_paystack_service)
from app.database import get_db
from app.models import Invoice
from app.models.approval import ClientApproval
from app.models.project import Project
from app.routers.websocket import websocket_events
from app.schemas.approval import (ClientDecisionRequest, ClientFeedbackCreate,
                                  ClientPortalApprovalResponse,
                                  ClientPortalDashboardResponse)
from app.schemas.invoice import (ClientInvoicePortalResponse,
                                 ClientPaymentInitiationRequest,
                                 PaymentLinkResponse)
from app.services.realtime_service import realtime_service

router = APIRouter(prefix="/portal", tags=["client-portal"])
limiter = Limiter(key_func=get_remote_address)


@router.get("/{token}/invoice/{invoice_id}", response_model=ClientInvoicePortalResponse)
@limiter.limit("10/minute")
async def get_client_invoice(
    request: Request, token: str, invoice_id: uuid.UUID, db: Session = Depends(get_db)
):
    """Get invoice details for client portal access using secure token."""
    try:
        # Find invoice by token and ID
        invoice = (
            db.query(Invoice)
            .filter(
                Invoice.id == invoice_id,
                Invoice.payment_token == token,
                Invoice.status.in_(["sent", "viewed", "overdue", "paid"]),
            )
            .first()
        )

        if not invoice:
            raise HTTPException(
                status_code=404, detail="Invoice not found or access denied"
            )

        # Mark as viewed if first time
        if invoice.status == "sent":
            invoice.mark_as_viewed()
            db.commit()

        # Get supported payment methods
        try:
            paystack = get_paystack_service()
            payment_methods = await paystack.get_supported_payment_methods(
                invoice.currency
            )
        except ValueError:
            # Fallback if Paystack is not configured
            payment_methods = ["card", "bank_transfer"]

        # Prepare company details
        company_details = invoice.company_details or {
            "name": "DevHQ",
            "email": "<EMAIL>",
            "phone": "******-0123",
            "address": "123 Business St, City, Country",
        }

        # Prepare billing address
        billing_address = invoice.billing_address or {
            "name": invoice.client.name,
            "email": invoice.client.email,
            "company": invoice.client.company,
            "address_line1": invoice.client.address_line1,
            "address_line2": invoice.client.address_line2,
            "city": invoice.client.city,
            "state": invoice.client.state,
            "postal_code": invoice.client.postal_code,
            "country": invoice.client.country,
        }

        return ClientInvoicePortalResponse(
            invoice_number=invoice.invoice_number,
            status=invoice.status,
            issue_date=invoice.issue_date,
            due_date=invoice.due_date,
            total_amount=invoice.total_amount,
            currency=invoice.currency,
            balance_due=invoice.balance_due,
            is_overdue=invoice.is_overdue,
            company_details=company_details,
            billing_address=billing_address,
            items=[
                {
                    "id": item.id,
                    "description": item.description,
                    "quantity": item.quantity,
                    "unit_price": item.unit_price,
                    "total_price": item.total_price,
                    "item_type": item.item_type,
                    "hours_worked": item.hours_worked,
                    "work_date": item.work_date,
                    "group_name": item.group_name,
                    "sequence_number": item.sequence_number,
                    "invoice_id": item.invoice_id,
                    "billing_status": item.billing_status,
                    "time_entry_id": item.time_entry_id,
                    "milestone_id": item.milestone_id,
                    "created_at": item.created_at,
                    "updated_at": item.updated_at,
                }
                for item in sorted(invoice.items, key=lambda x: x.sequence_number)
            ],
            payment_methods=payment_methods,
            can_pay_online=invoice.balance_due > 0 and invoice.status != "paid",
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to load invoice")


@router.post("/{token}/invoice/{invoice_id}/pay", response_model=PaymentLinkResponse)
@limiter.limit("5/minute")
async def initiate_client_payment(
    request: Request,
    token: str,
    invoice_id: uuid.UUID,
    payment_request: ClientPaymentInitiationRequest,
    db: Session = Depends(get_db),
):
    """Initiate payment for invoice from client portal."""
    try:
        # Verify invoice access
        invoice = (
            db.query(Invoice)
            .filter(
                Invoice.id == invoice_id,
                Invoice.payment_token == token,
                Invoice.status.in_(["sent", "viewed", "overdue"]),
            )
            .first()
        )

        if not invoice:
            raise HTTPException(
                status_code=404, detail="Invoice not found or not payable"
            )

        if invoice.balance_due <= 0:
            raise HTTPException(status_code=400, detail="Invoice is already paid")

        if invoice.balance_due <= 0:
            raise HTTPException(status_code=400, detail="Invoice is already paid")

        # Initialize payment service
        try:
            paystack = get_paystack_service()
        except ValueError as e:
            raise HTTPException(
                status_code=500, detail=f"Payment service configuration error: {str(e)}"
            )

        payment_service = PaymentProcessingService(db, paystack)

        # Generate payment link
        result = await payment_service.generate_payment_link(
            invoice_id=invoice_id,
            user_id=invoice.user_id,  # Use invoice owner's user_id
            success_url=payment_request.success_url,
            cancel_url=payment_request.cancel_url,
        )

        return PaymentLinkResponse(
            payment_link=result["payment_link"], payment_token=result["payment_token"]
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to initiate payment")


# Enhanced Real-time Portal Features


@router.post("/{token}/upload-file")
@limiter.limit("10/minute")
async def upload_project_file(
    request: Request,
    token: str,
    file: UploadFile = File(...),
    description: str = Form(None),
    db: Session = Depends(get_db),
):
    """Upload file to project with real-time progress tracking"""
    try:
        # Validate token and get approval
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.status.in_(["pending", "revision_requested"]),
                (ClientApproval.due_date.is_(None))
                | (ClientApproval.due_date > datetime.utcnow()),
            )
            .first()
        )

        if not approval:
            raise HTTPException(status_code=404, detail="Invalid or expired token")

        # Get project and client info
        project_id = str(approval.project_id) if approval.project_id else None
        client_id = str(approval.client_id) if approval.client_id else None

        if not project_id:
            raise HTTPException(
                status_code=400, detail="No project associated with this approval"
            )

        # Upload file with real-time progress
        file_data = await file_upload_service.upload_file(
            file=file,
            project_id=project_id,
            client_id=client_id,
            user_id=str(approval.user_id),
            upload_context="client_upload",
        )

        # Add description if provided
        if description:
            file_data["description"] = description

        # Emit client activity event
        await websocket_events.emit_client_activity(
            client_id=client_id,
            activity_type="file_uploaded",
            details={
                "filename": file.filename,
                "size": file_data["size"],
                "project_id": project_id,
                "description": description,
            },
        )

        return {
            "status": "uploaded",
            "file_data": file_data,
            "message": "File uploaded successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to upload file")


@router.get("/{token}/upload-progress/{upload_id}")
async def get_upload_progress(
    token: str, upload_id: str, db: Session = Depends(get_db)
):
    """Get real-time upload progress"""
    try:
        # Validate token
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.status.in_(["pending", "revision_requested"]),
                (ClientApproval.due_date.is_(None))
                | (ClientApproval.due_date > datetime.utcnow()),
            )
            .first()
        )

        if not approval:
            raise HTTPException(status_code=404, detail="Invalid or expired token")

        # Get upload progress
        progress = file_upload_service.get_upload_progress(upload_id)

        if not progress:
            raise HTTPException(status_code=404, detail="Upload not found")

        return progress

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get upload progress")


@router.post("/{token}/approval/{approval_id}/comment")
async def add_approval_comment(
    token: str, approval_id: str, comment_data: dict, db: Session = Depends(get_db)
):
    """Add real-time comment to approval"""
    try:
        # Validate token
        print(f"DEBUG: Validating token: {token}")
        client_approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.status.in_(["pending", "revision_requested"]),
                (ClientApproval.due_date.is_(None))
                | (ClientApproval.due_date > datetime.utcnow()),
            )
            .first()
        )

        print(f"DEBUG: Found client approval: {client_approval}")

        if not client_approval:
            raise HTTPException(status_code=404, detail="Invalid or expired token")

        # Get the approval
        print(f"DEBUG: Getting approval with ID: {approval_id}")
        try:
            approval_uuid = uuid.UUID(approval_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid approval ID format")

        approval = (
            db.query(ClientApproval).filter(ClientApproval.id == approval_uuid).first()
        )

        print(f"DEBUG: Found approval: {approval}")

        if not approval:
            raise HTTPException(status_code=404, detail="Approval not found")

        comment_text = comment_data.get("comment", "")
        if not comment_text:
            raise HTTPException(status_code=400, detail="Comment text is required")

        # Add comment to approval (assuming there's a comments field or related model)
        # For now, we'll emit the event and store in approval description
        current_description = approval.description or ""
        timestamp = datetime.utcnow().isoformat()
        new_comment = f"\n[{timestamp}] Client: {comment_text}"
        approval.description = current_description + new_comment

        db.commit()

        # Emit real-time event
        print(f"DEBUG: Emitting real-time event")
        await realtime_service.emit_event(
            "approval_comment_added",
            {
                "approval_id": approval_id,
                "project_id": str(approval.project_id) if approval.project_id else None,
                "client_id": (
                    str(client_approval.client_id)
                    if hasattr(client_approval, "client_id")
                    and client_approval.client_id
                    else None
                ),
                "user_id": str(approval.user_id),
                "comment": comment_text,
                "timestamp": timestamp,
            },
        )

        return {"status": "added", "comment": comment_text, "timestamp": timestamp}

    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Exception in add_approval_comment: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail="Failed to add comment")


@router.get("/{token}/project/{project_id}/timeline")
async def get_project_timeline(
    token: str, project_id: str, db: Session = Depends(get_db)
):
    """Get real-time project timeline"""
    try:
        # Validate token
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.status.in_(["pending", "revision_requested"]),
                (ClientApproval.due_date.is_(None))
                | (ClientApproval.due_date > datetime.utcnow()),
            )
            .first()
        )

        if not approval:
            raise HTTPException(status_code=404, detail="Invalid or expired token")

        # Get project with milestones
        project = (
            db.query(Project)
            .filter(Project.id == uuid.UUID(project_id))
            .options(joinedload(Project.milestones))
            .first()
        )

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Build timeline
        timeline_events = []

        # Add project creation
        timeline_events.append(
            {
                "type": "project_created",
                "title": "Project Started",
                "description": f"Project '{project.title}' was created",
                "date": project.created_at.isoformat(),
                "status": "completed",
            }
        )

        # Add milestones
        for milestone in project.milestones:
            if milestone.deleted_at is None:
                timeline_events.append(
                    {
                        "type": "milestone",
                        "title": milestone.title,
                        "description": milestone.description,
                        "date": (
                            milestone.due_date.date().isoformat()
                            if milestone.due_date
                            else None
                        ),
                        "status": milestone.status,
                        "completion_percentage": float(
                            milestone.completion_percentage or 0
                        ),
                    }
                )

        # Sort by date
        timeline_events.sort(key=lambda x: x["date"] or "")

        return {
            "project_id": project_id,
            "project_title": project.title,
            "project_status": project.status,
            "completion_percentage": float(project.completion_percentage or 0),
            "timeline": timeline_events,
            "last_updated": datetime.utcnow().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get project timeline")


@router.post("/{token}/activity")
async def log_client_activity(
    token: str, activity_data: dict, db: Session = Depends(get_db)
):
    """Log client portal activity for real-time tracking"""
    try:
        # Validate token
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.status.in_(["pending", "revision_requested"]),
                (ClientApproval.due_date.is_(None))
                | (ClientApproval.due_date > datetime.utcnow()),
            )
            .first()
        )

        if not approval:
            raise HTTPException(status_code=404, detail="Invalid or expired token")

        activity_type = activity_data.get("type", "page_view")
        page = activity_data.get("page", "unknown")
        duration = activity_data.get("duration", 0)

        client_id = str(approval.client_id) if approval.client_id else None

        # Emit client activity event
        await websocket_events.emit_client_activity(
            client_id=client_id,
            activity_type=activity_type,
            details={
                "page": page,
                "duration": duration,
                "timestamp": datetime.utcnow().isoformat(),
                "user_agent": activity_data.get("user_agent"),
                "ip_address": activity_data.get("ip_address"),
            },
        )

        return {
            "status": "logged",
            "activity_type": activity_type,
            "timestamp": datetime.utcnow().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to log activity")


@router.get("/{token}/websocket-auth")
async def get_websocket_auth(token: str, db: Session = Depends(get_db)):
    """Get WebSocket authentication token for real-time features"""
    try:
        # Validate token
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.status.in_(["pending", "revision_requested"]),
                (ClientApproval.due_date.is_(None))
                | (ClientApproval.due_date > datetime.utcnow()),
            )
            .first()
        )

        if not approval:
            raise HTTPException(status_code=404, detail="Invalid or expired token")

        # Generate WebSocket token (simplified - in production, use proper JWT)
        websocket_token = f"client_{approval.client_id}_{token[:16]}"

        return {
            "websocket_token": websocket_token,
            "websocket_url": "/ws",
            "client_id": str(approval.client_id) if approval.client_id else None,
            "project_id": str(approval.project_id) if approval.project_id else None,
            "expires_at": approval.due_date.isoformat() if approval.due_date else None,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get WebSocket auth")


@router.get("/{token}/live-updates")
async def get_live_updates(
    token: str,
    since: str = Query(None, description="ISO timestamp to get updates since"),
    db: Session = Depends(get_db),
):
    """Get live updates for fallback when WebSocket is not available"""
    try:
        # Validate token
        approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.status.in_(["pending", "revision_requested"]),
                (ClientApproval.due_date.is_(None))
                | (ClientApproval.due_date > datetime.utcnow()),
            )
            .first()
        )

        if not approval:
            raise HTTPException(status_code=404, detail="Invalid or expired token")

        # Parse since timestamp
        since_dt = None
        if since:
            try:
                since_dt = datetime.fromisoformat(since.replace("Z", "+00:00"))
            except:
                since_dt = datetime.utcnow() - timedelta(minutes=5)
        else:
            since_dt = datetime.utcnow() - timedelta(minutes=5)

        # Get recent updates (this would typically come from a dedicated updates table)
        updates = []

        # Check for approval updates
        if approval.updated_at > since_dt:
            updates.append(
                {
                    "type": "approval_updated",
                    "data": {
                        "approval_id": str(approval.id),
                        "status": approval.status,
                        "updated_at": approval.updated_at.isoformat(),
                    },
                }
            )

        # Check for project updates
        if approval.project_id:
            project = (
                db.query(Project)
                .filter(
                    Project.id == approval.project_id, Project.updated_at > since_dt
                )
                .first()
            )

            if project:
                updates.append(
                    {
                        "type": "project_updated",
                        "data": {
                            "project_id": str(project.id),
                            "status": project.status,
                            "completion_percentage": float(
                                project.completion_percentage or 0
                            ),
                            "updated_at": project.updated_at.isoformat(),
                        },
                    }
                )

        return {
            "updates": updates,
            "timestamp": datetime.utcnow().isoformat(),
            "has_more": False,  # In production, implement proper pagination
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get live updates")


@router.get("/{token}/invoice/{invoice_id}/download")
async def download_invoice_pdf(
    token: str, invoice_id: uuid.UUID, db: Session = Depends(get_db)
):
    """Download invoice PDF from client portal."""
    try:
        # Verify invoice access
        invoice = (
            db.query(Invoice)
            .filter(Invoice.id == invoice_id, Invoice.payment_token == token)
            .first()
        )

        if not invoice:
            raise HTTPException(
                status_code=404, detail="Invoice not found or access denied"
            )

        # TODO: Implement PDF generation and return file
        # For now, return placeholder response
        raise HTTPException(status_code=501, detail="PDF download not implemented yet")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to download PDF")


@router.get("/{token}/invoice/{invoice_id}/status")
async def get_client_invoice_status(
    token: str, invoice_id: uuid.UUID, db: Session = Depends(get_db)
):
    """Get invoice payment status for client portal."""
    try:
        # Verify invoice access
        invoice = (
            db.query(Invoice)
            .filter(Invoice.id == invoice_id, Invoice.payment_token == token)
            .first()
        )

        if not invoice:
            raise HTTPException(
                status_code=404, detail="Invoice not found or access denied"
            )

        return {
            "invoice_id": invoice.id,
            "invoice_number": invoice.invoice_number,
            "status": invoice.status,
            "total_amount": invoice.total_amount,
            "total_paid": invoice.total_paid,
            "balance_due": invoice.balance_due,
            "is_overdue": invoice.is_overdue,
            "days_overdue": invoice.days_overdue,
            "last_updated": invoice.updated_at,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get invoice status")


# Client Approval Portal Endpoints


@router.get("/{token}/approvals", response_model=List[ClientPortalApprovalResponse])
@limiter.limit("20/minute")
async def get_client_approvals(
    request: Request, token: str, db: Session = Depends(get_db)
):
    """Get all pending approvals for client using secure token."""
    try:
        # Find approval by token to get client_id
        sample_approval = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.approval_token == token,
                ClientApproval.deleted_at.is_(None),
            )
            .first()
        )

        if not sample_approval:
            raise HTTPException(
                status_code=404, detail="No approvals found for this token"
            )

        # Get all pending approvals for this client
        approvals = (
            db.query(ClientApproval)
            .options(joinedload(ClientApproval.project))
            .filter(
                ClientApproval.client_id == sample_approval.client_id,
                ClientApproval.status == "pending",
                ClientApproval.deleted_at.is_(None),
            )
            .order_by(
                ClientApproval.priority == "urgent",
                ClientApproval.priority == "high",
                ClientApproval.requested_at,
            )
            .all()
        )

        return [
            ClientPortalApprovalResponse(
                id=approval.id,
                title=approval.title,
                description=approval.description,
                status=approval.status,
                priority=approval.priority,
                requested_at=approval.requested_at,
                due_date=approval.due_date,
                is_overdue=approval.is_overdue,
                days_pending=approval.days_pending,
                project_title=approval.project.title,
                project_description=approval.project.description,
                approvable_type=approval.approvable_type,
                context_data=approval.context_data,
                client_instructions=approval.client_instructions,
                can_approve=True,
                can_request_revision=True,
            )
            for approval in approvals
        ]

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to load approvals")


@router.get(
    "/{token}/approvals/{approval_id}", response_model=ClientPortalApprovalResponse
)
async def get_client_approval_details(
    token: str, approval_id: uuid.UUID, db: Session = Depends(get_db)
):
    """Get detailed approval information for client portal."""
    try:
        approval_service = ApprovalService(db)
        approval = approval_service.get_approval_by_token(token, approval_id)

        if not approval:
            raise HTTPException(
                status_code=404, detail="Approval not found or access denied"
            )

        # Mark as viewed if first time
        approval.mark_as_viewed()
        db.commit()

        return ClientPortalApprovalResponse(
            id=approval.id,
            title=approval.title,
            description=approval.description,
            status=approval.status,
            priority=approval.priority,
            requested_at=approval.requested_at,
            due_date=approval.due_date,
            is_overdue=approval.is_overdue,
            days_pending=approval.days_pending,
            project_title=approval.project.title,
            project_description=approval.project.description,
            approvable_type=approval.approvable_type,
            context_data=approval.context_data,
            client_instructions=approval.client_instructions,
            can_approve=approval.status == "pending",
            can_request_revision=approval.status == "pending",
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to load approval details: {str(e)}"
        )


@router.post("/{token}/approvals/{approval_id}/approve")
async def approve_deliverable(
    token: str,
    approval_id: uuid.UUID,
    decision_request: ClientDecisionRequest,
    request: Request,
    db: Session = Depends(get_db),
):
    """Approve deliverable from client portal."""
    try:
        approval_service = ApprovalService(db)

        # Verify token access
        approval = approval_service.get_approval_by_token(token, approval_id)
        if not approval:
            raise HTTPException(
                status_code=404, detail="Approval not found or access denied"
            )

        if approval.status != "pending":
            raise HTTPException(
                status_code=400, detail="Approval is not in pending status"
            )

        # Get client IP for audit trail
        client_ip = request.client.host if request.client else None

        # Process approval decision
        approval_service.process_client_decision(
            approval_id=approval_id,
            decision="approve",
            client_name=decision_request.client_name,
            client_email=decision_request.client_email,
            feedback=decision_request.feedback,
            ip_address=client_ip,
        )

        return {
            "message": "Deliverable approved successfully",
            "approval_id": approval_id,
            "status": "approved",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to approve deliverable")


@router.post("/{token}/approvals/{approval_id}/revise")
async def request_revisions(
    token: str,
    approval_id: uuid.UUID,
    decision_request: ClientDecisionRequest,
    request: Request,
    db: Session = Depends(get_db),
):
    """Request revisions for deliverable from client portal."""
    try:
        if not decision_request.feedback:
            raise HTTPException(
                status_code=400, detail="Feedback is required for revision requests"
            )

        approval_service = ApprovalService(db)

        # Verify token access
        approval = approval_service.get_approval_by_token(token, approval_id)
        if not approval:
            raise HTTPException(
                status_code=404, detail="Approval not found or access denied"
            )

        if approval.status != "pending":
            raise HTTPException(
                status_code=400, detail="Approval is not in pending status"
            )

        # Get client IP for audit trail
        client_ip = request.client.host if request.client else None

        # Process revision request
        approval_service.process_client_decision(
            approval_id=approval_id,
            decision="revise",
            client_name=decision_request.client_name,
            client_email=decision_request.client_email,
            feedback=decision_request.feedback,
            ip_address=client_ip,
        )

        return {
            "message": "Revision request submitted successfully",
            "approval_id": approval_id,
            "status": "revision_requested",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to request revisions")


@router.get("/{token}/approvals/{approval_id}/history")
async def get_approval_history(
    token: str, approval_id: uuid.UUID, db: Session = Depends(get_db)
):
    """Get approval history and activity for client portal."""
    try:
        approval_service = ApprovalService(db)
        approval = approval_service.get_approval_by_token(token, approval_id)

        if not approval:
            raise HTTPException(
                status_code=404, detail="Approval not found or access denied"
            )

        return {
            "approval_id": approval_id,
            "current_status": approval.status,
            "activity_logs": [
                {
                    "id": activity.id,
                    "action": activity.action,
                    "actor_type": activity.actor_type,
                    "actor_name": activity.actor_name,
                    "timestamp": activity.timestamp,
                    "details": activity.details,
                }
                for activity in approval.activity_logs
            ],
            "feedback_items": [
                {
                    "id": feedback.id,
                    "content": feedback.content,
                    "feedback_type": feedback.feedback_type,
                    "submitted_at": feedback.submitted_at,
                    "is_addressed": feedback.is_addressed,
                    "developer_response": feedback.developer_response,
                    "responded_at": feedback.responded_at,
                }
                for feedback in approval.feedback_items
            ],
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get approval history")


@router.post("/{token}/approvals/{approval_id}/feedback")
async def submit_approval_feedback(
    token: str,
    approval_id: uuid.UUID,
    feedback_request: ClientFeedbackCreate,
    db: Session = Depends(get_db),
):
    """Submit feedback for an approval from client portal."""
    try:
        approval_service = ApprovalService(db)
        approval = approval_service.get_approval_by_token(token, approval_id)

        if not approval:
            raise HTTPException(
                status_code=404, detail="Approval not found or access denied"
            )

        # Create feedback record
        from app.models import ClientFeedback

        feedback = ClientFeedback(
            approval_id=approval_id,
            content=feedback_request.content,
            feedback_type=feedback_request.feedback_type,
            client_name=feedback_request.client_name,
            client_email=feedback_request.client_email,
            urgency_level=feedback_request.urgency_level,
            category=feedback_request.category,
        )

        db.add(feedback)
        db.commit()

        return {
            "message": "Feedback submitted successfully",
            "feedback_id": feedback.id,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to submit feedback")


@router.get("/{token}/approvals/{approval_id}/feedback")
async def get_approval_feedback(
    token: str, approval_id: uuid.UUID, db: Session = Depends(get_db)
):
    """Get feedback history for an approval from client portal."""
    try:
        approval_service = ApprovalService(db)
        approval = approval_service.get_approval_by_token(token, approval_id)

        if not approval:
            raise HTTPException(
                status_code=404, detail="Approval not found or access denied"
            )

        return {
            "approval_id": approval_id,
            "feedback_items": [
                {
                    "id": feedback.id,
                    "content": feedback.content,
                    "feedback_type": feedback.feedback_type,
                    "submitted_at": feedback.submitted_at,
                    "is_addressed": feedback.is_addressed,
                    "developer_response": feedback.developer_response,
                    "responded_at": feedback.responded_at,
                    "urgency_level": feedback.urgency_level,
                    "category": feedback.category,
                }
                for feedback in approval.feedback_items
            ],
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get feedback")
