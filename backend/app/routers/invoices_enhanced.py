"""
Enhanced invoice endpoints with email delivery and audit logging
"""

from fastapi import Request

from app.core.audit_service import get_audit_service
from app.core.email import email_service


def get_client_ip(request: Request) -> str:
    """Extract client IP address from request"""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    return request.client.host if request.client else "unknown"


def get_user_agent(request: Request) -> str:
    """Extract user agent from request"""
    return request.headers.get("User-Agent", "unknown")


async def send_invoice_with_email(
    invoice_id: str, invoice, pdf_bytes: bytes, request: Request, current_user, db
):
    """Send invoice via email with audit logging"""
    audit_service = get_audit_service(db)

    try:
        # Send email with PDF attachment
        success = email_service.send_invoice_email(
            client_email=invoice.client.email,
            client_name=invoice.client.name or invoice.client.company_name,
            invoice_number=invoice.invoice_number,
            invoice_amount=f"{invoice.total_amount:.2f}",
            currency=invoice.currency,
            due_date=(
                invoice.due_date.strftime("%B %d, %Y")
                if invoice.due_date
                else "Upon Receipt"
            ),
            pdf_attachment=pdf_bytes,
            sender_name=f"{current_user.first_name} {current_user.last_name}",
        )

        if success:
            # Log successful email delivery
            audit_service.log_invoice_activity(
                user_id=str(current_user.id),
                invoice_id=invoice_id,
                action="email_sent",
                description=f"Invoice {invoice.invoice_number} emailed to {invoice.client.email}",
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request),
                extra_data={
                    "client_email": invoice.client.email,
                    "invoice_amount": float(invoice.total_amount),
                    "currency": invoice.currency,
                },
            )

            # Update invoice status to sent if it was draft
            if invoice.status == "draft":
                invoice.status = "sent"
                db.commit()

                audit_service.log_invoice_activity(
                    user_id=str(current_user.id),
                    invoice_id=invoice_id,
                    action="status_change",
                    description=f"Invoice {invoice.invoice_number} status changed to sent",
                    ip_address=get_client_ip(request),
                    user_agent=get_user_agent(request),
                    extra_data={"old_status": "draft", "new_status": "sent"},
                )
        else:
            # Log email failure
            audit_service.log_invoice_activity(
                user_id=str(current_user.id),
                invoice_id=invoice_id,
                action="email_failed",
                description=f"Failed to email invoice {invoice.invoice_number} to {invoice.client.email}",
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request),
                extra_data={
                    "client_email": invoice.client.email,
                    "error": "Email delivery failed",
                },
            )

        return success

    except Exception as e:
        # Log email error
        audit_service.log_invoice_activity(
            user_id=str(current_user.id),
            invoice_id=invoice_id,
            action="email_error",
            description=f"Error sending invoice {invoice.invoice_number}: {str(e)}",
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
            extra_data={"client_email": invoice.client.email, "error": str(e)},
        )
        return False


async def log_pdf_generation(
    invoice_id: str,
    invoice,
    request: Request,
    current_user,
    db,
    success: bool = True,
    error: str = None,
):
    """Log PDF generation activity"""
    audit_service = get_audit_service(db)

    if success:
        audit_service.log_invoice_activity(
            user_id=str(current_user.id),
            invoice_id=invoice_id,
            action="pdf_generated",
            description=f"PDF generated for invoice {invoice.invoice_number}",
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
            extra_data={
                "invoice_number": invoice.invoice_number,
                "client_id": str(invoice.client_id),
            },
        )
    else:
        audit_service.log_invoice_activity(
            user_id=str(current_user.id),
            invoice_id=invoice_id,
            action="pdf_generation_failed",
            description=f"Failed to generate PDF for invoice {invoice.invoice_number}: {error}",
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
            extra_data={"invoice_number": invoice.invoice_number, "error": error},
        )


async def log_payment_confirmation(
    transaction, invoice, request: Request = None, db=None
):
    """Log payment confirmation and send email"""
    audit_service = get_audit_service(db)

    try:
        # Send payment confirmation email
        success = email_service.send_payment_confirmation_email(
            client_email=invoice.client.email,
            client_name=invoice.client.name or invoice.client.company_name,
            invoice_number=invoice.invoice_number,
            payment_amount=f"{transaction.amount:.2f}",
            currency=transaction.currency,
            transaction_reference=transaction.transaction_reference,
        )

        # Log payment activity
        audit_service.log_payment_activity(
            user_id=str(invoice.user_id),
            client_id=str(invoice.client_id),
            action="payment_confirmed",
            description=f"Payment confirmed for invoice {invoice.invoice_number}",
            transaction_id=str(transaction.id),
            ip_address=get_client_ip(request) if request else None,
            user_agent=get_user_agent(request) if request else None,
            extra_data={
                "invoice_number": invoice.invoice_number,
                "amount": float(transaction.amount),
                "currency": transaction.currency,
                "transaction_reference": transaction.transaction_reference,
                "email_sent": success,
            },
        )

        return success

    except Exception as e:
        # Log error
        audit_service.log_payment_activity(
            user_id=str(invoice.user_id),
            client_id=str(invoice.client_id),
            action="payment_confirmation_error",
            description=f"Error sending payment confirmation for invoice {invoice.invoice_number}: {str(e)}",
            transaction_id=str(transaction.id),
            ip_address=get_client_ip(request) if request else None,
            user_agent=get_user_agent(request) if request else None,
            extra_data={"invoice_number": invoice.invoice_number, "error": str(e)},
        )
        return False
