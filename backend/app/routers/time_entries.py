"""
Time tracking API endpoints
"""

import uuid
from datetime import date, datetime, timezone
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import Session, joinedload

from app.database import get_db
from app.dependencies import get_current_user
from app.models import ActivityLog, Project, TimeEntry, User
from app.schemas.project import (PaginatedTimeEntriesResponse, TimeEntryCreate,
                                 TimeEntryResponse, TimeEntryUpdate)
from app.schemas.timer import (BulkTimeEntryOperation, BulkTimeEntryResponse,
                               TimeEntryCreateAdvanced,
                               TimeEntryResponseAdvanced, TimeEntrySuggestion,
                               TimeEntrySuggestionsResponse, TimeEntryTemplate,
                               TimeEntryTemplateResponse,
                               TimeEntryValidationRequest,
                               TimeEntryValidationResponse)

router = APIRouter(prefix="/api/v1/time-entries", tags=["time-entries"])


def log_activity(
    db: Session,
    user_id: uuid.UUID,
    entity_type: str,
    entity_id: str,
    action: str,
    details: str = None,
):
    """Helper function to log activities"""
    activity = ActivityLog(
        user_id=user_id,
        entity_type=entity_type,
        entity_id=str(entity_id),
        action=action,
        details=details,
    )
    db.add(activity)


@router.get("/", response_model=PaginatedTimeEntriesResponse)
async def list_time_entries(
    page: int = Query(1, ge=1),
    per_page: int = Query(25, ge=1, le=100),
    project_id: Optional[str] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    status: Optional[str] = Query(None),
    is_billable: Optional[bool] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """List time entries with filtering and pagination"""

    # Base query - only user's time entries
    query = (
        db.query(TimeEntry)
        .join(Project)
        .filter(
            and_(
                TimeEntry.user_id == current_user.id,
                Project.user_id == current_user.id,
                TimeEntry.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
    )

    # Apply filters
    if project_id:
        try:
            project_uuid = uuid.UUID(project_id)
            query = query.filter(TimeEntry.project_id == project_uuid)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid project_id format",
            )

    if start_date:
        query = query.filter(func.date(TimeEntry.work_date) >= start_date)

    if end_date:
        query = query.filter(func.date(TimeEntry.work_date) <= end_date)

    if status:
        query = query.filter(TimeEntry.status == status)

    if is_billable is not None:
        query = query.filter(TimeEntry.is_billable == is_billable)

    # Get total count
    total = query.count()

    # Apply pagination and ordering
    offset = (page - 1) * per_page
    time_entries = (
        query.order_by(desc(TimeEntry.work_date), desc(TimeEntry.created_at))
        .offset(offset)
        .limit(per_page)
        .options(joinedload(TimeEntry.project))
        .all()
    )

    return PaginatedTimeEntriesResponse(
        total=total,
        page=page,
        per_page=per_page,
        items=[TimeEntryResponse.model_validate(entry) for entry in time_entries],
    )


@router.post("/", response_model=TimeEntryResponse, status_code=status.HTTP_201_CREATED)
async def create_time_entry(
    time_entry_data: TimeEntryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new time entry"""

    # Verify project exists and belongs to user
    project = (
        db.query(Project)
        .filter(
            and_(
                Project.id == time_entry_data.project_id,
                Project.user_id == current_user.id,
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found or access denied",
        )

    # Create time entry
    time_entry = TimeEntry(
        **time_entry_data.model_dump(exclude={"project_id"}),
        project_id=time_entry_data.project_id,
        user_id=current_user.id,
    )

    # Set default hourly rate from project if not provided
    if not time_entry.hourly_rate and project.hourly_rate:
        time_entry.hourly_rate = project.hourly_rate

    # Calculate billable amount
    time_entry.calculate_billable_amount()

    db.add(time_entry)
    db.flush()  # Get the ID

    # Log activity
    log_activity(
        db,
        current_user.id,
        "time_entry",
        str(time_entry.id),
        "create",
        f"Created time entry for {time_entry.duration_minutes} minutes on project '{project.title}'",
    )

    db.commit()
    db.refresh(time_entry)

    return TimeEntryResponse.model_validate(time_entry)


# Smart Suggestions (must be before /{entry_id} to avoid route conflicts)
@router.get("/suggestions", response_model=TimeEntrySuggestionsResponse)
async def get_time_entry_suggestions(
    project_id: str,
    description_hint: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get smart suggestions for time entry descriptions and tasks."""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    # Verify project access
    project = (
        db.query(Project)
        .filter(
            and_(
                Project.id == project_uuid,
                Project.user_id == current_user.id,
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    # Get recent time entries for this project to generate suggestions
    recent_entries = (
        db.query(TimeEntry)
        .filter(
            and_(
                TimeEntry.project_id == project_uuid,
                TimeEntry.user_id == current_user.id,
                TimeEntry.deleted_at.is_(None),
                TimeEntry.description.isnot(None),
            )
        )
        .order_by(desc(TimeEntry.created_at))
        .limit(20)
        .all()
    )

    suggestions = []

    # Generate suggestions based on recent entries
    description_counts = {}
    task_counts = {}

    for entry in recent_entries:
        if entry.description:
            description = entry.description.strip()
            if description:
                description_counts[description] = (
                    description_counts.get(description, 0) + 1
                )

        if entry.task_name:
            task = entry.task_name.strip()
            if task:
                task_counts[task] = task_counts.get(task, 0) + 1

    # Create suggestions from most common descriptions
    for description_text, count in sorted(
        description_counts.items(), key=lambda x: x[1], reverse=True
    )[:5]:
        # Calculate average duration for this description
        matching_entries = [
            e
            for e in recent_entries
            if e.description and description_text in e.description
        ]
        avg_duration = (
            sum(e.duration_minutes for e in matching_entries) // len(matching_entries)
            if matching_entries
            else None
        )

        confidence = min(0.9, count / len(recent_entries))

        suggestions.append(
            TimeEntrySuggestion(
                description=description_text,
                estimated_duration_minutes=avg_duration,
                confidence_score=confidence,
                based_on="history",
            )
        )

    # Add task-based suggestions
    for task, count in sorted(task_counts.items(), key=lambda x: x[1], reverse=True)[
        :3
    ]:
        if not any(s.task_name == task for s in suggestions):
            confidence = min(0.8, count / len(recent_entries))
            suggestions.append(
                TimeEntrySuggestion(
                    description=f"Work on {task}",
                    task_name=task,
                    confidence_score=confidence,
                    based_on="project_pattern",
                )
            )

    # If no suggestions from history, provide generic ones
    if not suggestions:
        generic_suggestions = [
            "Development work",
            "Code review",
            "Testing and debugging",
            "Documentation",
            "Meeting and planning",
        ]

        for generic_desc in generic_suggestions:
            suggestions.append(
                TimeEntrySuggestion(
                    description=generic_desc,
                    confidence_score=0.3,
                    based_on="ai_analysis",
                )
            )

    return TimeEntrySuggestionsResponse(
        suggestions=suggestions[:10],  # Limit to top 10
        project_id=project_id,
        context={
            "recent_entries_count": len(recent_entries),
            "unique_descriptions": len(description_counts),
            "unique_tasks": len(task_counts),
        },
    )


@router.get("/{entry_id}", response_model=TimeEntryResponse)
async def get_time_entry(
    entry_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get time entry details"""

    try:
        entry_uuid = uuid.UUID(entry_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid time entry ID format",
        )

    time_entry = (
        db.query(TimeEntry)
        .join(Project)
        .filter(
            and_(
                TimeEntry.id == entry_uuid,
                TimeEntry.user_id == current_user.id,
                Project.user_id == current_user.id,
                TimeEntry.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .options(joinedload(TimeEntry.project))
        .first()
    )

    if not time_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Time entry not found"
        )

    return TimeEntryResponse.model_validate(time_entry)


@router.put("/{entry_id}", response_model=TimeEntryResponse)
async def update_time_entry(
    entry_id: str,
    time_entry_data: TimeEntryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update time entry details"""

    try:
        entry_uuid = uuid.UUID(entry_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid time entry ID format",
        )

    time_entry = (
        db.query(TimeEntry)
        .join(Project)
        .filter(
            and_(
                TimeEntry.id == entry_uuid,
                TimeEntry.user_id == current_user.id,
                Project.user_id == current_user.id,
                TimeEntry.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not time_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Time entry not found"
        )

    # Check if entry can be modified
    if time_entry.status in ["approved", "billed"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify approved or billed time entries",
        )

    # Update fields
    update_data = time_entry_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(time_entry, field, value)

    # Recalculate billable amount if relevant fields changed
    if any(
        field in update_data
        for field in ["duration_minutes", "hourly_rate", "is_billable"]
    ):
        time_entry.calculate_billable_amount()

    # Log activity
    log_activity(
        db,
        current_user.id,
        "time_entry",
        str(time_entry.id),
        "update",
        f"Updated time entry for {time_entry.duration_minutes} minutes",
    )

    db.commit()
    db.refresh(time_entry)

    return TimeEntryResponse.model_validate(time_entry)


@router.delete("/{entry_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_time_entry(
    entry_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Soft delete a time entry"""

    try:
        entry_uuid = uuid.UUID(entry_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid time entry ID format",
        )

    time_entry = (
        db.query(TimeEntry)
        .join(Project)
        .filter(
            and_(
                TimeEntry.id == entry_uuid,
                TimeEntry.user_id == current_user.id,
                Project.user_id == current_user.id,
                TimeEntry.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not time_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Time entry not found"
        )

    # Check if entry can be deleted
    if time_entry.status in ["approved", "billed"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete approved or billed time entries",
        )

    # Soft delete
    time_entry.soft_delete()

    # Log activity
    log_activity(
        db,
        current_user.id,
        "time_entry",
        str(time_entry.id),
        "delete",
        f"Deleted time entry for {time_entry.duration_minutes} minutes",
    )

    db.commit()


@router.post("/{entry_id}/submit", response_model=TimeEntryResponse)
async def submit_time_entry(
    entry_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Submit time entry for approval"""

    try:
        entry_uuid = uuid.UUID(entry_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid time entry ID format",
        )

    time_entry = (
        db.query(TimeEntry)
        .join(Project)
        .filter(
            and_(
                TimeEntry.id == entry_uuid,
                TimeEntry.user_id == current_user.id,
                Project.user_id == current_user.id,
                TimeEntry.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not time_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Time entry not found"
        )

    if time_entry.status != "draft":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only draft time entries can be submitted",
        )

    # Submit for approval
    time_entry.status = "submitted"

    # Log activity
    log_activity(
        db,
        current_user.id,
        "time_entry",
        str(time_entry.id),
        "submit",
        f"Submitted time entry for approval",
    )

    db.commit()
    db.refresh(time_entry)

    return TimeEntryResponse.model_validate(time_entry)


@router.get("/projects/{project_id}/summary")
async def get_project_time_summary(
    project_id: str,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get time summary for a project"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    # Verify project access
    project = (
        db.query(Project)
        .filter(
            and_(
                Project.id == project_uuid,
                Project.user_id == current_user.id,
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Project not found"
        )

    # Build query for time entries
    query = db.query(TimeEntry).filter(
        and_(TimeEntry.project_id == project_uuid, TimeEntry.deleted_at.is_(None))
    )

    if start_date:
        query = query.filter(func.date(TimeEntry.work_date) >= start_date)

    if end_date:
        query = query.filter(func.date(TimeEntry.work_date) <= end_date)

    time_entries = query.all()

    # Calculate summary
    total_minutes = sum(entry.duration_minutes for entry in time_entries)
    total_hours = total_minutes / 60
    billable_entries = [entry for entry in time_entries if entry.is_billable]
    billable_minutes = sum(entry.duration_minutes for entry in billable_entries)
    billable_hours = billable_minutes / 60
    total_billable_amount = sum(
        entry.billable_amount or 0 for entry in billable_entries
    )

    # Status breakdown
    status_breakdown = {}
    for entry in time_entries:
        status = entry.status
        if status not in status_breakdown:
            status_breakdown[status] = {"count": 0, "hours": 0, "amount": 0}
        status_breakdown[status]["count"] += 1
        status_breakdown[status]["hours"] += entry.duration_minutes / 60
        if entry.is_billable:
            status_breakdown[status]["amount"] += float(entry.billable_amount or 0)

    return {
        "project_id": str(project_uuid),
        "project_title": project.title,
        "total_entries": len(time_entries),
        "total_hours": round(total_hours, 2),
        "billable_hours": round(billable_hours, 2),
        "non_billable_hours": round(total_hours - billable_hours, 2),
        "total_billable_amount": float(total_billable_amount),
        "status_breakdown": status_breakdown,
        "date_range": {
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None,
        },
    }


# Bulk Operations
@router.post("/bulk", response_model=BulkTimeEntryResponse)
async def bulk_time_entry_operation(
    operation_data: BulkTimeEntryOperation,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Perform bulk operations on time entries."""

    success_count = 0
    failed_count = 0
    failed_entries = []

    for entry_id_str in operation_data.entry_ids:
        try:
            entry_uuid = uuid.UUID(entry_id_str)

            # Get time entry with access check
            time_entry = (
                db.query(TimeEntry)
                .filter(
                    and_(
                        TimeEntry.id == entry_uuid,
                        TimeEntry.user_id == current_user.id,
                        TimeEntry.deleted_at.is_(None),
                    )
                )
                .first()
            )

            if not time_entry:
                failed_entries.append(
                    {
                        "entry_id": entry_id_str,
                        "error": "Time entry not found or access denied",
                    }
                )
                failed_count += 1
                continue

            # Perform operation
            if operation_data.operation == "delete":
                if time_entry.status in ["approved", "billed"]:
                    failed_entries.append(
                        {
                            "entry_id": entry_id_str,
                            "error": "Cannot delete approved or billed entries",
                        }
                    )
                    failed_count += 1
                    continue

                time_entry.soft_delete()
                log_activity(
                    db, current_user.id, "time_entry", entry_id_str, "bulk_delete"
                )

            elif operation_data.operation == "update":
                if not operation_data.update_data:
                    failed_entries.append(
                        {"entry_id": entry_id_str, "error": "No update data provided"}
                    )
                    failed_count += 1
                    continue

                if time_entry.status in ["approved", "billed"]:
                    failed_entries.append(
                        {
                            "entry_id": entry_id_str,
                            "error": "Cannot modify approved or billed entries",
                        }
                    )
                    failed_count += 1
                    continue

                # Update fields
                for field, value in operation_data.update_data.items():
                    if hasattr(time_entry, field):
                        setattr(time_entry, field, value)

                # Recalculate billable amount if needed
                if any(
                    field in operation_data.update_data
                    for field in ["duration_minutes", "hourly_rate", "is_billable"]
                ):
                    time_entry.calculate_billable_amount()

                log_activity(
                    db, current_user.id, "time_entry", entry_id_str, "bulk_update"
                )

            elif operation_data.operation == "submit":
                if time_entry.status != "draft":
                    failed_entries.append(
                        {
                            "entry_id": entry_id_str,
                            "error": "Only draft entries can be submitted",
                        }
                    )
                    failed_count += 1
                    continue

                time_entry.status = "submitted"
                log_activity(
                    db, current_user.id, "time_entry", entry_id_str, "bulk_submit"
                )

            elif operation_data.operation == "approve":
                if time_entry.status != "submitted":
                    failed_entries.append(
                        {
                            "entry_id": entry_id_str,
                            "error": "Only submitted entries can be approved",
                        }
                    )
                    failed_count += 1
                    continue

                time_entry.status = "approved"
                log_activity(
                    db, current_user.id, "time_entry", entry_id_str, "bulk_approve"
                )

            success_count += 1

        except ValueError:
            failed_entries.append(
                {"entry_id": entry_id_str, "error": "Invalid entry ID format"}
            )
            failed_count += 1
        except Exception as e:
            failed_entries.append({"entry_id": entry_id_str, "error": str(e)})
            failed_count += 1

    db.commit()

    return BulkTimeEntryResponse(
        success_count=success_count,
        failed_count=failed_count,
        failed_entries=failed_entries,
        message=f"Bulk {operation_data.operation} completed: {success_count} successful, {failed_count} failed",
    )


# Time Entry Templates
@router.get("/templates", response_model=List[TimeEntryTemplateResponse])
async def get_time_entry_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get user's time entry templates."""

    # For now, return empty list - templates would be stored in a separate table
    # This is a placeholder for the template system
    return []


@router.post(
    "/templates",
    response_model=TimeEntryTemplateResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_time_entry_template(
    template_data: TimeEntryTemplate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new time entry template."""

    # Placeholder implementation - would need a TimeEntryTemplate model
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Template system not yet implemented",
    )


# Time Entry Validation
@router.post("/validate", response_model=TimeEntryValidationResponse)
async def validate_time_entry(
    validation_data: TimeEntryValidationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Validate time entry for conflicts and business rules."""

    warnings = []
    errors = []
    suggestions = []
    overlapping_entries = []

    # Verify project access
    project = (
        db.query(Project)
        .filter(
            and_(
                Project.id == validation_data.project_id,
                Project.user_id == current_user.id,
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not project:
        errors.append("Project not found or access denied")
        return TimeEntryValidationResponse(is_valid=False, errors=errors)

    # Check for overlapping time entries
    if validation_data.start_time and validation_data.end_time:
        overlapping = (
            db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.user_id == current_user.id,
                    TimeEntry.deleted_at.is_(None),
                    TimeEntry.start_time.isnot(None),
                    TimeEntry.end_time.isnot(None),
                    or_(
                        and_(
                            TimeEntry.start_time <= validation_data.start_time,
                            TimeEntry.end_time > validation_data.start_time,
                        ),
                        and_(
                            TimeEntry.start_time < validation_data.end_time,
                            TimeEntry.end_time >= validation_data.end_time,
                        ),
                        and_(
                            TimeEntry.start_time >= validation_data.start_time,
                            TimeEntry.end_time <= validation_data.end_time,
                        ),
                    ),
                )
            )
            .all()
        )

        if overlapping:
            for entry in overlapping:
                overlapping_entries.append(
                    {
                        "id": str(entry.id),
                        "start_time": entry.start_time.isoformat(),
                        "end_time": entry.end_time.isoformat(),
                        "description": entry.description,
                        "project_id": str(entry.project_id),
                    }
                )
            errors.append(
                f"Time entry overlaps with {len(overlapping)} existing entries"
            )

    # Business rule validations
    if validation_data.duration_minutes > 720:  # 12 hours
        warnings.append(
            "Duration exceeds 12 hours - consider breaking into multiple entries"
        )

    if validation_data.duration_minutes < 5:
        warnings.append("Very short duration - consider combining with other tasks")

    # Check work date
    if validation_data.work_date > datetime.now(timezone.utc):
        errors.append("Work date cannot be in the future")

    # Check if work date is too far in the past (more than 30 days)
    days_ago = (datetime.now(timezone.utc) - validation_data.work_date).days
    if days_ago > 30:
        warnings.append(
            f"Work date is {days_ago} days ago - consider if this is correct"
        )

    # Suggestions
    if validation_data.duration_minutes % 15 != 0:
        rounded_duration = round(validation_data.duration_minutes / 15) * 15
        suggestions.append(
            f"Consider rounding to {rounded_duration} minutes for easier billing"
        )

    is_valid = len(errors) == 0

    return TimeEntryValidationResponse(
        is_valid=is_valid,
        warnings=warnings,
        errors=errors,
        suggestions=suggestions,
        overlapping_entries=overlapping_entries,
    )
