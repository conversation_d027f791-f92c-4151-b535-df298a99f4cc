"""
User profile management router - Standardized API patterns
"""

from typing import List

from fastapi import (APIRouter, Depends, File, HTTPException, Query,
                     UploadFile, status)
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.base_router import (BaseRouterMixin, FilterParams,
                                  PaginatedResponse, PaginationParams,
                                  StandardResponse)
from app.core.cache import CacheKeyBuilder, cache_manager
from app.core.cache_invalidation import invalidate_user_cache
from app.core.error_handlers import (BusinessLogicError, NotFoundError,
                                     ValidationException)
from app.database import get_async_db
from app.dependencies import get_current_active_user
from app.models.user import User
from app.schemas.user import (AvatarUploadResponse, UserProfileResponse,
                              UserProfileUpdate, UserSettingsResponse,
                              UserSettingsUpdate)
from app.services.user_service import UserService


class UserRouter(BaseRouterMixin):
    def __init__(self):
        super().__init__(prefix="/api/v1/users", tags=["Users"])
        self._setup_routes()

    def _setup_routes(self):
        """Setup all user-related routes"""

        @self.router.get(
            "/me", response_model=StandardResponse, responses=self.standard_responses
        )
        async def get_profile(current_user: User = Depends(get_current_active_user)):
            """Get current user profile with caching"""
            try:
                # Try to get from cache first
                cache_key = CacheKeyBuilder.user_profile(str(current_user.id))
                cached_profile = await cache_manager.get(cache_key)

                if cached_profile:
                    return self.create_success_response(
                        message="Profile retrieved successfully (cached)",
                        data=cached_profile,
                    )

                # Build profile data
                profile_data = UserProfileResponse(
                    id=str(current_user.id),
                    email=current_user.email,
                    first_name=current_user.first_name,
                    last_name=current_user.last_name,
                    full_name=current_user.full_name,
                    avatar_url=current_user.avatar_url,
                    phone=current_user.phone,
                    bio=current_user.bio,
                    company_name=current_user.company_name,
                    is_active=current_user.is_active,
                    is_verified=current_user.is_verified,
                    created_at=current_user.created_at,
                    updated_at=current_user.updated_at,
                )

                # Cache the profile data for 30 minutes
                profile_dict = profile_data.model_dump()
                await cache_manager.set(cache_key, profile_dict, ttl=1800)

                return self.create_success_response(
                    message="Profile retrieved successfully", data=profile_dict
                )
            except Exception as e:
                raise BusinessLogicError(f"Failed to retrieve profile: {str(e)}")

        @self.router.put(
            "/me", response_model=StandardResponse, responses=self.standard_responses
        )
        async def update_profile(
            profile_update: UserProfileUpdate,
            current_user: User = Depends(get_current_active_user),
            db: AsyncSession = Depends(get_async_db),
        ):
            """Update current user profile with cache invalidation"""
            try:
                user_service = UserService(db)
                updated_user = await user_service.update_user_profile(
                    user_id=current_user.id, profile_data=profile_update
                )

                # Invalidate user cache after update
                await invalidate_user_cache(str(current_user.id))

                profile_data = UserProfileResponse(
                    id=str(updated_user.id),
                    email=updated_user.email,
                    first_name=updated_user.first_name,
                    last_name=updated_user.last_name,
                    full_name=updated_user.full_name,
                    avatar_url=updated_user.avatar_url,
                    phone=updated_user.phone,
                    bio=updated_user.bio,
                    company_name=updated_user.company_name,
                    is_active=updated_user.is_active,
                    is_verified=updated_user.is_verified,
                    created_at=updated_user.created_at,
                    updated_at=updated_user.updated_at,
                )

                # Cache the updated profile data
                cache_key = CacheKeyBuilder.user_profile(str(updated_user.id))
                profile_dict = profile_data.model_dump()
                await cache_manager.set(cache_key, profile_dict, ttl=1800)

                return self.create_success_response(
                    message="Profile updated successfully", data=profile_dict
                )
            except Exception as e:
                raise BusinessLogicError(f"Failed to update profile: {str(e)}")

        @self.router.put(
            "/me/avatar",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def upload_avatar(
            file: UploadFile = File(...),
            db: AsyncSession = Depends(get_async_db),
            current_user: User = Depends(get_current_active_user),
        ):
            """Upload user avatar"""
            try:
                user_service = UserService(db)
                file_bytes = await file.read()
                result = await user_service.upload_user_avatar(
                    current_user.id, file_bytes, file.content_type
                )

                return self.create_success_response(
                    message="Avatar uploaded successfully", data=result
                )
            except ValueError as e:
                raise ValidationException(str(e))
            except Exception as e:
                raise BusinessLogicError(f"Failed to upload avatar: {str(e)}")

        @self.router.get(
            "/me/settings",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def get_settings(
            db: AsyncSession = Depends(get_async_db),
            current_user: User = Depends(get_current_active_user),
        ):
            """Get user settings with caching"""
            try:
                # Try to get from cache first
                cache_key = CacheKeyBuilder.user_settings(str(current_user.id))
                cached_settings = await cache_manager.get(cache_key)

                if cached_settings:
                    return self.create_success_response(
                        message="Settings retrieved successfully (cached)",
                        data=cached_settings,
                    )

                user_service = UserService(db)
                settings_data = await user_service.get_user_settings(current_user.id)

                # Convert SQLAlchemy object to Pydantic response model
                settings_response = UserSettingsResponse(
                    theme=settings_data.theme,
                    language=settings_data.language,
                    timezone=settings_data.timezone,
                    date_format=settings_data.date_format,
                    time_format=settings_data.time_format,
                    default_currency=settings_data.default_currency,
                    default_hourly_rate=float(settings_data.default_hourly_rate),
                    invoice_prefix=settings_data.invoice_prefix,
                    invoice_number_start=settings_data.invoice_number_start,
                    payment_terms_days=settings_data.payment_terms_days,
                    email_notifications=settings_data.email_notifications,
                    push_notifications=settings_data.push_notifications,
                    marketing_emails=settings_data.marketing_emails,
                )

                # Cache the settings data for 1 hour
                settings_dict = settings_response.model_dump()
                await cache_manager.set(cache_key, settings_dict, ttl=3600)

                return self.create_success_response(
                    message="Settings retrieved successfully", data=settings_dict
                )
            except Exception as e:
                raise BusinessLogicError(f"Failed to retrieve settings: {str(e)}")

        @self.router.put(
            "/me/settings",
            response_model=StandardResponse,
            responses=self.standard_responses,
        )
        async def update_settings(
            data: UserSettingsUpdate,
            db: AsyncSession = Depends(get_async_db),
            current_user: User = Depends(get_current_active_user),
        ):
            """Update user settings"""
            try:
                user_service = UserService(db)
                updated_settings = await user_service.update_user_settings(
                    current_user.id, data
                )

                # Convert SQLAlchemy object to Pydantic response model
                settings_response = UserSettingsResponse(
                    theme=updated_settings.theme,
                    language=updated_settings.language,
                    timezone=updated_settings.timezone,
                    date_format=updated_settings.date_format,
                    time_format=updated_settings.time_format,
                    default_currency=updated_settings.default_currency,
                    default_hourly_rate=float(updated_settings.default_hourly_rate),
                    invoice_prefix=updated_settings.invoice_prefix,
                    invoice_number_start=updated_settings.invoice_number_start,
                    payment_terms_days=updated_settings.payment_terms_days,
                    email_notifications=updated_settings.email_notifications,
                    push_notifications=updated_settings.push_notifications,
                    marketing_emails=updated_settings.marketing_emails,
                )

                return self.create_success_response(
                    message="Settings updated successfully",
                    data=settings_response.model_dump(),
                )
            except ValueError as e:
                raise ValidationException(str(e))
            except Exception as e:
                raise BusinessLogicError(f"Failed to update settings: {str(e)}")

        @self.router.delete(
            "/me", response_model=StandardResponse, responses=self.standard_responses
        )
        async def delete_account(
            db: AsyncSession = Depends(get_async_db),
            current_user: User = Depends(get_current_active_user),
        ):
            """Delete user account"""
            try:
                user_service = UserService(db)
                result = await user_service.delete_user_account(current_user.id)

                return self.create_success_response(
                    message="Account deleted successfully", data=result
                )
            except Exception as e:
                raise BusinessLogicError(f"Failed to delete account: {str(e)}")


# Create router instance
user_router = UserRouter()
router = user_router.router


# All routes are now defined in the UserRouter class above
