"""
Advanced Time Analytics API endpoints for DevHQ
Provides productivity insights, time distribution, and efficiency metrics
"""

from datetime import date, timed<PERSON>ta
from typing import Optional

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.analytics_service import TimeAnalyticsService
from app.database import get_async_db
from app.dependencies import get_current_user
from app.models import User

router = APIRouter(prefix="/api/v1/analytics", tags=["analytics"])


@router.get("/time/productivity")
async def get_productivity_insights(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get productivity insights and patterns."""

    analytics_service = TimeAnalyticsService(db)
    return await analytics_service.get_productivity_insights(
        user_id=current_user.id, start_date=start_date, end_date=end_date
    )


@router.get("/time/distribution")
async def get_time_distribution(
    group_by: str = Query("project", pattern="^(project|client|task|status)$"),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get time distribution analysis."""

    analytics_service = TimeAnalyticsService(db)
    return await analytics_service.get_time_distribution(
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        group_by=group_by,
    )


@router.get("/time/billable")
async def get_billable_analysis(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get billable vs non-billable time analysis."""

    analytics_service = TimeAnalyticsService(db)
    return await analytics_service.get_billable_analysis(
        user_id=current_user.id, start_date=start_date, end_date=end_date
    )


@router.get("/time/efficiency")
async def get_efficiency_metrics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get efficiency metrics and project velocity."""

    analytics_service = TimeAnalyticsService(db)
    return await analytics_service.get_efficiency_metrics(
        user_id=current_user.id, start_date=start_date, end_date=end_date
    )


@router.get("/time/export")
async def export_time_data(
    format: str = Query("csv", pattern="^(csv|pdf)$"),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    include_analytics: bool = Query(False),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Export time tracking data in various formats."""

    # Placeholder for export functionality
    return {
        "message": f"Export functionality for {format} format not yet implemented",
        "requested_format": format,
        "date_range": {
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None,
        },
        "include_analytics": include_analytics,
    }
