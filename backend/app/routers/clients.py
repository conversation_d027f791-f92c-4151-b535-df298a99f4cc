"""
Client management foundation router
"""

import uuid
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import ConflictError, NotFoundError
from app.database import get_async_db
from app.dependencies import get_current_active_user
from app.models.user import User
from app.schemas.client import (ClientCreate, ClientDetailResponse,
                                ClientResponse, ClientUpdate,
                                PaginatedClientsResponse)
from app.services.client_service import ClientService

router = APIRouter()


@router.post("/", response_model=ClientResponse, status_code=status.HTTP_201_CREATED)
async def create_client(
    data: ClientCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user),
):
    client_service = ClientService(db)
    try:
        return await client_service.create_client(current_user.id, data)
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message,
        )


@router.get("/", response_model=PaginatedClientsResponse)
async def list_clients(
    q: Optional[str] = Query(None, description="Search by name, email, or company"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    company: Optional[str] = Query(None, description="Filter by company (contains)"),
    email: Optional[str] = Query(None, description="Filter by email (contains)"),
    country: Optional[str] = Query(None, description="Filter by country (contains)"),
    workspace_id: Optional[str] = Query(None, description="Filter by workspace ID"),
    sort_by: Optional[str] = Query(
        "created_at",
        description="Sort field: name|email|company|created_at|updated_at",
    ),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc|desc"),
    page: int = Query(1, ge=1),
    per_page: int = Query(25, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user),
):
    client_service = ClientService(db)

    # Convert workspace_id string to UUID if provided
    workspace_uuid = None
    if workspace_id:
        try:
            workspace_uuid = uuid.UUID(workspace_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid workspace ID format"
            )

    result = await client_service.list_clients(
        user_id=current_user.id,
        q=q,
        is_active=is_active,
        company=company,
        email=email,
        country=country,
        workspace_id=workspace_uuid,
        sort_by=sort_by,
        sort_order=sort_order,
        page=page,
        per_page=per_page,
    )

    return PaginatedClientsResponse(**result)


@router.get("/{client_id}", response_model=ClientDetailResponse)
async def get_client(
    client_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user),
):
    # Convert string UUID to UUID object
    try:
        client_uuid = uuid.UUID(client_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid client ID format"
        )

    client_service = ClientService(db)
    try:
        # Use simple get_client method instead of get_client_with_details to avoid hanging
        client = await client_service.get_client(current_user.id, client_uuid)

        # Return basic client data without complex relationships
        client_data = {
            **client.__dict__,
            "projects": [],  # Empty for now to avoid hanging
            "invoices": [],  # Empty for now to avoid hanging
        }
        return ClientDetailResponse(**client_data)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


@router.put("/{client_id}", response_model=ClientResponse)
async def update_client(
    client_id: str,
    data: ClientUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user),
):
    # Convert string UUID to UUID object
    try:
        client_uuid = uuid.UUID(client_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid client ID format"
        )

    client_service = ClientService(db)
    try:
        return await client_service.update_client(current_user.id, client_uuid, data)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message,
        )


@router.delete("/{client_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_client(
    client_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user),
):
    # Convert string UUID to UUID object
    try:
        client_uuid = uuid.UUID(client_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid client ID format"
        )

    client_service = ClientService(db)
    try:
        await client_service.delete_client(current_user.id, client_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


@router.get("/{client_id}/statistics")
async def get_client_statistics(
    client_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user),
):
    """Get client statistics including projects, invoices, and revenue."""
    # Convert string UUID to UUID object
    try:
        client_uuid = uuid.UUID(client_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid client ID format"
        )

    client_service = ClientService(db)
    try:
        return await client_service.get_client_statistics(current_user.id, client_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)
