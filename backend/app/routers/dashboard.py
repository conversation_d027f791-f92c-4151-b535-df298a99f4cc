"""
Real-time Developer Dashboard
Live insights, client activity monitoring, and engagement analytics
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import and_, desc, func
from sqlalchemy.orm import Session

from app.core.websocket_manager import websocket_manager
from app.database import get_db
from app.dependencies import get_current_user
from app.models.activity import ActivityLog
from app.models.approval import ClientApproval
from app.models.client import Client
from app.models.invoice import Invoice
from app.models.project import Project
from app.models.user import User
from app.routers.websocket import websocket_events
from app.services.realtime_service import realtime_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/dashboard", tags=["dashboard"])


@router.get("/overview")
async def get_dashboard_overview(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    """Get comprehensive dashboard overview with real-time metrics"""
    try:
        # Get date ranges
        today = datetime.now(timezone.utc).date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # Basic counts
        total_clients = (
            db.query(Client)
            .filter(Client.user_id == current_user.id, Client.deleted_at.is_(None))
            .count()
        )

        total_projects = (
            db.query(Project)
            .filter(Project.user_id == current_user.id, Project.deleted_at.is_(None))
            .count()
        )

        active_projects = (
            db.query(Project)
            .filter(
                Project.user_id == current_user.id,
                Project.status.in_(["planning", "in_progress"]),
                Project.deleted_at.is_(None),
            )
            .count()
        )

        pending_approvals = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.user_id == current_user.id,
                ClientApproval.status == "pending",
            )
            .count()
        )

        # Revenue metrics
        total_revenue = (
            db.query(func.sum(Invoice.total_amount))
            .filter(
                Invoice.user_id == current_user.id,
                Invoice.status == "paid",
                Invoice.deleted_at.is_(None),
            )
            .scalar()
            or 0
        )

        monthly_revenue = (
            db.query(func.sum(Invoice.total_amount))
            .filter(
                Invoice.user_id == current_user.id,
                Invoice.status == "paid",
                Invoice.issue_date >= month_ago,
                Invoice.deleted_at.is_(None),
            )
            .scalar()
            or 0
        )

        outstanding_amount = (
            db.query(func.sum(Invoice.total_amount))
            .filter(
                Invoice.user_id == current_user.id,
                Invoice.status.in_(["sent", "viewed", "overdue"]),
                Invoice.deleted_at.is_(None),
            )
            .scalar()
            or 0
        )

        # Recent activity
        recent_activities = (
            db.query(ActivityLog)
            .filter(
                ActivityLog.user_id == current_user.id,
                ActivityLog.created_at
                >= datetime.now(timezone.utc) - timedelta(days=7),
            )
            .order_by(desc(ActivityLog.created_at))
            .limit(10)
            .all()
        )

        # WebSocket connection stats
        websocket_stats = websocket_manager.get_connection_stats()

        return {
            "metrics": {
                "total_clients": total_clients,
                "total_projects": total_projects,
                "active_projects": active_projects,
                "pending_approvals": pending_approvals,
                "total_revenue": float(total_revenue),
                "monthly_revenue": float(monthly_revenue),
                "outstanding_amount": float(outstanding_amount),
            },
            "recent_activities": [
                {
                    "id": str(activity.id),
                    "entity_type": activity.entity_type,
                    "action": activity.action,
                    "details": activity.details,
                    "created_at": activity.created_at.isoformat(),
                }
                for activity in recent_activities
            ],
            "realtime_stats": {
                "active_connections": websocket_stats["total_connections"],
                "connected_clients": websocket_stats["connections_by_type"].get(
                    "client", 0
                ),
                "active_rooms": websocket_stats["active_client_rooms"]
                + websocket_stats["active_project_rooms"],
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting dashboard overview: {e}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard overview")


@router.get("/live-activity")
async def get_live_activity_feed(
    limit: int = Query(50, ge=1, le=100),
    since: Optional[str] = Query(
        None, description="ISO timestamp to get activities since"
    ),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get live activity feed with real-time updates"""
    try:
        # Parse since timestamp
        since_dt = None
        if since:
            try:
                since_dt = datetime.fromisoformat(since.replace("Z", "+00:00"))
            except:
                since_dt = datetime.now(timezone.utc) - timedelta(hours=1)
        else:
            since_dt = datetime.now(timezone.utc) - timedelta(hours=1)

        # Get activities
        query = (
            db.query(ActivityLog)
            .filter(
                ActivityLog.user_id == current_user.id,
                ActivityLog.created_at >= since_dt,
            )
            .order_by(desc(ActivityLog.created_at))
            .limit(limit)
        )

        activities = query.all()

        # Format activities
        activity_feed = []
        for activity in activities:
            activity_data = {
                "id": str(activity.id),
                "entity_type": activity.entity_type,
                "entity_id": activity.entity_id,
                "action": activity.action,
                "details": activity.details,
                "created_at": activity.created_at.isoformat(),
                "type": "system_activity",
            }
            activity_feed.append(activity_data)

        return {
            "activities": activity_feed,
            "total": len(activity_feed),
            "since": since_dt.isoformat(),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting live activity feed: {e}")
        raise HTTPException(status_code=500, detail="Failed to get activity feed")


@router.get("/client-activity")
async def get_client_activity_insights(
    days: int = Query(7, ge=1, le=30),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get client activity insights and engagement metrics"""
    try:
        # Get date range
        start_date = datetime.now(timezone.utc) - timedelta(days=days)

        # Get client approvals with activity
        client_approvals = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.user_id == current_user.id,
                ClientApproval.created_at >= start_date,
            )
            .all()
        )

        # Aggregate client activity
        client_activity = {}
        for approval in client_approvals:
            client_id = str(approval.client_id) if approval.client_id else "unknown"

            if client_id not in client_activity:
                client_activity[client_id] = {
                    "client_id": client_id,
                    "total_approvals": 0,
                    "pending_approvals": 0,
                    "approved_count": 0,
                    "rejected_count": 0,
                    "last_activity": None,
                    "engagement_score": 0,
                }

            activity = client_activity[client_id]
            activity["total_approvals"] += 1

            if approval.status == "pending":
                activity["pending_approvals"] += 1
            elif approval.status == "approved":
                activity["approved_count"] += 1
            elif approval.status == "rejected":
                activity["rejected_count"] += 1

            if not activity[
                "last_activity"
            ] or approval.updated_at > datetime.fromisoformat(
                activity["last_activity"]
            ):
                activity["last_activity"] = approval.updated_at.isoformat()

            # Calculate engagement score
            activity["engagement_score"] = (
                activity["approved_count"] * 2
                + activity["rejected_count"] * 1
                + activity["pending_approvals"] * 0.5
            )

        # Sort by engagement score
        sorted_activity = sorted(
            client_activity.values(), key=lambda x: x["engagement_score"], reverse=True
        )

        # Get client names
        for activity in sorted_activity:
            if activity["client_id"] != "unknown":
                client = (
                    db.query(Client)
                    .filter(
                        Client.id == activity["client_id"],
                        Client.user_id == current_user.id,
                    )
                    .first()
                )
                if client:
                    activity["client_name"] = client.name
                    activity["client_email"] = client.email

        return {
            "client_activity": sorted_activity,
            "period_days": days,
            "total_clients": len(sorted_activity),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting client activity insights: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get client activity insights"
        )


@router.get("/project-health")
async def get_project_health_metrics(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    """Get project health metrics and status overview"""
    try:
        # Get all active projects
        projects = (
            db.query(Project)
            .filter(Project.user_id == current_user.id, Project.deleted_at.is_(None))
            .all()
        )

        project_health = []
        for project in projects:
            # Calculate health metrics
            total_milestones = len(
                [m for m in project.milestones if m.deleted_at is None]
            )
            completed_milestones = len(
                [
                    m
                    for m in project.milestones
                    if m.deleted_at is None and m.status == "completed"
                ]
            )

            # Get pending approvals for this project
            pending_approvals = (
                db.query(ClientApproval)
                .filter(
                    ClientApproval.project_id == project.id,
                    ClientApproval.status == "pending",
                )
                .count()
            )

            # Calculate health score
            completion_score = (
                (completed_milestones / total_milestones * 100)
                if total_milestones > 0
                else 0
            )
            approval_penalty = min(pending_approvals * 10, 30)  # Max 30% penalty
            health_score = max(completion_score - approval_penalty, 0)

            # Determine health status
            if health_score >= 80:
                health_status = "excellent"
            elif health_score >= 60:
                health_status = "good"
            elif health_score >= 40:
                health_status = "fair"
            else:
                health_status = "poor"

            project_health.append(
                {
                    "project_id": str(project.id),
                    "project_title": project.title,
                    "status": project.status,
                    "completion_percentage": float(project.completion_percentage or 0),
                    "health_score": round(health_score, 1),
                    "health_status": health_status,
                    "total_milestones": total_milestones,
                    "completed_milestones": completed_milestones,
                    "pending_approvals": pending_approvals,
                    "last_updated": project.updated_at.isoformat(),
                }
            )

        # Sort by health score
        project_health.sort(key=lambda x: x["health_score"], reverse=True)

        return {
            "projects": project_health,
            "summary": {
                "total_projects": len(project_health),
                "excellent_health": len(
                    [p for p in project_health if p["health_status"] == "excellent"]
                ),
                "good_health": len(
                    [p for p in project_health if p["health_status"] == "good"]
                ),
                "fair_health": len(
                    [p for p in project_health if p["health_status"] == "fair"]
                ),
                "poor_health": len(
                    [p for p in project_health if p["health_status"] == "poor"]
                ),
                "average_health_score": (
                    round(
                        sum(p["health_score"] for p in project_health)
                        / len(project_health),
                        1,
                    )
                    if project_health
                    else 0
                ),
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting project health metrics: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to get project health metrics"
        )


@router.get("/realtime-insights")
async def get_realtime_insights(current_user: User = Depends(get_current_user)):
    """Get real-time insights and connection analytics"""
    try:
        # Get WebSocket statistics
        websocket_stats = websocket_manager.get_connection_stats()
        realtime_stats = realtime_service.get_stats()

        # Get active connections details
        active_connections = []
        for sid, info in websocket_manager.active_connections.items():
            if info.get("user_type") == "client":
                active_connections.append(
                    {
                        "type": "client",
                        "connected_at": (
                            info.get("connected_at").isoformat()
                            if info.get("connected_at")
                            else None
                        ),
                        "last_activity": (
                            info.get("last_activity").isoformat()
                            if info.get("last_activity")
                            else None
                        ),
                        "duration_minutes": (
                            (
                                (
                                    datetime.now(timezone.utc)
                                    - info.get("connected_at")
                                ).total_seconds()
                                / 60
                            )
                            if info.get("connected_at")
                            else 0
                        ),
                    }
                )

        # Calculate engagement metrics
        total_connection_time = sum(
            conn["duration_minutes"] for conn in active_connections
        )
        average_session_length = (
            (total_connection_time / len(active_connections))
            if active_connections
            else 0
        )

        return {
            "websocket_stats": websocket_stats,
            "realtime_service": {
                "processing": realtime_stats["processing"],
                "queue_size": realtime_stats["queue_size"],
            },
            "client_connections": {
                "active_count": len(active_connections),
                "total_connection_time_minutes": round(total_connection_time, 2),
                "average_session_length_minutes": round(average_session_length, 2),
                "connections": active_connections,
            },
            "engagement_metrics": {
                "real_time_engagement": len(active_connections) > 0,
                "connection_health": (
                    "excellent"
                    if websocket_stats["total_connections"] > 0
                    else "no_connections"
                ),
                "event_processing_health": (
                    "healthy" if realtime_stats["processing"] else "stopped"
                ),
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting realtime insights: {e}")
        raise HTTPException(status_code=500, detail="Failed to get realtime insights")


@router.post("/broadcast-announcement")
async def broadcast_announcement(
    announcement_data: Dict[str, Any], current_user: User = Depends(get_current_user)
):
    """Broadcast announcement to all connected clients"""
    try:
        title = announcement_data.get("title", "Announcement")
        message = announcement_data.get("message", "")
        announcement_type = announcement_data.get("type", "info")

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Broadcast to all developers
        await websocket_manager.broadcast_to_developers(
            "announcement",
            {
                "title": title,
                "message": message,
                "type": announcement_type,
                "from": current_user.full_name or current_user.email,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )

        return {
            "status": "broadcasted",
            "title": title,
            "message": message,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error broadcasting announcement: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast announcement")


@router.get("/performance-metrics")
async def get_performance_metrics(
    days: int = Query(30, ge=1, le=90),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get performance metrics and productivity insights"""
    try:
        start_date = datetime.now(timezone.utc) - timedelta(days=days)

        # Project completion metrics
        completed_projects = (
            db.query(Project)
            .filter(
                Project.user_id == current_user.id,
                Project.status == "completed",
                Project.updated_at >= start_date,
                Project.deleted_at.is_(None),
            )
            .count()
        )

        # Approval response time
        approvals = (
            db.query(ClientApproval)
            .filter(
                ClientApproval.user_id == current_user.id,
                ClientApproval.status.in_(["approved", "revision_requested"]),
                ClientApproval.updated_at >= start_date,
            )
            .all()
        )

        response_times = []
        for approval in approvals:
            if approval.created_at and approval.updated_at:
                response_time = (
                    approval.updated_at - approval.created_at
                ).total_seconds() / 3600  # hours
                response_times.append(response_time)

        average_response_time = (
            sum(response_times) / len(response_times) if response_times else 0
        )

        # Revenue growth
        current_month_revenue = (
            db.query(func.sum(Invoice.total_amount))
            .filter(
                Invoice.user_id == current_user.id,
                Invoice.status == "paid",
                Invoice.issue_date >= datetime.now(timezone.utc).replace(day=1).date(),
                Invoice.deleted_at.is_(None),
            )
            .scalar()
            or 0
        )

        previous_month_start = (
            datetime.now(timezone.utc).replace(day=1) - timedelta(days=1)
        ).replace(day=1)
        previous_month_end = datetime.now(timezone.utc).replace(day=1) - timedelta(
            days=1
        )

        previous_month_revenue = (
            db.query(func.sum(Invoice.total_amount))
            .filter(
                Invoice.user_id == current_user.id,
                Invoice.status == "paid",
                Invoice.issue_date >= previous_month_start.date(),
                Invoice.issue_date <= previous_month_end.date(),
                Invoice.deleted_at.is_(None),
            )
            .scalar()
            or 0
        )

        revenue_growth = (
            (
                (current_month_revenue - previous_month_revenue)
                / previous_month_revenue
                * 100
            )
            if previous_month_revenue > 0
            else 0
        )

        return {
            "period_days": days,
            "project_metrics": {
                "completed_projects": completed_projects,
                "completion_rate": f"{completed_projects / days * 30:.1f} projects/month",
            },
            "approval_metrics": {
                "total_approvals": len(approvals),
                "average_response_time_hours": round(average_response_time, 2),
                "response_efficiency": (
                    "excellent"
                    if average_response_time < 24
                    else "good"
                    if average_response_time < 72
                    else "needs_improvement"
                ),
            },
            "revenue_metrics": {
                "current_month_revenue": float(current_month_revenue),
                "previous_month_revenue": float(previous_month_revenue),
                "growth_percentage": round(revenue_growth, 2),
                "growth_trend": (
                    "up"
                    if revenue_growth > 0
                    else "down"
                    if revenue_growth < 0
                    else "stable"
                ),
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")
