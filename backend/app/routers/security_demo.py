"""
Security demonstration - Example of how to use enhanced security features
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from app.core.auth import authenticate_user, create_access_token
from app.core.rate_limiting import api_rate_limit, auth_rate_limit
from app.core.security_service import SecurityService, get_security_service
from app.database import get_db
from app.dependencies import get_current_user
from app.models.user import User

router = APIRouter(prefix="/security-demo", tags=["Security Demo"])


@router.post("/login", dependencies=[Depends(auth_rate_limit)])
async def login(
    request: Request, email: str, password: str, db: Session = Depends(get_db)
):
    """
    Secure login endpoint with rate limiting and enhanced authentication
    """
    try:
        # Authenticate user with enhanced security (includes account lockout)
        user = authenticate_user(db, email, password)

        if not user:
            raise HTTPException(status_code=401, detail="Incorrect email or password")

        # Create access token
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": str(user.id)}, expires_delta=access_token_expires
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": str(user.id),
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
            },
        }

    except Exception as e:
        # Log security events
        logger.warning(
            "Failed login attempt",
            email=email,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            error=str(e),
        )
        raise HTTPException(status_code=401, detail="Incorrect email or password")


@router.post("/register", dependencies=[Depends(auth_rate_limit)])
async def register(
    request: Request,
    email: str,
    password: str,
    first_name: str,
    last_name: str,
    db: Session = Depends(get_db),
):
    """
    Secure user registration with password strength validation
    """
    # Get security service
    security_service = get_security_service(db)

    # Validate email format
    if not security_service.validate_email_format(email):
        raise HTTPException(status_code=400, detail="Invalid email format")

    # Validate password strength
    password_validation = security_service.validate_password_strength(password)
    if not password_validation["valid"]:
        raise HTTPException(status_code=400, detail=password_validation["feedback"])

    # Check if user already exists
    existing_user = db.query(User).filter(User.email == email).first()
    if existing_user:
        raise HTTPException(
            status_code=400, detail="User with this email already exists"
        )

    # Hash password securely
    hashed_password = security_service.hash_password(password)

    # Create user
    user = User(
        email=email,
        password_hash=hashed_password,
        first_name=security_service.sanitize_input(first_name),
        last_name=security_service.sanitize_input(last_name),
        is_active=True,
    )

    db.add(user)
    db.commit()
    db.refresh(user)

    return {
        "message": "User registered successfully",
        "user": {
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
        },
    }


@router.get("/profile", dependencies=[Depends(api_rate_limit)])
async def get_profile(current_user: User = Depends(get_current_user)):
    """
    Secure profile endpoint with rate limiting
    """
    return {
        "id": str(current_user.id),
        "email": current_user.email,
        "first_name": current_user.first_name,
        "last_name": current_user.last_name,
        "created_at": (
            current_user.created_at.isoformat() if current_user.created_at else None
        ),
    }


@router.post("/validate-password")
async def validate_password_strength(password: str):
    """
    Password strength validation endpoint
    """
    security_service = SecurityService(None)  # No DB needed
    validation = security_service.validate_password_strength(password)

    return {
        "valid": validation["valid"],
        "feedback": validation["feedback"],
        "strength_score": validation["strength_score"],
    }


@router.post("/generate-csrf-token")
async def generate_csrf_token():
    """
    Generate CSRF protection token
    """
    security_service = SecurityService(None)  # No DB needed
    csrf_token = security_service.generate_csrf_token()

    return {"csrf_token": csrf_token}
