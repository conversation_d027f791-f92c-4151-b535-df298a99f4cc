"""
Authentication router with login, register, and token management endpoints
"""

from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy import or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.core.auth import (create_access_token, create_password_reset_token,
                           create_refresh_token, create_verification_token,
                           revoke_all_user_sessions, revoke_refresh_token,
                           verify_refresh_token, verify_token)
from app.core.email import email_service
from app.core.error_handlers import (AuthenticationError, BusinessLogicError,
                                     ConflictError, NotFoundError,
                                     ValidationException)
from app.core.phone_validator import validate_phone
from app.core.transaction_manager import db_transaction, transactional
from app.database import get_async_db
from app.dependencies import get_current_user
from app.models.user import User, UserSettings, UserSession
from app.schemas.auth import (AuthResponse, EmailVerificationRequest,
                              MessageResponse, PasswordResetConfirm,
                              PasswordResetRequest, RefreshTokenRequest,
                              TokenResponse, UserLoginRequest,
                              UserRegisterRequest, UserResponse)
from app.schemas.errors import (COMMON_RESPONSES, AuthenticationErrorResponse,
                                ConflictErrorResponse, ValidationErrorResponse)

router = APIRouter(
    tags=["Authentication"],
    responses={
        500: COMMON_RESPONSES[500],  # Internal Server Error
    },
)


@router.post(
    "/register",
    response_model=MessageResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register a new user account",
    description="""
    Create a new user account with email verification.

    This endpoint:
    - Validates user input data
    - Checks for existing users with the same email
    - Creates a new user account with hashed password
    - Sends email verification link
    - Returns success message (user must verify email before login)

    **Password Requirements:**
    - At least 8 characters long
    - Must contain at least one uppercase letter
    - Must contain at least one lowercase letter
    - Must contain at least one digit
    - Must contain at least one special character
    """,
    responses={
        201: {
            "description": "User successfully registered, email verification required",
            "content": {
                "application/json": {
                    "example": {
                        "message": "Account created successfully! Please check your email (<EMAIL>) for verification instructions.",
                        "success": True
                    }
                }
            },
        },
        409: COMMON_RESPONSES[409],  # Conflict - User already exists
        422: COMMON_RESPONSES[422],  # Validation Error
        500: COMMON_RESPONSES[500],  # Internal Server Error
    },
)
async def register_user(
    user_data: UserRegisterRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Register a new user account with comprehensive validation and error handling.
    """
    # Check if user already exists
    result = await db.execute(select(User).where(User.email == user_data.email))
    existing_user = result.scalar_one_or_none()
    if existing_user:
        raise ConflictError("User with this email already exists")

    # Validate and normalize phone number if provided
    normalized_phone = None
    if user_data.phone and user_data.phone.strip():
        is_valid, formatted_phone, error_msg = validate_phone(user_data.phone)
        if not is_valid:
            # For development, log the error but don't fail registration
            print(f"⚠️  Phone validation failed: {error_msg} for {user_data.phone}")
            # In development, store the original phone if validation fails
            if settings.environment == "development":
                normalized_phone = user_data.phone
            else:
                raise ValidationException(f"Invalid phone number: {error_msg}")
        else:
            normalized_phone = formatted_phone

    # Create new user
    user = User(
        email=user_data.email,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        phone=normalized_phone,
        is_active=True,
        is_verified=False,
    )

    # Set password
    user.set_password(user_data.password)

    # Add to database
    db.add(user)
    await db.flush()  # Get the user ID

    # Create default user settings
    user_settings = UserSettings(user_id=user.id)
    db.add(user_settings)

    # Commit transaction
    await db.commit()
    await db.refresh(user)

    # Send verification email
    verification_token = create_verification_token(str(user.id))
    email_service.send_verification_email(
        user.email, user.first_name, verification_token
    )

    # Return success message instead of tokens for unverified users
    return MessageResponse(
        message=f"Account created successfully! Please check your email ({user.email}) for verification instructions.",
        success=True
    )


@router.post(
    "/login",
    response_model=AuthResponse,
    summary="Authenticate user and return tokens",
    description="""
    Authenticate a user with email and password.
    
    This endpoint:
    - Validates user credentials (email and password)
    - Checks if the user account is active
    - Generates new JWT access and refresh tokens
    - Returns user data and authentication tokens
    
    **Security Features:**
    - Password verification using secure hashing
    - Device tracking for refresh tokens
    - Account status validation
    """,
    responses={
        200: {
            "description": "User successfully authenticated",
            "content": {
                "application/json": {
                    "example": {
                        "user": {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "email": "<EMAIL>",
                            "first_name": "John",
                            "last_name": "Doe",
                            "full_name": "John Doe",
                            "avatar_url": "https://example.com/avatar.jpg",
                            "phone": "+**********",
                            "bio": "Software Developer",
                            "is_active": True,
                            "is_verified": True,
                            "created_at": "2024-01-15T10:30:00Z",
                            "updated_at": "2024-01-15T10:30:00Z",
                        },
                        "tokens": {
                            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                            "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                            "token_type": "bearer",
                            "expires_in": 3600,
                        },
                    }
                }
            },
        },
        401: COMMON_RESPONSES[401],  # Invalid credentials
        403: COMMON_RESPONSES[403],  # Account deactivated
        422: COMMON_RESPONSES[422],  # Validation Error
        500: COMMON_RESPONSES[500],  # Internal Server Error
    },
)
async def login_user(
    login_data: UserLoginRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Authenticate user with email and password, returning JWT tokens on success.
    """
    # Find user by email
    result = await db.execute(
        select(User).where(
            User.email == login_data.email_or_username, User.deleted_at.is_(None)
        )
    )
    user = result.scalar_one_or_none()

    if not user or not user.verify_password(login_data.password):
        raise AuthenticationError("Invalid email or password")

    if not user.is_active:
        raise BusinessLogicError("Account is deactivated")

    if not user.is_verified:
        raise BusinessLogicError("Please verify your email address before logging in. Check your email for verification instructions.")

    # Generate tokens
    access_token = create_access_token(data={"sub": str(user.id)})

    # Get device info from request
    device_info = f"{request.headers.get('user-agent', 'Unknown')} - {request.client.host if request.client else 'Unknown'}"
    refresh_token = create_refresh_token(str(user.id), db, device_info)

    # Prepare response
    user_response = UserResponse(
        id=str(user.id),
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        full_name=user.full_name,
        avatar_url=user.avatar_url,
        phone=user.phone,
        bio=user.bio,
        is_active=user.is_active,
        is_verified=user.is_verified,
        created_at=user.created_at,
        updated_at=user.updated_at,
    )

    tokens = TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60,
    )

    return AuthResponse(user=user_response, tokens=tokens)


@router.post("/refresh", response_model=TokenResponse)
async def refresh_access_token(
    refresh_data: RefreshTokenRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Refresh access token using refresh token
    """
    # For now, let's handle this without the transaction manager
    # TODO: Implement proper async transaction management
    try:
        # Verify token (need to make this async too)
        # For now, let's create a simple verification
        stmt = select(UserSession).where(
            UserSession.refresh_token == refresh_data.refresh_token,
            UserSession.is_active == True,
            UserSession.expires_at > datetime.now(timezone.utc)
        )
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()

        if not session or session.is_expired:
            raise AuthenticationError("Invalid or expired refresh token")

        # Get user
        user_stmt = select(User).where(
            User.id == session.user_id,
            User.is_active == True,
            User.deleted_at.is_(None)
        )
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()

        if not user:
            raise AuthenticationError("User not found or inactive")

        # Generate new access token
        access_token = create_access_token(data={"sub": str(user.id)})

        # For now, return the same refresh token (no rotation)
        # TODO: Implement proper token rotation with async support

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_data.refresh_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
        )
    except Exception as e:
        await db.rollback()
        raise e


@router.post("/logout", response_model=MessageResponse)
async def logout_user(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Logout user by revoking refresh token
    """
    # Revoke the specific refresh token
    revoked = await revoke_refresh_token(refresh_data.refresh_token, db)

    # Don't throw error if token is already gone - user is effectively logged out
    # This handles cases where token was already revoked or expired
    return MessageResponse(message="Successfully logged out")


@router.post("/logout-all", response_model=MessageResponse)
async def logout_all_sessions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """
    Logout user from all devices by revoking all refresh tokens
    """
    revoked_count = revoke_all_user_sessions(str(current_user.id), db)

    return MessageResponse(
        message=f"Successfully logged out from {revoked_count} device(s)"
    )


@router.post("/verify-email", response_model=MessageResponse)
async def verify_email(
    verification_data: EmailVerificationRequest,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Verify user email address using verification token
    """
    try:
        print(f"DEBUG: Received verification request with token: {verification_data.token}")
        print(f"DEBUG: Token length: {len(verification_data.token) if verification_data.token else 0}")
        
        # Check if token is provided
        if not verification_data.token:
            print("DEBUG: No token provided in request")
            raise ValidationException("No verification token provided")
            
        # Verify token
        user_id = verify_token(verification_data.token, "email_verification")
        print(f"DEBUG: Token verification result - user_id: {user_id}")
        
        if not user_id:
            raise ValidationException("Invalid or expired verification token")

        # Find user using async syntax
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        print(f"DEBUG: Found user: {user}")
        
        if not user:
            raise NotFoundError("User not found")

        if user.is_verified:
            return MessageResponse(message="Email already verified")

        # Update user verification status
        user.is_verified = True
        user.email_verified_at = datetime.now(timezone.utc)
        await db.commit()
        
        print("DEBUG: Successfully committed verification")

        return MessageResponse(message="Email successfully verified")
        
    except Exception as e:
        print(f"ERROR in verify_email: {str(e)}")
        print(f"ERROR type: {type(e)}")
        import traceback
        traceback.print_exc()
        raise


@router.post("/resend-verification", response_model=MessageResponse)
async def resend_verification_email(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """
    Resend email verification email
    """
    if current_user.is_verified:
        raise ValidationException("Email is already verified")

    # Generate new verification token
    verification_token = create_verification_token(str(current_user.id))

    # Send verification email
    success = email_service.send_verification_email(
        current_user.email, current_user.first_name, verification_token
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email",
        )

    return MessageResponse(message="Verification email sent successfully")


@router.post("/public-resend-verification", response_model=MessageResponse)
async def public_resend_verification_email(
    email_data: dict,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Public endpoint to resend email verification email
    """
    email = email_data.get("email")
    if not email:
        raise ValidationException("Email is required")

    # Find user by email
    result = await db.execute(
        select(User).where(User.email == email, User.deleted_at.is_(None))
    )
    user = result.scalar_one_or_none()

    if not user:
        # Return success even if user doesn't exist to prevent email enumeration
        return MessageResponse(
            message="If the email exists, a verification link has been sent"
        )

    if user.is_verified:
        return MessageResponse(message="Email is already verified")

    # Generate new verification token
    verification_token = create_verification_token(str(user.id))

    # Send verification email
    success = email_service.send_verification_email(
        user.email, user.first_name, verification_token
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email",
        )

    return MessageResponse(message="Verification email sent successfully")


@router.post("/forgot-password", response_model=MessageResponse)
async def forgot_password(
    reset_data: PasswordResetRequest,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Send password reset email
    """
    # Find user by email
    result = await db.execute(
        select(User).where(User.email == reset_data.email, User.deleted_at.is_(None))
    )
    user = result.scalar_one_or_none()

    # Always return success to prevent email enumeration
    if not user:
        return MessageResponse(
            message="If the email exists, a reset link has been sent"
        )

    if not user.is_active:
        return MessageResponse(
            message="If the email exists, a reset link has been sent"
        )

    # Generate password reset token
    reset_token = create_password_reset_token(str(user.id))

    # Send password reset email
    success = email_service.send_password_reset_email(
        user.email, user.first_name, reset_token
    )

    if not success:
        # Log error but don't expose it to user
        print(f"Failed to send password reset email to {user.email}")

    return MessageResponse(message="If the email exists, a reset link has been sent")


@router.post("/reset-password", response_model=MessageResponse)
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Reset user password using reset token
    """
    # Verify token
    user_id = verify_token(reset_data.token, "password_reset")
    if not user_id:
        raise ValidationException("Invalid or expired reset token")

    # Find user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise NotFoundError("User not found")

    if not user.is_active:
        raise ValidationException("Account is deactivated")

    # Update password with transaction
    with db_transaction(db) as tx_db:
        # Re-fetch user in transaction context
        user = tx_db.query(User).filter(User.id == user_id).first()
        if not user:
            raise NotFoundError("User not found")

        if not user.is_active:
            raise ValidationException("Account is deactivated")

        # Update password
        user.set_password(reset_data.new_password)

        # Revoke all existing sessions for security
        revoke_all_user_sessions(str(user.id), tx_db)

    return MessageResponse(message="Password successfully reset")


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
):
    """
    Get current authenticated user information
    """
    return UserResponse(
        id=str(current_user.id),
        email=current_user.email,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        full_name=current_user.full_name,
        avatar_url=current_user.avatar_url,
        phone=current_user.phone,
        bio=current_user.bio,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
    )
