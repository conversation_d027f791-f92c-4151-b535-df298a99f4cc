"""
OAuth Authentication Router for DevHQ
Handles Google and GitHub OAuth authentication flows
"""

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.core.auth import create_access_token, create_refresh_token
from app.core.error_handlers import AuthenticationError, ConflictError
from app.database import get_async_db
from app.models.user import User, UserSettings
from app.schemas.auth import AuthResponse, OAuthCallbackRequest, OAuthLoginResponse, TokenResponse, UserResponse
from app.services.oauth_service import oauth_service

router = APIRouter(prefix="/oauth", tags=["OAuth Authentication"])


@router.get(
    "/{provider}/login",
    response_model=OAuthLoginResponse,
    summary="Initiate OAuth login",
    description="""
    Initiate OAuth authentication flow with the specified provider.
    
    This endpoint:
    - Generates a secure authorization URL for the OAuth provider
    - Returns the URL for frontend to redirect the user
    - Includes CSRF protection via state parameter
    
    Supported providers: google, github
    """,
)
async def oauth_login(provider: str):
    """
    Initiate OAuth login flow for the specified provider
    """
    try:
        authorization_url, state = oauth_service.get_authorization_url(provider)
        
        # In a production app, you'd want to store the state in Redis/database
        # associated with the user's session for validation in the callback
        
        return OAuthLoginResponse(authorization_url=authorization_url)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate OAuth login: {str(e)}"
        )


@router.post(
    "/{provider}/callback",
    response_model=AuthResponse,
    summary="Handle OAuth callback",
    description="""
    Handle OAuth callback and complete authentication.
    
    This endpoint:
    - Exchanges authorization code for access token
    - Retrieves user information from OAuth provider
    - Creates or updates user account
    - Returns JWT tokens for authenticated session
    
    Supported providers: google, github
    """,
)
async def oauth_callback(
    provider: str,
    callback_data: OAuthCallbackRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Handle OAuth callback and complete user authentication
    """
    try:
        # Exchange authorization code for access token
        access_token = await oauth_service.exchange_code_for_token(
            provider, callback_data.code
        )
        
        if not access_token:
            raise AuthenticationError("Failed to obtain access token")

        # Get user information from OAuth provider
        oauth_user = await oauth_service.get_user_info(provider, access_token)
        
        # Check if user already exists
        result = await db.execute(
            select(User).where(
                User.email == oauth_user.email,
                User.deleted_at.is_(None)
            )
        )
        user = result.scalar_one_or_none()

        if user:
            # Update existing user with OAuth information if needed
            if not user.avatar_url and oauth_user.avatar_url:
                user.avatar_url = oauth_user.avatar_url
            
            # Ensure user is active
            if not user.is_active:
                raise AuthenticationError("Account is deactivated")
                
        else:
            # Create new user from OAuth information
            user = User(
                email=oauth_user.email,
                first_name=oauth_user.first_name or "User",
                last_name=oauth_user.last_name or "",
                avatar_url=oauth_user.avatar_url,
                is_active=True,
                is_verified=True,  # OAuth users are considered verified
                oauth_provider=provider,
                oauth_id=oauth_user.id,
            )
            
            # Set a random password (user will use OAuth for login)
            import secrets
            user.set_password(secrets.token_urlsafe(32))
            
            db.add(user)
            await db.flush()  # Get the user ID
            
            # Create default user settings
            user_settings = UserSettings(user_id=user.id)
            db.add(user_settings)

        # Commit changes
        await db.commit()
        await db.refresh(user)

        # Generate JWT tokens
        access_token_jwt = create_access_token(data={"sub": str(user.id)})
        
        # Get device info from request
        device_info = f"{request.headers.get('user-agent', 'Unknown')} - {request.client.host if request.client else 'Unknown'}"
        refresh_token = create_refresh_token(str(user.id), db, device_info)

        # Prepare response
        user_response = UserResponse(
            id=str(user.id),
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            full_name=user.full_name,
            avatar_url=user.avatar_url,
            phone=user.phone,
            bio=user.bio,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )

        tokens = TokenResponse(
            access_token=access_token_jwt,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
        )

        return AuthResponse(user=user_response, tokens=tokens)

    except AuthenticationError:
        raise
    except ConflictError:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"OAuth authentication failed: {str(e)}"
        )


@router.get(
    "/providers",
    summary="Get available OAuth providers",
    description="Get list of configured OAuth providers",
)
async def get_oauth_providers():
    """
    Get list of available OAuth providers
    """
    providers = []
    
    if settings.google_client_id and settings.google_client_secret:
        providers.append({
            "name": "google",
            "display_name": "Google",
            "icon": "google",
        })
    
    if settings.github_client_id and settings.github_client_secret:
        providers.append({
            "name": "github", 
            "display_name": "GitHub",
            "icon": "github",
        })
    
    return {"providers": providers}
