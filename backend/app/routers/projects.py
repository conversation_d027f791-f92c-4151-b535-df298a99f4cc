"""
Project management API endpoints
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload

from app.core.cache import CacheKeyBuilder, cache_manager
from app.core.cache_invalidation import invalidate_project_cache
from app.core.exceptions import NotFoundError
from app.database import get_async_db
from app.dependencies import get_current_user
from app.models import ActivityLog, Client, Project, ProjectMilestone, User
from app.schemas.project import (MilestoneCreate, MilestoneResponse,
                                 MilestoneUpdate, PaginatedMilestonesResponse,
                                 PaginatedProjectsResponse, ProjectCreate,
                                 ProjectDetailResponse, ProjectPlanningRequest,
                                 ProjectPlanningResponse, ProjectResponse,
                                 ProjectUpdate, ProjectWithDetails)
from app.services.async_project_service import AsyncProjectService

router = APIRouter(prefix="/api/v1/projects", tags=["projects"])


def milestone_to_response(milestone: ProjectMilestone) -> MilestoneResponse:
    """Convert milestone model to response format with proper date handling"""
    milestone_dict = {
        "id": milestone.id,
        "project_id": milestone.project_id,
        "title": milestone.title,
        "description": milestone.description,
        "estimated_hours": milestone.estimated_hours,
        "payment_amount": milestone.payment_amount,
        "due_date": milestone.due_date.date() if milestone.due_date else None,
        "status": milestone.status,
        "sequence_number": milestone.sequence_number,
        "completed_at": milestone.completed_at,
        "created_at": milestone.created_at,
        "updated_at": milestone.updated_at,
        "is_overdue": milestone.is_overdue,
        "logged_hours": milestone.logged_hours,
    }
    return MilestoneResponse.model_validate(milestone_dict)


@router.get("/", response_model=PaginatedProjectsResponse)
async def list_projects(
    page: int = Query(1, ge=1),
    per_page: int = Query(25, ge=1, le=100),
    client_id: Optional[str] = Query(None),
    workspace_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    billing_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """List projects with filtering and pagination (cached)"""

    # Create cache key based on user and filters
    cache_key = CacheKeyBuilder.user_projects(
        str(current_user.id),
        page=page,
        per_page=per_page,
        client_id=client_id,
        workspace_id=workspace_id,
        status=status,
        billing_type=billing_type,
        search=search,
    )

    # Try to get from cache first
    cached_result = await cache_manager.get(cache_key)
    if cached_result:
        return PaginatedProjectsResponse(**cached_result)

    project_service = AsyncProjectService(db)
    try:
        result = await project_service.list_projects(
            user_id=current_user.id,
            page=page,
            per_page=per_page,
            client_id=client_id,
            workspace_id=workspace_id,
            status=status,
            billing_type=billing_type,
            search=search,
        )

        response_data = {
            "total": result["total"],
            "page": result["page"],
            "per_page": result["per_page"],
            "items": [
                ProjectResponse(
                    id=project.id,
                    client_id=project.client_id,
                    user_id=project.user_id,
                    workspace_id=project.workspace_id,
                    title=project.title,
                    description=project.description,
                    billing_type=project.billing_type,
                    total_budget=project.total_budget,
                    estimated_hours=project.estimated_hours,
                    hourly_rate=project.hourly_rate,
                    currency=project.currency,
                    status=project.status,
                    start_date=project.start_date,
                    end_date=project.end_date,
                    deadline=project.deadline,
                    is_billable=project.is_billable,
                    is_active=project.is_active,
                    created_at=project.created_at,
                    updated_at=project.updated_at,
                    # Set safe default values for computed fields
                    total_logged_hours=Decimal("0.00"),
                    total_billable_amount=Decimal("0.00"),
                    completion_percentage=0.0,
                ).model_dump()
                for project in result["items"]
            ],
        }

        # Cache the result for 10 minutes
        await cache_manager.set(cache_key, response_data, ttl=600)

        return PaginatedProjectsResponse(**response_data)
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e),
        )


@router.post("/", response_model=ProjectResponse, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new project"""

    project_service = AsyncProjectService(db)
    try:
        project = await project_service.create_project(current_user.id, project_data)
        # Manually construct response to avoid lazy loading issues
        # Refresh the project to get the latest data without lazy loading issues
        await db.refresh(project)

        return ProjectResponse(
            id=project.id,
            client_id=project.client_id,
            user_id=project.user_id,
            workspace_id=project.workspace_id,
            title=project.title,
            description=project.description,
            billing_type=project.billing_type,
            total_budget=project.total_budget,
            estimated_hours=project.estimated_hours,
            hourly_rate=project.hourly_rate,
            currency=project.currency,
            status=project.status,
            start_date=project.start_date,
            end_date=project.end_date,
            deadline=project.deadline,
            is_billable=project.is_billable,
            is_active=project.is_active,
            # Include client information fields (these are direct columns, not relationships)
            client=project.client,
            contact_person=project.contact_person,
            client_email=project.client_email,
            client_phone=project.client_phone,
            client_address=project.client_address,
            client_industry=project.client_industry,
            client_company_size=project.client_company_size,
            client_priority=project.client_priority,
            client_status=project.client_status,
            client_notes=project.client_notes,
            created_at=project.created_at,
            updated_at=project.updated_at,
            # Set safe default values for computed fields
            total_logged_hours=Decimal("0.00"),
            total_billable_amount=Decimal("0.00"),
            completion_percentage=0.0,
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message,
        )


@router.get("/{project_id}", response_model=ProjectDetailResponse)
async def get_project(
    project_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get project details with client, milestones, and time entries (cached)"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    # Try to get from cache first
    cache_key = CacheKeyBuilder.project_details(project_id)
    cached_project = await cache_manager.get(cache_key)
    if cached_project:
        # Ensure workspace_id is included in cached data
        if 'workspace_id' not in cached_project:
            # If workspace_id is missing, we need to fetch fresh data
            pass
        else:
            return ProjectDetailResponse(**cached_project)

    project_service = AsyncProjectService(db)
    try:
        project_data = await project_service.get_project_with_details(
            current_user.id, project_uuid
        )

        # Extract data from the service response (already dictionaries)
        project = project_data["project"]
        client = project_data["client"]
        milestones = project_data["milestones"]

        # Create response data with flattened project fields
        response_data = {
            # Project fields (already converted to proper types in service)
            **project,
            # Related data
            "client": {
                "id": str(client["id"]) if client else None,
                "name": client["name"] if client else None,
                "email": client["email"] if client else None,
                "company": client["company"] if client else None,
            }
            if client
            else None,
            "milestones": [
                {
                    "id": str(milestone["id"]),
                    "project_id": str(milestone["project_id"]),
                    "title": milestone["title"],
                    "description": milestone["description"],
                    "estimated_hours": milestone["estimated_hours"],
                    "payment_amount": milestone["payment_amount"],
                    "due_date": milestone["due_date"].date() if milestone["due_date"] else None,
                    "completed_at": milestone["completed_at"],
                    "status": milestone["status"],
                    "sequence_number": milestone["sequence_number"],
                    "is_client_visible": milestone["is_client_visible"],
                    "billing_status": milestone["billing_status"],
                    "created_at": milestone["created_at"],
                    "updated_at": milestone["updated_at"],
                }
                for milestone in milestones
            ]
            if milestones
            else [],
            "time_entries": [],  # Empty for now, can be populated if needed
            "time_entries_count": 0,
        }

        # Debug: Print response_data to see what's missing
        print(f"DEBUG: response_data keys: {list(response_data.keys())}")
        print(f"DEBUG: workspace_id in response_data: {'workspace_id' in response_data}")

        # Cache the project details for 15 minutes
        await cache_manager.set(cache_key, response_data, ttl=900)

        return ProjectDetailResponse(**response_data)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: str,
    project_data: ProjectUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Update project details with cache invalidation"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    project_service = AsyncProjectService(db)
    try:
        project = await project_service.update_project(
            current_user.id, project_uuid, project_data
        )

        # Invalidate project-related caches
        await invalidate_project_cache(project_id, str(current_user.id))

        # Manually construct response to avoid lazy loading issues
        return ProjectResponse(
            id=project.id,
            client_id=project.client_id,
            user_id=project.user_id,
            workspace_id=project.workspace_id,
            title=project.title,
            description=project.description,
            billing_type=project.billing_type,
            total_budget=project.total_budget,
            estimated_hours=project.estimated_hours,
            hourly_rate=project.hourly_rate,
            currency=project.currency,
            status=project.status,
            start_date=project.start_date,
            end_date=project.end_date,
            deadline=project.deadline,
            is_billable=project.is_billable,
            is_active=project.is_active,
            # Include client information fields - use same approach as contact_person
            client=getattr(project, 'client_name', None),
            client_name=getattr(project, 'client_name', None),
            contact_person=project.__dict__.get('contact_person', None),
            client_email=project.__dict__.get('client_email', None),
            client_phone=project.__dict__.get('client_phone', None),
            client_address=project.__dict__.get('client_address', None),
            client_industry=project.__dict__.get('client_industry', None),
            client_company_size=project.__dict__.get('client_company_size', None),
            client_priority=project.__dict__.get('client_priority', None),
            client_status=project.__dict__.get('client_status', None),
            client_notes=project.__dict__.get('client_notes', None),
            created_at=project.created_at,
            updated_at=project.updated_at,
            # Set safe default values for computed fields
            total_logged_hours=Decimal("0.00"),
            total_billable_amount=Decimal("0.00"),
            completion_percentage=0.0,
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project(
    project_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Soft delete a project with cache invalidation"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    project_service = AsyncProjectService(db)
    try:
        await project_service.delete_project(current_user.id, project_uuid)

        # Invalidate project-related caches
        await invalidate_project_cache(project_id, str(current_user.id))

    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


# Milestone management endpoints
@router.get("/{project_id}/milestones", response_model=PaginatedMilestonesResponse)
async def list_project_milestones(
    project_id: str,
    page: int = Query(1, ge=1),
    per_page: int = Query(25, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """List milestones for a project"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    # Verify project access
    project_service = AsyncProjectService(db)
    try:
        project = await project_service.get_project(current_user.id, project_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)

    # Query milestones
    count_query = select(func.count(ProjectMilestone.id)).where(
        and_(
            ProjectMilestone.project_id == project_uuid,
            ProjectMilestone.deleted_at.is_(None),
        )
    )
    total_result = await db.execute(count_query)
    total = total_result.scalar()

    offset = (page - 1) * per_page
    milestones_query = (
        select(ProjectMilestone)
        .where(
            and_(
                ProjectMilestone.project_id == project_uuid,
                ProjectMilestone.deleted_at.is_(None),
            )
        )
        .order_by(ProjectMilestone.sequence_number)
        .offset(offset)
        .limit(per_page)
    )
    milestones_result = await db.execute(milestones_query)
    milestones = milestones_result.scalars().all()

    return PaginatedMilestonesResponse(
        total=total,
        page=page,
        per_page=per_page,
        items=[milestone_to_response(milestone) for milestone in milestones],
    )


@router.post(
    "/{project_id}/milestones",
    response_model=MilestoneResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_milestone(
    project_id: str,
    milestone_data: MilestoneCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new milestone for a project"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    # Verify project access
    project_service = AsyncProjectService(db)
    try:
        project = await project_service.get_project(current_user.id, project_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)

    # Create milestone
    milestone = ProjectMilestone(**milestone_data.model_dump(), project_id=project_uuid)

    db.add(milestone)
    await db.flush()

    # Log activity
    await project_service.log_activity(
        current_user.id,
        "milestone",
        str(milestone.id),
        "create",
        f"Created milestone '{milestone.title}' for project '{project.title}'",
    )

    await db.commit()
    await db.refresh(milestone)

    return milestone_to_response(milestone)


@router.get("/{project_id}/milestones/{milestone_id}", response_model=MilestoneResponse)
async def get_milestone(
    project_id: str,
    milestone_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific milestone"""

    try:
        project_uuid = uuid.UUID(project_id)
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
        )

    # Verify project access
    project_service = AsyncProjectService(db)
    try:
        project = await project_service.get_project(current_user.id, project_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)

    # Get milestone
    milestone_query = select(ProjectMilestone).where(
        and_(
            ProjectMilestone.id == milestone_uuid,
            ProjectMilestone.project_id == project_uuid,
            ProjectMilestone.deleted_at.is_(None),
        )
    )
    result = await db.execute(milestone_query)
    milestone = result.scalar_one_or_none()

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    return milestone_to_response(milestone)


@router.put("/{project_id}/milestones/{milestone_id}", response_model=MilestoneResponse)
async def update_milestone(
    project_id: str,
    milestone_id: str,
    milestone_data: MilestoneUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Update a specific milestone"""

    try:
        project_uuid = uuid.UUID(project_id)
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
        )

    # Verify project access
    project_service = AsyncProjectService(db)
    try:
        project = await project_service.get_project(current_user.id, project_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)

    # Get milestone
    milestone_query = select(ProjectMilestone).where(
        and_(
            ProjectMilestone.id == milestone_uuid,
            ProjectMilestone.project_id == project_uuid,
            ProjectMilestone.deleted_at.is_(None),
        )
    )
    result = await db.execute(milestone_query)
    milestone = result.scalar_one_or_none()

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    # Update milestone
    update_data = milestone_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(milestone, field, value)

    # Log activity
    await project_service.log_activity(
        current_user.id,
        "milestone",
        str(milestone.id),
        "update",
        f"Updated milestone '{milestone.title}' for project '{project.title}'",
    )

    await db.commit()
    await db.refresh(milestone)

    return milestone_to_response(milestone)


@router.delete(
    "/{project_id}/milestones/{milestone_id}", status_code=status.HTTP_204_NO_CONTENT
)
async def delete_milestone(
    project_id: str,
    milestone_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a specific milestone (soft delete)"""

    try:
        project_uuid = uuid.UUID(project_id)
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
        )

    # Verify project access
    project_service = AsyncProjectService(db)
    try:
        project = await project_service.get_project(current_user.id, project_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)

    # Get milestone
    milestone_query = select(ProjectMilestone).where(
        and_(
            ProjectMilestone.id == milestone_uuid,
            ProjectMilestone.project_id == project_uuid,
            ProjectMilestone.deleted_at.is_(None),
        )
    )
    result = await db.execute(milestone_query)
    milestone = result.scalar_one_or_none()

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    # Soft delete milestone
    milestone.deleted_at = datetime.utcnow()

    # Log activity
    await project_service.log_activity(
        current_user.id,
        "milestone",
        str(milestone.id),
        "delete",
        f"Deleted milestone '{milestone.title}' from project '{project.title}'",
    )

    await db.commit()


@router.get("/{project_id}/overview")
async def get_project_overview(
    project_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get project overview with statistics, milestones, and recent activity"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    project_service = AsyncProjectService(db)
    try:
        return await project_service.get_project_overview(current_user.id, project_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


@router.get("/{project_id}/metrics")
async def get_project_metrics(
    project_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get project metrics and performance data"""

    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    project_service = AsyncProjectService(db)
    try:
        return await project_service.get_project_metrics(current_user.id, project_uuid)
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


# Project planning calculator endpoint
@router.post("/plan", response_model=ProjectPlanningResponse)
async def calculate_project_plan(
    planning_request: ProjectPlanningRequest,
    current_user: User = Depends(get_current_user),
):
    """Calculate comprehensive project planning with templates and intelligent analysis"""
    from app.core.project_planning_service import ProjectPlanningService

    planning_service = ProjectPlanningService()

    # Convert deadline to string if provided
    deadline_str = (
        planning_request.deadline.isoformat() if planning_request.deadline else None
    )

    result = planning_service.calculate_project_plan(
        total_budget=planning_request.total_budget,
        internal_hourly_rate=planning_request.internal_hourly_rate,
        milestone_count=planning_request.milestone_count or 3,
        project_template=planning_request.project_template or "custom",
        planning_mode=planning_request.planning_mode,
        estimated_total_hours=planning_request.estimated_total_hours,
        deadline=deadline_str,
    )

    return ProjectPlanningResponse(**result)


@router.get("/templates")
async def get_project_templates(
    current_user: User = Depends(get_current_user),
):
    """Get available project templates for planning"""
    from app.core.project_planning_service import ProjectPlanningService

    planning_service = ProjectPlanningService()
    templates = planning_service.get_available_templates()

    return {"templates": templates, "total_count": len(templates)}


@router.get("/{project_id}/time-entries")
async def get_project_time_entries(
    project_id: str,
    page: int = Query(1, ge=1),
    per_page: int = Query(25, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get time entries for a project"""
    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    project_service = AsyncProjectService(db)
    try:
        return await project_service.get_project_time_entries(
            current_user.id, project_uuid, page, per_page
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)


@router.get("/{project_id}/analytics")
async def get_project_analytics(
    project_id: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get analytics for a project"""
    try:
        project_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid project ID format"
        )

    project_service = AsyncProjectService(db)
    try:
        return await project_service.get_project_analytics(
            current_user.id, project_uuid
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=e.message)
