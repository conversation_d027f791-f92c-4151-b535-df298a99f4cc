"""
Project milestone management API endpoints
"""

import uuid
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import and_
from sqlalchemy.orm import Session

from app.database import get_db
from app.dependencies import get_current_user
from app.models import ActivityLog, Project, ProjectMilestone, User
from app.schemas.project import MilestoneResponse, MilestoneUpdate

router = APIRouter(prefix="/api/v1/milestones", tags=["milestones"])


def milestone_to_response(milestone: ProjectMilestone) -> MilestoneResponse:
    """Convert milestone model to response format with proper date handling"""
    milestone_dict = {
        "id": milestone.id,
        "project_id": milestone.project_id,
        "title": milestone.title,
        "description": milestone.description,
        "estimated_hours": milestone.estimated_hours,
        "payment_amount": milestone.payment_amount,
        "due_date": milestone.due_date.date() if milestone.due_date else None,
        "status": milestone.status,
        "sequence_number": milestone.sequence_number,
        "completed_at": milestone.completed_at,
        "created_at": milestone.created_at,
        "updated_at": milestone.updated_at,
        "is_overdue": milestone.is_overdue,
        "logged_hours": milestone.logged_hours,
    }
    return MilestoneResponse.model_validate(milestone_dict)


def log_activity(
    db: Session,
    user_id: uuid.UUID,
    entity_type: str,
    entity_id: str,
    action: str,
    details: str = None,
):
    """Helper function to log activities"""
    activity = ActivityLog(
        user_id=user_id,
        entity_type=entity_type,
        entity_id=str(entity_id),
        action=action,
        details=details,
    )
    db.add(activity)


@router.get("/{milestone_id}", response_model=MilestoneResponse)
async def get_milestone(
    milestone_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get milestone details"""

    try:
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid milestone ID format",
        )

    # Get milestone with project to verify access
    milestone = (
        db.query(ProjectMilestone)
        .join(Project)
        .filter(
            and_(
                ProjectMilestone.id == milestone_uuid,
                Project.user_id == current_user.id,
                ProjectMilestone.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    return milestone_to_response(milestone)


@router.put("/{milestone_id}", response_model=MilestoneResponse)
async def update_milestone(
    milestone_id: str,
    milestone_data: MilestoneUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update milestone details"""

    try:
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid milestone ID format",
        )

    # Get milestone with project to verify access
    milestone = (
        db.query(ProjectMilestone)
        .join(Project)
        .filter(
            and_(
                ProjectMilestone.id == milestone_uuid,
                Project.user_id == current_user.id,
                ProjectMilestone.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    # Store old status for activity logging
    old_status = milestone.status

    # Update fields
    update_data = milestone_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(milestone, field, value)

    # Handle status changes
    if milestone.status == "completed" and old_status != "completed":
        milestone.mark_completed()
        activity_detail = f"Completed milestone '{milestone.title}'"
    elif milestone.status != "completed" and old_status == "completed":
        milestone.completed_at = None
        activity_detail = f"Reopened milestone '{milestone.title}'"
    else:
        activity_detail = f"Updated milestone '{milestone.title}'"

    # Log activity
    log_activity(
        db, current_user.id, "milestone", str(milestone.id), "update", activity_detail
    )

    db.commit()
    db.refresh(milestone)

    return milestone_to_response(milestone)


@router.delete("/{milestone_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_milestone(
    milestone_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Soft delete a milestone"""

    try:
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid milestone ID format",
        )

    # Get milestone with project to verify access
    milestone = (
        db.query(ProjectMilestone)
        .join(Project)
        .filter(
            and_(
                ProjectMilestone.id == milestone_uuid,
                Project.user_id == current_user.id,
                ProjectMilestone.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    # Soft delete
    milestone.soft_delete()

    # Log activity
    log_activity(
        db,
        current_user.id,
        "milestone",
        str(milestone.id),
        "delete",
        f"Deleted milestone '{milestone.title}'",
    )

    db.commit()


@router.post("/{milestone_id}/complete", response_model=MilestoneResponse)
async def complete_milestone(
    milestone_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Mark milestone as completed"""

    try:
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid milestone ID format",
        )

    # Get milestone with project to verify access
    milestone = (
        db.query(ProjectMilestone)
        .join(Project)
        .filter(
            and_(
                ProjectMilestone.id == milestone_uuid,
                Project.user_id == current_user.id,
                ProjectMilestone.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    if milestone.status == "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Milestone is already completed",
        )

    # Mark as completed
    milestone.mark_completed()

    # Log activity
    log_activity(
        db,
        current_user.id,
        "milestone",
        str(milestone.id),
        "complete",
        f"Completed milestone '{milestone.title}'",
    )

    db.commit()
    db.refresh(milestone)

    return milestone_to_response(milestone)


@router.post("/{milestone_id}/reopen", response_model=MilestoneResponse)
async def reopen_milestone(
    milestone_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Reopen a completed milestone"""

    try:
        milestone_uuid = uuid.UUID(milestone_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid milestone ID format",
        )

    # Get milestone with project to verify access
    milestone = (
        db.query(ProjectMilestone)
        .join(Project)
        .filter(
            and_(
                ProjectMilestone.id == milestone_uuid,
                Project.user_id == current_user.id,
                ProjectMilestone.deleted_at.is_(None),
                Project.deleted_at.is_(None),
            )
        )
        .first()
    )

    if not milestone:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Milestone not found"
        )

    if milestone.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Milestone is not completed"
        )

    # Reopen milestone
    milestone.status = "in_progress"
    milestone.completed_at = None

    # Log activity
    log_activity(
        db,
        current_user.id,
        "milestone",
        str(milestone.id),
        "reopen",
        f"Reopened milestone '{milestone.title}'",
    )

    db.commit()
    db.refresh(milestone)

    return milestone_to_response(milestone)
