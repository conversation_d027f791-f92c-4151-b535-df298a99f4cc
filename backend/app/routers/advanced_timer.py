"""
Advanced Timer API endpoints for Day 9 features
Includes smart timer management, conflict resolution, and AI suggestions
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..core.advanced_timer_service import AdvancedTimerService
from ..core.analytics_service import TimeAnalyticsService
from ..database import get_db
from ..dependencies import get_current_user
from ..models.user import User
from ..schemas.timer import (ProductivitySuggestionResponse, SmartTimerRequest,
                             SmartTimerResponse, TimerConflictResponse,
                             WebhookSetupRequest)

router = APIRouter(prefix="/api/v1/timer/advanced", tags=["Advanced Timer"])


@router.post("/start-smart", response_model=SmartTimerResponse)
async def start_smart_timer(
    request: SmartTimerRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Start a smart timer with conflict detection and AI suggestions"""

    timer_service = AdvancedTimerService(db)

    try:
        result = await timer_service.start_smart_timer(
            user_id=current_user.id,
            project_id=request.project_id,
            description=request.description,
            device_id=request.device_id,
            auto_resolve_conflicts=request.auto_resolve_conflicts,
            context_data=request.context_data,
        )

        return SmartTimerResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/conflicts", response_model=List[TimerConflictResponse])
async def detect_timer_conflicts(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    """Detect active timer conflicts across devices"""

    timer_service = AdvancedTimerService(db)

    try:
        conflicts = await timer_service.detect_active_conflicts(
            user_id=current_user.id, device_id="api_check"
        )

        return [TimerConflictResponse(**conflict) for conflict in conflicts]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/conflicts/resolve")
async def resolve_timer_conflicts(
    conflict_ids: List[str],
    resolution_strategy: str = "intelligent",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Resolve timer conflicts using AI or manual strategy"""

    timer_service = AdvancedTimerService(db)

    try:
        # Get conflicts by IDs
        conflicts = []
        for conflict_id in conflict_ids:
            # Parse conflict ID to get timer IDs
            timer_ids = conflict_id.split("_")
            if len(timer_ids) == 2:
                conflicts.append({"conflict_id": conflict_id, "timer_id": timer_ids[0]})

        if resolution_strategy == "intelligent":
            result = await timer_service.resolve_conflicts_intelligently(conflicts)
        else:
            # Manual resolution logic here
            result = {"resolved": False, "message": "Manual resolution not implemented"}

        return result

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/suggestions/productivity", response_model=List[ProductivitySuggestionResponse]
)
async def get_productivity_suggestions(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    """Get real-time productivity suggestions during timer sessions"""

    timer_service = AdvancedTimerService(db)

    try:
        suggestions = await timer_service.generate_productivity_suggestions(
            current_user.id
        )
        return [
            ProductivitySuggestionResponse(**suggestion) for suggestion in suggestions
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/suggestions/ai-time", response_model=List[Dict])
async def get_ai_time_suggestions(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    """Get AI-powered time entry suggestions based on patterns"""

    analytics_service = TimeAnalyticsService(db)

    try:
        suggestions = await analytics_service.generate_ai_time_suggestions(
            current_user.id
        )
        return suggestions

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analytics/advanced", response_model=Dict)
async def get_advanced_analytics(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get advanced productivity analytics with AI insights"""

    analytics_service = TimeAnalyticsService(db)

    try:
        insights = await analytics_service.get_advanced_productivity_insights(
            user_id=current_user.id, days=days
        )
        return insights

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/webhooks/setup")
async def setup_timer_webhooks(
    request: WebhookSetupRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Setup webhook endpoints for timer events"""

    timer_service = AdvancedTimerService(db)

    try:
        success = await timer_service.setup_timer_webhooks(
            webhook_url=request.webhook_url, events=request.events
        )

        if success:
            return {"status": "success", "message": "Webhook configured successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to configure webhook")

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/session/predict-duration")
async def predict_session_duration(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Predict likely session duration for a project"""

    timer_service = AdvancedTimerService(db)

    try:
        predicted_duration = await timer_service._predict_session_duration(
            user_id=current_user.id, project_id=project_id
        )

        return {
            "project_id": str(project_id),
            "predicted_duration_hours": predicted_duration,
            "confidence": "medium",  # Could be enhanced with actual confidence calculation
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/patterns/analysis")
async def analyze_timer_patterns(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    """Analyze user's timer patterns for insights"""

    analytics_service = TimeAnalyticsService(db)

    try:
        # Get timer conflicts analysis
        conflicts = await analytics_service.detect_timer_conflicts(current_user.id)

        # Get AI suggestions
        ai_suggestions = await analytics_service.generate_ai_time_suggestions(
            current_user.id
        )

        return {
            "conflicts_detected": len(conflicts),
            "conflict_details": conflicts,
            "ai_suggestions": ai_suggestions,
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
