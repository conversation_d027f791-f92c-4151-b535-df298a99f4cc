"""
Workspace management API endpoints
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.exceptions import NotFoundError, ValidationError
from app.database import get_async_db
from app.dependencies import get_current_user
from app.models import Project, User, Workspace
from app.schemas.workspace import (
    PaginatedWorkspacesResponse,
    SetActiveWorkspaceRequest,
    WorkspaceCreate,
    WorkspaceResponse,
    WorkspaceStatsResponse,
    WorkspaceUpdate,
    WorkspaceWithProjects,
)

router = APIRouter(prefix="/api/v1/workspaces", tags=["workspaces"])


@router.get("/", response_model=PaginatedWorkspacesResponse)
async def list_workspaces(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term for workspace name or description"),
    status: Optional[str] = Query(None, description="Filter by status: active, inactive"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Get list of user's workspaces with pagination and filtering"""
    
    # Build query
    query = select(Workspace).where(
        and_(
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None)
        )
    )
    
    # Apply search filter
    if search:
        search_term = f"%{search}%"
        query = query.where(
            Workspace.name.ilike(search_term) | 
            Workspace.description.ilike(search_term)
        )
    
    # Apply status filter
    if status:
        if status.lower() == "active":
            query = query.where(Workspace.is_active == True)
        elif status.lower() == "inactive":
            query = query.where(Workspace.is_active == False)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # Apply pagination and ordering
    query = query.order_by(desc(Workspace.created_at)).offset(skip).limit(limit)
    
    # Execute query
    result = await db.execute(query)
    workspaces = result.scalars().all()

    # Calculate project counts for each workspace
    workspace_responses = []
    for workspace in workspaces:
        # Count projects for this workspace
        project_count_query = select(func.count(Project.id)).where(
            and_(
                Project.workspace_id == workspace.id,
                Project.deleted_at.is_(None)
            )
        )
        project_count_result = await db.execute(project_count_query)
        project_count = project_count_result.scalar() or 0

        # Set the project count on the workspace object
        workspace.project_count = project_count
        workspace_responses.append(WorkspaceResponse.model_validate(workspace))

    return PaginatedWorkspacesResponse(
        data=workspace_responses,
        total=total,
        skip=skip,
        limit=limit,
    )


@router.post("/", response_model=WorkspaceResponse)
async def create_workspace(
    workspace_data: WorkspaceCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Create a new workspace"""
    
    # Check if this is the first workspace (make it default)
    existing_count_query = select(func.count()).where(
        and_(
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None)
        )
    )
    count_result = await db.execute(existing_count_query)
    existing_count = count_result.scalar()
    
    # If this is the first workspace or explicitly marked as default, make it default
    is_default = workspace_data.is_default or existing_count == 0
    
    # If setting as default, unset other default workspaces
    if is_default:
        update_query = select(Workspace).where(
            and_(
                Workspace.user_id == current_user.id,
                Workspace.is_default == True,
                Workspace.deleted_at.is_(None)
            )
        )
        result = await db.execute(update_query)
        existing_defaults = result.scalars().all()
        
        for workspace in existing_defaults:
            workspace.is_default = False
    
    # Create new workspace
    new_workspace = Workspace(
        user_id=current_user.id,
        name=workspace_data.name,
        description=workspace_data.description,
        is_default=is_default,
    )
    
    db.add(new_workspace)
    await db.commit()
    await db.refresh(new_workspace)
    
    # If this is the first workspace, set it as active workspace for user
    if existing_count == 0:
        current_user.active_workspace_id = new_workspace.id
        await db.commit()

    # Set project count for new workspace (will be 0)
    new_workspace.project_count = 0

    return WorkspaceResponse.model_validate(new_workspace)


@router.get("/{workspace_id}", response_model=WorkspaceWithProjects)
async def get_workspace(
    workspace_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific workspace with its projects"""
    
    query = select(Workspace).options(
        selectinload(Workspace.projects)
    ).where(
        and_(
            Workspace.id == workspace_id,
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None)
        )
    )
    
    result = await db.execute(query)
    workspace = result.scalar_one_or_none()
    
    if not workspace:
        raise NotFoundError("Workspace not found")

    # Calculate project count for the workspace
    project_count_query = select(func.count(Project.id)).where(
        and_(
            Project.workspace_id == workspace.id,
            Project.deleted_at.is_(None)
        )
    )
    project_count_result = await db.execute(project_count_query)
    project_count = project_count_result.scalar() or 0

    # Convert workspace and projects to dictionary format for Pydantic validation
    workspace_dict = {
        "id": workspace.id,
        "user_id": workspace.user_id,
        "name": workspace.name,
        "description": workspace.description,
        "is_active": workspace.is_active,
        "is_default": workspace.is_default,
        "status": "active" if workspace.is_active else "inactive",  # Compute status from is_active
        "created_at": workspace.created_at,
        "updated_at": workspace.updated_at,
        "project_count": project_count,
        "projects": [
            {
                "id": project.id,
                "title": project.title,
                "description": project.description,
                "client_id": project.client_id,
                "user_id": project.user_id,
                "workspace_id": project.workspace_id,
                "billing_type": project.billing_type,
                "total_budget": project.total_budget,
                "estimated_hours": project.estimated_hours,
                "hourly_rate": project.hourly_rate,
                "currency": project.currency,
                "status": project.status,
                "start_date": project.start_date,
                "end_date": project.end_date,
                "deadline": project.deadline,
                "is_billable": project.is_billable,
                "is_active": project.is_active,
                "created_at": project.created_at,
                "updated_at": project.updated_at,
            }
            for project in workspace.projects
        ]
    }

    return WorkspaceWithProjects.model_validate(workspace_dict)


@router.put("/{workspace_id}", response_model=WorkspaceResponse)
async def update_workspace(
    workspace_id: UUID,
    workspace_data: WorkspaceUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Update a workspace"""
    
    query = select(Workspace).where(
        and_(
            Workspace.id == workspace_id,
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None)
        )
    )
    
    result = await db.execute(query)
    workspace = result.scalar_one_or_none()
    
    if not workspace:
        raise NotFoundError("Workspace not found")
    
    # Update fields
    update_data = workspace_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(workspace, field, value)
    
    await db.commit()
    await db.refresh(workspace)

    # Calculate project count for the updated workspace
    project_count_query = select(func.count(Project.id)).where(
        and_(
            Project.workspace_id == workspace.id,
            Project.deleted_at.is_(None)
        )
    )
    project_count_result = await db.execute(project_count_query)
    project_count = project_count_result.scalar() or 0

    # Set the project count on the workspace object
    workspace.project_count = project_count

    return WorkspaceResponse.model_validate(workspace)


@router.delete("/{workspace_id}")
async def delete_workspace(
    workspace_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Delete a workspace (soft delete)"""
    
    query = select(Workspace).where(
        and_(
            Workspace.id == workspace_id,
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None)
        )
    )
    
    result = await db.execute(query)
    workspace = result.scalar_one_or_none()
    
    if not workspace:
        raise NotFoundError("Workspace not found")
    
    # Check if this is the default workspace
    if workspace.is_default:
        # Check if there are other workspaces to make default
        other_workspaces_query = select(Workspace).where(
            and_(
                Workspace.user_id == current_user.id,
                Workspace.id != workspace_id,
                Workspace.deleted_at.is_(None)
            )
        ).limit(1)
        
        other_result = await db.execute(other_workspaces_query)
        other_workspace = other_result.scalar_one_or_none()
        
        if other_workspace:
            other_workspace.is_default = True
            current_user.active_workspace_id = other_workspace.id
        else:
            current_user.active_workspace_id = None
    
    # Check if this is the active workspace
    if current_user.active_workspace_id == workspace_id:
        # Find another workspace to set as active
        other_workspaces_query = select(Workspace).where(
            and_(
                Workspace.user_id == current_user.id,
                Workspace.id != workspace_id,
                Workspace.deleted_at.is_(None)
            )
        ).limit(1)
        
        other_result = await db.execute(other_workspaces_query)
        other_workspace = other_result.scalar_one_or_none()
        
        current_user.active_workspace_id = other_workspace.id if other_workspace else None
    
    # Soft delete the workspace
    workspace.soft_delete()
    
    await db.commit()
    
    return {"message": "Workspace deleted successfully"}


@router.post("/set-active", response_model=WorkspaceResponse)
async def set_active_workspace(
    request: SetActiveWorkspaceRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Set the active workspace for the current user"""
    
    query = select(Workspace).where(
        and_(
            Workspace.id == request.workspace_id,
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None),
            Workspace.is_active == True
        )
    )
    
    result = await db.execute(query)
    workspace = result.scalar_one_or_none()
    
    if not workspace:
        raise NotFoundError("Workspace not found or inactive")
    
    # Update user's active workspace
    current_user.active_workspace_id = workspace.id
    await db.commit()

    # Calculate project count for the workspace
    project_count_query = select(func.count(Project.id)).where(
        and_(
            Project.workspace_id == workspace.id,
            Project.deleted_at.is_(None)
        )
    )
    project_count_result = await db.execute(project_count_query)
    project_count = project_count_result.scalar() or 0

    # Set the project count on the workspace object
    workspace.project_count = project_count

    return WorkspaceResponse.model_validate(workspace)


@router.get("/stats/overview", response_model=WorkspaceStatsResponse)
async def get_workspace_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Get workspace statistics for the current user"""
    
    # Get workspace counts
    workspace_query = select(
        func.count().label("total"),
        func.sum(func.cast(Workspace.is_active, db.dialect.name == 'postgresql' and 'INTEGER' or 'SIGNED')).label("active"),
    ).where(
        and_(
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None)
        )
    )
    
    workspace_result = await db.execute(workspace_query)
    workspace_stats = workspace_result.first()
    
    total_workspaces = workspace_stats.total or 0
    active_workspaces = workspace_stats.active or 0
    inactive_workspaces = total_workspaces - active_workspaces
    
    # Get project counts
    project_query = select(
        func.count().label("total"),
        func.sum(func.case((Project.status == 'active', 1), else_=0)).label("active"),
    ).join(Workspace).where(
        and_(
            Workspace.user_id == current_user.id,
            Workspace.deleted_at.is_(None),
            Project.deleted_at.is_(None)
        )
    )
    
    project_result = await db.execute(project_query)
    project_stats = project_result.first()
    
    total_projects = project_stats.total or 0
    active_projects = project_stats.active or 0
    
    # Get default workspace
    default_workspace_query = select(Workspace.id).where(
        and_(
            Workspace.user_id == current_user.id,
            Workspace.is_default == True,
            Workspace.deleted_at.is_(None)
        )
    )
    
    default_result = await db.execute(default_workspace_query)
    default_workspace_id = default_result.scalar_one_or_none()
    
    return WorkspaceStatsResponse(
        total_workspaces=total_workspaces,
        active_workspaces=active_workspaces,
        inactive_workspaces=inactive_workspaces,
        total_projects=total_projects,
        active_projects=active_projects,
        default_workspace_id=default_workspace_id,
    )
