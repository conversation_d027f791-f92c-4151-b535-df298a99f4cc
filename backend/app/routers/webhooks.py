"""
Webhook handlers for payment processing and external integrations
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Optional

from fastapi import APIRouter, Depends, Header, HTTPException, Request, status
from sqlalchemy.orm import Session

from app.core.payment_service import (PaymentProcessingService,
                                      PaystackService, get_paystack_service)
from app.core.payment_service_enhanced import EnhancedPaymentService
from app.core.webhook_processor import WebhookProcessingError, WebhookProcessor
from app.database import get_db

router = APIRouter(prefix="/webhooks", tags=["webhooks"])
logger = logging.getLogger(__name__)


@router.post("/paystack", status_code=status.HTTP_200_OK)
async def paystack_webhook(
    request: Request,
    db: Session = Depends(get_db),
    x_paystack_signature: Optional[str] = Header(None),
):
    """Handle Paystack payment webhooks with enhanced error handling."""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()

    try:
        # Get raw body
        body = await request.body()
        payload = body.decode("utf-8")

        logger.info(
            f"Received Paystack webhook - Request ID: {request_id}, "
            f"Signature present: {bool(x_paystack_signature)}"
        )

        # Process webhook using enhanced processor
        webhook_processor = WebhookProcessor(db)
        result = await webhook_processor.process_paystack_webhook(
            payload=payload, signature=x_paystack_signature, request_id=request_id
        )

        processing_time = (datetime.utcnow() - start_time).total_seconds()
        logger.info(
            f"Paystack webhook processed successfully - Request ID: {request_id}, "
            f"Processing time: {processing_time:.3f}s"
        )

        return result

    except WebhookProcessingError as e:
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        logger.warning(
            f"Webhook processing error - Request ID: {request_id}, "
            f"Error: {e.error_code}, Message: {str(e)}, "
            f"Processing time: {processing_time:.3f}s"
        )

        # Return appropriate HTTP status based on error type
        if e.error_code == "MISSING_SIGNATURE" or e.retry_after:
            raise HTTPException(
                status_code=503,
                detail=str(e),
                headers={"Retry-After": str(e.retry_after)},
            )
        elif e.error_code in [
            "INVALID_SIGNATURE",
            "INVALID_JSON",
            "MISSING_EVENT_TYPE",
        ]:
            raise HTTPException(status_code=400, detail=str(e))
        else:
            # Return 200 to prevent unnecessary retries for non-retryable errors
            return {"status": "error", "error_code": e.error_code, "message": str(e)}

    except HTTPException:
        # Re-raise HTTP exceptions (like 400 for invalid signature)
        raise
    except Exception as e:
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        logger.error(
            f"Unexpected error in Paystack webhook - Request ID: {request_id}, "
            f"Error: {str(e)}, Processing time: {processing_time:.3f}s",
            exc_info=True,
        )

        # Return 200 to prevent Paystack from retrying on unexpected errors
        return {
            "status": "error",
            "message": "Internal server error",
            "request_id": request_id,
        }


@router.post("/dpo", status_code=status.HTTP_200_OK)
async def dpo_webhook(request: Request, db: Session = Depends(get_db)):
    """Handle DPO payment webhooks with enhanced error handling."""
    request_id = str(uuid.uuid4())
    start_time = datetime.utcnow()

    try:
        # Get JSON body
        webhook_data = await request.json()

        logger.info(
            f"Received DPO webhook - Request ID: {request_id}, "
            f"Data keys: {list(webhook_data.keys()) if isinstance(webhook_data, dict) else 'Invalid format'}"
        )

        # Process webhook using enhanced processor
        webhook_processor = WebhookProcessor(db)
        result = await webhook_processor.process_dpo_webhook(
            payload=webhook_data, request_id=request_id
        )

        processing_time = (datetime.utcnow() - start_time).total_seconds()
        logger.info(
            f"DPO webhook processed successfully - Request ID: {request_id}, "
            f"Processing time: {processing_time:.3f}s"
        )

        return result

    except WebhookProcessingError as e:
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        logger.warning(
            f"DPO webhook processing error - Request ID: {request_id}, "
            f"Error: {e.error_code}, Message: {str(e)}, "
            f"Processing time: {processing_time:.3f}s"
        )

        # Return appropriate HTTP status based on error type
        if e.error_code in ["INVALID_FORMAT", "MISSING_REFERENCE"]:
            raise HTTPException(status_code=400, detail=str(e))
        elif e.retry_after:
            raise HTTPException(
                status_code=503,
                detail=str(e),
                headers={"Retry-After": str(e.retry_after)},
            )
        else:
            # Return 200 to prevent unnecessary retries for non-retryable errors
            return {"status": "error", "error_code": e.error_code, "message": str(e)}

    except Exception as e:
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        logger.error(
            f"Unexpected error in DPO webhook - Request ID: {request_id}, "
            f"Error: {str(e)}, Processing time: {processing_time:.3f}s",
            exc_info=True,
        )

        # Return 200 to prevent DPO from retrying on unexpected errors
        return {
            "status": "error",
            "message": "Internal server error",
            "request_id": request_id,
        }


@router.post("/paystack/test", status_code=status.HTTP_200_OK)
async def test_paystack_webhook(webhook_data: Dict, db: Session = Depends(get_db)):
    """Test endpoint for Paystack webhook processing (development only)."""
    request_id = str(uuid.uuid4())

    try:
        logger.info(f"Test Paystack webhook - Request ID: {request_id}")

        # Process using enhanced webhook processor
        webhook_processor = WebhookProcessor(db)

        # Convert dict to JSON string for signature verification (test mode)
        payload = json.dumps(webhook_data)

        # Process without signature verification for testing
        result = await webhook_processor._process_paystack_event(
            event_type=webhook_data.get("event", "charge.success"),
            event_data=webhook_data.get("data", webhook_data),
            request_id=request_id,
        )

        return {
            "status": "success",
            "request_id": request_id,
            "data": result,
        }

    except Exception as e:
        logger.error(
            f"Test webhook failed - Request ID: {request_id}, Error: {str(e)}",
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"Test webhook failed: {str(e)}")


@router.get("/health", status_code=status.HTTP_200_OK)
async def webhook_health_check(db: Session = Depends(get_db)):
    """Health check endpoint for webhook processing system."""
    try:
        webhook_processor = WebhookProcessor(db)
        health_status = webhook_processor.get_webhook_health_status()

        return health_status

    except Exception as e:
        logger.error(f"Webhook health check failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=503, detail="Webhook system unhealthy")
