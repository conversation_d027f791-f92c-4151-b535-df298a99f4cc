"""Async Project service for DevHQ Backend
Handles project-related business logic and operations with async database operations
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from app.core.exceptions import NotFoundError
from app.models import ActivityLog, Client, Project, ProjectMilestone, User
from app.models.user import UserRole
from app.schemas.project import ProjectCreate, ProjectUpdate


class AsyncProjectService:
    """Async service for managing project operations"""

    def __init__(self, db: AsyncSession):
        """
        Initialize AsyncProjectService with async database session

        Args:
            db (AsyncSession): Async database session
        """
        self.db = db

    async def log_activity(
        self,
        user_id: uuid.UUID,
        entity_type: str,
        entity_id: str,
        action: str,
        details: str = None,
    ):
        """
        Log an activity for audit trail purposes

        Args:
            user_id (uuid.UUID): ID of user performing the action
            entity_type (str): Type of entity (e.g., 'project')
            entity_id (str): ID of the entity
            action (str): Action performed
            details (str, optional): Additional details
        """
        activity = ActivityLog(
            user_id=user_id,
            entity_type=entity_type,
            entity_id=entity_id,
            action=action,
            details=details,
        )
        self.db.add(activity)
        await self.db.flush()

    async def list_projects(
        self,
        user_id: uuid.UUID,
        page: int = 1,
        per_page: int = 25,
        client_id: Optional[str] = None,
        workspace_id: Optional[str] = None,
        status: Optional[str] = None,
        billing_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> dict:
        """
        List projects for a user with filtering and pagination

        Args:
            user_id (uuid.UUID): User ID
            page (int): Page number
            per_page (int): Items per page
            client_id (str, optional): Filter by client ID
            workspace_id (str, optional): Filter by workspace ID
            status (str, optional): Filter by status
            billing_type (str, optional): Filter by billing type
            search (str, optional): Search term

        Returns:
            dict: Paginated projects data
        """
        query = select(Project).where(
            and_(Project.user_id == user_id, Project.deleted_at.is_(None))
        )

        # Apply filters
        if client_id:
            query = query.where(Project.client_id == client_id)
        if workspace_id:
            query = query.where(Project.workspace_id == workspace_id)
        if status:
            query = query.where(Project.status == status)
        if billing_type:
            query = query.where(Project.billing_type == billing_type)
        if search:
            query = query.where(
                or_(
                    Project.name.ilike(f"%{search}%"),
                    Project.description.ilike(f"%{search}%"),
                )
            )

        # Add ordering
        query = query.order_by(desc(Project.created_at))

        # Calculate offset
        offset = (page - 1) * per_page

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # Get paginated results
        paginated_query = query.offset(offset).limit(per_page)
        result = await self.db.execute(paginated_query)
        projects = result.scalars().all()

        return {
            "items": projects,
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page,
        }

    async def create_project(
        self, user_id: uuid.UUID, project_data: ProjectCreate
    ) -> Project:
        """
        Create a new project

        Args:
            user_id (uuid.UUID): User ID
            project_data (ProjectCreate): Project creation data

        Returns:
            Project: Created project
        """
        # Verify client exists if provided
        if project_data.client_id:
            client_query = select(Client).where(
                and_(
                    Client.id == project_data.client_id,
                    Client.user_id == user_id,
                    Client.deleted_at.is_(None),
                )
            )
            client_result = await self.db.execute(client_query)
            client = client_result.scalar_one_or_none()
            if not client:
                raise NotFoundError("Client not found")

        # Verify workspace exists and belongs to user
        if project_data.workspace_id:
            from app.models.workspace import Workspace
            workspace_query = select(Workspace).where(
                and_(
                    Workspace.id == project_data.workspace_id,
                    Workspace.user_id == user_id,
                    Workspace.deleted_at.is_(None),
                )
            )
            workspace_result = await self.db.execute(workspace_query)
            workspace = workspace_result.scalar_one_or_none()
            if not workspace:
                raise NotFoundError("Workspace not found")

        project = Project(
            id=uuid.uuid4(),
            user_id=user_id,
            title=project_data.title,
            description=project_data.description,
            client_id=project_data.client_id,
            workspace_id=project_data.workspace_id,
            status=project_data.status or "active",
            billing_type=project_data.billing_type or "time_and_materials",
            hourly_rate=project_data.hourly_rate,
            total_budget=project_data.total_budget,
            estimated_hours=project_data.estimated_hours,
            start_date=project_data.start_date,
            end_date=project_data.end_date,
            deadline=project_data.deadline,
            currency=project_data.currency or "USD",
            is_billable=project_data.is_billable,
            is_active=project_data.is_active,
            # Include client information fields
            client_name=project_data.client_name,
            contact_person=project_data.contact_person,
            client_email=project_data.client_email,
            client_phone=project_data.client_phone,
            client_address=project_data.client_address,
            client_industry=project_data.client_industry,
            client_company_size=project_data.client_company_size,
            client_priority=project_data.client_priority,
            client_status=project_data.client_status,
            client_notes=project_data.client_notes,
            created_at=datetime.utcnow(),
        )

        self.db.add(project)
        await self.db.flush()
        await self.db.commit()  # Explicitly commit the transaction
        await self.db.refresh(project)

        # Log activity
        await self.log_activity(
            user_id=user_id,
            entity_type="project",
            entity_id=str(project.id),
            action="created",
            details=f"Created project: {project.title}",
        )

        return project

    async def get_project(self, user_id: uuid.UUID, project_id: uuid.UUID) -> Project:
        """
        Get a project by ID

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID

        Returns:
            Project: Project instance

        Raises:
            NotFoundError: If project not found
        """
        query = select(Project).where(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.deleted_at.is_(None),
            )
        )
        result = await self.db.execute(query)
        project = result.scalar_one_or_none()

        if not project:
            raise NotFoundError("Project not found")

        return project

    async def update_project(
        self, user_id: uuid.UUID, project_id: uuid.UUID, project_data: ProjectUpdate
    ) -> Project:
        """
        Update a project

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID
            project_data (ProjectUpdate): Project update data

        Returns:
            Project: Updated project
        """
        project = await self.get_project(user_id, project_id)

        # Update fields
        update_data = project_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(project, field, value)

        project.updated_at = datetime.utcnow()
        await self.db.flush()
        await self.db.commit()
        await self.db.refresh(project)

        # Log activity
        await self.log_activity(
            user_id=user_id,
            entity_type="project",
            entity_id=str(project.id),
            action="updated",
            details=f"Updated project: {project.title}",
        )

        return project

    async def delete_project(self, user_id: uuid.UUID, project_id: uuid.UUID) -> None:
        """
        Soft delete a project

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID
        """
        project = await self.get_project(user_id, project_id)

        project.deleted_at = datetime.utcnow()
        await self.db.flush()
        await self.db.commit()

        # Log activity
        await self.log_activity(
            user_id=user_id,
            entity_type="project",
            entity_id=str(project.id),
            action="deleted",
            details=f"Deleted project: {project.title}",
        )

    async def get_project_overview(
        self, user_id: uuid.UUID, project_id: uuid.UUID
    ) -> dict:
        """
        Get project overview with basic statistics

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID

        Returns:
            dict: Project overview data
        """
        project = await self.get_project(user_id, project_id)

        # Import here to avoid circular imports
        from app.models.project import TimeEntry

        # Get basic statistics
        total_hours_query = select(func.sum(TimeEntry.duration_hours)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_hours_result = await self.db.execute(total_hours_query)
        total_hours = total_hours_result.scalar() or Decimal("0")

        return {
            "project": project,
            "total_hours": float(total_hours),
            "completion_percentage": float(project.completion_percentage or 0),
            "status": project.status,
        }

    async def get_project_with_details(
        self, user_id: uuid.UUID, project_id: uuid.UUID
    ) -> dict:
        """
        Get project with detailed information including client and milestones

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID

        Returns:
            dict: Project with detailed information
        """
        # Get project information
        query = select(Project).where(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.deleted_at.is_(None),
            )
        )
        result = await self.db.execute(query)
        project = result.scalar_one_or_none()

        if not project:
            raise NotFoundError("Project not found")

        # Get client information separately to avoid lazy loading
        client = None
        if project.client_id:
            from app.models.client import Client

            client_query = select(Client).where(Client.id == project.client_id)
            client_result = await self.db.execute(client_query)
            client = client_result.scalar_one_or_none()

        # Get milestones
        milestones_query = (
            select(ProjectMilestone)
            .where(
                and_(
                    ProjectMilestone.project_id == project_id,
                    ProjectMilestone.deleted_at.is_(None),
                )
            )
            .order_by(ProjectMilestone.sequence_number)
        )
        milestones_result = await self.db.execute(milestones_query)
        milestones = milestones_result.scalars().all()

        # Convert to dictionaries to avoid lazy loading issues
        project_dict = {
            "id": project.id,
            "workspace_id": project.workspace_id,
            "client_id": project.client_id,
            "user_id": project.user_id,
            "title": project.title,
            "description": project.description,
            "billing_type": project.billing_type,
            "total_budget": project.total_budget,
            "estimated_hours": project.estimated_hours,
            "hourly_rate": project.hourly_rate,
            "currency": project.currency,
            "status": project.status,
            "start_date": project.start_date,
            "end_date": project.end_date,
            "deadline": project.deadline,
            "is_billable": project.is_billable,
            "is_active": project.is_active,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            # Project-specific client information - use same approach as contact_person
            "client": getattr(project, 'client_name', None),
            "client_name": getattr(project, 'client_name', None),
            "contact_person": getattr(project, 'contact_person', None),
            "client_email": getattr(project, 'client_email', None),
            "client_phone": getattr(project, 'client_phone', None),
            "client_address": getattr(project, 'client_address', None),
            "client_industry": getattr(project, 'client_industry', None),
            "client_company_size": getattr(project, 'client_company_size', None),
            "client_priority": getattr(project, 'client_priority', None),
            "client_status": getattr(project, 'client_status', None),
            "client_notes": getattr(project, 'client_notes', None),
            "total_logged_hours": Decimal("0.00"),
            "total_billable_amount": Decimal("0.00"),
            "completion_percentage": 0.0,
        }

        client_dict = None
        if client:
            client_dict = {
                "id": client.id,
                "name": client.name,
                "email": client.email,
                "company": client.company,
            }

        milestones_list = [
            {
                "id": milestone.id,
                "project_id": milestone.project_id,
                "title": milestone.title,
                "description": milestone.description,
                "estimated_hours": milestone.estimated_hours,
                "payment_amount": milestone.payment_amount,
                "due_date": milestone.due_date.date() if milestone.due_date else None,
                "completed_at": milestone.completed_at,
                "status": milestone.status,
                "sequence_number": milestone.sequence_number,
                "is_client_visible": milestone.is_client_visible,
                "billing_status": milestone.billing_status,
                "created_at": milestone.created_at,
                "updated_at": milestone.updated_at,
            }
            for milestone in milestones
        ]

        return {
            "project": project_dict,
            "client": client_dict,
            "milestones": milestones_list,
        }

    async def get_project_time_entries(
        self,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        page: int = 1,
        per_page: int = 25,
    ) -> dict:
        """
        Get paginated time entries for a project

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID
            page (int): Page number
            per_page (int): Items per page

        Returns:
            dict: Paginated time entries data
        """
        # Verify project access
        await self.get_project(user_id, project_id)

        # Import here to avoid circular imports
        from app.models.project import TimeEntry

        # Get total count
        count_query = select(func.count(TimeEntry.id)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_result = await self.db.execute(count_query)
        total = total_result.scalar() or 0

        # Calculate offset
        offset = (page - 1) * per_page

        # Get paginated time entries
        time_entries_query = (
            select(TimeEntry)
            .where(
                and_(
                    TimeEntry.project_id == project_id,
                    TimeEntry.user_id == user_id,
                    TimeEntry.deleted_at.is_(None),
                )
            )
            .order_by(desc(TimeEntry.start_time))
            .offset(offset)
            .limit(per_page)
        )
        time_entries_result = await self.db.execute(time_entries_query)
        time_entries = time_entries_result.scalars().all()

        return {
            "time_entries": time_entries,
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page,
        }

    async def get_project_metrics(
        self, user_id: uuid.UUID, project_id: uuid.UUID
    ) -> dict:
        """
        Get detailed project metrics

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID

        Returns:
            dict: Project metrics data
        """
        project = await self.get_project(user_id, project_id)

        # Import here to avoid circular imports
        from app.models.invoice import Invoice
        from app.models.project import TimeEntry

        # Calculate metrics
        total_time_query = select(func.count(TimeEntry.id)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_time_result = await self.db.execute(total_time_query)
        total_time_entries = total_time_result.scalar() or 0

        total_hours_query = select(func.sum(TimeEntry.duration_hours)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_hours_result = await self.db.execute(total_hours_query)
        total_hours = total_hours_result.scalar() or Decimal("0")

        total_revenue_query = select(func.sum(TimeEntry.billable_amount)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_revenue_result = await self.db.execute(total_revenue_query)
        total_revenue = total_revenue_result.scalar() or Decimal("0")

        return {
            "project_id": str(project_id),
            "total_time_entries": total_time_entries,
            "total_hours": float(total_hours),
            "total_revenue": float(total_revenue),
            "completion_percentage": float(project.completion_percentage or 0),
            "status": project.status,
        }

    async def get_project_analytics(
        self, user_id: uuid.UUID, project_id: uuid.UUID
    ) -> dict:
        """
        Get analytics for a project

        Args:
            user_id (uuid.UUID): User ID
            project_id (uuid.UUID): Project ID

        Returns:
            dict: Project analytics data
        """
        # Verify project access
        project = await self.get_project(user_id, project_id)

        # Import here to avoid circular imports
        from app.models.invoice import Invoice
        from app.models.project import TimeEntry

        # Calculate basic analytics
        total_time_entries_query = select(func.count(TimeEntry.id)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_time_entries_result = await self.db.execute(total_time_entries_query)
        total_time_entries = total_time_entries_result.scalar() or 0

        total_hours_query = select(func.sum(TimeEntry.duration_hours)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_hours_result = await self.db.execute(total_hours_query)
        total_hours = total_hours_result.scalar() or Decimal("0")

        total_revenue_query = select(func.sum(TimeEntry.billable_amount)).where(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )
        total_revenue_result = await self.db.execute(total_revenue_query)
        total_revenue = total_revenue_result.scalar() or Decimal("0")

        total_invoices_query = select(func.count(Invoice.id)).where(
            and_(
                Invoice.project_id == project_id,
                Invoice.user_id == user_id,
                Invoice.deleted_at.is_(None),
            )
        )
        total_invoices_result = await self.db.execute(total_invoices_query)
        total_invoices = total_invoices_result.scalar() or 0

        return {
            "project_id": str(project_id),
            "total_time_entries": total_time_entries,
            "total_hours": float(total_hours),
            "total_revenue": float(total_revenue),
            "total_invoices": total_invoices,
            "completion_percentage": float(project.completion_percentage or 0),
            "status": project.status,
        }
