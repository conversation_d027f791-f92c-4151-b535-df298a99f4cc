"""
OAuth Service for DevHQ
Handles Google and GitHub OAuth authentication
"""

import secrets
from typing import Dict, <PERSON><PERSON>, Tuple
from urllib.parse import urlencode

import httpx
from authlib.integrations.requests_client import OAuth2Session
from fastapi import <PERSON>TT<PERSON>Ex<PERSON>, status

from app.config import settings
from app.schemas.auth import OAuthUserInfo


class OAuthService:
    """Service for handling OAuth authentication with Google and GitHub"""

    # OAuth provider configurations
    GOOGLE_CONFIG = {
        "authorization_endpoint": "https://accounts.google.com/o/oauth2/v2/auth",
        "token_endpoint": "https://oauth2.googleapis.com/token",
        "userinfo_endpoint": "https://www.googleapis.com/oauth2/v2/userinfo",
        "scope": "openid email profile",
    }

    GITHUB_CONFIG = {
        "authorization_endpoint": "https://github.com/login/oauth/authorize",
        "token_endpoint": "https://github.com/login/oauth/access_token",
        "userinfo_endpoint": "https://api.github.com/user",
        "scope": "user:email",
    }

    def __init__(self):
        self.google_client_id = settings.google_client_id
        self.google_client_secret = settings.google_client_secret
        self.github_client_id = settings.github_client_id
        self.github_client_secret = settings.github_client_secret
        self.redirect_url = settings.oauth_redirect_url

    def get_authorization_url(self, provider: str) -> Tuple[str, str]:
        """
        Generate OAuth authorization URL for the specified provider
        
        Args:
            provider: OAuth provider ('google' or 'github')
            
        Returns:
            Tuple of (authorization_url, state)
        """
        if provider not in ["google", "github"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported OAuth provider"
            )

        # Generate secure state parameter for CSRF protection
        state = secrets.token_urlsafe(32)

        if provider == "google":
            if not self.google_client_id or not self.google_client_secret:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Google OAuth not configured"
                )

            params = {
                "client_id": self.google_client_id,
                "redirect_uri": f"{self.redirect_url}/{provider}",
                "scope": self.GOOGLE_CONFIG["scope"],
                "response_type": "code",
                "state": state,
                "access_type": "offline",
                "prompt": "consent",
            }
            
            authorization_url = f"{self.GOOGLE_CONFIG['authorization_endpoint']}?{urlencode(params)}"

        elif provider == "github":
            if not self.github_client_id or not self.github_client_secret:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="GitHub OAuth not configured"
                )

            params = {
                "client_id": self.github_client_id,
                "redirect_uri": f"{self.redirect_url}/{provider}",
                "scope": self.GITHUB_CONFIG["scope"],
                "state": state,
            }
            
            authorization_url = f"{self.GITHUB_CONFIG['authorization_endpoint']}?{urlencode(params)}"

        return authorization_url, state

    async def exchange_code_for_token(self, provider: str, code: str) -> str:
        """
        Exchange authorization code for access token
        
        Args:
            provider: OAuth provider ('google' or 'github')
            code: Authorization code from OAuth callback
            
        Returns:
            Access token
        """
        if provider == "google":
            return await self._exchange_google_code(code)
        elif provider == "github":
            return await self._exchange_github_code(code)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported OAuth provider"
            )

    async def get_user_info(self, provider: str, access_token: str) -> OAuthUserInfo:
        """
        Get user information from OAuth provider
        
        Args:
            provider: OAuth provider ('google' or 'github')
            access_token: OAuth access token
            
        Returns:
            User information from OAuth provider
        """
        if provider == "google":
            return await self._get_google_user_info(access_token)
        elif provider == "github":
            return await self._get_github_user_info(access_token)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported OAuth provider"
            )

    async def _exchange_google_code(self, code: str) -> str:
        """Exchange Google authorization code for access token"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.GOOGLE_CONFIG["token_endpoint"],
                data={
                    "client_id": self.google_client_id,
                    "client_secret": self.google_client_secret,
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": f"{self.redirect_url}/google",
                },
                headers={"Accept": "application/json"},
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to exchange code for token"
                )

            token_data = response.json()
            return token_data.get("access_token")

    async def _exchange_github_code(self, code: str) -> str:
        """Exchange GitHub authorization code for access token"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.GITHUB_CONFIG["token_endpoint"],
                data={
                    "client_id": self.github_client_id,
                    "client_secret": self.github_client_secret,
                    "code": code,
                },
                headers={"Accept": "application/json"},
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to exchange code for token"
                )

            token_data = response.json()
            return token_data.get("access_token")

    async def _get_google_user_info(self, access_token: str) -> OAuthUserInfo:
        """Get user information from Google"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                self.GOOGLE_CONFIG["userinfo_endpoint"],
                headers={"Authorization": f"Bearer {access_token}"},
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get user info from Google"
                )

            user_data = response.json()
            
            # Parse name into first and last name
            name_parts = user_data.get("name", "").split(" ", 1)
            first_name = name_parts[0] if name_parts else ""
            last_name = name_parts[1] if len(name_parts) > 1 else ""

            return OAuthUserInfo(
                id=user_data["id"],
                email=user_data["email"],
                name=user_data.get("name", ""),
                first_name=first_name,
                last_name=last_name,
                avatar_url=user_data.get("picture"),
                provider="google",
            )

    async def _get_github_user_info(self, access_token: str) -> OAuthUserInfo:
        """Get user information from GitHub"""
        async with httpx.AsyncClient() as client:
            # Get user profile
            response = await client.get(
                self.GITHUB_CONFIG["userinfo_endpoint"],
                headers={"Authorization": f"Bearer {access_token}"},
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get user info from GitHub"
                )

            user_data = response.json()
            
            # Get user email (GitHub may not return email in profile)
            email = user_data.get("email")
            if not email:
                email_response = await client.get(
                    "https://api.github.com/user/emails",
                    headers={"Authorization": f"Bearer {access_token}"},
                )
                if email_response.status_code == 200:
                    emails = email_response.json()
                    primary_email = next((e for e in emails if e.get("primary")), None)
                    if primary_email:
                        email = primary_email["email"]

            if not email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Unable to get email from GitHub"
                )

            # Parse name into first and last name
            name = user_data.get("name") or user_data.get("login", "")
            name_parts = name.split(" ", 1) if name else ["", ""]
            first_name = name_parts[0] if name_parts else user_data.get("login", "")
            last_name = name_parts[1] if len(name_parts) > 1 else ""

            return OAuthUserInfo(
                id=str(user_data["id"]),
                email=email,
                name=name,
                first_name=first_name,
                last_name=last_name,
                avatar_url=user_data.get("avatar_url"),
                provider="github",
            )


# Global OAuth service instance
oauth_service = OAuthService()
