"""
Project service for DevHQ Backend
Handles project-related business logic and operations
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import Session, joinedload

from app.core.database_utils import DataIntegrity<PERSON><PERSON><PERSON><PERSON>, ForeignKeyManager
from app.core.exceptions import NotFoundError
from app.core.pagination import paginate_query
from app.core.query_optimization import QueryOptimizer, QueryProfiler
from app.core.transaction_manager import atomic, db_transaction, transactional
from app.models import ActivityLog, Client, Project, ProjectMilestone, User
from app.models.user import UserRole
from app.schemas.project import ProjectCreate, ProjectUpdate


class ProjectService:
    """Service for managing project operations with transaction management"""

    def __init__(self, db: Session):
        """
        Initialize ProjectService with database session

        Args:
            db (Session): Database session
        """
        self.db = db
        self.fk_manager = ForeignKeyManager()
        self.validator = DataIntegrityValidator()

    def log_activity(
        self,
        user_id: uuid.UUID,
        entity_type: str,
        entity_id: str,
        action: str,
        details: str = None,
    ):
        """
        Log an activity for audit trail purposes

        Args:
            user_id (uuid.UUID): ID of user performing the action
            entity_type (str): Type of entity (project, milestone, etc.)
            entity_id (str): ID of the entity
            action (str): Action performed (create, update, delete, etc.)
            details (str, optional): Additional details about the action
        """
        activity = ActivityLog(
            user_id=user_id,
            entity_type=entity_type,
            entity_id=str(entity_id),
            action=action,
            details=details,
        )
        self.db.add(activity)

    def list_projects(
        self,
        user_id: uuid.UUID,
        page: int = 1,
        per_page: int = 25,
        client_id: Optional[str] = None,
        status: Optional[str] = None,
        billing_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> dict:
        """List projects with filtering and pagination"""

        # Base query - only user's projects with soft delete filter
        query = self.db.query(Project).filter(Project.user_id == user_id)
        query = QueryOptimizer.add_soft_delete_filter(query, Project)

        # Apply filters using QueryOptimizer
        filters = {}
        if client_id:
            try:
                client_uuid = uuid.UUID(client_id)
                filters["client_id"] = client_uuid
            except ValueError:
                raise ValueError("Invalid client_id format")

        if status:
            filters["status"] = status

        if billing_type:
            filters["billing_type"] = billing_type

        query = QueryOptimizer.add_filters(query, Project, filters)

        # Add search filter
        if search:
            search_fields = ["title", "description"]
            query = QueryOptimizer.add_search_filter(
                query, Project, search, search_fields
            )

        # Optimize joins and ordering
        query = QueryOptimizer.optimize_joins(query, [Project.client])
        query = QueryOptimizer.optimize_pagination(query, Project.created_at, "desc")

        # Use pagination utility
        pagination_result = paginate_query(query, page, per_page)

        return pagination_result.to_dict()

    @transactional(rollback_on_exception=True)
    def create_project(
        self, user_id: uuid.UUID, project_data: ProjectCreate
    ) -> Project:
        """Create a new project with transaction management"""

        # Validate foreign key references
        if not self.fk_manager.validate_foreign_key_exists(
            self.db, Client, project_data.client_id
        ):
            raise NotFoundError("Client not found")

        if not self.fk_manager.validate_foreign_key_exists(self.db, User, user_id):
            raise NotFoundError("User not found")

        # Verify client belongs to user
        client = (
            self.db.query(Client)
            .filter(
                and_(
                    Client.id == project_data.client_id,
                    Client.user_id == user_id,
                    Client.deleted_at.is_(None),
                )
            )
            .first()
        )

        if not client:
            raise NotFoundError("Client not found or access denied")

        # Create project
        project = Project(
            **project_data.model_dump(exclude={"client_id"}),
            client_id=project_data.client_id,
            user_id=user_id,
        )

        self.db.add(project)
        self.db.flush()  # Get the ID

        # Validate data integrity
        self.validator.validate_foreign_key_constraints(self.db, project)

        # Log activity
        self.log_activity(
            user_id,
            "project",
            str(project.id),
            "create",
            f"Created project '{project.title}' for client '{client.name}'",
        )

        # Transaction will auto-commit on successful exit
        self.db.refresh(project)
        return project

    def get_project(self, user_id: uuid.UUID, project_id: uuid.UUID) -> Project:
        """Get project details with client, milestones, and time entries"""

        project = (
            self.db.query(Project)
            .filter(
                and_(
                    Project.id == project_id,
                    Project.user_id == user_id,
                    Project.deleted_at.is_(None),
                )
            )
            .options(
                joinedload(Project.client_record),
                joinedload(Project.milestones),
                joinedload(Project.time_entries),
            )
            .first()
        )

        if not project:
            raise NotFoundError("Project not found")

        return project

    def get_project_with_details(
        self, user_id: uuid.UUID, project_id: uuid.UUID
    ) -> dict:
        """Get project with all related details"""
        # Use joinedload to avoid N+1 queries
        project = (
            self.db.query(Project)
            .filter(
                and_(
                    Project.id == project_id,
                    Project.user_id == user_id,
                    Project.deleted_at.is_(None),
                )
            )
            .options(
                joinedload(Project.client_record),
                joinedload(Project.milestones),
                joinedload(Project.time_entries),
            )
            .first()
        )

        if not project:
            raise NotFoundError("Project not found")

        # Get active milestones
        active_milestones = [m for m in project.milestones if not m.is_deleted]

        # Get active time entries
        active_time_entries = [te for te in project.time_entries if not te.is_deleted]

        # Convert to response format
        return {
            **project.__dict__,
            "total_logged_hours": project.total_logged_hours,
            "total_billable_amount": project.total_billable_amount,
            "completion_percentage": project.completion_percentage,
            "client": {
                "id": str(project.client_record.id),
                "name": project.client_record.name,
                "email": project.client_record.email,
                "company": project.client_record.company,
            },
            "milestones": [
                {
                    "id": str(m.id),
                    "project_id": str(m.project_id),
                    "title": m.title,
                    "description": m.description,
                    "estimated_hours": m.estimated_hours,
                    "payment_amount": m.payment_amount,
                    "due_date": m.due_date.date() if m.due_date else None,
                    "status": m.status,
                    "sequence_number": m.sequence_number,
                    "completed_at": m.completed_at,
                    "created_at": m.created_at,
                    "updated_at": m.updated_at,
                    "is_overdue": m.is_overdue,
                    "logged_hours": m.logged_hours,
                }
                for m in active_milestones
            ],
            "time_entries": [
                {
                    "id": str(te.id),
                    "description": te.description,
                    "duration_hours": te.duration_hours,
                    "is_billable": te.is_billable,
                    "work_date": te.work_date,
                }
                for te in active_time_entries
            ],
            "time_entries_count": len(active_time_entries),
        }

    def update_project(
        self, user_id: uuid.UUID, project_id: uuid.UUID, project_data: ProjectUpdate
    ) -> Project:
        """Update project details with transaction management"""

        with db_transaction(self.db) as tx_db:
            # Get project within transaction
            project = (
                tx_db.query(Project)
                .filter(
                    and_(
                        Project.id == project_id,
                        Project.user_id == user_id,
                        Project.deleted_at.is_(None),
                    )
                )
                .first()
            )

            if not project:
                raise NotFoundError("Project not found")

            # Update fields
            update_data = project_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(project, field, value)

            # Validate data integrity if foreign keys were updated
            if any(field.endswith("_id") for field in update_data.keys()):
                self.validator.validate_foreign_key_constraints(tx_db, project)

            # Log activity
            self.log_activity(
                user_id,
                "project",
                str(project.id),
                "update",
                f"Updated project '{project.title}'",
            )

            tx_db.refresh(project)
            return project

    def delete_project(self, user_id: uuid.UUID, project_id: uuid.UUID) -> None:
        """Soft delete a project with transaction management"""

        with db_transaction(self.db) as tx_db:
            # Get project within transaction
            project = (
                tx_db.query(Project)
                .filter(
                    and_(
                        Project.id == project_id,
                        Project.user_id == user_id,
                        Project.deleted_at.is_(None),
                    )
                )
                .first()
            )

            if not project:
                raise NotFoundError("Project not found")

            # Check for referencing records
            referencing_records = self.fk_manager.find_referencing_records(
                tx_db, Project, project_id
            )

            # Perform safe soft delete
            project.soft_delete()

            # Log activity
            self.log_activity(
                user_id,
                "project",
                str(project.id),
                "delete",
                f"Deleted project '{project.title}' (found {len(referencing_records)} related records)",
            )

    def get_project_overview(self, user_id: uuid.UUID, project_id: uuid.UUID) -> dict:
        """Get project overview with statistics, milestones, and recent activity"""
        project = self.get_project(user_id, project_id)

        # Get active milestones
        active_milestones = [m for m in project.milestones if not m.deleted_at]

        # Get active time entries
        active_time_entries = [te for te in project.time_entries if not te.deleted_at]

        # Calculate statistics
        total_hours = sum(te.duration_hours for te in active_time_entries)
        billable_hours = sum(
            te.duration_hours for te in active_time_entries if te.is_billable
        )
        completed_milestones = len(
            [m for m in active_milestones if m.status == "completed"]
        )

        return {
            "project": {
                "id": str(project.id),
                "title": project.title,
                "status": project.status,
                "billing_type": project.billing_type,
                "client": {
                    "id": str(project.client_record.id),
                    "name": project.client_record.name,
                    "company": project.client_record.company,
                },
            },
            "statistics": {
                "total_hours": float(total_hours),
                "billable_hours": float(billable_hours),
                "total_milestones": len(active_milestones),
                "completed_milestones": completed_milestones,
                "completion_percentage": project.completion_percentage,
            },
            "milestones": [
                {
                    "id": str(m.id),
                    "title": m.title,
                    "status": m.status,
                    "due_date": m.due_date.isoformat() if m.due_date else None,
                    "is_overdue": m.is_overdue,
                }
                for m in active_milestones[:5]  # Show first 5 milestones
            ],
            "recent_activity": [
                {
                    "type": "time_entry",
                    "description": f"Logged {te.duration_hours} hours",
                    "date": te.work_date.isoformat(),
                }
                for te in sorted(
                    active_time_entries, key=lambda x: x.work_date, reverse=True
                )[:5]
            ],
        }

    def get_project_metrics(self, user_id: uuid.UUID, project_id: uuid.UUID) -> dict:
        """Get project metrics and performance data"""
        project = self.get_project(user_id, project_id)

        # Get active milestones and time entries
        active_milestones = [m for m in project.milestones if not m.deleted_at]
        active_time_entries = [te for te in project.time_entries if not te.deleted_at]

        # Calculate metrics
        total_logged_hours = sum(te.duration_hours for te in active_time_entries)
        total_estimated_hours = project.estimated_hours or 0
        completed_milestones = len(
            [m for m in active_milestones if m.status == "completed"]
        )
        total_milestones = len(active_milestones)

        milestone_completion = (
            (completed_milestones / total_milestones * 100)
            if total_milestones > 0
            else 0
        )

        return {
            "logged_hours": float(total_logged_hours),
            "estimated_hours": float(total_estimated_hours),
            "completion_percentage": project.completion_percentage,
            "milestone_completion": milestone_completion,
            "total_milestones": total_milestones,
            "completed_milestones": completed_milestones,
            "billable_hours": float(
                sum(te.duration_hours for te in active_time_entries if te.is_billable)
            ),
            "non_billable_hours": float(
                sum(
                    te.duration_hours
                    for te in active_time_entries
                    if not te.is_billable
                )
            ),
        }

    def get_project_time_entries(
        self,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        page: int = 1,
        per_page: int = 25,
    ) -> dict:
        """Get time entries for a project"""
        # Verify project access
        project = self.get_project(user_id, project_id)

        # Import here to avoid circular imports
        from app.models.project import TimeEntry

        # Query time entries
        query = self.db.query(TimeEntry).filter(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        )

        total = query.count()
        offset = (page - 1) * per_page
        time_entries = (
            query.order_by(desc(TimeEntry.created_at))
            .offset(offset)
            .limit(per_page)
            .all()
        )

        return {
            "total": total,
            "page": page,
            "per_page": per_page,
            "items": [
                {
                    "id": str(te.id),
                    "description": te.description,
                    "start_time": te.start_time.isoformat() if te.start_time else None,
                    "end_time": te.end_time.isoformat() if te.end_time else None,
                    "duration_hours": (
                        str(te.duration_hours) if te.duration_hours else "0"
                    ),
                    "billable_amount": (
                        str(te.billable_amount) if te.billable_amount else "0"
                    ),
                    "status": te.status,
                    "created_at": te.created_at.isoformat(),
                }
                for te in time_entries
            ],
        }

    async def _can_access_project(self, project: Project, user_id: str) -> bool:
        """Check if a user can access a project.

        Args:
            project (Project): The project to check access for
            user_id (str): The ID of the user attempting to access the project

        Returns:
            bool: True if the user can access the project, False otherwise
        """
        # User can access their own projects
        if str(project.user_id) == str(user_id):
            return True

        # Check if user is admin (would need to query the user)
        # For testing purposes, we'll mock this behavior
        # In a real implementation, we would query the user from the database
        # and check if they have admin role

        # This is a simplified implementation for testing
        return False

    def get_project_analytics(self, user_id: uuid.UUID, project_id: uuid.UUID) -> dict:
        """Get analytics for a project"""
        # Verify project access
        project = self.get_project(user_id, project_id)

        # Import here to avoid circular imports
        from app.models.invoice import Invoice
        from app.models.project import TimeEntry

        # Calculate basic analytics
        total_time_entries = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.project_id == project_id,
                    TimeEntry.user_id == user_id,
                    TimeEntry.deleted_at.is_(None),
                )
            )
            .count()
        )

        total_hours = self.db.query(func.sum(TimeEntry.duration_hours)).filter(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        ).scalar() or Decimal("0")

        total_revenue = self.db.query(func.sum(TimeEntry.billable_amount)).filter(
            and_(
                TimeEntry.project_id == project_id,
                TimeEntry.user_id == user_id,
                TimeEntry.deleted_at.is_(None),
            )
        ).scalar() or Decimal("0")

        total_invoices = (
            self.db.query(Invoice)
            .filter(
                and_(
                    Invoice.project_id == project_id,
                    Invoice.user_id == user_id,
                    Invoice.deleted_at.is_(None),
                )
            )
            .count()
        )

        return {
            "project_id": str(project_id),
            "total_time_entries": total_time_entries,
            "total_hours": float(total_hours),
            "total_revenue": float(total_revenue),
            "total_invoices": total_invoices,
            "completion_percentage": float(project.completion_percentage or 0),
            "status": project.status,
        }
