import uuid
from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, joinedload

from app.core.database_utils import (BulkOperationManager,
                                     DataIntegrityValidator, ForeignKeyManager)
from app.core.exceptions import (BusinessLogicError, NotFoundError,
                                 ValidationError)
from app.core.logging import get_logger
from app.core.transaction_manager import (TransactionManager, atomic,
                                          db_transaction, transactional)
from app.models.client import Client
from app.models.invoice import Invoice, InvoiceItem
from app.models.project import Project, TimeEntry
from app.models.user import User
from app.schemas.invoice import (InvoiceCreate, InvoiceItemCreate,
                                 InvoiceResponse, InvoiceUpdate)

logger = get_logger(__name__)


class InvoiceService:
    """Service for managing invoices with comprehensive transaction management

    This service demonstrates advanced transaction patterns including:
    - Nested transactions with savepoints
    - Complex business logic validation
    - Bulk operations with rollback capabilities
    - Foreign key constraint management
    - Data integrity validation
    """

    def __init__(self, db: Session):
        self.db = db
        self.fk_manager = ForeignKeyManager()
        self.validator = DataIntegrityValidator()
        self.bulk_manager = BulkOperationManager()
        self.tx_manager = TransactionManager(db)

    @atomic
    def create_invoice_from_time_entries(
        self,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        time_entry_ids: List[uuid.UUID],
        invoice_data: InvoiceCreate,
    ) -> Invoice:
        """Create an invoice from selected time entries with complex validation"""

        # Validate all foreign key references upfront
        self._validate_invoice_prerequisites(user_id, project_id, time_entry_ids)

        # Get project and validate ownership
        project = self._get_user_project(user_id, project_id)

        # Get and validate time entries
        time_entries = self._get_billable_time_entries(user_id, time_entry_ids)

        # Create invoice with nested transaction for complex operations
        with self.tx_manager.savepoint() as sp:
            try:
                # Create the main invoice record
                invoice = self._create_invoice_record(user_id, project, invoice_data)

                # Create invoice items from time entries
                invoice_items = self._create_invoice_items_from_time_entries(
                    invoice.id, time_entries
                )

                # Update time entries to mark as invoiced
                self._mark_time_entries_as_invoiced(time_entries, invoice.id)

                # Calculate and update invoice totals
                self._calculate_invoice_totals(invoice, invoice_items)

                # Validate final invoice state
                self._validate_invoice_integrity(invoice)

                logger.info(
                    f"Created invoice {invoice.invoice_number} for project {project.title}"
                )

                return invoice

            except Exception as e:
                logger.error(f"Failed to create invoice: {str(e)}")
                sp.rollback()
                raise BusinessLogicError(f"Invoice creation failed: {str(e)}")

    def _validate_invoice_prerequisites(
        self, user_id: uuid.UUID, project_id: uuid.UUID, time_entry_ids: List[uuid.UUID]
    ) -> None:
        """Validate all prerequisites for invoice creation"""

        # Validate user exists
        if not self.fk_manager.validate_foreign_key_exists(self.db, User, user_id):
            raise NotFoundError("User not found")

        # Validate project exists and belongs to user
        if not self.fk_manager.validate_foreign_key_exists(
            self.db, Project, project_id
        ):
            raise NotFoundError("Project not found")

        # Validate all time entries exist
        for time_entry_id in time_entry_ids:
            if not self.fk_manager.validate_foreign_key_exists(
                self.db, TimeEntry, time_entry_id
            ):
                raise NotFoundError(f"Time entry {time_entry_id} not found")

    def _get_user_project(self, user_id: uuid.UUID, project_id: uuid.UUID) -> Project:
        """Get project and validate user ownership"""
        project = (
            self.db.query(Project)
            .options(joinedload(Project.client_record))
            .filter(
                and_(
                    Project.id == project_id,
                    Project.user_id == user_id,
                    Project.deleted_at.is_(None),
                )
            )
            .first()
        )

        if not project:
            raise NotFoundError("Project not found or access denied")

        return project

    def _get_billable_time_entries(
        self, user_id: uuid.UUID, time_entry_ids: List[uuid.UUID]
    ) -> List[TimeEntry]:
        """Get time entries and validate they are billable and not already invoiced"""
        time_entries = (
            self.db.query(TimeEntry)
            .filter(
                and_(
                    TimeEntry.id.in_(time_entry_ids),
                    TimeEntry.user_id == user_id,
                    TimeEntry.is_billable == True,
                    TimeEntry.invoice_id.is_(None),  # Not already invoiced
                    TimeEntry.deleted_at.is_(None),
                )
            )
            .all()
        )

        if len(time_entries) != len(time_entry_ids):
            raise ValidationError(
                "Some time entries are not found, not billable, or already invoiced"
            )

        return time_entries

    def _create_invoice_record(
        self, user_id: uuid.UUID, project: Project, invoice_data: InvoiceCreate
    ) -> Invoice:
        """Create the main invoice record"""
        invoice = Invoice(
            user_id=user_id,
            project_id=project.id,
            client_id=project.client_id,
            invoice_number=self._generate_invoice_number(user_id),
            issue_date=invoice_data.issue_date or date.today(),
            due_date=invoice_data.due_date,
            status="draft",
            notes=invoice_data.notes,
            tax_rate=invoice_data.tax_rate or Decimal("0.00"),
            discount_amount=invoice_data.discount_amount or Decimal("0.00"),
        )

        self.db.add(invoice)
        self.db.flush()  # Get the ID

        return invoice

    def _create_invoice_items_from_time_entries(
        self, invoice_id: uuid.UUID, time_entries: List[TimeEntry]
    ) -> List[InvoiceItem]:
        """Create invoice items from time entries"""
        invoice_items = []

        for time_entry in time_entries:
            item = InvoiceItem(
                invoice_id=invoice_id,
                description=f"Time Entry: {time_entry.description}",
                quantity=time_entry.duration_hours,
                unit_price=time_entry.hourly_rate or Decimal("0.00"),
                amount=time_entry.billable_amount or Decimal("0.00"),
                time_entry_id=time_entry.id,
            )
            invoice_items.append(item)

        # Use bulk operation for efficiency
        self.bulk_manager.bulk_create(self.db, invoice_items)

        return invoice_items

    def _mark_time_entries_as_invoiced(
        self, time_entries: List[TimeEntry], invoice_id: uuid.UUID
    ) -> None:
        """Mark time entries as invoiced"""
        for time_entry in time_entries:
            time_entry.invoice_id = invoice_id
            time_entry.status = "invoiced"

    def _calculate_invoice_totals(
        self, invoice: Invoice, invoice_items: List[InvoiceItem]
    ) -> None:
        """Calculate and update invoice totals"""
        subtotal = sum(item.amount for item in invoice_items)
        tax_amount = subtotal * (invoice.tax_rate / 100)
        total_amount = subtotal + tax_amount - invoice.discount_amount

        invoice.subtotal = subtotal
        invoice.tax_amount = tax_amount
        invoice.total_amount = total_amount

    def _validate_invoice_integrity(self, invoice: Invoice) -> None:
        """Validate invoice data integrity"""
        # Validate foreign key constraints
        self.validator.validate_foreign_key_constraints(self.db, invoice)

        # Business logic validation
        if invoice.total_amount <= 0:
            raise ValidationError("Invoice total must be greater than zero")

        if invoice.due_date and invoice.due_date < invoice.issue_date:
            raise ValidationError("Due date cannot be before issue date")

    def _generate_invoice_number(self, user_id: uuid.UUID) -> str:
        """Generate unique invoice number"""
        # Get the latest invoice number for this user
        latest_invoice = (
            self.db.query(Invoice)
            .filter(Invoice.user_id == user_id)
            .order_by(desc(Invoice.created_at))
            .first()
        )

        if latest_invoice and latest_invoice.invoice_number:
            try:
                # Extract number from format INV-YYYY-NNNN
                parts = latest_invoice.invoice_number.split("-")
                if len(parts) == 3 and parts[0] == "INV":
                    year = parts[1]
                    number = int(parts[2]) + 1
                else:
                    number = 1
            except (ValueError, IndexError):
                number = 1
        else:
            number = 1

        current_year = datetime.now().year
        return f"INV-{current_year}-{number:04d}"

    @transactional(rollback_on_exception=True)
    def update_invoice_status(
        self, user_id: uuid.UUID, invoice_id: uuid.UUID, new_status: str
    ) -> Invoice:
        """Update invoice status with validation"""

        invoice = self._get_user_invoice(user_id, invoice_id)

        # Validate status transition
        self._validate_status_transition(invoice.status, new_status)

        old_status = invoice.status
        invoice.status = new_status

        # Handle status-specific logic
        if new_status == "sent":
            invoice.sent_date = datetime.utcnow()
        elif new_status == "paid":
            invoice.paid_date = datetime.utcnow()
        elif new_status == "cancelled":
            # Unlink time entries if cancelling
            self._unlink_time_entries(invoice_id)

        logger.info(
            f"Updated invoice {invoice.invoice_number} status from {old_status} to {new_status}"
        )

        return invoice

    def _get_user_invoice(self, user_id: uuid.UUID, invoice_id: uuid.UUID) -> Invoice:
        """Get invoice and validate user ownership"""
        invoice = (
            self.db.query(Invoice)
            .filter(
                and_(
                    Invoice.id == invoice_id,
                    Invoice.user_id == user_id,
                    Invoice.deleted_at.is_(None),
                )
            )
            .first()
        )

        if not invoice:
            raise NotFoundError("Invoice not found or access denied")

        return invoice

    def _validate_status_transition(self, current_status: str, new_status: str) -> None:
        """Validate invoice status transitions"""
        valid_transitions = {
            "draft": ["sent", "cancelled"],
            "sent": ["paid", "overdue", "cancelled"],
            "paid": [],  # Final state
            "overdue": ["paid", "cancelled"],
            "cancelled": ["draft"],  # Allow reactivation
        }

        if new_status not in valid_transitions.get(current_status, []):
            raise ValidationError(
                f"Cannot transition from {current_status} to {new_status}"
            )

    def _unlink_time_entries(self, invoice_id: uuid.UUID) -> None:
        """Unlink time entries from cancelled invoice"""
        time_entries = (
            self.db.query(TimeEntry).filter(TimeEntry.invoice_id == invoice_id).all()
        )

        for time_entry in time_entries:
            time_entry.invoice_id = None
            time_entry.status = "completed"

    def get_invoice_analytics(self, user_id: uuid.UUID) -> Dict[str, Any]:
        """Get comprehensive invoice analytics"""

        # Use read-only transaction for analytics
        with db_transaction(self.db, read_only=True) as tx_db:
            total_invoices = (
                tx_db.query(Invoice)
                .filter(and_(Invoice.user_id == user_id, Invoice.deleted_at.is_(None)))
                .count()
            )

            total_revenue = tx_db.query(func.sum(Invoice.total_amount)).filter(
                and_(
                    Invoice.user_id == user_id,
                    Invoice.status == "paid",
                    Invoice.deleted_at.is_(None),
                )
            ).scalar() or Decimal("0.00")

            pending_amount = tx_db.query(func.sum(Invoice.total_amount)).filter(
                and_(
                    Invoice.user_id == user_id,
                    Invoice.status.in_(["sent", "overdue"]),
                    Invoice.deleted_at.is_(None),
                )
            ).scalar() or Decimal("0.00")

            return {
                "total_invoices": total_invoices,
                "total_revenue": float(total_revenue),
                "pending_amount": float(pending_amount),
                "average_invoice_value": float(total_revenue / max(total_invoices, 1)),
            }
