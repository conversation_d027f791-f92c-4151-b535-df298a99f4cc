"""
Real-time Service for Event Handling and Notifications
Manages live updates for approvals, project changes, and client interactions
"""

import asyncio
import logging
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

from app.core.websocket_manager import websocket_manager
from app.models.approval import ClientApproval
from app.models.client import Client
from app.models.project import Project
from app.models.user import User

logger = logging.getLogger(__name__)


class EventType(str, Enum):
    """Real-time event types"""

    # Approval Events
    APPROVAL_CREATED = "approval_created"
    APPROVAL_UPDATED = "approval_updated"
    APPROVAL_APPROVED = "approval_approved"
    APPROVAL_REJECTED = "approval_rejected"
    APPROVAL_COMMENT_ADDED = "approval_comment_added"

    # Project Events
    PROJECT_UPDATED = "project_updated"
    PROJECT_STATUS_CHANGED = "project_status_changed"
    PROJECT_MILESTONE_COMPLETED = "project_milestone_completed"
    PROJECT_FILE_UPLOADED = "project_file_uploaded"

    # Time Tracking Events
    TIME_ENTRY_STARTED = "time_entry_started"
    TIME_ENTRY_STOPPED = "time_entry_stopped"
    TIME_ENTRY_UPDATED = "time_entry_updated"

    # Invoice Events
    INVOICE_CREATED = "invoice_created"
    INVOICE_SENT = "invoice_sent"
    INVOICE_PAID = "invoice_paid"

    # Client Portal Events
    CLIENT_ACTIVITY = "client_activity"
    CLIENT_MESSAGE = "client_message"
    CLIENT_FILE_UPLOADED = "client_file_uploaded"

    # System Events
    NOTIFICATION = "notification"
    SYSTEM_ALERT = "system_alert"


class RealtimeService:
    """
    Service for managing real-time events and notifications
    """

    def __init__(self):
        self.event_queue = asyncio.Queue()
        self.processing = False

    async def start_processing(self):
        """Start the event processing loop"""
        if self.processing:
            return

        self.processing = True
        asyncio.create_task(self._process_events())
        logger.info("Real-time event processing started")

    async def stop_processing(self):
        """Stop the event processing loop"""
        self.processing = False
        logger.info("Real-time event processing stopped")

    async def _process_events(self):
        """Process events from the queue"""
        while self.processing:
            try:
                # Wait for events with timeout
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                await self._handle_event(event)
                self.event_queue.task_done()
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing event: {e}")

    async def _handle_event(self, event: Dict[str, Any]):
        """Handle a single event"""
        try:
            event_type = event.get("type")
            data = event.get("data", {})

            # Add timestamp if not present
            if "timestamp" not in data:
                data["timestamp"] = datetime.now(timezone.utc).isoformat()

            # Route event based on type
            if event_type.startswith("approval_"):
                await self._handle_approval_event(event_type, data)
            elif event_type.startswith("project_"):
                await self._handle_project_event(event_type, data)
            elif event_type.startswith("time_entry_"):
                await self._handle_time_entry_event(event_type, data)
            elif event_type.startswith("invoice_"):
                await self._handle_invoice_event(event_type, data)
            elif event_type.startswith("client_"):
                await self._handle_client_event(event_type, data)
            else:
                await self._handle_system_event(event_type, data)

        except Exception as e:
            logger.error(f"Error handling event {event.get('type')}: {e}")

    async def _handle_approval_event(self, event_type: str, data: Dict[str, Any]):
        """Handle approval-related events"""
        approval_id = data.get("approval_id")
        project_id = data.get("project_id")
        client_id = data.get("client_id")
        user_id = data.get("user_id")

        # Broadcast to project room
        if project_id:
            await websocket_manager.broadcast_to_project(project_id, event_type, data)

        # Broadcast to client room
        if client_id:
            await websocket_manager.broadcast_to_client(client_id, event_type, data)

        # Send to developer
        if user_id:
            await websocket_manager.send_to_user(user_id, event_type, data)

        # Special handling for approval status changes
        if event_type in [EventType.APPROVAL_APPROVED, EventType.APPROVAL_REJECTED]:
            await self._handle_approval_decision(data)

    async def _handle_project_event(self, event_type: str, data: Dict[str, Any]):
        """Handle project-related events"""
        project_id = data.get("project_id")
        client_id = data.get("client_id")
        user_id = data.get("user_id")

        # Broadcast to project room
        if project_id:
            await websocket_manager.broadcast_to_project(project_id, event_type, data)

        # Broadcast to client room
        if client_id:
            await websocket_manager.broadcast_to_client(client_id, event_type, data)

        # Send to developer
        if user_id:
            await websocket_manager.send_to_user(user_id, event_type, data)

    async def _handle_time_entry_event(self, event_type: str, data: Dict[str, Any]):
        """Handle time tracking events"""
        project_id = data.get("project_id")
        user_id = data.get("user_id")

        # Send to developer dashboard
        if user_id:
            await websocket_manager.send_to_user(user_id, event_type, data)

        # Update project room with time tracking info
        if project_id:
            await websocket_manager.broadcast_to_project(
                project_id,
                event_type,
                {
                    "project_id": project_id,
                    "status": data.get("status"),
                    "duration": data.get("duration"),
                    "timestamp": data.get("timestamp"),
                },
            )

    async def _handle_invoice_event(self, event_type: str, data: Dict[str, Any]):
        """Handle invoice-related events"""
        client_id = data.get("client_id")
        project_id = data.get("project_id")
        user_id = data.get("user_id")

        # Broadcast to client room
        if client_id:
            await websocket_manager.broadcast_to_client(client_id, event_type, data)

        # Broadcast to project room
        if project_id:
            await websocket_manager.broadcast_to_project(project_id, event_type, data)

        # Send to developer
        if user_id:
            await websocket_manager.send_to_user(user_id, event_type, data)

    async def _handle_client_event(self, event_type: str, data: Dict[str, Any]):
        """Handle client portal events"""
        client_id = data.get("client_id")
        project_id = data.get("project_id")
        user_id = data.get("user_id")

        # Send to developer dashboard
        if user_id:
            await websocket_manager.send_to_user(user_id, event_type, data)

        # Broadcast to all developers for activity monitoring
        await websocket_manager.broadcast_to_developers(event_type, data)

    async def _handle_system_event(self, event_type: str, data: Dict[str, Any]):
        """Handle system-wide events"""
        user_id = data.get("user_id")

        if user_id:
            await websocket_manager.send_to_user(user_id, event_type, data)
        else:
            # Broadcast to all developers
            await websocket_manager.broadcast_to_developers(event_type, data)

    async def _handle_approval_decision(self, data: Dict[str, Any]):
        """Handle approval decision with automated workflows"""
        approval_id = data.get("approval_id")
        status = data.get("status")
        project_id = data.get("project_id")

        # Trigger automated workflows based on approval decision
        if status == "approved":
            await self._trigger_approval_workflows(approval_id, project_id)

    async def _trigger_approval_workflows(self, approval_id: str, project_id: str):
        """Trigger automated workflows after approval"""
        try:
            # Example workflows:
            # 1. Auto-advance project milestone
            # 2. Generate invoice if applicable
            # 3. Send client notification
            # 4. Update project timeline

            workflow_data = {
                "approval_id": approval_id,
                "project_id": project_id,
                "workflow_type": "post_approval",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            await self.emit_event(EventType.SYSTEM_ALERT, workflow_data)

        except Exception as e:
            logger.error(f"Error triggering approval workflows: {e}")

    # Public API methods
    async def emit_event(self, event_type: EventType, data: Dict[str, Any]):
        """Emit a real-time event"""
        event = {"type": event_type, "data": data}
        await self.event_queue.put(event)

    async def emit_approval_created(self, approval: ClientApproval, user_id: str):
        """Emit approval created event"""
        data = {
            "approval_id": str(approval.id),
            "project_id": str(approval.project_id) if approval.project_id else None,
            "client_id": str(approval.client_id) if approval.client_id else None,
            "user_id": user_id,
            "title": approval.title,
            "description": approval.description,
            "status": approval.status,
            "priority": approval.priority,
        }
        await self.emit_event(EventType.APPROVAL_CREATED, data)

    async def emit_approval_updated(
        self, approval: ClientApproval, user_id: str, changes: Dict[str, Any]
    ):
        """Emit approval updated event"""
        data = {
            "approval_id": str(approval.id),
            "project_id": str(approval.project_id) if approval.project_id else None,
            "client_id": str(approval.client_id) if approval.client_id else None,
            "user_id": user_id,
            "status": approval.status,
            "changes": changes,
        }
        await self.emit_event(EventType.APPROVAL_UPDATED, data)

    async def emit_project_updated(
        self, project: Project, user_id: str, changes: Dict[str, Any]
    ):
        """Emit project updated event"""
        data = {
            "project_id": str(project.id),
            "client_id": str(project.client_id),
            "user_id": user_id,
            "title": project.title,
            "status": project.status,
            "completion_percentage": float(project.completion_percentage or 0),
            "changes": changes,
        }
        await self.emit_event(EventType.PROJECT_UPDATED, data)

    async def emit_file_uploaded(
        self, file_info: Dict[str, Any], project_id: str, client_id: str
    ):
        """Emit file uploaded event"""
        data = {
            "project_id": project_id,
            "client_id": client_id,
            "file_info": file_info,
        }
        await self.emit_event(EventType.PROJECT_FILE_UPLOADED, data)

    async def emit_client_activity(
        self, client_id: str, activity_type: str, details: Dict[str, Any]
    ):
        """Emit client activity event"""
        data = {
            "client_id": client_id,
            "activity_type": activity_type,
            "details": details,
        }
        await self.emit_event(EventType.CLIENT_ACTIVITY, data)

    async def emit_notification(
        self,
        user_id: str,
        title: str,
        message: str,
        notification_type: str = "info",
        action_url: Optional[str] = None,
    ):
        """Emit notification event"""
        data = {
            "user_id": user_id,
            "title": title,
            "message": message,
            "type": notification_type,
            "action_url": action_url,
        }
        await self.emit_event(EventType.NOTIFICATION, data)

    def get_stats(self) -> Dict[str, Any]:
        """Get real-time service statistics"""
        return {
            "processing": self.processing,
            "queue_size": self.event_queue.qsize(),
            "websocket_stats": websocket_manager.get_connection_stats(),
        }


# Global real-time service instance
realtime_service = RealtimeService()
