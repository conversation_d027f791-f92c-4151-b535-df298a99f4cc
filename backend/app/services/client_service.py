"""
Client service for DevHQ Backend
Handles client-related business logic and operations
"""

import uuid
from typing import List, Optional

from sqlalchemy import and_, case, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

from app.core.exceptions import ConflictError, NotFoundError
from app.core.pagination import paginate_query
from app.core.query_optimization import QueryOptimizer, QueryProfiler
from app.models.activity import ActivityLog
from app.models.client import Client
from app.models.invoice import Invoice
from app.models.project import Project
from app.models.user import User
from app.schemas.client import ClientCreate, ClientUpdate


class ClientService:
    """Service for managing client operations"""

    def __init__(self, db: AsyncSession):
        """
        Initialize ClientService with database session

        Args:
            db (AsyncSession): Database session
        """
        self.db = db

    async def create_client(
        self, user_id: uuid.UUID, client_data: ClientCreate
    ) -> Client:
        """
        Create a new client for the specified user

        Args:
            user_id (uuid.UUID): ID of user creating the client
            client_data (ClientCreate): Client creation data

        Returns:
            Client: Created client object

        Raises:
            ConflictError: If client with same email already exists

        Example:
            >>> client_service = ClientService(db_session)
            >>> client_data = ClientCreate(name="Acme Corp", email="<EMAIL>")
            >>> new_client = await client_service.create_client(user_id, client_data)
        """
        # Check for duplicate email if provided
        if client_data.email:
            stmt = select(Client).filter(
                Client.email == client_data.email,
                Client.user_id == user_id,
                Client.deleted_at.is_(None),
            )
            result = await self.db.execute(stmt)
            existing_client = result.scalar_one_or_none()
            if existing_client:
                raise ConflictError("Client with this email already exists")

        client = Client(user_id=user_id, **client_data.model_dump())
        self.db.add(client)
        await self.db.flush()
        await self.db.refresh(client)

        # Log activity
        self.db.add(
            ActivityLog(
                user_id=user_id,
                entity_type="client",
                entity_id=str(client.id),
                action="create",
                details=f"Created client {client.name}",
            )
        )
        await self.db.commit()

        return client

    async def get_client(self, user_id: uuid.UUID, client_id: uuid.UUID) -> Client:
        """
        Get a client by ID for the specified user

        Args:
            user_id (uuid.UUID): ID of user who owns the client
            client_id (uuid.UUID): ID of client to retrieve

        Returns:
            Client: Client object

        Raises:
            NotFoundError: If client is not found
        """
        stmt = select(Client).filter(
            Client.id == client_id,
            Client.user_id == user_id,
            Client.deleted_at.is_(None),
        )
        result = await self.db.execute(stmt)
        client = result.scalar_one_or_none()
        if not client:
            raise NotFoundError("Client not found")
        return client

    async def get_client_with_details(
        self, user_id: uuid.UUID, client_id: uuid.UUID
    ) -> dict:
        """
        Get client with related projects and invoices

        Args:
            user_id (uuid.UUID): ID of user who owns the client
            client_id (uuid.UUID): ID of client to retrieve

        Returns:
            dict: Client data with related projects and invoices

        Raises:
            NotFoundError: If client is not found
        """
        # Get client first
        client = await self.get_client(user_id, client_id)

        # Get related projects and invoices in separate efficient queries
        projects_stmt = select(Project).filter(
            and_(
                Project.client_id == client_id,
                Project.user_id == user_id,
                Project.deleted_at.is_(None),
            )
        )
        projects_result = await self.db.execute(projects_stmt)
        projects = projects_result.scalars().all()

        invoices_stmt = select(Invoice).filter(
            and_(
                Invoice.client_id == client_id,
                Invoice.user_id == user_id,
                Invoice.deleted_at.is_(None),
            )
        )
        invoices_result = await self.db.execute(invoices_stmt)
        invoices = invoices_result.scalars().all()

        return {
            **client.__dict__,
            "projects": [
                {
                    "id": str(p.id),
                    "name": p.title,  # Project model uses 'title' not 'name'
                    "status": p.status,
                    "billing_type": p.billing_type,
                }
                for p in projects
            ],
            "invoices": [
                {
                    "id": str(i.id),
                    "invoice_number": i.invoice_number,
                    "status": i.status,
                    "total_amount": str(i.total_amount),
                }
                for i in invoices
            ],
        }

    async def list_clients(
        self,
        user_id: uuid.UUID,
        q: Optional[str] = None,
        is_active: Optional[bool] = None,
        company: Optional[str] = None,
        email: Optional[str] = None,
        country: Optional[str] = None,
        workspace_id: Optional[uuid.UUID] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        page: int = 1,
        per_page: int = 25,
    ) -> dict:
        """
        List clients with filtering, sorting, and pagination

        Args:
            user_id (uuid.UUID): ID of user whose clients to list
            q (Optional[str]): Search query for name, email, or company
            is_active (Optional[bool]): Filter by active status
            company (Optional[str]): Filter by company name
            email (Optional[str]): Filter by email
            country (Optional[str]): Filter by country
            workspace_id (Optional[uuid.UUID]): Filter by workspace ID
            sort_by (str): Field to sort by (name, email, company, created_at, updated_at)
            sort_order (str): Sort order (asc or desc)
            page (int): Page number (1-indexed)
            per_page (int): Number of items per page

        Returns:
            dict: Paginated client list with metadata

        Example:
            >>> result = client_service.list_clients(
            ...     user_id=user_id,
            ...     q="acme",
            ...     is_active=True,
            ...     sort_by="name",
            ...     sort_order="asc",
            ...     page=1,
            ...     per_page=20
            ... )
            >>> print(f"Found {result['total']} clients")
        """
        # Build base query
        stmt = select(Client).filter(
            Client.user_id == user_id, Client.deleted_at.is_(None)
        )

        # Apply filters
        if is_active is not None:
            stmt = stmt.filter(Client.is_active == is_active)
        if workspace_id:
            stmt = stmt.filter(Client.workspace_id == workspace_id)
        if country:
            stmt = stmt.filter(Client.country.ilike(f"%{country}%"))
        if company:
            stmt = stmt.filter(Client.company.ilike(f"%{company}%"))
        if email:
            stmt = stmt.filter(Client.email.ilike(f"%{email}%"))

        # Add search filter
        if q:
            search_filter = or_(
                Client.name.ilike(f"%{q}%"),
                Client.email.ilike(f"%{q}%"),
                Client.company.ilike(f"%{q}%"),
            )
            stmt = stmt.filter(search_filter)

        # Add sorting
        sort_field_map = {
            "name": Client.name,
            "email": Client.email,
            "company": Client.company,
            "created_at": Client.created_at,
            "updated_at": Client.updated_at,
        }
        sort_column = sort_field_map.get(sort_by or "created_at", Client.created_at)
        if (sort_order or "desc").lower() == "desc":
            stmt = stmt.order_by(desc(sort_column))
        else:
            stmt = stmt.order_by(sort_column)

        # Get total count
        count_stmt = select(func.count()).select_from(stmt.subquery())
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()

        # Apply pagination
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)

        # Execute query
        result = await self.db.execute(stmt)
        clients = result.scalars().all()

        return {
            "items": [client.__dict__ for client in clients],
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page,
        }

    async def update_client(
        self, user_id: uuid.UUID, client_id: uuid.UUID, client_data: ClientUpdate
    ) -> Client:
        """
        Update a client with provided data

        Args:
            user_id (uuid.UUID): ID of user who owns the client
            client_id (uuid.UUID): ID of client to update
            client_data (ClientUpdate): Client update data

        Returns:
            Client: Updated client object

        Raises:
            NotFoundError: If client is not found
            ConflictError: If email already exists for another client
        """
        client = await self.get_client(user_id, client_id)

        # Check for duplicate email if email is being updated
        update_data = client_data.model_dump(exclude_unset=True)
        if "email" in update_data and update_data["email"]:
            stmt = select(Client).filter(
                Client.email == update_data["email"],
                Client.user_id == user_id,
                Client.id != client_id,  # Exclude current client
                Client.deleted_at.is_(None),
            )
            result = await self.db.execute(stmt)
            existing_client = result.scalar_one_or_none()
            if existing_client:
                raise ConflictError("Client with this email already exists")

        for field, value in update_data.items():
            setattr(client, field, value)

        self.db.add(client)
        await self.db.flush()
        await self.db.refresh(client)

        # Log activity
        self.db.add(
            ActivityLog(
                user_id=user_id,
                entity_type="client",
                entity_id=str(client.id),
                action="update",
                details=f"Updated client {client.name}",
            )
        )
        await self.db.commit()

        return client

    async def delete_client(self, user_id: uuid.UUID, client_id: uuid.UUID) -> None:
        """
        Soft delete a client

        Args:
            user_id (uuid.UUID): ID of user who owns the client
            client_id (uuid.UUID): ID of client to delete

        Raises:
            NotFoundError: If client is not found
        """
        client = await self.get_client(user_id, client_id)
        client.soft_delete()
        await self.db.flush()

        # Log activity
        self.db.add(
            ActivityLog(
                user_id=user_id,
                entity_type="client",
                entity_id=str(client.id),
                action="delete",
                details=f"Deleted client {client.name}",
            )
        )
        await self.db.commit()

    async def get_client_statistics(
        self, user_id: uuid.UUID, client_id: uuid.UUID
    ) -> dict:
        """
        Get client statistics including projects, invoices, and revenue

        Args:
            user_id (uuid.UUID): ID of user who owns the client
            client_id (uuid.UUID): ID of client to get statistics for

        Returns:
            dict: Client statistics including total projects, invoices, revenue, etc.

        Example:
            >>> stats = await client_service.get_client_statistics(user_id, client_id)
            >>> print(f"Total revenue: ${stats['total_revenue']}")
        """
        client = await self.get_client(user_id, client_id)

        # Use a single query with joins for better performance

        # Get all statistics in a single query
        stats_query = (
            select(
                func.count(Project.id).label("total_projects"),
                func.count(Invoice.id).label("total_invoices"),
                func.sum(
                    case((Invoice.status == "paid", Invoice.total_amount), else_=0)
                ).label("total_revenue"),
                func.sum(
                    case(
                        (
                            Invoice.status.in_(["sent", "viewed", "overdue"]),
                            Invoice.total_amount,
                        ),
                        else_=0,
                    )
                ).label("outstanding_balance"),
                func.avg(Invoice.total_amount).label("average_invoice_amount"),
            )
            .select_from(
                Client.__table__.outerjoin(Project.__table__).outerjoin(
                    Invoice.__table__
                )
            )
            .filter(
                and_(
                    Client.id == client_id,
                    Client.user_id == user_id,
                    Client.deleted_at.is_(None),
                )
            )
        )

        result = await self.db.execute(stats_query)
        stats = result.first()

        return {
            "total_projects": stats.total_projects or 0,
            "total_invoices": stats.total_invoices or 0,
            "total_revenue": float(stats.total_revenue or 0),
            "outstanding_balance": float(stats.outstanding_balance or 0),
            "average_invoice_amount": float(stats.average_invoice_amount or 0),
        }
