"""
Scalability Demo Service for DevHQ Backend
Demonstrates usage of background tasks, caching, and WebSocket enhancements
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.core.background_tasks import background_task, background_task_manager
from app.core.cache import cache_manager, cached, cached_sync
from app.core.websocket_manager import websocket_manager
from app.models.user import User

logger = logging.getLogger(__name__)


class ScalabilityDemoService:
    """Demo service showing scalability features"""

    def __init__(self, db: Session):
        self.db = db

    @background_task(priority=3, max_retries=2, name="send_notification_email")
    async def send_notification_email(
        self, user_id: str, subject: str, body: str
    ) -> Dict:
        """
        Send notification email as a background task

        This demonstrates:
        - Background task execution with priority
        - Retry logic
        - Async operation handling
        """
        logger.info(f"Sending email to user {user_id}: {subject}")

        # Simulate email sending (replace with actual email service)
        await asyncio.sleep(2)  # Simulate network delay

        # In a real implementation, you would use an email service here
        # email_service.send(user_id, subject, body)

        result = {
            "status": "sent",
            "user_id": user_id,
            "subject": subject,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        logger.info(f"Email sent successfully to user {user_id}")
        return result

    @background_task(priority=2, name="generate_project_report")
    async def generate_project_report(self, project_id: str, user_id: str) -> Dict:
        """
        Generate project report as a background task

        This demonstrates:
        - CPU-intensive task offloading
        - Progress reporting via WebSocket
        - Result caching
        """
        logger.info(f"Generating report for project {project_id}")

        # Notify user via WebSocket that report generation started
        await websocket_manager.send_to_user(
            user_id,
            "report_generation_started",
            {
                "project_id": project_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )

        # Simulate intensive report generation
        for i in range(10):
            # Update progress
            progress = (i + 1) * 10
            await websocket_manager.send_to_user(
                user_id,
                "report_generation_progress",
                {
                    "project_id": project_id,
                    "progress": progress,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )

            # Simulate work
            await asyncio.sleep(0.5)

        # Generate report data
        report_data = {
            "project_id": project_id,
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "metrics": {
                "total_hours": 40.5,
                "billable_hours": 35.2,
                "completion_rate": 87.5,
            },
            "report_id": str(uuid.uuid4()),
        }

        # Cache the report
        cache_key = f"report:{project_id}:{report_data['report_id']}"
        await cache_manager.set(cache_key, report_data, ttl=3600)  # Cache for 1 hour

        # Notify completion
        await websocket_manager.send_to_user(
            user_id,
            "report_generation_completed",
            {
                "project_id": project_id,
                "report_id": report_data["report_id"],
                "timestamp": report_data["generated_at"],
            },
        )

        logger.info(f"Report generated for project {project_id}")
        return report_data

    @cached(ttl=300, key_prefix="user_dashboard")
    async def get_user_dashboard_data(self, user_id: str) -> Dict:
        """
        Get user dashboard data with caching

        This demonstrates:
        - Function result caching
        - Automatic cache key generation
        - TTL management
        """
        logger.info(f"Fetching dashboard data for user {user_id} (not cached)")

        # Simulate database queries
        await asyncio.sleep(1)  # Simulate query time

        # In a real implementation, you would query the database here
        user = self.db.query(User).filter(User.id == user_id).first()

        dashboard_data = {
            "user_id": user_id,
            "user_name": f"{user.first_name} {user.last_name}" if user else "Unknown",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "stats": {
                "active_projects": 3,
                "pending_approvals": 2,
                "upcoming_deadlines": 1,
                "recent_activity": 5,
            },
            "recent_projects": [
                {"id": "proj1", "name": "Website Redesign", "status": "in_progress"},
                {"id": "proj2", "name": "Mobile App", "status": "completed"},
                {"id": "proj3", "name": "API Integration", "status": "pending"},
            ],
        }

        return dashboard_data

    @cached_sync(ttl=600, key_prefix="project_metrics")
    def get_project_metrics_sync(self, project_id: str) -> Dict:
        """
        Get project metrics synchronously with caching

        This demonstrates:
        - Synchronous function caching
        - Longer TTL for less frequently changing data
        """
        logger.info(f"Fetching project metrics for {project_id} (not cached)")

        # Simulate database query
        time.sleep(0.5)  # Simulate query time

        # In a real implementation, you would query the database here
        metrics = {
            "project_id": project_id,
            "total_hours": 45.2,
            "billable_hours": 38.7,
            "completion_percentage": 78.5,
            "milestones_completed": 3,
            "total_milestones": 5,
            "team_productivity": 85.2,
        }

        return metrics

    async def process_bulk_operation(self, user_id: str, operations: List[Dict]) -> str:
        """
        Process bulk operations using background tasks

        This demonstrates:
        - Bulk task submission
        - Task tracking
        - Progress reporting
        """
        logger.info(
            f"Processing bulk operation for user {user_id} with {len(operations)} items"
        )

        # Submit tasks and collect task IDs
        task_ids = []
        for i, operation in enumerate(operations):
            # Submit each operation as a background task
            task_id = await background_task_manager.submit_task(
                self._process_single_operation,
                operation,
                name=f"bulk_operation_{i}",
                priority=2 if operation.get("priority") == "high" else 1,
            )
            task_ids.append(task_id)

        # Notify user that bulk operation started
        await websocket_manager.send_to_user(
            user_id,
            "bulk_operation_started",
            {
                "total_operations": len(operations),
                "task_ids": task_ids,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )

        return f"Bulk operation submitted with {len(task_ids)} tasks"

    async def _process_single_operation(self, operation: Dict) -> Dict:
        """Process a single operation (used by bulk operations)"""
        operation_type = operation.get("type", "unknown")
        operation_id = operation.get("id", str(uuid.uuid4()))

        logger.info(f"Processing operation {operation_id} of type {operation_type}")

        # Simulate processing time
        await asyncio.sleep(1)

        result = {
            "operation_id": operation_id,
            "type": operation_type,
            "status": "completed",
            "processed_at": datetime.now(timezone.utc).isoformat(),
        }

        logger.info(f"Operation {operation_id} completed")
        return result

    def get_system_stats(self) -> Dict:
        """Get system statistics from all scalability components"""
        return {
            "background_tasks": background_task_manager.get_stats(),
            "cache": cache_manager.get_stats(),
            "websockets": websocket_manager.get_connection_stats(),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# Example usage functions
async def demo_background_tasks():
    """Demonstrate background task usage"""
    # Send email in background
    email_task_id = await background_task_manager.submit_task(
        send_demo_email,
        "user123",
        "Welcome!",
        "Welcome to our platform!",
        name="welcome_email",
        priority=3,
    )

    print(f"Email task submitted: {email_task_id}")

    # Check task status
    status = background_task_manager.get_task_status(email_task_id)
    print(f"Task status: {status}")


async def send_demo_email(user_id: str, subject: str, body: str):
    """Demo email sending function"""
    print(f"Sending email to {user_id}: {subject}")
    await asyncio.sleep(2)  # Simulate email sending
    print(f"Email sent to {user_id}")


# Decorated background task example
@background_task(priority=4, name="critical_system_task")
async def perform_critical_operation(data: Dict) -> Dict:
    """
    Critical system operation that runs with highest priority
    """
    print(f"Performing critical operation with data: {data}")
    await asyncio.sleep(1)
    return {"status": "completed", "data": data}


# Example of how to use the demo service
async def run_scalability_demo():
    """Run a complete scalability demo"""
    print("Starting scalability demo...")

    # Initialize components
    await cache_manager.connect()
    await background_task_manager.start()

    try:
        # Demonstrate background tasks
        await demo_background_tasks()

        # Demonstrate cached functions
        @cached(ttl=60, key_prefix="demo")
        async def get_demo_data(param: str) -> Dict:
            print(f"Computing demo data for {param}")
            await asyncio.sleep(1)  # Simulate computation
            return {"param": param, "value": "demo_result", "timestamp": time.time()}

        # First call (not cached)
        result1 = await get_demo_data("test1")
        print(f"First call result: {result1}")

        # Second call (cached)
        result2 = await get_demo_data("test1")
        print(f"Second call result: {result2}")

        # Wait for background tasks to complete
        await asyncio.sleep(3)

        # Show stats
        stats = {
            "background_tasks": background_task_manager.get_stats(),
            "cache": cache_manager.get_stats(),
        }
        print(f"System stats: {stats}")

    finally:
        # Cleanup
        await background_task_manager.stop()
        await cache_manager.disconnect()

    print("Scalability demo completed!")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(run_scalability_demo())
