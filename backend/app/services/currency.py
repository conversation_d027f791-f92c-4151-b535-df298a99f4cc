"""
Currency conversion service using ExchangeRate-API
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, timedelta

import aiohttp
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.core.error_handlers import BusinessLogicError, ValidationException

logger = logging.getLogger(__name__)


class CurrencyService:
    """Service for currency conversion and exchange rate management"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.base_url = "https://v6.exchangerate-api.com/v6"
        self.api_key = settings.currency_api_key
        self.cache_duration = timedelta(hours=1)  # Cache rates for 1 hour
        self._rate_cache: Dict[str, Tuple[Dict, datetime]] = {}

    async def get_exchange_rates(self, base_currency: str = "USD") -> Dict[str, float]:
        """
        Get current exchange rates for a base currency
        
        Args:
            base_currency: The base currency code (e.g., 'USD', 'EUR')
            
        Returns:
            Dictionary of currency codes to exchange rates
        """
        try:
            # Check cache first
            cache_key = f"rates_{base_currency}"
            if cache_key in self._rate_cache:
                rates, cached_at = self._rate_cache[cache_key]
                if datetime.now() - cached_at < self.cache_duration:
                    return rates

            # Fetch from API
            url = f"{self.base_url}/{self.api_key}/latest/{base_currency}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        raise BusinessLogicError(f"Currency API error: {response.status}")
                    
                    data = await response.json()
                    
                    if data.get("result") != "success":
                        raise BusinessLogicError(f"Currency API error: {data.get('error-type', 'Unknown error')}")
                    
                    rates = data.get("conversion_rates", {})
                    
                    # Cache the results
                    self._rate_cache[cache_key] = (rates, datetime.now())
                    
                    return rates
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error fetching exchange rates: {e}")
            raise BusinessLogicError("Unable to fetch current exchange rates")
        except Exception as e:
            logger.error(f"Error fetching exchange rates: {e}")
            raise BusinessLogicError("Currency conversion service unavailable")

    async def convert_currency(
        self, 
        amount: Decimal, 
        from_currency: str, 
        to_currency: str
    ) -> Decimal:
        """
        Convert amount from one currency to another
        
        Args:
            amount: Amount to convert
            from_currency: Source currency code
            to_currency: Target currency code
            
        Returns:
            Converted amount as Decimal
        """
        if from_currency == to_currency:
            return amount
            
        try:
            # Get rates with from_currency as base
            rates = await self.get_exchange_rates(from_currency)
            
            if to_currency not in rates:
                raise ValidationException(f"Currency '{to_currency}' not supported")
            
            rate = Decimal(str(rates[to_currency]))
            converted_amount = amount * rate
            
            # Round to 2 decimal places for currency
            return converted_amount.quantize(Decimal('0.01'))
            
        except Exception as e:
            logger.error(f"Error converting {amount} from {from_currency} to {to_currency}: {e}")
            raise

    async def get_supported_currencies(self) -> Dict[str, str]:
        """
        Get list of supported currencies with their names
        
        Returns:
            Dictionary mapping currency codes to currency names
        """
        try:
            url = f"{self.base_url}/{self.api_key}/codes"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        raise BusinessLogicError(f"Currency API error: {response.status}")
                    
                    data = await response.json()
                    
                    if data.get("result") != "success":
                        raise BusinessLogicError(f"Currency API error: {data.get('error-type', 'Unknown error')}")
                    
                    # Convert list of [code, name] pairs to dictionary
                    currencies = {}
                    for code, name in data.get("supported_codes", []):
                        currencies[code] = name
                    
                    return currencies
                    
        except Exception as e:
            logger.error(f"Error fetching supported currencies: {e}")
            # Return common currencies as fallback
            return {
                "USD": "United States Dollar",
                "EUR": "Euro",
                "GBP": "British Pound Sterling",
                "JPY": "Japanese Yen",
                "CAD": "Canadian Dollar",
                "AUD": "Australian Dollar",
                "CHF": "Swiss Franc",
                "CNY": "Chinese Yuan",
                "KES": "Kenyan Shilling",
                "NGN": "Nigerian Naira",
                "ZAR": "South African Rand",
                "GHS": "Ghanaian Cedi",
                "UGX": "Ugandan Shilling",
                "TZS": "Tanzanian Shilling",
            }

    async def convert_user_balances(
        self, 
        user_id: str, 
        old_currency: str, 
        new_currency: str
    ) -> Dict[str, Decimal]:
        """
        Convert user's financial data when changing default currency
        
        Args:
            user_id: User ID
            old_currency: Previous currency
            new_currency: New currency
            
        Returns:
            Dictionary with conversion details
        """
        try:
            if old_currency == new_currency:
                return {"converted": False, "rate": Decimal("1.0")}
            
            # Get conversion rate
            rates = await self.get_exchange_rates(old_currency)
            if new_currency not in rates:
                raise ValidationException(f"Currency '{new_currency}' not supported")
            
            rate = Decimal(str(rates[new_currency]))
            
            # Here you would update user's financial data
            # For now, just return the conversion rate
            return {
                "converted": True,
                "rate": rate,
                "old_currency": old_currency,
                "new_currency": new_currency,
                "conversion_date": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error converting user balances: {e}")
            raise

    def get_currency_symbol(self, currency_code: str) -> str:
        """
        Get currency symbol for a given currency code
        
        Args:
            currency_code: ISO currency code
            
        Returns:
            Currency symbol string
        """
        symbols = {
            "USD": "$",
            "EUR": "€",
            "GBP": "£",
            "JPY": "¥",
            "CAD": "C$",
            "AUD": "A$",
            "CHF": "CHF",
            "CNY": "¥",
            "KES": "KSh",
            "NGN": "₦",
            "ZAR": "R",
            "GHS": "₵",
            "UGX": "USh",
            "TZS": "TSh",
            "INR": "₹",
            "BRL": "R$",
            "MXN": "$",
            "RUB": "₽",
            "KRW": "₩",
            "SGD": "S$",
            "HKD": "HK$",
            "NOK": "kr",
            "SEK": "kr",
            "DKK": "kr",
            "PLN": "zł",
            "CZK": "Kč",
            "HUF": "Ft",
            "RON": "lei",
            "BGN": "лв",
            "HRK": "kn",
            "TRY": "₺",
            "ILS": "₪",
            "AED": "د.إ",
            "SAR": "﷼",
            "QAR": "﷼",
            "KWD": "د.ك",
            "BHD": ".د.ب",
            "OMR": "﷼",
            "JOD": "د.ا",
            "LBP": "ل.ل",
            "EGP": "£",
            "MAD": "د.م.",
            "TND": "د.ت",
            "DZD": "د.ج",
            "LYD": "ل.د",
            "SDG": "ج.س.",
            "ETB": "Br",
            "RWF": "RF",
            "BWP": "P",
            "SZL": "L",
            "LSL": "L",
            "MWK": "MK",
            "ZMW": "ZK",
            "AOA": "Kz",
            "MZN": "MT",
            "MGA": "Ar",
            "MUR": "₨",
            "SCR": "₨",
            "KMF": "CF",
            "DJF": "Fdj",
            "SOS": "S",
            "ERN": "Nfk",
        }
        
        return symbols.get(currency_code, currency_code)
