"""
Authentication Service for DevHQ
"""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from sqlalchemy.orm import Session

from app.core.auth import (create_access_token, create_refresh_token,
                           get_password_hash, verify_password, verify_token)
from app.core.validators import validate_password_strength
from app.models.user import User
from app.schemas.auth import (TokenResponse, UserLoginRequest,
                              UserRegisterRequest)


class AuthService:
    """Service for handling authentication operations"""

    def __init__(self, db: Optional[Session] = None):
        self.db = db

    def register_user(self, user_data: UserRegisterRequest, db: Session) -> User:
        """Register a new user"""
        # Check if user already exists
        existing_user = (
            db.query(User)
            .filter(
                (User.email == user_data.email) | (User.username == user_data.username)
            )
            .first()
        )

        if existing_user:
            if existing_user.email == user_data.email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered",
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken",
                )

        # Create new user
        hashed_password = get_password_hash(user_data.password)
        user = User(
            email=user_data.email,
            username=user_data.username,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            hashed_password=hashed_password,
            is_active=True,
            is_verified=False,
        )

        db.add(user)
        db.commit()
        db.refresh(user)

        return user

    def login_user(self, login_data: UserLoginRequest, db: Session) -> TokenResponse:
        """Authenticate user and return tokens"""
        # Find user by email or username
        user = (
            db.query(User)
            .filter(
                (User.email == login_data.email_or_username)
                | (User.username == login_data.email_or_username)
            )
            .first()
        )

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials"
            )

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Account is inactive"
            )

        if not user.is_verified:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Email not verified"
            )

        if not verify_password(login_data.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials"
            )

        # Create tokens
        access_token = create_access_token(data={"sub": str(user.id)})
        refresh_token = create_refresh_token(data={"sub": str(user.id)})

        return TokenResponse(
            access_token=access_token, refresh_token=refresh_token, token_type="bearer"
        )

    def verify_email(self, token: str, db: Session) -> bool:
        """Verify user email with token"""
        try:
            payload = verify_token(token)
            user_id = payload.get("sub")

            if not user_id:
                return False

            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            user.is_verified = True
            db.commit()

            return True

        except Exception:
            return False

    def refresh_token(self, refresh_token: str, db: Session) -> TokenResponse:
        """Refresh access token using refresh token"""
        try:
            payload = verify_token(refresh_token)
            user_id = payload.get("sub")

            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token",
                )

            user = db.query(User).filter(User.id == user_id).first()
            if not user or not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive",
                )

            # Create new tokens
            access_token = create_access_token(data={"sub": str(user.id)})
            new_refresh_token = create_refresh_token(data={"sub": str(user.id)})

            return TokenResponse(
                access_token=access_token,
                refresh_token=new_refresh_token,
                token_type="bearer",
            )

        except Exception:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token"
            )

    def get_current_user(self, token: str, db: Session) -> User:
        """Get current user from token"""
        try:
            payload = verify_token(token)
            user_id = payload.get("sub")

            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
                )

            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
                )

            return user

        except Exception:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
            )

    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """Validate password strength"""
        return validate_password_strength(password)

    def request_password_reset(self, email: str, db: Session) -> bool:
        """Request password reset"""
        user = db.query(User).filter(User.email == email).first()
        if not user:
            return False

        # In a real implementation, you would send an email here
        return True

    def reset_password(self, token: str, new_password: str, db: Session) -> bool:
        """Reset password with token"""
        try:
            payload = verify_token(token)
            user_id = payload.get("sub")

            if not user_id:
                return False

            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            user.hashed_password = get_password_hash(new_password)
            db.commit()

            return True

        except Exception:
            return False
