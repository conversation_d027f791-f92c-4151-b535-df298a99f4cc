"""User service for DevHQ Backend
Handles user-related business logic and operations
"""

import uuid
from typing import Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import NotFoundError
from app.core.query_optimization import QueryOptimizer, QueryProfiler
from app.core.upload import upload_service
from app.models.activity import ActivityLog
from app.models.user import User, UserSettings
from app.schemas.user import UserProfileUpdate, UserSettingsUpdate


class UserService:
    """Service for managing user operations"""

    def __init__(self, db: AsyncSession):
        """
        Initialize UserService with database session

        Args:
            db (AsyncSession): Database session
        """
        self.db = db

    async def get_user_profile(self, user_id: uuid.UUID) -> User:
        """
        Get user profile by ID

        Args:
            user_id (uuid.UUID): User ID to retrieve

        Returns:
            User: User profile object

        Raises:
            NotFoundError: If user is not found
        """
        result = await self.db.execute(select(User).filter(User.id == user_id))
        user = result.scalars().first()
        if not user:
            raise NotFoundError("User not found")
        return user

    async def update_user_profile(
        self, user_id: uuid.UUID, profile_data: UserProfileUpdate
    ) -> User:
        """
        Update user profile with provided data

        Args:
            user_id (uuid.UUID): ID of user to update
            profile_data (UserProfileUpdate): Profile update data

        Returns:
            User: Updated user profile

        Example:
            >>> user_service = UserService(db_session)
            >>> update_data = UserProfileUpdate(first_name="John", last_name="Doe")
            >>> updated_user = await user_service.update_user_profile(user_id, update_data)
        """
        user = await self.get_user_profile(user_id)

        updated_fields = []
        if profile_data.first_name is not None:
            user.first_name = profile_data.first_name
            updated_fields.append("first_name")
        if profile_data.last_name is not None:
            user.last_name = profile_data.last_name
            updated_fields.append("last_name")
        if profile_data.phone is not None:
            user.phone = profile_data.phone
            updated_fields.append("phone")
        if profile_data.bio is not None:
            user.bio = profile_data.bio
            updated_fields.append("bio")

        if updated_fields:
            self.db.add(user)
            await self.db.commit()

            # Get updated user data
            result = await self.db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()

            # Log activity
            self.db.add(
                ActivityLog(
                    user_id=user.id,
                    entity_type="user",
                    entity_id=str(user.id),
                    action="update_profile",
                    details=f"Updated fields: {', '.join(updated_fields)}",
                )
            )
            await self.db.commit()

        return user

    async def upload_user_avatar(
        self, user_id: uuid.UUID, file_bytes: bytes, content_type: str
    ) -> dict:
        """
        Upload user avatar image

        Args:
            user_id (uuid.UUID): ID of user uploading avatar
            file_bytes (bytes): Avatar image bytes
            content_type (str): MIME type of the image

        Returns:
            dict: Upload result with avatar URL and metadata

        Raises:
            ValueError: If file type is not supported
            Exception: If upload service is not configured

        Example:
            >>> with open("avatar.jpg", "rb") as f:
            ...     file_bytes = f.read()
            ...     result = await user_service.upload_user_avatar(user_id, file_bytes, "image/jpeg")
        """
        user = await self.get_user_profile(user_id)

        # Validate content type
        if content_type not in {"image/jpeg", "image/png", "image/gif", "image/webp"}:
            raise ValueError("Invalid file type. Only images are allowed")

        uploaded = upload_service.upload_avatar(file_bytes, public_id=str(user.id))

        if not uploaded:
            raise Exception("Upload service not configured")

        # Save avatar URL
        user.avatar_url = uploaded.url
        self.db.add(user)
        await self.db.commit()

        # Get updated user data
        result = await self.db.execute(select(User).filter(User.id == user_id))
        user = result.scalars().first()

        return {
            "url": uploaded.url,
            "content_type": uploaded.content_type,
            "width": uploaded.width,
            "height": uploaded.height,
            "size_bytes": uploaded.size_bytes,
        }

    async def get_user_settings(self, user_id: uuid.UUID) -> UserSettings:
        """
        Get or create user settings

        Args:
            user_id (uuid.UUID): ID of user to get settings for

        Returns:
            UserSettings: User settings object
        """
        result = await self.db.execute(
            select(UserSettings).filter(
                UserSettings.user_id == user_id, UserSettings.deleted_at.is_(None)
            )
        )
        settings = result.scalars().first()

        if not settings:
            # Create default if missing
            settings = UserSettings(user_id=user_id)
            self.db.add(settings)
            await self.db.commit()

            # Get the newly created settings
            result = await self.db.execute(
                select(UserSettings).filter(
                    UserSettings.user_id == user_id, UserSettings.deleted_at.is_(None)
                )
            )
            settings = result.scalars().first()

        return settings

    async def update_user_settings(
        self, user_id: uuid.UUID, settings_data: UserSettingsUpdate
    ) -> UserSettings:
        """
        Update user settings with provided data

        Args:
            user_id (uuid.UUID): ID of user to update settings for
            settings_data (UserSettingsUpdate): Settings update data

        Returns:
            UserSettings: Updated user settings
        """
        settings = await self.get_user_settings(user_id)

        # Apply partial updates
        for field, value in settings_data.model_dump(exclude_unset=True).items():
            setattr(settings, field, value)

        self.db.add(settings)
        await self.db.commit()

        # Get updated settings
        result = await self.db.execute(
            select(UserSettings).filter(
                UserSettings.id == settings.id, UserSettings.deleted_at.is_(None)
            )
        )
        settings = result.scalars().first()

        return settings

    async def delete_user_account(self, user_id: uuid.UUID) -> dict:
        """
        Soft delete user account and related settings

        Args:
            user_id (uuid.UUID): ID of user to delete

        Returns:
            dict: Deletion result with success message
        """
        user = await self.get_user_profile(user_id)

        # Soft delete user and related settings
        user.soft_delete()

        # Log activity
        self.db.add(
            ActivityLog(
                user_id=user.id,
                entity_type="user",
                entity_id=str(user.id),
                action="delete_account",
                details="User soft-deleted account",
            )
        )

        result = await self.db.execute(
            select(UserSettings).filter(
                UserSettings.user_id == user_id, UserSettings.deleted_at.is_(None)
            )
        )
        settings = result.scalars().first()
        if settings:
            settings.soft_delete()

        await self.db.commit()

        return {"message": "Account deleted successfully", "success": True}
