"""Database configuration and session management
Handles PostgreSQL connections with SQLAlchemy
"""

import logging
from datetime import datetime, timezone
from typing import Async<PERSON>enerator, Generator, Optional

from sqlalchemy import MetaD<PERSON>, create_engine, event, text
from sqlalchemy.exc import DisconnectionError, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import QueuePool

from app.config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy engine configuration with improved connection pooling
engine = create_engine(
    settings.database_url,
    # Connection pool settings - optimized for production
    poolclass=QueuePool,
    pool_size=30,  # Increased from 20
    max_overflow=50,  # Increased from 0 to allow overflow connections
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=300,  # Recycle connections every 5 minutes
    pool_timeout=30,  # Timeout for getting connection from pool
    echo=settings.debug,  # Log SQL statements in debug mode
    # <PERSON>le disconnects gracefully
    pool_reset_on_return="rollback",
)

# Async engine for async operations
async_engine = create_async_engine(
    settings.database_url.replace("postgresql://", "postgresql+asyncpg://"),
    pool_size=30,
    max_overflow=50,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_timeout=30,
    echo=settings.debug,
)

# Session factory with proper configuration
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False,  # Don't expire objects on commit
)

# Async session factory
AsyncSessionLocal = sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=async_engine,
    expire_on_commit=False,
)

# Metadata for migrations
metadata = MetaData()

# Base class for all models
Base = declarative_base(metadata=metadata)


def get_db() -> Generator[Session, None, None]:
    """
    Database dependency for FastAPI
    Provides database session with automatic cleanup
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}", exc_info=True)
        db.rollback()
        raise
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Async database dependency for FastAPI
    Provides async database session with automatic cleanup
    """
    async with AsyncSessionLocal() as db:
        try:
            yield db
        except Exception as e:
            logger.error(f"Async database session error: {e}", exc_info=True)
            await db.rollback()
            raise


def create_tables() -> None:
    """Create all database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}", exc_info=True)
        raise


def drop_tables() -> None:
    """Drop all database tables (use with caution!)"""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}", exc_info=True)
        raise


def check_database_health() -> dict:
    """
    Check if database is accessible with detailed information

    Returns:
        Dictionary with health check results
    """
    try:
        with engine.connect() as connection:
            # Execute a simple query to check connectivity
            result = connection.execute(text("SELECT 1"))
            is_healthy = result.fetchone()[0] == 1

            # Get database version
            version_result = connection.execute(text("SELECT version()"))
            version = (
                version_result.fetchone()[0]
                if version_result.rowcount > 0
                else "Unknown"
            )

            # Get connection info
            connection_info = get_database_info()

            return {
                "healthy": is_healthy,
                "version": version,
                "connection_info": connection_info,
                "checked_at": datetime.now(timezone.utc).isoformat(),
            }
    except SQLAlchemyError as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "checked_at": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error(
            f"Unexpected error during database health check: {e}", exc_info=True
        )
        return {
            "healthy": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "checked_at": datetime.now(timezone.utc).isoformat(),
        }


def get_database_info() -> dict:
    """Get database connection information (without exposing credentials)"""
    try:
        # Parse database URL to extract non-sensitive information
        from urllib.parse import urlparse

        parsed_url = urlparse(settings.database_url)

        return {
            "database_type": "postgresql",
            "host": parsed_url.hostname,
            "port": parsed_url.port,
            "database_name": parsed_url.path[1:] if parsed_url.path else None,
            "username": parsed_url.username,
            "connection_status": (
                "connected" if engine.pool.checkedout() >= 0 else "disconnected"
            ),
            "pool_status": {
                "size": engine.pool.size(),
                "checkedout": engine.pool.checkedout(),
                "overflow": engine.pool.overflow(),
            },
        }
    except Exception as e:
        logger.error(f"Error parsing database URL: {e}")
        return {
            "database_type": "unknown",
            "connection_status": "error",
            "error": str(e),
        }


def get_database_statistics() -> dict:
    """
    Get database performance statistics

    Returns:
        Dictionary with database statistics
    """
    try:
        with engine.connect() as connection:
            # Get connection pool statistics
            pool_stats = {
                "pool_size": engine.pool.size(),
                "checkedout_connections": engine.pool.checkedout(),
                "overflow_connections": engine.pool.overflow(),
            }

            # Get database size (PostgreSQL specific)
            try:
                size_result = connection.execute(
                    text(
                        """
                    SELECT pg_size_pretty(pg_database_size(current_database())) as size,
                           pg_database_size(current_database()) as size_bytes
                """
                    )
                )
                size_row = size_result.fetchone()
                if size_row:
                    database_size = {
                        "pretty": size_row[0],
                        "bytes": size_row[1],
                    }
                else:
                    database_size = {"pretty": "Unknown", "bytes": 0}
            except Exception:
                database_size = {"pretty": "Unknown", "bytes": 0}

            # Get active connections count
            try:
                conn_result = connection.execute(
                    text(
                        """
                    SELECT count(*) FROM pg_stat_activity 
                    WHERE datname = current_database() AND state = 'active'
                """
                    )
                )
                active_connections = (
                    conn_result.fetchone()[0] if conn_result.rowcount > 0 else 0
                )
            except Exception:
                active_connections = 0

            return {
                "pool_statistics": pool_stats,
                "database_size": database_size,
                "active_connections": active_connections,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
    except Exception as e:
        logger.error(f"Error getting database statistics: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# Add connection event listeners for better monitoring
@event.listens_for(engine, "connect")
def on_connect(dbapi_connection, connection_record):
    """Event listener for database connections"""
    logger.debug("New database connection established")


@event.listens_for(engine, "checkout")
def on_checkout(dbapi_connection, connection_record, connection_proxy):
    """Event listener for connection checkout"""
    logger.debug("Database connection checked out from pool")


@event.listens_for(engine, "checkin")
def on_checkin(dbapi_connection, connection_record):
    """Event listener for connection checkin"""
    logger.debug("Database connection checked back into pool")
