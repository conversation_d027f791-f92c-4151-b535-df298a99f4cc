# 📧 Resend Email Setup Guide for DevHQ

## 🚨 Current Status: Development Mode
- **Emails are logged to console** (not actually sent)
- **Perfect for development and testing**
- **No real emails sent** to avoid spam during development

## 🔧 For Production: Set Up Verified Domain

### Step 1: Add Domain to Resend
1. Go to [Resend Dashboard](https://resend.com/domains)
2. Click "Add Domain"
3. Enter your domain (e.g., `devhq.com`)

### Step 2: Configure DNS Records
Resend will provide DNS records to add to your domain:
```
Type: TXT
Name: _resend
Value: resend-verify=abc123...

Type: MX
Name: @
Value: feedback-smtp.resend.com
Priority: 10
```

### Step 3: Update DevHQ Configuration
Once domain is verified, update `backend/.env`:

```env
# Production Resend Config
SMTP_HOST=smtp.resend.com
SMTP_USER=resend
SMTP_PASSWORD=re_JHYWF64y_AahntVMsXVBztSwNCSTkz3UT
FROM_EMAIL=<EMAIL>
FROM_NAME=DevHQ Team
```

### Step 4: Update Frontend URLs
Update email templates to use production URLs:
```env
FRONTEND_URL=https://app.devhq.com
VERIFICATION_URL=https://app.devhq.com/verify-email
RESET_PASSWORD_URL=https://app.devhq.com/reset-password
```

## 🧪 Testing Email Templates

### Development Testing (Console Output)
```bash
cd backend
source venv/bin/activate
python3 -c "
from app.core.email import email_service
email_service.send_verification_email('<EMAIL>', 'John', 'token123')
"
```

### Production Testing (Real Emails)
```bash
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "first_name": "Test",
    "last_name": "User"
  }'
```

## 🌍 Recommended Domains for African Markets

### Free Options
- **Netlify**: `yourapp.netlify.app`
- **Vercel**: `yourapp.vercel.app`
- **GitHub Pages**: `username.github.io`

### Custom Domains
- **Namecheap**: ~$10/year
- **Cloudflare**: Free DNS management
- **Google Domains**: ~$12/year

## 🔒 Security Best Practices

1. **Use environment variables** for API keys
2. **Never commit** `.env` files to git
3. **Rotate API keys** regularly
4. **Monitor email usage** in Resend dashboard
5. **Set up SPF/DKIM** records for better deliverability

## 📊 Email Analytics

Resend provides:
- ✅ **Delivery rates**
- ✅ **Open rates**
- ✅ **Click tracking**
- ✅ **Bounce handling**
- ✅ **Spam reports**

## 🚀 Ready for Production?

When you're ready to send real emails:

1. ✅ **Domain verified** in Resend
2. ✅ **DNS records** configured
3. ✅ **Environment variables** updated
4. ✅ **Frontend URLs** updated
5. ✅ **Test emails** working

Then uncomment the production config in `.env` and restart the backend!