#!/usr/bin/env python3
"""
Smart DevHQ development server with automatic port management
Automatically finds available ports and provides helpful information
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.port_manager import smart_port_selection


def check_dependencies():
    """Check if required dependencies are available"""
    try:
        import fastapi
        import uvicorn

        return True
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False


def activate_venv():
    """Check if virtual environment is activated"""
    if hasattr(sys, "real_prefix") or (
        hasattr(sys, "base_prefix") and sys.base_prefix != sys.prefix
    ):
        return True

    # Check if we're in a venv directory
    venv_path = backend_dir / "venv"
    if venv_path.exists():
        print("⚠️  Virtual environment not activated!")
        print(f"💡 Run: source {venv_path}/bin/activate")
        return False

    return True


def start_server(
    port: int, host: str = "0.0.0.0", reload: bool = True, kill_existing: bool = False
):
    """Start the FastAPI development server"""

    print("🚀 Starting DevHQ Development Server")
    print("=" * 50)

    # Check virtual environment
    if not activate_venv():
        print("\n🔧 To fix this:")
        print("cd backend && source venv/bin/activate && python smart_server.py")
        return

    # Check dependencies
    if not check_dependencies():
        return

    # Smart port selection
    selected_port = smart_port_selection(port, kill_existing)

    print(f"\n🌐 Server Configuration:")
    print(f"   Host: {host}")
    print(f"   Port: {selected_port}")
    print(f"   Reload: {reload}")
    print(f"   App: app.main:app")

    print(f"\n📚 Available URLs:")
    print(f"   API Docs (Swagger): http://localhost:{selected_port}/docs")
    print(f"   API Docs (ReDoc):   http://localhost:{selected_port}/redoc")
    print(f"   Health Check:       http://localhost:{selected_port}/health")
    print(f"   Authentication:     http://localhost:{selected_port}/api/v1/auth/")

    print(f"\n🧪 Quick Test Commands:")
    print(f"   curl http://localhost:{selected_port}/health")
    print(f"   curl http://localhost:{selected_port}/api/v1/auth/me")

    print("\n" + "=" * 50)
    print("🎯 Starting server... (Press Ctrl+C to stop)")
    print("=" * 50)

    try:
        # Import here to avoid issues if not in venv
        import uvicorn

        # Start the server
        uvicorn.run(
            "app.main:app",
            host=host,
            port=selected_port,
            reload=reload,
            reload_dirs=["app"] if reload else None,
            log_level="info",
        )

    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Smart DevHQ development server with automatic port management"
    )

    parser.add_argument(
        "--port", "-p", type=int, default=8000, help="Preferred port (default: 8000)"
    )

    parser.add_argument(
        "--host", default="0.0.0.0", help="Host to bind to (default: 0.0.0.0)"
    )

    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")

    parser.add_argument(
        "--kill-existing",
        action="store_true",
        help="Kill existing processes on the preferred port",
    )

    parser.add_argument(
        "--check-port", type=int, help="Just check if a specific port is available"
    )

    args = parser.parse_args()

    # If just checking a port
    if args.check_port:
        from app.core.port_manager import PortManager

        manager = PortManager()

        if manager.is_port_available(args.check_port):
            print(f"✅ Port {args.check_port} is available")
        else:
            port_info = manager.get_port_info(args.check_port)
            print(f"❌ Port {args.check_port} is in use by: {port_info}")
        return

    # Start the server
    start_server(
        port=args.port,
        host=args.host,
        reload=not args.no_reload,
        kill_existing=args.kill_existing,
    )


if __name__ == "__main__":
    main()
