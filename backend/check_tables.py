import sys
sys.path.append('.')
from sqlalchemy import create_engine, text
from app.config import settings

engine = create_engine(settings.database_url)
with engine.connect() as conn:
    # Check all tables that should have deleted_at column
    result = conn.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name NOT LIKE 'alembic%'"))
    tables = [row[0] for row in result]
    
    print('Tables and their deleted_at status:')
    for table in tables:
        # Check if deleted_at column exists
        result = conn.execute(text(f"SELECT column_name FROM information_schema.columns WHERE table_name = '{table}' AND column_name = 'deleted_at'"))
        has_deleted_at = bool(result.fetchone())
        status = 'HAS deleted_at' if has_deleted_at else 'MISSING deleted_at'
        print(f'  {table}: {status}')