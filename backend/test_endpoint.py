#!/usr/bin/env python3
"""
Manual test of the verification endpoint
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment
os.environ.setdefault('ENVIRONMENT', 'development')

import asyncio
from sqlalchemy.orm import sessionmaker
from app.core.auth import create_verification_token, verify_token
from app.config import settings
from sqlalchemy import create_engine

# Import the verification function
from app.routers.auth import verify_email
from app.schemas.auth import EmailVerificationRequest

async def test_verification_endpoint():
    """Test the verification endpoint manually"""
    print("Testing verification endpoint...")
    
    # Create a test token
    user_id = "test-user-123"
    token = create_verification_token(user_id)
    print(f"Created test token: {token}")
    
    # Create a mock database session
    engine = create_engine(settings.database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Create verification request data
        verification_data = EmailVerificationRequest(token=token)
        print(f"Verification data: {verification_data}")
        print(f"Token in request: {verification_data.token}")
        
        # This would normally be called by FastAPI, but we're testing manually
        # We can't easily call the async function directly without the full FastAPI context
        print("Would call verify_email endpoint with this data...")
        
        # Let's manually test the token verification logic
        print("\nManually testing token verification:")
        user_id_result = verify_token(token, "email_verification")
        print(f"Token verification result: {user_id_result}")
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_verification_endpoint())