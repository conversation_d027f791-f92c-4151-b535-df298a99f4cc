#!/bin/bash

# DevHQ Development Server Starter
# Quick script to start the development server with proper environment

set -e

echo "🚀 Starting DevHQ Development Server"
echo "===================================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run ./setup_dev.sh first"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please run ./setup_dev.sh first"
    exit 1
fi

# Check if PostgreSQL container is running
if ! docker ps --format 'table {{.Names}}' | grep -q "devhq-postgres"; then
    echo "❌ PostgreSQL container not running. Starting it..."
    
    # Try to start existing container first
    if docker start devhq-postgres 2>/dev/null; then
        echo "✅ Started existing PostgreSQL container"
    else
        echo "🔧 Creating new PostgreSQL container..."
        docker run -d \
            --name devhq-postgres \
            -e POSTGRES_PASSWORD=postgres \
            -e POSTGRES_DB=devhq_dev \
            -p 5433:5432 \
            --health-cmd="pg_isready -U postgres -d devhq_dev" \
            --health-interval=10s \
            --health-timeout=5s \
            --health-retries=5 \
            postgres:15
        
        if [ $? -eq 0 ]; then
            echo "✅ PostgreSQL container created successfully"
        else
            echo "❌ Failed to create PostgreSQL container"
            echo "💡 Try running: docker-compose up postgres -d"
            exit 1
        fi
    fi
    
    echo "⏳ Waiting for PostgreSQL to be ready..."
    sleep 8
fi

# Activate virtual environment and start server
echo "🔧 Activating virtual environment..."
source venv/bin/activate

echo "🔧 Testing database connection..."
python -c "
from app.database import engine
from sqlalchemy import text
with engine.connect() as conn:
    conn.execute(text('SELECT 1'))
print('✅ Database connection successful!')
"

echo ""
echo "🌟 Starting FastAPI development server..."
echo "📍 Server will be available at: http://localhost:8000"
echo "📚 API documentation: http://localhost:8000/docs"
echo "🛑 Press Ctrl+C to stop the server"
echo ""

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000