# Core Framework
fastapi>=0.115.0,<0.116.0  # Updated for security (PYSEC-2024-38)
uvicorn[standard]>=0.24.0,<0.25.0

# Database & ORM
sqlalchemy>=2.0.23,<2.1.0
alembic>=1.12.1,<1.13.0
psycopg2-binary>=2.9.9,<3.0.0
asyncpg>=0.29.0,<0.30.0

# Authentication & Security
passlib[argon2]>=1.7.4,<1.8.0
bcrypt>=4.0.1,<5.0.0
python-jose[cryptography]>=3.4.0,<3.5.0  # Updated for security (PYSEC-2024-232, PYSEC-2024-233)
python-multipart>=0.0.6,<0.1.0
slowapi>=0.1.9,<0.2.0
authlib>=1.3.0,<2.0.0  # OAuth2 client library
requests>=2.31.0,<3.0.0  # HTTP requests for OAuth

# Validation & Serialization
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0
email-validator>=2.1.0,<3.0.0
phonenumbers>=8.13.0,<9.0.0

# External Integrations
redis>=5.0.1,<6.0.0
cloudinary>=1.36.0,<2.0.0
httpx>=0.25.2,<0.26.0

# WebSocket & Real-time
python-socketio>=5.10.0,<6.0.0
websockets>=12.0,<13.0

# Utilities
python-dotenv>=1.0.0,<2.0.0
Pillow>=10.1.0,<11.0.0

# PDF Generation (temporarily disabled for Windows compatibility)
# weasyprint>=60.0,<61.0
jinja2>=3.1.2,<4.0.0

# Monitoring & Logging
sentry-sdk[fastapi]>=1.38.0,<2.0.0
structlog>=23.2.0,<24.0.0

# OpenTelemetry for Distributed Tracing & Metrics
opentelemetry-api>=1.21.0,<2.0.0
opentelemetry-sdk>=1.21.0,<2.0.0
opentelemetry-exporter-otlp>=1.21.0,<2.0.0
opentelemetry-instrumentation-fastapi>=0.42b0,<1.0.0
opentelemetry-instrumentation-httpx>=0.42b0,<1.0.0
opentelemetry-instrumentation-psycopg2>=0.42b0,<1.0.0
opentelemetry-instrumentation-redis>=0.42b0,<1.0.0
opentelemetry-instrumentation-sqlalchemy>=0.42b0,<1.0.0