#!/usr/bin/env python3
"""
Test the verification endpoint with a real user in the database
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment
os.environ.setdefault('ENVIRONMENT', 'development')

import requests

def test_verification_with_real_user():
    """Test verification endpoint with a real user"""
    print("Testing verification with real user...")
    
    # Create a real user in the database
    import subprocess
    result = subprocess.run([
        'python', '-c', '''
import sys
sys.path.append(".")
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from app.config import settings
from app.models.user import User

engine = create_engine(settings.database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

try:
    # Create a test user
    user = User(
        email="<EMAIL>",
        first_name="Verification",
        last_name="Test",
        is_active=True,
        is_verified=False
    )
    user.set_password("TestPassword123!")
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    print(user.id)
finally:
    db.close()
'''
    ], capture_output=True, text=True, cwd='.')
    
    user_id = result.stdout.strip()
    if not user_id:
        print("Failed to create user")
        return
    print(f"Created test user with ID: {user_id}")
    
    # Create a verification token for this real user
    result = subprocess.run([
        'python', '-c', 
        f'import sys; sys.path.append("."); from app.core.auth import create_verification_token; print(create_verification_token("{user_id}"))'
    ], capture_output=True, text=True, cwd='.')
    
    token = result.stdout.strip()
    print(f"Created verification token for real user: {token}")
    print(f"Token length: {len(token)}")
    
    # Make a real HTTP request to the verification endpoint
    url = "http://localhost:8000/api/v1/auth/verify-email"
    headers = {
        "Content-Type": "application/json"
    }
    data = {
        "token": token
    }
    
    print(f"\nMaking request to: {url}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"\nResponse status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Verification successful!")
        else:
            print("❌ Verification failed!")
            
    except Exception as e:
        print(f"Error making request: {e}")
    
    # Clean up - delete test user
    subprocess.run([
        'python', '-c', f'''
import sys
sys.path.append(".")
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from app.config import settings
from app.models.user import User

engine = create_engine(settings.database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

try:
    user = db.query(User).filter(User.email == "<EMAIL>").first()
    if user:
        db.delete(user)
        db.commit()
        print("🧹 Cleaned up test user")
finally:
    db.close()
'''
    ], cwd='.')

if __name__ == "__main__":
    test_verification_with_real_user()
