#!/usr/bin/env python3
import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy import select
from app.models.user import User
from app.database import get_async_db

async def main():
    async for db in get_async_db():
        result = await db.execute(select(User).where(User.email == '<EMAIL>'))
        user = result.scalar_one_or_none()
        if user:
            print(f'User verified: {user.is_verified}')
            print(f'Email verified at: {user.email_verified_at}')
        else:
            print('User not found')
        break

if __name__ == '__main__':
    asyncio.run(main())