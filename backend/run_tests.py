#!/usr/bin/env python3
"""
Test Runner Script for Freelancer Time Tracking Backend

This script provides various options for running the test suite:
- Run all tests
- Run specific test categories (unit, integration, e2e)
- Run tests with different verbosity levels
- Generate coverage reports
- Run performance tests
- Run security tests

Usage:
    python run_tests.py [options]

Examples:
    python run_tests.py --all                    # Run all tests
    python run_tests.py --unit                   # Run only unit tests
    python run_tests.py --integration            # Run only integration tests
    python run_tests.py --e2e                    # Run only e2e tests
    python run_tests.py --coverage               # Run with coverage report
    python run_tests.py --fast                   # Run fast tests only
    python run_tests.py --slow                   # Run slow tests only
    python run_tests.py --security               # Run security tests
    python run_tests.py --performance            # Run performance tests
    python run_tests.py --smoke                  # Run smoke tests
    python run_tests.py --parallel               # Run tests in parallel
    python run_tests.py --verbose                # Run with verbose output
    python run_tests.py --quiet                  # Run with minimal output
    python run_tests.py --failfast               # Stop on first failure
    python run_tests.py --lf                     # Run last failed tests
    python run_tests.py --collect-only           # Collect tests without running
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from typing import List, Optional


class TestRunner:
    """Test runner for the freelancer time tracking backend."""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.tests_dir = self.project_root / "tests"

    def run_command(
        self, cmd: List[str], capture_output: bool = False
    ) -> subprocess.CompletedProcess:
        """Run a command and return the result."""
        print(f"Running: {' '.join(cmd)}")

        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=capture_output,
                text=True,
                check=False,
            )
            return result
        except FileNotFoundError:
            print(f"Error: Command not found: {cmd[0]}")
            print("Make sure pytest is installed: pip install pytest")
            sys.exit(1)

    def build_pytest_command(self, args: argparse.Namespace) -> List[str]:
        """Build the pytest command based on arguments."""
        cmd = ["python", "-m", "pytest"]

        # Test selection
        if args.all:
            cmd.append(str(self.tests_dir))
        elif args.unit:
            cmd.extend(["-m", "unit", str(self.tests_dir / "unit")])
        elif args.integration:
            cmd.extend(["-m", "integration", str(self.tests_dir / "integration")])
        elif args.e2e:
            cmd.extend(["-m", "e2e", str(self.tests_dir / "e2e")])
        elif args.security:
            cmd.extend(["-m", "security"])
        elif args.performance:
            cmd.extend(["-m", "performance"])
        elif args.smoke:
            cmd.extend(["-m", "smoke"])
        elif args.fast:
            cmd.extend(["-m", "not slow"])
        elif args.slow:
            cmd.extend(["-m", "slow"])
        elif args.auth:
            cmd.extend(["-m", "auth"])
        elif args.payment:
            cmd.extend(["-m", "payment"])
        elif args.email:
            cmd.extend(["-m", "email"])
        elif args.api:
            cmd.extend(["-m", "api"])
        elif args.models:
            cmd.extend(["-m", "models"])
        elif args.services:
            cmd.extend(["-m", "services"])
        elif args.utils:
            cmd.extend(["-m", "utils"])
        elif args.workflow:
            cmd.extend(["-m", "workflow"])
        elif args.file:
            cmd.append(args.file)
        elif args.pattern:
            cmd.extend(["-k", args.pattern])
        else:
            # Default to all tests
            cmd.append(str(self.tests_dir))

        # Output options
        if args.verbose:
            cmd.append("-v")
        elif args.quiet:
            cmd.append("-q")

        # Execution options
        if args.failfast:
            cmd.append("-x")

        if args.lf:
            cmd.append("--lf")

        if args.collect_only:
            cmd.append("--collect-only")

        if args.parallel:
            cmd.extend(["-n", "auto"])

        if args.maxfail:
            cmd.extend(["--maxfail", str(args.maxfail)])

        # Coverage options
        if args.coverage:
            cmd.extend(
                [
                    "--cov=app",
                    "--cov-report=html:htmlcov",
                    "--cov-report=term-missing",
                    "--cov-report=xml",
                ]
            )

            if args.cov_fail_under:
                cmd.extend(["--cov-fail-under", str(args.cov_fail_under)])

        # Additional options
        if args.durations:
            cmd.extend(["--durations", str(args.durations)])

        if args.tb:
            cmd.extend(["--tb", args.tb])

        if args.capture:
            cmd.extend(["--capture", args.capture])

        if args.timeout:
            cmd.extend(["--timeout", str(args.timeout)])

        # Custom pytest arguments
        if args.pytest_args:
            cmd.extend(args.pytest_args.split())

        return cmd

    def check_dependencies(self) -> bool:
        """Check if required dependencies are installed."""
        required_packages = [
            "pytest",
            "pytest-cov",
            "pytest-asyncio",
            "pytest-mock",
            "pytest-xdist",
            "pytest-timeout",
        ]

        missing_packages = []

        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            print("Missing required packages:")
            for package in missing_packages:
                print(f"  - {package}")
            print("\nInstall missing packages with:")
            print(f"pip install {' '.join(missing_packages)}")
            return False

        return True

    def setup_environment(self):
        """Setup test environment variables."""
        os.environ["TESTING"] = "true"
        os.environ["DATABASE_URL"] = "sqlite:///:memory:"
        os.environ["SECRET_KEY"] = "test-secret-key"
        os.environ["JWT_SECRET_KEY"] = "test-jwt-secret"

    def run_tests(self, args: argparse.Namespace) -> int:
        """Run the tests with the given arguments."""
        # Check dependencies
        if not self.check_dependencies():
            return 1

        # Setup environment
        self.setup_environment()

        # Build and run pytest command
        cmd = self.build_pytest_command(args)
        result = self.run_command(cmd)

        # Print summary
        if result.returncode == 0:
            print("\n✅ All tests passed!")
        else:
            print(f"\n❌ Tests failed with exit code {result.returncode}")

        # Open coverage report if requested
        if args.coverage and args.open_coverage:
            coverage_file = self.project_root / "htmlcov" / "index.html"
            if coverage_file.exists():
                import webbrowser

                webbrowser.open(f"file://{coverage_file.absolute()}")

        return result.returncode


def create_parser() -> argparse.ArgumentParser:
    """Create the argument parser."""
    parser = argparse.ArgumentParser(
        description="Test runner for Freelancer Time Tracking Backend",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    # Test selection
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument("--all", action="store_true", help="Run all tests")
    test_group.add_argument("--unit", action="store_true", help="Run unit tests only")
    test_group.add_argument(
        "--integration", action="store_true", help="Run integration tests only"
    )
    test_group.add_argument(
        "--e2e", action="store_true", help="Run end-to-end tests only"
    )
    test_group.add_argument(
        "--security", action="store_true", help="Run security tests only"
    )
    test_group.add_argument(
        "--performance", action="store_true", help="Run performance tests only"
    )
    test_group.add_argument("--smoke", action="store_true", help="Run smoke tests only")
    test_group.add_argument(
        "--fast", action="store_true", help="Run fast tests only (exclude slow)"
    )
    test_group.add_argument("--slow", action="store_true", help="Run slow tests only")
    test_group.add_argument(
        "--auth", action="store_true", help="Run authentication tests only"
    )
    test_group.add_argument(
        "--payment", action="store_true", help="Run payment tests only"
    )
    test_group.add_argument("--email", action="store_true", help="Run email tests only")
    test_group.add_argument("--api", action="store_true", help="Run API tests only")
    test_group.add_argument(
        "--models", action="store_true", help="Run model tests only"
    )
    test_group.add_argument(
        "--services", action="store_true", help="Run service tests only"
    )
    test_group.add_argument(
        "--utils", action="store_true", help="Run utility tests only"
    )
    test_group.add_argument(
        "--workflow", action="store_true", help="Run workflow tests only"
    )
    test_group.add_argument("-f", "--file", help="Run tests in specific file")
    test_group.add_argument("-k", "--pattern", help="Run tests matching pattern")

    # Output options
    output_group = parser.add_mutually_exclusive_group()
    output_group.add_argument(
        "-v", "--verbose", action="store_true", help="Verbose output"
    )
    output_group.add_argument("-q", "--quiet", action="store_true", help="Quiet output")

    # Execution options
    parser.add_argument(
        "-x", "--failfast", action="store_true", help="Stop on first failure"
    )
    parser.add_argument("--lf", action="store_true", help="Run last failed tests")
    parser.add_argument(
        "--collect-only", action="store_true", help="Collect tests without running"
    )
    parser.add_argument(
        "-n", "--parallel", action="store_true", help="Run tests in parallel"
    )
    parser.add_argument("--maxfail", type=int, help="Stop after N failures")

    # Coverage options
    parser.add_argument(
        "--coverage", action="store_true", help="Generate coverage report"
    )
    parser.add_argument(
        "--cov-fail-under",
        type=int,
        default=80,
        help="Fail if coverage under threshold",
    )
    parser.add_argument(
        "--open-coverage", action="store_true", help="Open coverage report in browser"
    )

    # Additional options
    parser.add_argument(
        "--durations", type=int, default=10, help="Show N slowest tests"
    )
    parser.add_argument(
        "--tb",
        choices=["short", "long", "line", "native", "no"],
        default="short",
        help="Traceback style",
    )
    parser.add_argument(
        "--capture", choices=["sys", "fd", "no"], default="sys", help="Capture method"
    )
    parser.add_argument("--timeout", type=int, help="Test timeout in seconds")
    parser.add_argument("--pytest-args", help="Additional pytest arguments")

    return parser


def main():
    """Main entry point."""
    parser = create_parser()
    args = parser.parse_args()

    # Default to running all tests if no specific option is given
    if not any(
        [
            args.all,
            args.unit,
            args.integration,
            args.e2e,
            args.security,
            args.performance,
            args.smoke,
            args.fast,
            args.slow,
            args.auth,
            args.payment,
            args.email,
            args.api,
            args.models,
            args.services,
            args.utils,
            args.workflow,
            args.file,
            args.pattern,
        ]
    ):
        args.all = True

    runner = TestRunner()
    exit_code = runner.run_tests(args)
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
