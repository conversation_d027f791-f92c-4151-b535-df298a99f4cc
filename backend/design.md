# 🏗️ DevHQ Backend Architecture & Design
*Comprehensive design documentation for enterprise-ready backend*

## 🎯 **Architecture Overview**

DevHQ backend follows a **modern, scalable, and maintainable architecture** designed to support the revolutionary features outlined in our technical decisions while ensuring production readiness and optimal developer experience.

### **🏛️ Architectural Principles**

1. **API-First Design** - RESTful APIs with OpenAPI documentation
2. **Domain-Driven Design** - Clear separation of business logic
3. **Dependency Injection** - Loose coupling and testability
4. **Event-Driven Architecture** - Async processing capabilities
5. **Security by Design** - Built-in security at every layer
6. **Observability First** - Comprehensive logging and monitoring

---

## 🏗️ **System Architecture**

```mermaid
graph TB
    Client[Frontend/Mobile] --> LB[Load Balancer]
    LB --> API[FastAPI Application]
    API --> Auth[Authentication Layer]
    API --> BL[Business Logic Layer]
    BL --> DB[(PostgreSQL Database)]
    BL --> Cache[(Redis Cache)]
    BL --> Files[Cloudinary CDN]
    BL --> Payment[Paystack API]
    API --> Queue[Background Jobs]
    Queue --> Worker[Celery Workers]
    Worker --> Email[Email Service]
    Worker --> PDF[PDF Generator]
```

### **🔧 Technology Stack**

| Layer | Technology | Purpose |
|-------|------------|---------|
| **API Framework** | FastAPI 0.104+ | High-performance async API |
| **Database** | PostgreSQL 14+ | Relational data with ACID compliance |
| **ORM** | SQLAlchemy 2.0+ | Type-safe database operations |
| **Migrations** | Alembic | Database schema versioning |
| **Authentication** | JWT + Passlib | Secure token-based auth |
| **Validation** | Pydantic V2 | Request/response validation |
| **File Storage** | Cloudinary | Image/file CDN with optimization |
| **Payments** | Paystack API | Payment processing for Kenya/Africa |
| **Background Jobs** | Celery + Redis | Async task processing |
| **Monitoring** | Sentry + Prometheus | Error tracking and metrics |
| **Deployment** | Docker + Fly.io | Containerized cloud deployment |

---

## 📁 **Project Structure**

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config.py               # Configuration and settings
│   ├── database.py             # Database connection and session
│   ├── dependencies.py         # Dependency injection
│   │
│   ├── core/                   # Core utilities and base classes
│   │   ├── __init__.py
│   │   ├── security.py         # Authentication and authorization
│   │   ├── exceptions.py       # Custom exception classes
│   │   └── middleware.py       # Custom middleware
│   │
│   ├── models/                 # SQLAlchemy models
│   │   ├── __init__.py
│   │   ├── base.py            # Base model with common fields
│   │   ├── user.py            # User and authentication models
│   │   ├── client.py          # Client and CRM models
│   │   ├── project.py         # Project and milestone models
│   │   ├── task.py            # Task and time tracking models
│   │   ├── wallet.py          # Financial management models
│   │   ├── invoice.py         # Invoicing and payment models
│   │   └── approval.py        # Client approval workflow models
│   │
│   ├── schemas/                # Pydantic schemas for validation
│   │   ├── __init__.py
│   │   ├── auth.py            # Authentication schemas
│   │   ├── client.py          # Client management schemas
│   │   ├── project.py         # Project management schemas
│   │   ├── task.py            # Task and time tracking schemas
│   │   ├── wallet.py          # Financial schemas
│   │   ├── invoice.py         # Invoicing schemas
│   │   └── common.py          # Shared schemas
│   │
│   ├── routers/                # API route handlers
│   │   ├── __init__.py
│   │   ├── auth.py            # Authentication endpoints
│   │   ├── clients.py         # Client management endpoints
│   │   ├── projects.py        # Project management endpoints
│   │   ├── tasks.py           # Task management endpoints
│   │   ├── time.py            # Time tracking endpoints
│   │   ├── wallet.py          # Financial management endpoints
│   │   ├── invoices.py        # Invoicing endpoints
│   │   ├── payments.py        # Payment processing endpoints
│   │   ├── portal.py          # Client portal endpoints
│   │   ├── approvals.py       # Approval workflow endpoints
│   │   ├── analytics.py       # Analytics and reporting endpoints
│   │   └── webhooks.py        # External webhook handlers
│   │
│   ├── services/               # Business logic layer
│   │   ├── __init__.py
│   │   ├── auth_service.py    # Authentication business logic
│   │   ├── client_service.py  # Client management logic
│   │   ├── project_service.py # Project management logic
│   │   ├── time_service.py    # Time tracking logic
│   │   ├── wallet_service.py  # Financial management logic
│   │   ├── payment_service.py # Payment processing logic
│   │   ├── email_service.py   # Email notifications
│   │   ├── pdf_service.py     # PDF generation
│   │   └── analytics_service.py # Analytics and reporting
│   │
│   ├── utils/                  # Utility functions
│   │   ├── __init__.py
│   │   ├── datetime_utils.py  # Date/time utilities
│   │   ├── file_utils.py      # File handling utilities
│   │   ├── validation_utils.py # Custom validators
│   │   └── formatting_utils.py # Data formatting
│   │
│   └── tasks/                  # Background tasks
│       ├── __init__.py
│       ├── email_tasks.py     # Email sending tasks
│       ├── payment_tasks.py   # Payment processing tasks
│       └── analytics_tasks.py # Analytics computation tasks
│
├── alembic/                    # Database migrations
│   ├── versions/              # Migration files
│   ├── env.py                 # Alembic configuration
│   └── script.py.mako         # Migration template
│
├── tests/                      # Test suite
│   ├── __init__.py
│   ├── conftest.py            # Test configuration and fixtures
│   ├── test_auth.py           # Authentication tests
│   ├── test_clients.py        # Client management tests
│   ├── test_projects.py       # Project management tests
│   ├── test_time_tracking.py  # Time tracking tests
│   ├── test_payments.py       # Payment processing tests
│   └── test_integration.py    # Integration tests
│
├── scripts/                    # Utility scripts
│   ├── init_db.py             # Database initialization
│   ├── seed_data.py           # Test data seeding
│   └── backup_db.py           # Database backup
│
├── requirements.txt            # Python dependencies
├── requirements-dev.txt        # Development dependencies
├── Dockerfile                  # Container configuration
├── docker-compose.yml          # Development environment
├── fly.toml                    # Fly.io deployment config
└── README.md                   # Setup and usage instructions
```

---

## 🗄️ **Database Design**

### **🎯 Design Principles**

1. **Normalization** - Eliminate data redundancy while maintaining performance
2. **Soft Deletes** - Preserve data integrity with `deleted_at` timestamps
3. **Audit Trails** - Track creation and modification timestamps
4. **Indexing Strategy** - Optimize for common query patterns
5. **Multi-tenancy Ready** - Support for organization-based data isolation

### **📊 Entity Relationship Overview**

```mermaid
erDiagram
    User ||--o{ Client : manages
    User ||--o{ Project : owns
    User ||--o{ Task : creates
    User ||--o{ TimeEntry : tracks
    User ||--o{ WalletAccount : owns
    User ||--o{ Transaction : records
    User ||--o{ Invoice : generates
    
    Client ||--o{ Project : has
    Client ||--o{ Invoice : receives
    Client ||--o{ ClientApproval : approves
    
    Project ||--o{ Task : contains
    Project ||--o{ ProjectMilestone : has
    Project ||--o{ TimeEntry : tracks
    Project ||--o{ ProjectNote : documents
    Project ||--o{ DesignUpload : stores
    
    Task ||--o{ TimeEntry : measures
    
    Invoice ||--o{ InvoiceItem : contains
    Invoice ||--o{ PaymentTransaction : processes
    
    WalletAccount ||--o{ Transaction : records
    
    ClientApproval ||--o{ ClientFeedback : receives
```

### **🔑 Key Design Decisions**

#### **UUID Primary Keys**
```sql
-- Benefits: Better distribution, security, merge-friendly
id UUID PRIMARY KEY DEFAULT gen_random_uuid()
```

#### **Soft Delete Pattern**
```sql
-- Preserve data while hiding from normal queries
deleted_at TIMESTAMP NULL
-- Query pattern: WHERE deleted_at IS NULL
```

#### **Audit Trail Fields**
```sql
-- Track data lifecycle
created_at TIMESTAMP DEFAULT NOW()
updated_at TIMESTAMP DEFAULT NOW()
```

#### **Multi-tenant Architecture**
```sql
-- Support for organization-based isolation
user_id UUID NOT NULL REFERENCES users(id)
organization_id UUID REFERENCES organizations(id) -- NULL for personal use
```

### **📈 Performance Optimizations**

#### **Strategic Indexing**
```sql
-- User-based queries (most common pattern)
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_tasks_user_id ON tasks(user_id);

-- Composite indexes for common filters
CREATE INDEX idx_projects_user_status ON projects(user_id, status);
CREATE INDEX idx_tasks_user_status ON tasks(user_id, status);

-- Time-based queries
CREATE INDEX idx_time_entries_start_time ON time_entries(start_time);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);

-- Soft delete optimization
CREATE INDEX idx_projects_deleted_at ON projects(deleted_at);
```

#### **Denormalization for Performance**
```sql
-- Cache calculated values to avoid expensive joins
total_actual_hours DECIMAL(8,2) DEFAULT 0.00 -- calculated from time_entries
completion_percentage DECIMAL(5,2) DEFAULT 0.00 -- calculated from milestones
```

---

## 🔐 **Security Architecture**

### **🛡️ Authentication & Authorization**

#### **JWT Token Strategy**
```python
# Token Configuration
ACCESS_TOKEN_EXPIRE_MINUTES = 15    # Short-lived for security
REFRESH_TOKEN_EXPIRE_DAYS = 7       # Longer-lived for UX
ALGORITHM = "HS256"                 # HMAC with SHA-256

# Token Structure
{
    "sub": "user_id",               # Subject (user identifier)
    "exp": 1640995200,              # Expiration timestamp
    "iat": 1640908800,              # Issued at timestamp
    "type": "access",               # Token type
    "permissions": ["read", "write"] # User permissions
}
```

#### **Password Security**
```python
# Argon2 Configuration (OWASP recommended)
from passlib.context import CryptContext

pwd_context = CryptContext(
    schemes=["argon2"],
    argon2__memory_cost=65536,      # 64 MB memory
    argon2__time_cost=3,            # 3 iterations
    argon2__parallelism=1,          # Single thread
)
```

#### **Rate Limiting Strategy**
```python
# Authentication endpoints
"/auth/login": "5/minute",          # Prevent brute force
"/auth/register": "3/minute",       # Prevent spam
"/auth/refresh": "10/minute",       # Allow normal usage

# API endpoints
"/api/v1/*": "100/minute",          # General API usage
"/portal/*": "50/minute",           # Client portal access
```

### **🔒 Data Protection**

#### **Input Validation**
```python
# Pydantic schemas with strict validation
class UserCreate(BaseModel):
    email: EmailStr                 # Email format validation
    password: str = Field(min_length=8, max_length=128)
    first_name: str = Field(min_length=1, max_length=50)
    
    @validator('password')
    def validate_password(cls, v):
        # Custom password complexity validation
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain digit')
        return v
```

#### **SQL Injection Prevention**
```python
# SQLAlchemy ORM with parameterized queries
def get_user_projects(db: Session, user_id: UUID, status: str = None):
    query = db.query(Project).filter(
        Project.user_id == user_id,
        Project.deleted_at.is_(None)
    )
    if status:
        query = query.filter(Project.status == status)
    return query.all()
```

#### **CORS Configuration**
```python
# Strict CORS policy
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://devhq.com", "https://app.devhq.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

---

## 🚀 **API Design**

### **🎯 RESTful API Principles**

#### **Resource Naming Conventions**
```
# Collections (plural nouns)
GET    /api/v1/projects           # List projects
POST   /api/v1/projects           # Create project

# Resources (singular identifiers)
GET    /api/v1/projects/{id}      # Get specific project
PUT    /api/v1/projects/{id}      # Update project
DELETE /api/v1/projects/{id}      # Delete project

# Sub-resources
GET    /api/v1/projects/{id}/tasks        # Project tasks
POST   /api/v1/projects/{id}/milestones   # Create milestone

# Actions (verbs for non-CRUD operations)
POST   /api/v1/tasks/{id}/time/start      # Start timer
POST   /api/v1/invoices/{id}/send         # Send invoice
```

#### **HTTP Status Code Strategy**
```python
# Success responses
200 OK          # Successful GET, PUT
201 Created     # Successful POST
204 No Content  # Successful DELETE

# Client error responses
400 Bad Request         # Invalid input data
401 Unauthorized        # Missing/invalid authentication
403 Forbidden          # Insufficient permissions
404 Not Found          # Resource doesn't exist
409 Conflict           # Resource conflict (duplicate email)
422 Unprocessable Entity # Validation errors

# Server error responses
500 Internal Server Error # Unexpected server error
503 Service Unavailable   # Temporary service issues
```

### **📝 Request/Response Patterns**

#### **Standardized Response Format**
```python
# Success Response
{
    "success": true,
    "data": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Website Redesign",
        "status": "active"
    },
    "meta": {
        "timestamp": "2024-01-15T10:30:00Z",
        "version": "1.0"
    }
}

# Error Response
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": [
            {
                "field": "email",
                "message": "Invalid email format"
            }
        ]
    },
    "meta": {
        "timestamp": "2024-01-15T10:30:00Z",
        "request_id": "req_123456789"
    }
}
```

#### **Pagination Pattern**
```python
# Request
GET /api/v1/projects?page=2&limit=20&sort=created_at&order=desc

# Response
{
    "success": true,
    "data": [...],
    "pagination": {
        "page": 2,
        "limit": 20,
        "total": 150,
        "pages": 8,
        "has_next": true,
        "has_prev": true
    }
}
```

#### **Filtering and Search**
```python
# Query parameters for filtering
GET /api/v1/projects?status=active&client_id=123&search=website

# Date range filtering
GET /api/v1/transactions?start_date=2024-01-01&end_date=2024-01-31

# Complex filtering with operators
GET /api/v1/tasks?due_date__gte=2024-01-15&priority__in=high,urgent
```

---

## ⚡ **Performance Architecture**

### **🚀 Optimization Strategies**

#### **Database Query Optimization**
```python
# Eager loading to prevent N+1 queries
def get_projects_with_tasks(db: Session, user_id: UUID):
    return db.query(Project)\
        .options(joinedload(Project.tasks))\
        .filter(Project.user_id == user_id)\
        .all()

# Selective field loading
def get_project_summary(db: Session, project_id: UUID):
    return db.query(
        Project.id,
        Project.name,
        Project.status,
        Project.completion_percentage
    ).filter(Project.id == project_id).first()
```

#### **Caching Strategy**
```python
# Redis caching for frequently accessed data
@cache(expire=300)  # 5 minutes
def get_user_dashboard_metrics(user_id: UUID):
    # Expensive calculation cached for 5 minutes
    return calculate_dashboard_metrics(user_id)

# Cache invalidation on data changes
def update_project(db: Session, project_id: UUID, data: dict):
    project = update_project_in_db(db, project_id, data)
    cache.delete(f"project_metrics:{project.user_id}")
    return project
```

#### **Background Job Processing**
```python
# Celery task configuration
@celery_app.task(bind=True, max_retries=3)
def send_invoice_email(self, invoice_id: str):
    try:
        invoice = get_invoice(invoice_id)
        send_email(invoice.client.email, invoice_template, invoice)
    except Exception as exc:
        # Exponential backoff retry
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))
```

### **📊 Monitoring and Observability**

#### **Structured Logging**
```python
import structlog

logger = structlog.get_logger()

# Contextual logging
logger.info(
    "User login successful",
    user_id=user.id,
    email=user.email,
    ip_address=request.client.host,
    user_agent=request.headers.get("user-agent")
)

# Error logging with context
logger.error(
    "Payment processing failed",
    invoice_id=invoice.id,
    client_id=invoice.client_id,
    amount=invoice.total_amount,
    error_code=error.code,
    exc_info=True
)
```

#### **Health Check Endpoints**
```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0"
    }

@app.get("/health/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    checks = {
        "database": check_database_connection(db),
        "redis": check_redis_connection(),
        "paystack": check_paystack_api(),
        "cloudinary": check_cloudinary_api()
    }
    
    overall_status = "healthy" if all(checks.values()) else "unhealthy"
    
    return {
        "status": overall_status,
        "checks": checks,
        "timestamp": datetime.utcnow()
    }
```

---

## 🔄 **Integration Architecture**

### **💳 Payment Processing (Paystack)**

#### **Webhook Handling**
```python
@app.post("/webhooks/paystack")
async def paystack_webhook(
    request: Request,
    paystack_signature: str = Header(None, alias="x-paystack-signature")
):
    payload = await request.body()
    
    try:
        # Verify webhook signature
        computed_signature = hmac.new(
            PAYSTACK_SECRET_KEY.encode(),
            payload,
            hashlib.sha512
        ).hexdigest()
        
        if not hmac.compare_digest(paystack_signature, computed_signature):
            raise HTTPException(status_code=400, detail="Invalid signature")
            
        event = json.loads(payload)
    except (ValueError, json.JSONDecodeError):
        raise HTTPException(status_code=400, detail="Invalid payload")
    
    # Handle different event types
    if event["event"] == "charge.success":
        await handle_payment_success(event["data"])
    elif event["event"] == "charge.failed":
        await handle_payment_failure(event["data"])
    elif event["event"] == "transfer.success":
        await handle_payout_success(event["data"])
    
    return {"status": "success"}

# Payment initialization for Kenya
async def initialize_payment(amount: int, email: str, currency: str = "KES"):
    """Initialize Paystack payment with Kenya-specific options"""
    
    headers = {
        "Authorization": f"Bearer {PAYSTACK_SECRET_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "email": email,
        "amount": amount * 100,  # Convert to kobo
        "currency": currency,
        "channels": ["card", "mobile_money", "bank_transfer", "ussd", "qr"],
        "metadata": {
            "custom_fields": [
                {
                    "display_name": "Payment Method",
                    "variable_name": "payment_method",
                    "value": "DevHQ Invoice Payment"
                }
            ]
        }
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "https://api.paystack.co/transaction/initialize",
            headers=headers,
            json=data
        )
        return response.json()
```

### **☁️ File Storage (Cloudinary)**

#### **Upload Service**
```python
import cloudinary.uploader

class FileUploadService:
    def upload_file(self, file: UploadFile, folder: str = "uploads"):
        try:
            result = cloudinary.uploader.upload(
                file.file,
                folder=folder,
                resource_type="auto",
                transformation=[
                    {"quality": "auto:good"},
                    {"fetch_format": "auto"}
                ]
            )
            
            return {
                "public_id": result["public_id"],
                "url": result["secure_url"],
                "format": result["format"],
                "size": result["bytes"]
            }
        except Exception as e:
            logger.error("File upload failed", error=str(e))
            raise HTTPException(status_code=500, detail="Upload failed")
```

---

## 🧪 **Testing Strategy**

### **🎯 Testing Pyramid**

#### **Unit Tests (70%)**
```python
# Model tests
def test_user_password_hashing():
    user = User(email="<EMAIL>", password="TestPass123!")
    assert user.verify_password("TestPass123!")
    assert not user.verify_password("WrongPassword")

# Service tests
def test_time_calculation_service():
    start_time = datetime(2024, 1, 15, 9, 0, 0)
    end_time = datetime(2024, 1, 15, 17, 30, 0)
    
    duration = TimeCalculationService.calculate_duration(start_time, end_time)
    assert duration == 510  # 8.5 hours in minutes
```

#### **Integration Tests (20%)**
```python
# API endpoint tests
def test_create_project_endpoint(client, auth_headers):
    project_data = {
        "name": "Test Project",
        "client_id": str(uuid4()),
        "hourly_rate": 75.00
    }
    
    response = client.post(
        "/api/v1/projects",
        json=project_data,
        headers=auth_headers
    )
    
    assert response.status_code == 201
    assert response.json()["data"]["name"] == "Test Project"
```

#### **End-to-End Tests (10%)**
```python
# Complete workflow tests
def test_complete_time_tracking_workflow(client, auth_headers):
    # Create project
    project = create_test_project(client, auth_headers)
    
    # Create task
    task = create_test_task(client, auth_headers, project["id"])
    
    # Start timer
    start_response = client.post(
        f"/api/v1/tasks/{task['id']}/time/start",
        headers=auth_headers
    )
    assert start_response.status_code == 200
    
    # Stop timer
    stop_response = client.post(
        f"/api/v1/tasks/{task['id']}/time/stop",
        headers=auth_headers
    )
    assert stop_response.status_code == 200
    
    # Verify time entry created
    time_entries = client.get(
        f"/api/v1/tasks/{task['id']}/time",
        headers=auth_headers
    )
    assert len(time_entries.json()["data"]) == 1
```

---

## 🚀 **Deployment Architecture**

### **🐳 Containerization**

#### **Multi-stage Dockerfile**
```dockerfile
# Build stage
FROM python:3.11-slim as builder

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# Create non-root user
RUN useradd --create-home --shell /bin/bash app

# Copy dependencies
COPY --from=builder /root/.local /home/<USER>/.local
ENV PATH=/home/<USER>/.local/bin:$PATH

# Copy application
WORKDIR /app
COPY --chown=app:app . .

# Switch to non-root user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### **☁️ Fly.io Configuration**

#### **fly.toml**
```toml
app = "devhq-backend"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile"

[env]
  PORT = "8000"
  PYTHONPATH = "/app"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1

[[http_service.checks]]
  grace_period = "10s"
  interval = "30s"
  method = "GET"
  timeout = "5s"
  path = "/health"

[postgres]
  app = "devhq-db"
  
[[statics]]
  guest_path = "/app/static"
  url_prefix = "/static"
```

This comprehensive design document ensures DevHQ's backend is built with enterprise-grade architecture, security, and scalability from day one! 🚀