#!/usr/bin/env python3
"""
Complete database cleanup script for DevHQ
WARNING: This will delete ALL data from the database!
Use only for development/testing environments.
"""

import asyncio
import os
import sys
from datetime import datetime, timezone

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment
os.environ.setdefault('ENVIRONMENT', 'development')

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Import settings
try:
    from app.config import settings
except ImportError as e:
    print(f"Error importing settings: {e}")
    print("Make sure you're running this script from the backend directory")
    sys.exit(1)

def clean_database():
    """Clean ALL tables in the database"""
    print("🔥 WARNING: This will DELETE ALL DATA from the database!")
    print("This operation cannot be undone!")
    print("")
    
    # Confirm with user
    response = input("Are you sure you want to proceed? Type 'YES' to confirm: ")
    if response != 'YES':
        print("Operation cancelled.")
        return
    
    print("🧹 Starting complete database cleanup...")
    
    try:
        # Create database engine
        engine = create_engine(settings.database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # List of tables to truncate in correct order (children first, parents last)
            # This handles foreign key constraints properly
            table_groups = [
                # Child tables (no foreign keys pointing to them)
                ['audit_logs', 'notifications'],
                # Intermediate tables
                ['user_sessions', 'project_members', 'tasks', 'projects', 'invoices', 'clients', 'payments'],
                # Parent tables (have foreign keys pointing to them)
                ['user_settings', 'users']
            ]
            
            deleted_counts = {}
            
            # Delete from each group
            for table_group in table_groups:
                for table in table_group:
                    try:
                        result = db.execute(text(f"DELETE FROM {table}"))
                        deleted_counts[table] = result.rowcount
                        print(f"  ✅ Deleted {result.rowcount} records from {table}")
                    except Exception as e:
                        print(f"  ⚠️  Could not delete from {table}: {e}")
            
            db.commit()
            
            print("")
            print("✅ Database cleanup completed!")
            print("Deleted records summary:")
            for table, count in deleted_counts.items():
                print(f"  {table}: {count} records")
                
        except Exception as e:
            print(f"❌ Error during cleanup: {e}")
            db.rollback()
            raise
        finally:
            db.close()
            
    except Exception as e:
        print(f"💥 Cleanup failed: {e}")
        sys.exit(1)

def clean_unverified_accounts_only():
    """Clean only unverified accounts (safer option)"""
    print("🧹 Cleaning only unverified accounts...")
    
    try:
        # Create database engine
        engine = create_engine(settings.database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # Delete related records first (due to foreign key constraints)
            # Delete user settings for unverified users
            result_settings = db.execute(
                text("""
                    DELETE FROM user_settings 
                    WHERE user_id IN (
                        SELECT id FROM users WHERE is_verified = false
                    )
                """)
            )
            print(f"  ✅ Deleted {result_settings.rowcount} user settings records")
            
            # Delete user sessions for unverified users
            result_sessions = db.execute(
                text("""
                    DELETE FROM user_sessions 
                    WHERE user_id IN (
                        SELECT id FROM users WHERE is_verified = false
                    )
                """)
            )
            print(f"  ✅ Deleted {result_sessions.rowcount} user session records")
            
            # Delete unverified users
            result_users = db.execute(
                text("""
                    DELETE FROM users 
                    WHERE is_verified = false
                """)
            )
            
            deleted_count = result_users.rowcount
            db.commit()
            
            print(f"  ✅ Deleted {deleted_count} unverified user accounts")
            print(f"✅ Total cleanup completed!")
            return deleted_count
            
        except Exception as e:
            print(f"❌ Error during cleanup: {e}")
            db.rollback()
            raise
        finally:
            db.close()
            
    except Exception as e:
        print(f"💥 Cleanup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("DevHQ Database Cleanup Tool")
    print("=" * 30)
    print("1. Clean ALL database tables (DANGEROUS)")
    print("2. Clean only unverified accounts (SAFE)")
    print("")
    
    choice = input("Select option (1 or 2): ")
    
    if choice == "1":
        clean_database()
    elif choice == "2":
        clean_unverified_accounts_only()
        print("✨ Cleanup completed successfully.")
    else:
        print("Invalid choice. Exiting.")
        sys.exit(1)