import asyncio
from app.database import get_async_db
from app.models.user import User
from sqlalchemy import select
from app.core.auth import create_verification_token

async def verify_test_user():
    async for db in get_async_db():
        # Get user
        result = await db.execute(select(User).where(User.email == '<EMAIL>'))
        user = result.scalar_one_or_none()
        
        if user:
            token = create_verification_token(str(user.id))
            print(f"Verification token: {token}")
        else:
            print("User not found")
        break

if __name__ == "__main__":
    asyncio.run(verify_test_user())