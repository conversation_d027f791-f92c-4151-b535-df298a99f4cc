#!/usr/bin/env python3
"""
Manual test of the verification endpoint with a real HTTP request
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment
os.environ.setdefault('ENVIRONMENT', 'development')

import requests

def test_verification_endpoint():
    """Test the verification endpoint with a real HTTP request"""
    print("Testing verification endpoint with real HTTP request...")
    
    # Create a test token
    import subprocess
    result = subprocess.run([
        'python', '-c', 
        'import sys; sys.path.append("."); from app.core.auth import create_verification_token; print(create_verification_token("test-user-id"))'
    ], capture_output=True, text=True, cwd='.')
    
    token = result.stdout.strip()
    print(f"Created test token: {token}")
    print(f"Token length: {len(token)}")
    
    # Make a real HTTP request to the verification endpoint
    url = "http://localhost:8000/api/v1/auth/verify-email"
    headers = {
        "Content-Type": "application/json"
    }
    data = {
        "token": token
    }
    
    print(f"\nMaking request to: {url}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"\nResponse status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Verification successful!")
        else:
            print("❌ Verification failed!")
            
    except Exception as e:
        print(f"Error making request: {e}")

if __name__ == "__main__":
    test_verification_endpoint()