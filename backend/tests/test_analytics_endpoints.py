"""Tests for analytics endpoints"""

from datetime import date, datetime, timedelta, timezone
from decimal import Decimal

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.client import Client
from app.models.project import Project, TimeEntry
from app.models.user import User


@pytest.mark.asyncio
class TestAnalyticsEndpoints:
    """Test analytics API endpoints"""

    async def test_get_productivity_insights(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test getting productivity insights"""
        # Create test data
        project = Project(
            title="Analytics Test Project",
            description="Project for analytics testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=Decimal("100.00"),
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Create time entries for the last week
        today = date.today()
        for i in range(7):
            work_date = today - timedelta(days=i)
            time_entry = TimeEntry(
                user_id=test_user.id,
                project_id=project.id,
                description=f"Work on day {i+1}",
                start_time=datetime.combine(
                    work_date, datetime.min.time().replace(hour=9)
                ),
                end_time=datetime.combine(
                    work_date, datetime.min.time().replace(hour=17)
                ),
                duration_minutes=480,  # 8 hours
                work_date=work_date,
                is_billable=True,
                status="completed",
            )
            db_session.add(time_entry)

        await db_session.commit()

        # Test productivity insights
        response = await authenticated_client.get("/api/v1/analytics/time/productivity")

        assert response.status_code == 200
        data = response.json()

        # Check response structure
        assert "overview" in data
        assert "period" in data
        assert "patterns" in data

        # Check overview section
        overview = data["overview"]
        assert "total_hours" in overview
        assert "total_sessions" in overview
        assert "productivity_score" in overview

        # Verify data makes sense
        assert overview["total_hours"] > 0
        assert overview["total_sessions"] > 0
        assert 0 <= overview["productivity_score"] <= 100

    async def test_get_productivity_insights_with_date_range(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test productivity insights with specific date range"""
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()

        response = await authenticated_client.get(
            f"/api/v1/analytics/time/productivity?start_date={start_date}&end_date={end_date}"
        )

        assert response.status_code == 200
        data = response.json()
        assert "overview" in data
        assert "period" in data
        assert data["period"]["start_date"] == start_date.isoformat()
        assert data["period"]["end_date"] == end_date.isoformat()

    async def test_get_time_distribution(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test time distribution analysis grouped by project"""
        # Create multiple projects
        projects = []
        for i in range(3):
            project = Project(
                title=f"Distribution Project {i+1}",
                description=f"Project {i+1} for distribution testing",
                client_id=test_client.id,
                user_id=test_user.id,
                billing_type="time_and_materials",
                hourly_rate=Decimal("100.00"),
                currency="USD",
                status="active",
                is_billable=True,
                is_active=True,
            )
            db_session.add(project)
            projects.append(project)

        await db_session.commit()
        for project in projects:
            await db_session.refresh(project)

        # Create time entries for each project
        today = date.today()
        for i, project in enumerate(projects):
            time_entry = TimeEntry(
                user_id=test_user.id,
                project_id=project.id,
                description=f"Work on project {i+1}",
                start_time=datetime.combine(today, datetime.min.time().replace(hour=9)),
                end_time=datetime.combine(
                    today, datetime.min.time().replace(hour=9 + i + 2)
                ),
                duration_minutes=(i + 2) * 60,  # Different durations
                work_date=today,
                is_billable=True,
                status="completed",
            )
            db_session.add(time_entry)

        await db_session.commit()

        # Test time distribution by project
        response = await authenticated_client.get(
            "/api/v1/analytics/time/distribution?group_by=project"
        )

        assert response.status_code == 200
        data = response.json()

        assert "summary" in data
        assert "distribution" in data
        assert "period" in data

        # Verify data structure
        summary = data["summary"]
        assert "total_hours" in summary
        assert "group_by" in summary
        assert summary["group_by"] == "project"

        # Should have entries for each project
        distribution = data["distribution"]
        assert len(distribution) >= 3

        # Each distribution entry should have required fields
        for entry in distribution:
            assert "name" in entry
            assert "hours" in entry
            assert "percentage" in entry

    async def test_get_time_distribution_by_client(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test time distribution analysis grouped by client"""
        response = await authenticated_client.get(
            "/api/v1/analytics/time/distribution?group_by=client"
        )

        assert response.status_code == 200
        data = response.json()

        assert "summary" in data
        assert "distribution" in data

        summary = data["summary"]
        assert "group_by" in summary
        assert summary["group_by"] == "client"

    async def test_get_billable_analysis(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test billable vs non-billable time analysis"""
        # Create project
        project = Project(
            title="Billable Analysis Project",
            description="Project for billable analysis testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=Decimal("100.00"),
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Create billable and non-billable time entries
        today = date.today()

        # Billable entry
        billable_entry = TimeEntry(
            user_id=test_user.id,
            project_id=project.id,
            description="Billable work",
            start_time=datetime.combine(today, datetime.min.time().replace(hour=9)),
            end_time=datetime.combine(today, datetime.min.time().replace(hour=13)),
            duration_minutes=240,  # 4 hours
            work_date=today,
            is_billable=True,
            status="completed",
        )

        # Non-billable entry
        non_billable_entry = TimeEntry(
            user_id=test_user.id,
            project_id=project.id,
            description="Non-billable work",
            start_time=datetime.combine(today, datetime.min.time().replace(hour=13)),
            end_time=datetime.combine(today, datetime.min.time().replace(hour=15)),
            duration_minutes=120,  # 2 hours
            work_date=today,
            is_billable=False,
            status="completed",
        )

        db_session.add_all([billable_entry, non_billable_entry])
        await db_session.commit()

        # Test billable analysis
        response = await authenticated_client.get("/api/v1/analytics/time/billable")

        assert response.status_code == 200
        data = response.json()

        assert "overview" in data
        assert "period" in data
        assert "billable_hours" in data["overview"]
        assert "non_billable_hours" in data["overview"]
        assert "total_hours" in data["overview"]
        assert "billable_percentage" in data["overview"]
        assert "revenue_potential" in data["overview"]

        # Verify calculations
        assert data["overview"]["billable_hours"] > 0
        assert data["overview"]["non_billable_hours"] > 0
        assert (
            data["overview"]["total_hours"]
            == data["overview"]["billable_hours"]
            + data["overview"]["non_billable_hours"]
        )
        assert 0 <= data["overview"]["billable_percentage"] <= 100

    async def test_get_efficiency_metrics(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test efficiency metrics and project velocity"""
        # Create project
        project = Project(
            title="Efficiency Metrics Project",
            description="Project for efficiency metrics testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=Decimal("100.00"),
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Create time entries with different patterns
        today = date.today()
        for i in range(5):
            work_date = today - timedelta(days=i)
            time_entry = TimeEntry(
                user_id=test_user.id,
                project_id=project.id,
                description=f"Efficiency work day {i+1}",
                start_time=datetime.combine(
                    work_date, datetime.min.time().replace(hour=9)
                ),
                end_time=datetime.combine(
                    work_date, datetime.min.time().replace(hour=17)
                ),
                duration_minutes=480,  # 8 hours
                work_date=work_date,
                is_billable=True,
                status="completed",
            )
            db_session.add(time_entry)

        await db_session.commit()

        # Test efficiency metrics
        response = await authenticated_client.get("/api/v1/analytics/time/efficiency")

        assert response.status_code == 200
        data = response.json()

        assert "average_daily_hours" in data
        assert "consistency_score" in data
        assert "velocity_trend" in data
        assert "efficiency_rating" in data
        assert "recommendations" in data
        assert "period" in data
        assert "session_analysis" in data
        assert "task_efficiency" in data
        assert "weekly_velocity" in data

        # Verify data types and ranges
        assert data["average_daily_hours"] >= 0
        assert 0 <= data["consistency_score"] <= 100
        assert data["velocity_trend"] in ["increasing", "decreasing", "stable"]
        assert 0 <= data["efficiency_rating"] <= 100
        assert isinstance(data["recommendations"], list)

    async def test_export_time_data_csv(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test exporting time data in CSV format"""
        start_date = date.today() - timedelta(days=7)
        end_date = date.today()

        response = await authenticated_client.get(
            f"/api/v1/analytics/time/export?format=csv&start_date={start_date}&end_date={end_date}"
        )

        assert response.status_code == 200
        data = response.json()

        # Since export is not fully implemented, check for placeholder response
        assert "message" in data
        assert "requested_format" in data
        assert data["requested_format"] == "csv"
        assert "date_range" in data
        assert "include_analytics" in data

    async def test_export_time_data_pdf(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test exporting time data in PDF format"""
        response = await authenticated_client.get(
            "/api/v1/analytics/time/export?format=pdf&include_analytics=true"
        )

        assert response.status_code == 200
        data = response.json()

        # Since export is not fully implemented, check for placeholder response
        assert "message" in data
        assert "requested_format" in data
        assert data["requested_format"] == "pdf"
        assert "date_range" in data
        assert "include_analytics" in data

    async def test_analytics_with_no_data(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test analytics endpoints when user has no time entries"""
        # Test productivity insights with no data
        response = await authenticated_client.get("/api/v1/analytics/time/productivity")
        assert response.status_code == 200
        data = response.json()
        assert "overview" in data
        assert data["overview"]["total_hours"] == 0
        assert data["overview"]["total_sessions"] == 0

        # Test time distribution with no data
        response = await authenticated_client.get("/api/v1/analytics/time/distribution")
        assert response.status_code == 200
        data = response.json()

        summary = data["summary"]
        assert summary["total_hours"] == 0
        assert len(data["distribution"]) == 0

        # Test billable analysis with no data
        response = await authenticated_client.get("/api/v1/analytics/time/billable")
        assert response.status_code == 200
        data = response.json()
        assert "overview" in data
        assert data["overview"]["billable_hours"] == 0
        assert data["overview"]["non_billable_hours"] == 0

    async def test_analytics_unauthorized_access(self, async_client: AsyncClient):
        """Test that unauthorized users cannot access analytics endpoints"""
        endpoints = [
            "/api/v1/analytics/time/productivity",
            "/api/v1/analytics/time/distribution",
            "/api/v1/analytics/time/billable",
            "/api/v1/analytics/time/efficiency",
            "/api/v1/analytics/time/export",
        ]

        for endpoint in endpoints:
            response = await async_client.get(endpoint)
            assert response.status_code == 401

    async def test_invalid_group_by_parameter(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test time distribution with invalid group_by parameter"""
        response = await authenticated_client.get(
            "/api/v1/analytics/time/distribution?group_by=invalid"
        )
        assert response.status_code == 422  # Validation error

    async def test_invalid_export_format(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test export with invalid format parameter"""
        response = await authenticated_client.get(
            "/api/v1/analytics/time/export?format=invalid"
        )
        assert response.status_code == 422  # Validation error

    async def test_invalid_date_range(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test analytics with invalid date range"""
        # End date before start date
        start_date = date.today()
        end_date = date.today() - timedelta(days=7)

        response = await authenticated_client.get(
            f"/api/v1/analytics/time/productivity?start_date={start_date}&end_date={end_date}"
        )

        # Should handle gracefully or return validation error
        assert response.status_code in [200, 400, 422]

    async def test_analytics_data_isolation(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        db_session: AsyncSession,
    ):
        """Test that analytics only show data for the authenticated user"""
        try:
            import uuid

            unique_id = str(uuid.uuid4())[:8]

            # Create another user with password hash
            other_user = User(
                email=f"analytics_other_{unique_id}@example.com",
                first_name="Other",
                last_name="User",
                password_hash="$argon2id$v=19$m=65536,t=3,p=4$dummy_hash",
                is_active=True,
                is_verified=True,
            )
            db_session.add(other_user)
            await db_session.flush()
            await db_session.refresh(other_user)

            # Create client for other user
            other_client = Client(
                user_id=other_user.id,
                name=f"Analytics Other Client {unique_id}",
                email=f"analytics_otherclient_{unique_id}@example.com",
                company="Other Company",
                is_active=True,
            )
            db_session.add(other_client)
            await db_session.flush()
            await db_session.refresh(other_client)

            # Create project and time entry for other user
            other_project = Project(
                title=f"Analytics Other User Project {unique_id}",
                description="Project for other user",
                client_id=other_client.id,
                user_id=other_user.id,
                billing_type="time_and_materials",
                hourly_rate=Decimal("150.00"),
                currency="USD",
                status="active",
                is_billable=True,
                is_active=True,
            )
            db_session.add(other_project)
            await db_session.flush()
            await db_session.refresh(other_project)

            other_time_entry = TimeEntry(
                user_id=other_user.id,
                project_id=other_project.id,
                description="Other user work",
                start_time=datetime.combine(
                    date.today(), datetime.min.time().replace(hour=9)
                ),
                end_time=datetime.combine(
                    date.today(), datetime.min.time().replace(hour=17)
                ),
                duration_minutes=480,
                work_date=date.today(),
                is_billable=True,
                status="completed",
            )
            db_session.add(other_time_entry)
            await db_session.commit()

            # Test that current user's analytics don't include other user's data
            response = await authenticated_client.get(
                "/api/v1/analytics/time/productivity"
            )
            assert response.status_code == 200
            data = response.json()

            # The analytics should not include the other user's 8 hours
            # Since test_user has no time entries, total_hours should be 0
            assert "overview" in data
            assert data["overview"]["total_hours"] == 0
            assert data["overview"]["total_sessions"] == 0

        except Exception as e:
            await db_session.rollback()
            raise e
