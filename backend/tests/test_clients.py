"""Tests for client management functionality"""

import uuid
from datetime import datetime

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from httpx import Async<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.client import Client
from app.models.user import User
from app.schemas.client import ClientCreate, ClientUpdate


class TestClientModel:
    """Test Client model functionality"""

    async def test_create_client(self, db_session: AsyncSession, test_user: User):
        """Test creating a client"""
        client = Client(
            user_id=test_user.id,
            name="Test Client",
            email="<EMAIL>",
            company="Test Company",
            phone="+1234567890",
            position="CEO",
            address_line1="123 Main St",
            city="Test City",
            state="Test State",
            postal_code="12345",
            country="Test Country",
            notes="Test notes",
            is_active=True,
        )

        db_session.add(client)
        await db_session.commit()
        await db_session.refresh(client)

        assert client.id is not None
        assert client.name == "Test Client"
        assert client.email == "<EMAIL>"
        assert client.company == "Test Company"
        assert client.user_id == test_user.id
        assert client.is_active is True
        assert client.created_at is not None
        assert client.updated_at is not None

    async def test_client_relationships(
        self, db_session: AsyncSession, test_user: User
    ):
        """Test client relationships with user"""
        client = Client(
            user_id=test_user.id,
            name="Relationship Test Client",
            email="<EMAIL>",
        )

        db_session.add(client)
        await db_session.commit()
        await db_session.refresh(client)

        # Test user relationship
        assert client.user is not None
        assert client.user.id == test_user.id
        assert client.user.email == test_user.email

    async def test_client_repr(self, db_session: AsyncSession, test_user: User):
        """Test client string representation"""
        client = Client(
            user_id=test_user.id, name="Repr Test Client", email="<EMAIL>"
        )

        db_session.add(client)
        await db_session.commit()

        expected_repr = "<Client(name=Repr Test Client, email=<EMAIL>)>"
        assert repr(client) == expected_repr

    async def test_client_optional_fields(
        self, db_session: AsyncSession, test_user: User
    ):
        """Test client with minimal required fields"""
        client = Client(user_id=test_user.id, name="Minimal Client")

        db_session.add(client)
        await db_session.commit()
        await db_session.refresh(client)

        assert client.id is not None
        assert client.name == "Minimal Client"
        assert client.email is None
        assert client.company is None
        assert client.phone is None
        assert client.is_active is True  # Default value
        assert client.portal_enabled is False  # Default value


class TestClientSchemas:
    """Test Client Pydantic schemas"""

    def test_client_create_schema(self):
        """Test ClientCreate schema validation"""
        client_data = {
            "name": "Schema Test Client",
            "email": "<EMAIL>",
            "company": "Schema Company",
            "phone": "+1234567890",
            "position": "Manager",
            "address_line1": "456 Schema St",
            "city": "Schema City",
            "state": "Schema State",
            "postal_code": "54321",
            "country": "Schema Country",
            "notes": "Schema test notes",
            "is_active": True,
        }

        client_create = ClientCreate(**client_data)

        assert client_create.name == "Schema Test Client"
        assert client_create.email == "<EMAIL>"
        assert client_create.company == "Schema Company"
        assert client_create.is_active is True

    def test_client_create_minimal(self):
        """Test ClientCreate with minimal required fields"""
        client_data = {"name": "Minimal Schema Client"}

        client_create = ClientCreate(**client_data)

        assert client_create.name == "Minimal Schema Client"
        assert client_create.email is None
        assert client_create.company is None
        assert client_create.is_active is True  # Default value

    def test_client_create_validation_errors(self):
        """Test ClientCreate schema validation errors"""
        # Test empty name
        with pytest.raises(ValueError):
            ClientCreate(name="")

        # Test name too long
        with pytest.raises(ValueError):
            ClientCreate(name="x" * 201)

        # Test invalid email
        with pytest.raises(ValueError):
            ClientCreate(name="Test", email="invalid-email")

    def test_client_update_schema(self):
        """Test ClientUpdate schema"""
        update_data = {
            "name": "Updated Client Name",
            "email": "<EMAIL>",
            "is_active": False,
        }

        client_update = ClientUpdate(**update_data)

        assert client_update.name == "Updated Client Name"
        assert client_update.email == "<EMAIL>"
        assert client_update.is_active is False

    def test_client_update_partial(self):
        """Test ClientUpdate with partial data"""
        update_data = {"name": "Partially Updated Client"}

        client_update = ClientUpdate(**update_data)

        assert client_update.name == "Partially Updated Client"
        assert client_update.email is None
        assert client_update.company is None
        assert client_update.is_active is None


class TestClientAPI:
    """Test Client API endpoints"""

    async def test_create_client_endpoint(self, authenticated_client: AsyncClient):
        """Test POST /api/v1/clients endpoint"""
        client_data = {
            "name": "API Test Client",
            "email": "<EMAIL>",
            "company": "API Company",
            "phone": "+1234567890",
            "position": "Developer",
            "notes": "Created via API test",
        }

        response = await authenticated_client.post("/api/v1/clients/", json=client_data)

        assert response.status_code == 201
        data = response.json()

        assert data["name"] == "API Test Client"
        assert data["email"] == "<EMAIL>"
        assert data["company"] == "API Company"
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

    async def test_get_clients_endpoint(
        self, authenticated_client: AsyncClient, test_client_data: dict
    ):
        """Test GET /api/v1/clients endpoint"""
        # First create a client
        create_response = await authenticated_client.post(
            "/api/v1/clients/", json=test_client_data
        )
        assert create_response.status_code == 201

        # Get all clients
        response = await authenticated_client.get("/api/v1/clients/")

        assert response.status_code == 200
        data = response.json()

        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "per_page" in data
        assert len(data["items"]) >= 1

        # Check if our created client is in the list
        client_names = [item["name"] for item in data["items"]]
        assert test_client_data["name"] in client_names

    async def test_get_client_by_id_endpoint(
        self, authenticated_client: AsyncClient, test_client_data: dict
    ):
        """Test GET /api/v1/clients/{id} endpoint"""
        # First create a client
        create_response = await authenticated_client.post(
            "/api/v1/clients/", json=test_client_data
        )
        assert create_response.status_code == 201
        created_client = create_response.json()
        client_id = created_client["id"]

        # Then get the specific client
        response = await authenticated_client.get(f"/api/v1/clients/{client_id}")

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == client_id
        assert data["name"] == test_client_data["name"]
        assert data["email"] == test_client_data["email"]

    async def test_update_client_endpoint(
        self, authenticated_client: AsyncClient, test_client_data: dict
    ):
        """Test PUT /api/v1/clients/{id} endpoint"""
        # First create a client
        create_response = await authenticated_client.post(
            "/api/v1/clients/", json=test_client_data
        )
        assert create_response.status_code == 201
        created_client = create_response.json()
        client_id = created_client["id"]

        # Update the client
        update_data = {
            "name": "Updated API Client",
            "company": "Updated Company",
            "is_active": False,
        }

        response = await authenticated_client.put(
            f"/api/v1/clients/{client_id}", json=update_data
        )

        assert response.status_code == 200
        data = response.json()

        assert data["name"] == "Updated API Client"
        assert data["company"] == "Updated Company"
        assert data["is_active"] is False
        assert data["email"] == test_client_data["email"]  # Should remain unchanged

    async def test_delete_client_endpoint(
        self, authenticated_client: AsyncClient, test_client_data: dict
    ):
        """Test DELETE /api/v1/clients/{id} endpoint"""
        # First create a client
        create_response = await authenticated_client.post(
            "/api/v1/clients/", json=test_client_data
        )
        assert create_response.status_code == 201
        created_client = create_response.json()
        client_id = created_client["id"]

        # Delete the client
        response = await authenticated_client.delete(f"/api/v1/clients/{client_id}")

        assert response.status_code == 204

        # Verify client is deleted
        get_response = await authenticated_client.get(f"/api/v1/clients/{client_id}")
        assert get_response.status_code == 404

    def test_client_api_unauthorized(self, client: TestClient):
        """Test client endpoints without authentication"""
        response = client.get("/api/v1/clients/")
        assert response.status_code == 401

        response = client.post("/api/v1/clients/", json={"name": "Test"})
        assert response.status_code == 401

    async def test_client_api_validation_errors(
        self, authenticated_client: AsyncClient
    ):
        """Test client API validation errors"""
        # Test missing required fields
        response = await authenticated_client.post("/api/v1/clients/", json={})
        assert response.status_code == 422

        # Test invalid email
        response = await authenticated_client.post(
            "/api/v1/clients/", json={"name": "Test", "email": "invalid-email"}
        )
        assert response.status_code == 422


@pytest.fixture
def test_client_data():
    """Fixture providing test client data"""
    return {
        "name": "Test Client Fixture",
        "email": "<EMAIL>",
        "company": "Fixture Company",
        "phone": "+1234567890",
        "position": "Test Position",
        "notes": "Test client created by fixture",
    }
