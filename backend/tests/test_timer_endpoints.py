"""Tests for timer management endpoints"""

import uuid
from datetime import datetime, timezone

import pytest
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.client import Client
from app.models.project import Project, TimeEntry
from app.models.user import User


@pytest.mark.asyncio
class TestTimerEndpoints:
    """Test timer management API endpoints"""

    async def test_start_timer_success(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test successfully starting a timer"""
        # Create a project for the timer
        project = Project(
            title="Timer Test Project",
            description="Project for timer testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        timer_data = {
            "project_id": str(project.id),
            "description": "Working on feature development",
            "task_name": "Feature Implementation",
            "device_id": "web-browser",
            "mood_rating": 4,
            "focus_level": 5,
        }

        response = await authenticated_client.post(
            "/api/v1/timer/start", json=timer_data
        )

        assert response.status_code == 201
        data = response.json()
        assert "timer_id" in data
        assert data["project_id"] == str(project.id)
        assert data["description"] == "Working on feature development"
        assert data["task_name"] == "Feature Implementation"
        assert data["is_active"] is True
        assert data["is_paused"] is False
        assert data["device_id"] == "web-browser"
        assert data["mood_rating"] == 4
        assert data["focus_level"] == 5

    async def test_stop_timer_success(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test successfully stopping a timer"""
        # Create a project
        project = Project(
            title="Stop Timer Test Project",
            description="Project for stop timer testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Start a timer first
        timer_data = {
            "project_id": str(project.id),
            "description": "Timer to be stopped",
            "device_id": "web",
        }
        start_response = await authenticated_client.post(
            "/api/v1/timer/start", json=timer_data
        )
        assert start_response.status_code == 201
        timer_id = start_response.json()["timer_id"]

        # Stop the timer
        stop_data = {"timer_id": timer_id, "device_id": "web"}
        response = await authenticated_client.post("/api/v1/timer/stop", json=stop_data)

        assert response.status_code == 200
        data = response.json()
        assert data["timer_id"] == timer_id
        assert data["is_active"] is False

    async def test_pause_timer_success(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test successfully pausing a timer"""
        # Create a project
        project = Project(
            title="Pause Timer Test Project",
            description="Project for pause timer testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Start a timer first
        timer_data = {
            "project_id": str(project.id),
            "description": "Timer to be paused",
            "device_id": "web",
        }
        start_response = await authenticated_client.post(
            "/api/v1/timer/start", json=timer_data
        )
        assert start_response.status_code == 201
        timer_id = start_response.json()["timer_id"]

        # Pause the timer
        pause_data = {"timer_id": timer_id, "device_id": "web"}
        response = await authenticated_client.post(
            "/api/v1/timer/pause", json=pause_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["timer_id"] == timer_id
        assert data["is_paused"] is True
        assert data["is_active"] is True

    async def test_resume_timer_success(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test successfully resuming a paused timer"""
        # Create a project
        project = Project(
            title="Resume Timer Test Project",
            description="Project for resume timer testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Start and pause a timer first
        timer_data = {
            "project_id": str(project.id),
            "description": "Timer to be resumed",
            "device_id": "web",
        }
        start_response = await authenticated_client.post(
            "/api/v1/timer/start", json=timer_data
        )
        timer_id = start_response.json()["timer_id"]

        await authenticated_client.post(
            "/api/v1/timer/pause", json={"timer_id": timer_id, "device_id": "web"}
        )

        # Resume the timer
        resume_data = {"timer_id": timer_id, "device_id": "web"}
        response = await authenticated_client.post(
            "/api/v1/timer/resume", json=resume_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["timer_id"] == timer_id
        assert data["is_paused"] is False
        assert data["is_active"] is True

    async def test_get_timer_status(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test getting current timer status"""
        # Create a project
        project = Project(
            title="Status Timer Test Project",
            description="Project for status timer testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Start a timer
        timer_data = {
            "project_id": str(project.id),
            "description": "Status check timer",
            "device_id": "web",
        }
        start_response = await authenticated_client.post(
            "/api/v1/timer/start", json=timer_data
        )
        timer_id = start_response.json()["timer_id"]

        # Get timer status
        response = await authenticated_client.get("/api/v1/timer/status")

        assert response.status_code == 200
        data = response.json()
        assert "has_active_timer" in data
        assert "timer" in data
        if data["has_active_timer"] and data["timer"]:
            assert data["timer"]["timer_id"] == timer_id
            assert data["timer"]["is_active"] is True

    async def test_timer_heartbeat(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test timer heartbeat functionality"""
        # Create a project
        project = Project(
            title="Heartbeat Timer Test Project",
            description="Project for heartbeat timer testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Start a timer
        timer_data = {
            "project_id": str(project.id),
            "description": "Heartbeat timer",
            "device_id": "web",
        }
        start_response = await authenticated_client.post(
            "/api/v1/timer/start", json=timer_data
        )
        timer_id = start_response.json()["timer_id"]

        # Send heartbeat
        heartbeat_data = {
            "timer_id": timer_id,
            "device_id": "web",
            "interruption_occurred": False,
        }
        response = await authenticated_client.post(
            "/api/v1/timer/heartbeat", json=heartbeat_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "timer_status" in data

    async def test_start_timer_without_project(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test starting timer with invalid project ID"""
        timer_data = {
            "project_id": str(uuid.uuid4()),  # Non-existent project
            "description": "Invalid project timer",
            "device_id": "web",
        }

        response = await authenticated_client.post(
            "/api/v1/timer/start", json=timer_data
        )
        assert response.status_code == 400

    async def test_stop_nonexistent_timer(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test stopping a timer that doesn't exist"""
        stop_data = {"timer_id": str(uuid.uuid4()), "device_id": "web"}

        response = await authenticated_client.post("/api/v1/timer/stop", json=stop_data)
        assert response.status_code == 404

    async def test_multiple_active_timers_prevention(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test that starting a new timer stops the previous active timer"""
        # Create projects
        project1 = Project(
            title="Project 1",
            description="First project",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        project2 = Project(
            title="Project 2",
            description="Second project",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add_all([project1, project2])
        await db_session.commit()
        await db_session.refresh(project1)
        await db_session.refresh(project2)

        # Start first timer
        timer1_data = {
            "project_id": str(project1.id),
            "description": "First timer",
            "device_id": "web",
        }
        response1 = await authenticated_client.post(
            "/api/v1/timer/start", json=timer1_data
        )
        assert response1.status_code == 201
        timer1_id = response1.json()["timer_id"]

        # Start second timer (should stop first one)
        timer2_data = {
            "project_id": str(project2.id),
            "description": "Second timer",
            "device_id": "web",
        }
        response2 = await authenticated_client.post(
            "/api/v1/timer/start", json=timer2_data
        )
        assert response2.status_code == 201
        timer2_id = response2.json()["timer_id"]

        # Check status - should only have one active timer
        status_response = await authenticated_client.get("/api/v1/timer/status")
        assert status_response.status_code == 200
        status_data = status_response.json()

        if status_data["has_active_timer"] and status_data["timer"]:
            # The active timer should be the second one
            assert status_data["timer"]["timer_id"] == timer2_id

    async def test_timer_unauthorized_access(self, async_client: AsyncClient):
        """Test that unauthorized users cannot access timer endpoints"""
        # Test without authentication
        response = await async_client.get("/api/v1/timer/status")
        assert response.status_code == 401

        # Test starting timer without auth
        timer_data = {
            "project_id": str(uuid.uuid4()),
            "description": "Unauthorized timer",
            "device_id": "web",
        }
        response = await async_client.post("/api/v1/timer/start", json=timer_data)
        assert response.status_code == 401

    async def test_invalid_timer_data(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test starting timer with invalid data"""
        invalid_data = {
            "project_id": "invalid-uuid",
            "description": "Invalid timer",
            "device_id": "web",
            "mood_rating": 10,  # Invalid rating (should be 1-5)
            "focus_level": 0,  # Invalid level (should be 1-5)
        }

        response = await authenticated_client.post(
            "/api/v1/timer/start", json=invalid_data
        )
        assert response.status_code == 422  # Validation error

    async def test_timer_device_isolation(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        test_client: Client,
        db_session: AsyncSession,
    ):
        """Test that timers are isolated by device ID"""
        # Create a project
        project = Project(
            title="Device Isolation Test Project",
            description="Project for device isolation testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            hourly_rate=100.00,
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Start timer on web device
        web_timer_data = {
            "project_id": str(project.id),
            "description": "Web timer",
            "device_id": "web",
        }
        web_response = await authenticated_client.post(
            "/api/v1/timer/start", json=web_timer_data
        )
        assert web_response.status_code == 201
        web_timer_id = web_response.json()["timer_id"]

        # Start timer on mobile device
        mobile_timer_data = {
            "project_id": str(project.id),
            "description": "Mobile timer",
            "device_id": "mobile",
        }
        mobile_response = await authenticated_client.post(
            "/api/v1/timer/start", json=mobile_timer_data
        )
        assert mobile_response.status_code == 201
        mobile_timer_id = mobile_response.json()["timer_id"]

        # Both timers should be different
        assert web_timer_id != mobile_timer_id

        # Stop web timer specifically
        stop_web_data = {"timer_id": web_timer_id, "device_id": "web"}
        stop_response = await authenticated_client.post(
            "/api/v1/timer/stop", json=stop_web_data
        )
        assert stop_response.status_code == 200

        # Try to stop mobile timer using wrong device ID (should fail)
        stop_mobile_wrong_device = {
            "timer_id": mobile_timer_id,
            "device_id": "web",  # Wrong device ID
        }
        wrong_device_response = await authenticated_client.post(
            "/api/v1/timer/stop", json=stop_mobile_wrong_device
        )
        assert wrong_device_response.status_code == 404
