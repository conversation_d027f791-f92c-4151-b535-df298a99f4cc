"""Tests for project management functionality"""

import uuid
from datetime import datetime, timedelta, timezone
from decimal import Decimal

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.client import Client
from app.models.project import Project, ProjectMilestone, TimeEntry
from app.models.user import User
from app.schemas.project import (MilestoneCreate, ProjectCreate, ProjectUpdate,
                                 TimeEntryCreate)


class TestProjectModel:
    """Test Project model functionality"""

    async def test_create_project(
        self, db_session: AsyncSession, test_user: User, test_client: Client
    ):
        """Test creating a project"""
        project = Project(
            title="Test Project",
            description="A test project for testing",
            client_id=test_client.id,
            user_id=test_user.id,
            billing_type="time_and_materials",
            total_budget=Decimal("10000.00"),
            estimated_hours=Decimal("100.0"),
            hourly_rate=Decimal("100.00"),
            currency="USD",
            status="active",
            is_billable=True,
            is_active=True,
        )

        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        assert project.id is not None
        assert project.title == "Test Project"
        assert project.description == "A test project for testing"
        assert project.client_id == test_client.id
        assert project.user_id == test_user.id
        assert project.billing_type == "time_and_materials"
        assert project.total_budget == Decimal("10000.00")
        assert project.estimated_hours == Decimal("100.0")
        assert project.hourly_rate == Decimal("100.00")
        assert project.currency == "USD"
        assert project.status == "active"
        assert project.is_billable is True
        assert project.is_active is True
        assert project.created_at is not None
        assert project.updated_at is not None

    async def test_project_relationships(
        self, db_session: AsyncSession, test_user: User, test_client: Client
    ):
        """Test project relationships"""
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload

        project = Project(
            title="Relationships Test Project",
            client_id=test_client.id,
            user_id=test_user.id,
        )

        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Load project with relationships
        stmt = (
            select(Project)
            .options(selectinload(Project.client), selectinload(Project.user))
            .where(Project.id == project.id)
        )
        result = await db_session.execute(stmt)
        project = result.scalar_one()

        # Test client relationship
        assert project.client is not None
        assert project.client.id == test_client.id
        assert project.client.name == test_client.name

        # Test user relationship
        assert project.user is not None
        assert project.user.id == test_user.id
        assert project.user.email == test_user.email

    async def test_project_properties(
        self, db_session: AsyncSession, test_user: User, test_client: Client
    ):
        """Test project calculated properties"""
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload

        project = Project(
            title="Properties Test Project",
            client_id=test_client.id,
            user_id=test_user.id,
            hourly_rate=Decimal("50.00"),
        )

        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        # Load project with relationships for property access
        stmt = (
            select(Project)
            .options(
                selectinload(Project.time_entries), selectinload(Project.milestones)
            )
            .where(Project.id == project.id)
        )
        result = await db_session.execute(stmt)
        project = result.scalar_one()

        # Test initial properties
        assert project.total_logged_hours == Decimal("0.00")
        assert project.total_billable_amount == Decimal("0.00")
        assert project.completion_percentage == 0.0

        # Add a time entry
        time_entry = TimeEntry(
            project_id=project.id,
            user_id=test_user.id,
            duration_minutes=120,  # 2 hours
            description="Test work",
            hourly_rate=Decimal("50.00"),
            is_billable=True,
            work_date=datetime.now(timezone.utc),
        )

        # Calculate billable amount
        time_entry.calculate_billable_amount()

        db_session.add(time_entry)
        await db_session.commit()
        await db_session.refresh(time_entry)

        # Reload project with relationships after adding time entry
        db_session.expunge_all()  # Clear session to force fresh load
        stmt = (
            select(Project)
            .options(
                selectinload(Project.time_entries), selectinload(Project.milestones)
            )
            .where(Project.id == project.id)
        )
        result = await db_session.execute(stmt)
        project = result.scalar_one()

        # Test updated properties - check if time entry was properly saved
        assert (
            len(project.time_entries) == 1
        ), f"Expected 1 time entry, got {len(project.time_entries)}"
        time_entry = project.time_entries[0]
        assert time_entry.duration_minutes == 120
        assert not time_entry.is_deleted

        # Now test the calculated properties
        assert project.total_logged_hours == Decimal("2.00")
        assert project.total_billable_amount == Decimal("100.00")

    async def test_project_defaults(
        self, db_session: AsyncSession, test_user: User, test_client: Client
    ):
        """Test project default values"""
        project = Project(
            title="Defaults Test Project",
            client_id=test_client.id,
            user_id=test_user.id,
        )

        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)

        assert project.billing_type == "time_and_materials"
        assert project.currency == "USD"
        assert project.status == "draft"
        assert project.is_billable is True
        assert project.is_active is True


class TestProjectMilestoneModel:
    """Test ProjectMilestone model functionality"""

    async def test_create_milestone(
        self, db_session: AsyncSession, test_project: Project
    ):
        """Test creating a project milestone"""
        milestone = ProjectMilestone(
            project_id=test_project.id,
            title="Test Milestone",
            description="A test milestone",
            estimated_hours=Decimal("20.0"),
            payment_amount=Decimal("2000.00"),
            status="pending",
            sequence_number=1,
            is_client_visible=True,
        )

        db_session.add(milestone)
        await db_session.commit()
        await db_session.refresh(milestone)

        assert milestone.id is not None
        assert milestone.project_id == test_project.id
        assert milestone.title == "Test Milestone"
        assert milestone.description == "A test milestone"
        assert milestone.estimated_hours == Decimal("20.0")
        assert milestone.payment_amount == Decimal("2000.00")
        assert milestone.status == "pending"
        assert milestone.sequence_number == 1
        assert milestone.is_client_visible is True
        assert milestone.billing_status == "unbilled"

    async def test_milestone_properties(
        self, db_session: AsyncSession, test_project: Project
    ):
        """Test milestone calculated properties"""
        # Set due_date to tomorrow to ensure it's not overdue
        future_date = datetime.now(timezone.utc) + timedelta(days=1)
        milestone = ProjectMilestone(
            project_id=test_project.id,
            title="Properties Test Milestone",
            due_date=future_date,
        )

        db_session.add(milestone)
        await db_session.commit()
        await db_session.refresh(milestone)

        # Test is_overdue property (should be False for current date)
        assert milestone.is_overdue is False

        # Test logged_hours property
        assert milestone.logged_hours == Decimal("0.00")

    async def test_milestone_mark_completed(
        self, db_session: AsyncSession, test_project: Project
    ):
        """Test marking milestone as completed"""
        milestone = ProjectMilestone(
            project_id=test_project.id,
            title="Completion Test Milestone",
            status="in_progress",
        )

        db_session.add(milestone)
        await db_session.commit()

        # Mark as completed
        milestone.mark_completed()
        await db_session.commit()
        await db_session.refresh(milestone)

        assert milestone.status == "completed"
        assert milestone.completed_at is not None


class TestTimeEntryModel:
    """Test TimeEntry model functionality"""

    async def test_create_time_entry(
        self, db_session: AsyncSession, test_user: User, test_project: Project
    ):
        """Test creating a time entry"""
        work_date = datetime.now(timezone.utc)
        time_entry = TimeEntry(
            project_id=test_project.id,
            user_id=test_user.id,
            duration_minutes=90,
            description="Test time entry",
            task_name="Testing",
            hourly_rate=Decimal("75.00"),
            is_billable=True,
            status="submitted",
            work_date=work_date,
        )

        db_session.add(time_entry)
        await db_session.commit()
        await db_session.refresh(time_entry)

        assert time_entry.id is not None
        assert time_entry.project_id == test_project.id
        assert time_entry.user_id == test_user.id
        assert time_entry.duration_minutes == 90
        assert time_entry.description == "Test time entry"
        assert time_entry.task_name == "Testing"
        assert time_entry.hourly_rate == Decimal("75.00")
        assert time_entry.is_billable is True
        assert time_entry.status == "submitted"
        assert time_entry.work_date == work_date
        assert time_entry.billing_status == "unbilled"

    async def test_time_entry_calculate_billable_amount(
        self, db_session: AsyncSession, test_user: User, test_project: Project
    ):
        """Test time entry billable amount calculation"""
        time_entry = TimeEntry(
            project_id=test_project.id,
            user_id=test_user.id,
            duration_minutes=120,  # 2 hours
            hourly_rate=Decimal("50.00"),
            is_billable=True,
            work_date=datetime.now(timezone.utc),
        )

        db_session.add(time_entry)
        await db_session.commit()

        # Calculate billable amount
        time_entry.calculate_billable_amount()
        await db_session.commit()
        await db_session.refresh(time_entry)

        assert time_entry.billable_amount == Decimal("100.00")  # 2 hours * $50

    async def test_time_entry_duration_hours_property(
        self, db_session: AsyncSession, test_user: User, test_project: Project
    ):
        """Test time entry duration_hours property"""
        time_entry = TimeEntry(
            project_id=test_project.id,
            user_id=test_user.id,
            duration_minutes=150,  # 2.5 hours
            work_date=datetime.now(timezone.utc),
        )

        db_session.add(time_entry)
        await db_session.commit()
        await db_session.refresh(time_entry)

        assert time_entry.duration_hours == Decimal("2.50")


class TestProjectSchemas:
    """Test Project Pydantic schemas"""

    def test_project_create_schema(self):
        """Test ProjectCreate schema validation"""
        client_id = str(uuid.uuid4())
        project_data = {
            "title": "Schema Test Project",
            "description": "A project for schema testing",
            "client_id": client_id,
            "billing_type": "fixed_price",
            "total_budget": "15000.00",
            "estimated_hours": "150.0",
            "hourly_rate": "100.00",
            "currency": "USD",
            "status": "active",
            "is_billable": True,
        }

        project_create = ProjectCreate(**project_data)

        assert project_create.title == "Schema Test Project"
        assert project_create.description == "A project for schema testing"
        assert str(project_create.client_id) == client_id
        assert project_create.billing_type == "fixed_price"
        assert project_create.total_budget == Decimal("15000.00")
        assert project_create.estimated_hours == Decimal("150.0")
        assert project_create.hourly_rate == Decimal("100.00")
        assert project_create.currency == "USD"
        assert project_create.status == "active"
        assert project_create.is_billable is True

    def test_project_create_minimal(self):
        """Test ProjectCreate with minimal required fields"""
        client_id = str(uuid.uuid4())
        project_data = {"title": "Minimal Project", "client_id": client_id}

        project_create = ProjectCreate(**project_data)

        assert project_create.title == "Minimal Project"
        assert str(project_create.client_id) == client_id
        assert project_create.billing_type == "time_and_materials"  # Default
        assert project_create.currency == "USD"  # Default
        assert project_create.status == "draft"  # Default
        assert project_create.is_billable is True  # Default

    def test_project_create_validation_errors(self):
        """Test ProjectCreate schema validation errors"""
        client_id = str(uuid.uuid4())

        # Test empty title
        with pytest.raises(ValueError):
            ProjectCreate(title="", client_id=client_id)

        # Test invalid billing_type
        with pytest.raises(ValueError):
            ProjectCreate(title="Test", client_id=client_id, billing_type="invalid")

        # Test invalid currency
        with pytest.raises(ValueError):
            ProjectCreate(title="Test", client_id=client_id, currency="INVALID")

        # Test invalid status
        with pytest.raises(ValueError):
            ProjectCreate(title="Test", client_id=client_id, status="invalid")

        # Test negative budget
        with pytest.raises(ValueError):
            ProjectCreate(title="Test", client_id=client_id, total_budget="-100.00")

    def test_milestone_create_schema(self):
        """Test MilestoneCreate schema validation"""
        milestone_data = {
            "title": "Schema Test Milestone",
            "description": "A milestone for schema testing",
            "estimated_hours": "25.0",
            "payment_amount": "2500.00",
            "status": "in_progress",
            "sequence_number": 2,
        }

        milestone_create = MilestoneCreate(**milestone_data)

        assert milestone_create.title == "Schema Test Milestone"
        assert milestone_create.description == "A milestone for schema testing"
        assert milestone_create.estimated_hours == Decimal("25.0")
        assert milestone_create.payment_amount == Decimal("2500.00")
        assert milestone_create.status == "in_progress"
        assert milestone_create.sequence_number == 2

    def test_time_entry_create_schema(self):
        """Test TimeEntryCreate schema validation"""
        project_id = str(uuid.uuid4())
        time_entry_data = {
            "project_id": project_id,
            "duration_minutes": 180,
            "description": "Schema test work",
            "task_name": "Testing",
            "hourly_rate": "80.00",
            "is_billable": True,
            "status": "approved",
            "work_date": "2024-01-15T10:00:00Z",
        }

        time_entry_create = TimeEntryCreate(**time_entry_data)

        assert str(time_entry_create.project_id) == project_id
        assert time_entry_create.duration_minutes == 180
        assert time_entry_create.description == "Schema test work"
        assert time_entry_create.task_name == "Testing"
        assert time_entry_create.hourly_rate == Decimal("80.00")
        assert time_entry_create.is_billable is True
        assert time_entry_create.status == "approved"


class TestProjectAPI:
    """Test Project API endpoints"""

    async def test_create_project_endpoint(
        self, authenticated_client: AsyncClient, test_client_id: str
    ):
        """Test POST /api/v1/projects endpoint"""
        project_data = {
            "title": "API Test Project",
            "description": "A project created via API test",
            "client_id": test_client_id,
            "billing_type": "time_and_materials",
            "estimated_hours": "80.0",
            "hourly_rate": "90.00",
            "status": "active",
        }

        response = await authenticated_client.post(
            "/api/v1/projects/", json=project_data
        )

        assert response.status_code == 201
        data = response.json()

        assert data["title"] == "API Test Project"
        assert data["description"] == "A project created via API test"
        assert data["client_id"] == test_client_id
        assert data["billing_type"] == "time_and_materials"
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

    async def test_get_projects_endpoint(
        self, authenticated_client: AsyncClient, test_project_data: dict
    ):
        """Test GET /api/v1/projects endpoint"""
        # First create a project
        create_response = await authenticated_client.post(
            "/api/v1/projects/", json=test_project_data
        )
        assert create_response.status_code == 201

        # Then get all projects
        response = await authenticated_client.get("/api/v1/projects/")

        assert response.status_code == 200
        data = response.json()

        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "per_page" in data
        assert len(data["items"]) >= 1

        # Check if our created project is in the list
        project_titles = [item["title"] for item in data["items"]]
        assert test_project_data["title"] in project_titles

    async def test_get_project_by_id_endpoint(
        self, authenticated_client: AsyncClient, test_project_data: dict
    ):
        """Test GET /api/v1/projects/{id} endpoint"""
        # First create a project
        create_response = await authenticated_client.post(
            "/api/v1/projects/", json=test_project_data
        )
        assert create_response.status_code == 201
        created_project = create_response.json()
        project_id = created_project["id"]

        # Then get the specific project
        response = await authenticated_client.get(f"/api/v1/projects/{project_id}")

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == project_id
        assert data["title"] == test_project_data["title"]
        assert data["description"] == test_project_data["description"]

    async def test_update_project_endpoint(
        self, authenticated_client: AsyncClient, test_project_data: dict
    ):
        """Test PUT /api/v1/projects/{id} endpoint"""
        # First create a project
        create_response = await authenticated_client.post(
            "/api/v1/projects/", json=test_project_data
        )
        assert create_response.status_code == 201
        created_project = create_response.json()
        project_id = created_project["id"]

        # Update the project
        update_data = {
            "title": "Updated API Project",
            "status": "completed",
            "hourly_rate": "120.00",
        }

        response = await authenticated_client.put(
            f"/api/v1/projects/{project_id}", json=update_data
        )

        assert response.status_code == 200
        data = response.json()

        assert data["title"] == "Updated API Project"
        assert data["status"] == "completed"
        assert (
            data["description"] == test_project_data["description"]
        )  # Should remain unchanged

    async def test_delete_project_endpoint(
        self, authenticated_client: AsyncClient, test_project_data: dict
    ):
        """Test DELETE /api/v1/projects/{id} endpoint"""
        # First create a project
        create_response = await authenticated_client.post(
            "/api/v1/projects/", json=test_project_data
        )
        assert create_response.status_code == 201
        created_project = create_response.json()
        project_id = created_project["id"]

        # Delete the project
        response = await authenticated_client.delete(f"/api/v1/projects/{project_id}")

        assert response.status_code == 204

        # Verify project is deleted
        get_response = await authenticated_client.get(f"/api/v1/projects/{project_id}")
        assert get_response.status_code == 404

    async def test_project_api_validation_errors(
        self, authenticated_client: AsyncClient
    ):
        """Test project API validation errors"""
        # Test missing required fields
        response = await authenticated_client.post("/api/v1/projects/", json={})
        assert response.status_code == 422

        # Test invalid client_id
        response = await authenticated_client.post(
            "/api/v1/projects/", json={"title": "Test", "client_id": "invalid-uuid"}
        )
        assert response.status_code == 422


# Fixtures
@pytest.fixture
async def test_client(db_session: AsyncSession, test_user: User) -> Client:
    """Fixture providing a test client"""
    client = Client(
        user_id=test_user.id,
        name="Test Client for Projects",
        email="<EMAIL>",
        company="Project Test Company",
    )
    db_session.add(client)
    await db_session.commit()
    await db_session.refresh(client)
    return client


@pytest.fixture
async def test_project(
    db_session: AsyncSession, test_user: User, test_client: Client
) -> Project:
    """Fixture providing a test project"""
    project = Project(
        title="Test Project Fixture",
        description="A project created by fixture",
        client_id=test_client.id,
        user_id=test_user.id,
        billing_type="time_and_materials",
        hourly_rate=Decimal("75.00"),
    )
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    return project


@pytest.fixture
def test_client_id(test_client: Client) -> str:
    """Fixture providing test client ID as string"""
    return str(test_client.id)


@pytest.fixture
def test_project_data(test_client_id: str):
    """Fixture providing test project data"""
    return {
        "title": "Test Project Fixture",
        "description": "A project created by fixture for API testing",
        "client_id": test_client_id,
        "billing_type": "time_and_materials",
        "estimated_hours": "60.0",
        "hourly_rate": "85.00",
        "status": "active",
    }
