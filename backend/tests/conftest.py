import asyncio
import os
import uuid
from typing import Any, As<PERSON><PERSON>enerator, Dict, Generator

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from starlette.testclient import TestClient

from app.config import settings
from app.core.auth import create_access_token, get_password_hash
from app.database import Base, get_async_db
from app.main import app
from app.models.user import User

# Database configuration for tests
USE_POSTGRES = os.getenv("USE_POSTGRES_TESTS", "true").lower() == "true"
USE_DOCKER_DB = os.getenv("USE_DOCKER_DB", "true").lower() == "true"

if USE_POSTGRES:
    TEST_DB_NAME = "devhq_test"

    if USE_DOCKER_DB:
        DB_USER = "devhq_user"
        DB_PASSWORD = "devhq_password"
        DB_HOST = "localhost"
        DB_PORT = "5433"
    else:
        DB_USER = "postgres"
        DB_PASSWORD = "postgres"
        DB_HOST = "localhost"
        DB_PORT = "5433"

    TEST_DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{TEST_DB_NAME}"
else:
    TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine
if USE_POSTGRES:
    test_engine = create_async_engine(
        TEST_DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
    )
else:
    test_engine = create_async_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a fresh database session for each test."""
    # Create all tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # Create session
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        try:
            yield session
        finally:
            await session.rollback()

    # Drop all tables after test
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture(scope="function")
def sync_db_session(db_session: AsyncSession):
    """Create a synchronous session for services that require it."""
    # Create a mock session that behaves like a sync session
    # but delegates to the async session
    from unittest.mock import MagicMock

    mock_session = MagicMock()
    mock_session.query = MagicMock()
    mock_session.commit = MagicMock()
    mock_session.rollback = MagicMock()
    mock_session.add = MagicMock()
    mock_session.delete = MagicMock()
    mock_session.flush = MagicMock()
    mock_session.refresh = MagicMock()

    return mock_session


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """Override the get_async_db dependency."""

    async def _override_get_db():
        yield db_session

    return _override_get_db


@pytest.fixture
def client(override_get_db) -> TestClient:
    """Create a test client with overridden database dependency."""
    app.dependency_overrides[get_async_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
async def async_client(override_get_db) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client with overridden database dependency."""
    from httpx import ASGITransport

    app.dependency_overrides[get_async_db] = override_get_db
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        yield ac
    app.dependency_overrides.clear()


@pytest.fixture
async def user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User",
        "phone": "+1234567890",
        "bio": "Test user bio",
    }


@pytest.fixture
async def user(db_session: AsyncSession, user_data) -> User:
    """Create a test user."""
    user = User(
        email=user_data["email"],
        password_hash=get_password_hash(user_data["password"]),
        first_name=user_data["first_name"],
        last_name=user_data["last_name"],
        phone=user_data["phone"],
        bio=user_data["bio"],
        is_active=True,
        is_verified=True,
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def auth_headers(user: User):
    """Create authentication headers for the test user."""
    access_token = create_access_token(data={"sub": str(user.id)})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
async def authenticated_client(
    override_get_db, auth_headers
) -> AsyncGenerator[AsyncClient, None]:
    """Create an authenticated async client."""
    from httpx import ASGITransport

    app.dependency_overrides[get_async_db] = override_get_db
    transport = ASGITransport(app=app)
    async with AsyncClient(
        transport=transport, base_url="http://test", headers=auth_headers
    ) as ac:
        yield ac
    app.dependency_overrides.clear()


@pytest.fixture
async def test_client(db_session: AsyncSession, test_user: User):
    """Create a test client for testing."""
    from app.models.client import Client

    client = Client(
        user_id=test_user.id,
        name="Test Client",
        email="<EMAIL>",
        company="Test Company",
        phone="+1234567890",
        position="Test Position",
        notes="Test client for testing",
    )
    db_session.add(client)
    await db_session.commit()
    await db_session.refresh(client)
    return client


@pytest.fixture
async def test_user(user: User) -> User:
    """Alias for user fixture for backward compatibility."""
    return user


@pytest.fixture
def sample_paystack_webhook_payload():
    """Sample Paystack webhook payload"""
    return {
        "event": "charge.success",
        "data": {
            "id": 302961,
            "domain": "live",
            "status": "success",
            "reference": "test_ref_123",
            "amount": 5000000,  # 50,000 in kobo
            "message": "Approved",
            "gateway_response": "Successful",
            "paid_at": "2023-01-01T12:00:00.000Z",
            "created_at": "2023-01-01T11:55:00.000Z",
            "channel": "card",
            "currency": "NGN",
            "ip_address": "***********",
            "metadata": {"invoice_id": "test_invoice_123"},
            "customer": {
                "id": 123456,
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "customer_code": "CUS_abc123",
                "phone": "+*************",
                "metadata": {},
                "risk_action": "default",
            },
            "authorization": {
                "authorization_code": "AUTH_abc123",
                "bin": "408408",
                "last4": "4081",
                "exp_month": "12",
                "exp_year": "2030",
                "channel": "card",
                "card_type": "visa DEBIT",
                "bank": "Test Bank",
                "country_code": "NG",
                "brand": "visa",
                "reusable": True,
                "signature": "SIG_abc123",
            },
        },
    }


@pytest.fixture
def sample_dpo_webhook_payload():
    """Sample DPO webhook payload"""
    return {
        "TransactionRef": "test_ref_456",
        "TransactionStatus": "1",  # Success
        "Amount": "500.00",
        "Currency": "KES",
        "TransactionDate": "2023-01-01 12:00:00",
        "CustomerEmail": "<EMAIL>",
        "CustomerPhone": "+************",
        "PaymentMethod": "MPESA",
    }
