import pytest
from httpx import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User


@pytest.mark.asyncio
class TestUserRegistration:
    async def test_register_user_success(
        self, async_client: AsyncClient, db_session: AsyncSession
    ):
        """Test successful user registration."""
        # Arrange
        user_data = {
            "email": "<EMAIL>",
            "password": "StrongPassword123!",
            "first_name": "New",
            "last_name": "User",
            "phone": "+1234567890",
            "bio": "New test user",
        }

        # Act
        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Assert
        assert response.status_code == 201
        json_response = response.json()

        # Check response structure based on the actual API
        assert "user" in json_response
        assert "tokens" in json_response

        user_data_response = json_response["user"]
        assert "id" in user_data_response
        assert user_data_response["email"] == user_data["email"]
        assert user_data_response["first_name"] == user_data["first_name"]
        assert user_data_response["last_name"] == user_data["last_name"]
        assert "password" not in user_data_response

        # Verify user was created in the database
        db_user = await db_session.get(User, user_data_response["id"])
        assert db_user is not None
        assert db_user.email == user_data["email"]
        assert db_user.is_active is True
        assert db_user.is_verified is False  # Users should start unverified

    async def test_register_duplicate_email(
        self, async_client: AsyncClient, db_session: AsyncSession
    ):
        """Test registration with an email that already exists."""
        # Arrange - Create a user first
        user_data = {
            "email": "<EMAIL>",
            "password": "StrongPassword123!",
            "first_name": "First",
            "last_name": "User",
            "phone": "+1234567890",
            "bio": "First test user",
        }

        # Register the first user
        first_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert first_response.status_code == 201

        # Try to register another user with the same email
        duplicate_user_data = {
            "email": "<EMAIL>",  # Same email
            "password": "AnotherPassword123!",
            "first_name": "Second",
            "last_name": "User",
            "phone": "+9876543210",
            "bio": "Second test user",
        }

        # Act - Try to register with duplicate email
        response = await async_client.post(
            "/api/v1/auth/register", json=duplicate_user_data
        )

        # Assert
        assert response.status_code == 409  # Conflict status code
        json_response = response.json()
        assert "detail" in json_response  # Error message should be in the response

    async def test_register_invalid_email_format(self, async_client: AsyncClient):
        """Test registration with an invalid email format."""
        # Arrange
        user_data = {
            "email": "invalid-email-format",  # Invalid email format
            "password": "StrongPassword123!",
            "first_name": "Invalid",
            "last_name": "Email",
            "phone": "+1234567890",
            "bio": "Test user with invalid email",
        }

        # Act
        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Assert
        assert response.status_code == 422  # Unprocessable Entity for validation errors
        json_response = response.json()
        assert (
            "detail" in json_response
        )  # Validation error details should be in the response

    async def test_register_weak_password(self, async_client: AsyncClient):
        """Test registration with a weak password."""
        # Arrange
        user_data = {
            "email": "<EMAIL>",
            "password": "weak",  # Too short/simple password
            "first_name": "Weak",
            "last_name": "Password",
            "phone": "+1234567890",
            "bio": "Test user with weak password",
        }

        # Act
        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Assert
        assert response.status_code == 422  # Unprocessable Entity for validation errors
        json_response = response.json()
        assert (
            "detail" in json_response
        )  # Validation error details should be in the response

    async def test_register_missing_required_fields(self, async_client: AsyncClient):
        """Test registration with missing required fields."""
        # Arrange - Missing email and password
        user_data = {
            "first_name": "Missing",
            "last_name": "Fields",
            "phone": "+1234567890",
            "bio": "Test user with missing fields",
        }

        # Act
        response = await async_client.post("/api/v1/auth/register", json=user_data)

        # Assert
        assert response.status_code == 422  # Unprocessable Entity for validation errors
        json_response = response.json()
        assert (
            "detail" in json_response
        )  # Validation error details should be in the response


@pytest.mark.asyncio
class TestUserLogin:
    async def test_login_success(
        self, async_client: AsyncClient, db_session: AsyncSession
    ):
        """Test successful user login."""
        # Arrange - Create a user first
        user_data = {
            "email": "<EMAIL>",
            "password": "StrongPassword123!",
            "first_name": "Login",
            "last_name": "User",
        }

        # Register the user
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Act - Login with the user
        login_data = {
            "email_or_username": user_data["email"],
            "password": user_data["password"],
        }

        response = await async_client.post("/api/v1/auth/login", json=login_data)

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure based on the actual API
        assert "user" in json_response
        assert "tokens" in json_response

        tokens = json_response["tokens"]
        assert "access_token" in tokens
        assert "token_type" in tokens
        assert tokens["token_type"] == "bearer"

    async def test_login_invalid_credentials(self, async_client: AsyncClient):
        """Test login with invalid credentials."""
        # Arrange
        login_data = {
            "email_or_username": "<EMAIL>",
            "password": "WrongPassword123!",
        }

        # Act
        response = await async_client.post("/api/v1/auth/login", json=login_data)

        # Assert
        assert response.status_code == 401  # Unauthorized
        json_response = response.json()
        assert "detail" in json_response  # Error message should be in the response


@pytest.mark.asyncio
class TestPasswordReset:
    async def test_request_password_reset(
        self, async_client: AsyncClient, db_session: AsyncSession
    ):
        """Test requesting a password reset."""
        # Arrange - Create a user first
        user_data = {
            "email": "<EMAIL>",
            "password": "StrongPassword123!",
            "first_name": "Reset",
            "last_name": "User",
        }

        # Register the user
        register_response = await async_client.post(
            "/api/v1/auth/register", json=user_data
        )
        assert register_response.status_code == 201

        # Act - Request password reset
        reset_data = {"email": user_data["email"]}

        response = await async_client.post(
            "/api/v1/auth/forgot-password", json=reset_data
        )

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure - should contain a success message
        assert "message" in json_response

    async def test_request_password_reset_nonexistent_email(
        self, async_client: AsyncClient
    ):
        """Test requesting a password reset for a non-existent email."""
        # Arrange
        reset_data = {"email": "<EMAIL>"}

        # Act
        response = await async_client.post(
            "/api/v1/auth/forgot-password", json=reset_data
        )

        # Assert - Should still return 200 for security reasons (not revealing if email exists)
        assert response.status_code == 200
        json_response = response.json()
        assert "message" in json_response
