"""Advanced performance monitoring utilities for testing"""

import asyncio
import json
import os
import statistics
import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import psutil
from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session

from .performance_config import (PerformanceConfig, PerformanceLevel,
                                 PerformanceReporter)


@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""

    start_time: float
    end_time: float
    duration: float
    cpu_usage_percent: float
    memory_usage_mb: float
    memory_peak_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    database_queries: int = 0
    database_query_time: float = 0.0
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RequestMetrics:
    """Container for HTTP request metrics"""

    method: str
    url: str
    status_code: int
    response_time: float
    request_size: int
    response_size: int
    timestamp: datetime
    error: Optional[str] = None
    custom_data: Dict[str, Any] = field(default_factory=dict)


class SystemMonitor:
    """System resource monitoring"""

    def __init__(self, interval: float = 0.1):
        self.interval = interval
        self.monitoring = False
        self.metrics: List[Dict[str, Any]] = []
        self._monitor_thread: Optional[threading.Thread] = None
        self.process = psutil.Process()

    def start_monitoring(self):
        """Start system monitoring in background thread"""
        if self.monitoring:
            return

        self.monitoring = True
        self.metrics.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop)
        self._monitor_thread.daemon = True
        self._monitor_thread.start()

    def stop_monitoring(self) -> PerformanceMetrics:
        """Stop monitoring and return aggregated metrics"""
        if not self.monitoring:
            raise ValueError("Monitoring not started")

        self.monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)

        return self._aggregate_metrics()

    def _monitor_loop(self):
        """Background monitoring loop"""
        start_time = time.time()
        initial_io = self.process.io_counters()
        initial_net = psutil.net_io_counters()

        while self.monitoring:
            try:
                # CPU and memory
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()

                # I/O counters
                io_counters = self.process.io_counters()
                net_counters = psutil.net_io_counters()

                metrics = {
                    "timestamp": time.time(),
                    "cpu_percent": cpu_percent,
                    "memory_rss_mb": memory_info.rss / 1024 / 1024,
                    "memory_vms_mb": memory_info.vms / 1024 / 1024,
                    "disk_read_bytes": io_counters.read_bytes - initial_io.read_bytes,
                    "disk_write_bytes": io_counters.write_bytes
                    - initial_io.write_bytes,
                    "network_sent_bytes": net_counters.bytes_sent
                    - initial_net.bytes_sent,
                    "network_recv_bytes": net_counters.bytes_recv
                    - initial_net.bytes_recv,
                }

                self.metrics.append(metrics)
                time.sleep(self.interval)

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                break
            except Exception as e:
                print(f"Monitoring error: {e}")
                break

    def _aggregate_metrics(self) -> PerformanceMetrics:
        """Aggregate collected metrics"""
        if not self.metrics:
            raise ValueError("No metrics collected")

        start_time = self.metrics[0]["timestamp"]
        end_time = self.metrics[-1]["timestamp"]
        duration = end_time - start_time

        # Calculate averages and peaks
        cpu_values = [m["cpu_percent"] for m in self.metrics]
        memory_values = [m["memory_rss_mb"] for m in self.metrics]

        # Get final I/O values (cumulative)
        final_metrics = self.metrics[-1]

        return PerformanceMetrics(
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            cpu_usage_percent=statistics.mean(cpu_values) if cpu_values else 0,
            memory_usage_mb=statistics.mean(memory_values) if memory_values else 0,
            memory_peak_mb=max(memory_values) if memory_values else 0,
            disk_io_read_mb=final_metrics["disk_read_bytes"] / 1024 / 1024,
            disk_io_write_mb=final_metrics["disk_write_bytes"] / 1024 / 1024,
            network_sent_mb=final_metrics["network_sent_bytes"] / 1024 / 1024,
            network_recv_mb=final_metrics["network_recv_bytes"] / 1024 / 1024,
        )


class DatabaseMonitor:
    """Database query monitoring"""

    def __init__(self, engine: Engine):
        self.engine = engine
        self.query_metrics: List[Dict[str, Any]] = []
        self.monitoring = False

    def start_monitoring(self):
        """Start database monitoring"""
        if self.monitoring:
            return

        self.monitoring = True
        self.query_metrics.clear()

        # Register SQLAlchemy event listeners
        event.listen(self.engine, "before_cursor_execute", self._before_cursor_execute)
        event.listen(self.engine, "after_cursor_execute", self._after_cursor_execute)

    def stop_monitoring(self) -> Dict[str, Any]:
        """Stop monitoring and return query metrics"""
        if not self.monitoring:
            return {}

        self.monitoring = False

        # Remove event listeners
        event.remove(self.engine, "before_cursor_execute", self._before_cursor_execute)
        event.remove(self.engine, "after_cursor_execute", self._after_cursor_execute)

        return self._aggregate_query_metrics()

    def _before_cursor_execute(
        self, conn, cursor, statement, parameters, context, executemany
    ):
        """Called before SQL execution"""
        if self.monitoring:
            context._query_start_time = time.perf_counter()

    def _after_cursor_execute(
        self, conn, cursor, statement, parameters, context, executemany
    ):
        """Called after SQL execution"""
        if self.monitoring and hasattr(context, "_query_start_time"):
            duration = time.perf_counter() - context._query_start_time

            self.query_metrics.append(
                {
                    "statement": statement[:200],  # Truncate long statements
                    "duration": duration,
                    "timestamp": time.time(),
                    "executemany": executemany,
                    "rowcount": cursor.rowcount if hasattr(cursor, "rowcount") else -1,
                }
            )

    def _aggregate_query_metrics(self) -> Dict[str, Any]:
        """Aggregate query metrics"""
        if not self.query_metrics:
            return {
                "total_queries": 0,
                "total_time": 0.0,
                "avg_time": 0.0,
                "min_time": 0.0,
                "max_time": 0.0,
                "slow_queries": [],
            }

        durations = [m["duration"] for m in self.query_metrics]

        # Find slow queries (> 100ms)
        slow_queries = [
            {
                "statement": m["statement"],
                "duration": m["duration"],
                "timestamp": m["timestamp"],
            }
            for m in self.query_metrics
            if m["duration"] > 0.1
        ]

        return {
            "total_queries": len(self.query_metrics),
            "total_time": sum(durations),
            "avg_time": statistics.mean(durations),
            "min_time": min(durations),
            "max_time": max(durations),
            "slow_queries": slow_queries[:10],  # Top 10 slowest
        }


class RequestTracker:
    """HTTP request tracking"""

    def __init__(self):
        self.requests: List[RequestMetrics] = []
        self.active_requests: Dict[str, float] = {}

    def start_request(self, method: str, url: str, request_id: str = None) -> str:
        """Start tracking a request"""
        if request_id is None:
            request_id = f"{method}_{url}_{time.time()}"

        self.active_requests[request_id] = time.perf_counter()
        return request_id

    def end_request(
        self,
        request_id: str,
        status_code: int,
        request_size: int = 0,
        response_size: int = 0,
        error: str = None,
        **custom_data,
    ) -> RequestMetrics:
        """End tracking a request"""
        if request_id not in self.active_requests:
            raise ValueError(f"Request {request_id} not found")

        start_time = self.active_requests.pop(request_id)
        response_time = time.perf_counter() - start_time

        # Extract method and URL from request_id
        parts = request_id.split("_", 2)
        method = parts[0] if len(parts) > 0 else "UNKNOWN"
        url = parts[1] if len(parts) > 1 else "UNKNOWN"

        metrics = RequestMetrics(
            method=method,
            url=url,
            status_code=status_code,
            response_time=response_time,
            request_size=request_size,
            response_size=response_size,
            timestamp=datetime.now(),
            error=error,
            custom_data=custom_data,
        )

        self.requests.append(metrics)
        return metrics

    def get_statistics(self) -> Dict[str, Any]:
        """Get request statistics"""
        if not self.requests:
            return {}

        response_times = [r.response_time for r in self.requests]
        status_codes = [r.status_code for r in self.requests]

        # Calculate error rate
        error_count = sum(1 for code in status_codes if code >= 400)
        error_rate = (error_count / len(status_codes)) * 100

        # Group by endpoint
        endpoint_stats = {}
        for request in self.requests:
            endpoint = f"{request.method} {request.url}"
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = []
            endpoint_stats[endpoint].append(request.response_time)

        return {
            "total_requests": len(self.requests),
            "avg_response_time": statistics.mean(response_times),
            "min_response_time": min(response_times),
            "max_response_time": max(response_times),
            "p50_response_time": statistics.median(response_times),
            "p95_response_time": self._percentile(sorted(response_times), 95),
            "p99_response_time": self._percentile(sorted(response_times), 99),
            "error_rate": error_rate,
            "requests_per_second": len(self.requests)
            / (
                max(r.timestamp for r in self.requests)
                - min(r.timestamp for r in self.requests)
            ).total_seconds()
            if len(self.requests) > 1
            else 0,
            "endpoint_stats": {
                endpoint: {
                    "count": len(times),
                    "avg_time": statistics.mean(times),
                    "max_time": max(times),
                }
                for endpoint, times in endpoint_stats.items()
            },
        }

    @staticmethod
    def _percentile(sorted_data: List[float], percentile: int) -> float:
        """Calculate percentile from sorted data"""
        if not sorted_data:
            return 0
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]


class PerformanceTestRunner:
    """Comprehensive performance test runner"""

    def __init__(self, db_engine: Engine = None):
        self.system_monitor = SystemMonitor()
        self.db_monitor = DatabaseMonitor(db_engine) if db_engine else None
        self.request_tracker = RequestTracker()
        self.results: Dict[str, Any] = {}

    @contextmanager
    def monitor_performance(self, test_name: str):
        """Context manager for performance monitoring"""
        # Start monitoring
        self.system_monitor.start_monitoring()
        if self.db_monitor:
            self.db_monitor.start_monitoring()

        start_time = time.perf_counter()

        try:
            yield self
        finally:
            # Stop monitoring
            end_time = time.perf_counter()

            system_metrics = self.system_monitor.stop_monitoring()
            db_metrics = self.db_monitor.stop_monitoring() if self.db_monitor else {}
            request_stats = self.request_tracker.get_statistics()

            # Combine all metrics
            self.results[test_name] = {
                "duration": end_time - start_time,
                "system": {
                    "cpu_usage_percent": system_metrics.cpu_usage_percent,
                    "memory_usage_mb": system_metrics.memory_usage_mb,
                    "memory_peak_mb": system_metrics.memory_peak_mb,
                    "disk_io_read_mb": system_metrics.disk_io_read_mb,
                    "disk_io_write_mb": system_metrics.disk_io_write_mb,
                    "network_sent_mb": system_metrics.network_sent_mb,
                    "network_recv_mb": system_metrics.network_recv_mb,
                },
                "database": db_metrics,
                "requests": request_stats,
            }

    def validate_performance(
        self, test_name: str, level: PerformanceLevel, operation_type: str = "api"
    ) -> Dict[str, bool]:
        """Validate performance results against thresholds"""
        if test_name not in self.results:
            raise ValueError(f"No results found for test: {test_name}")

        results = self.results[test_name]

        # Prepare results for validation
        validation_data = {}

        if operation_type == "api" and "requests" in results:
            req_stats = results["requests"]
            validation_data.update(
                {
                    "mean_response_time": req_stats.get("avg_response_time", 0),
                    "p95_response_time": req_stats.get("p95_response_time", 0),
                    "p99_response_time": req_stats.get("p99_response_time", 0),
                    "requests_per_second": req_stats.get("requests_per_second", 0),
                    "error_rate": req_stats.get("error_rate", 0),
                }
            )

        elif operation_type == "database" and "database" in results:
            db_stats = results["database"]
            validation_data.update({"mean_query_time": db_stats.get("avg_time", 0)})

        elif operation_type == "memory" and "system" in results:
            sys_stats = results["system"]
            validation_data.update(
                {"memory_usage_mb": sys_stats.get("memory_peak_mb", 0)}
            )

        return PerformanceReporter.validate_results(
            validation_data, level, operation_type
        )

    def generate_report(
        self, test_name: str, level: PerformanceLevel, operation_type: str = "api"
    ) -> str:
        """Generate performance report"""
        if test_name not in self.results:
            raise ValueError(f"No results found for test: {test_name}")

        results = self.results[test_name]

        # Prepare results for reporting
        report_data = {
            "duration": results["duration"],
            "cpu_usage": results["system"]["cpu_usage_percent"],
            "memory_peak_mb": results["system"]["memory_peak_mb"],
        }

        if "requests" in results and results["requests"]:
            report_data.update(
                {
                    "avg_response_time": results["requests"]["avg_response_time"],
                    "p95_response_time": results["requests"]["p95_response_time"],
                    "p99_response_time": results["requests"]["p99_response_time"],
                    "requests_per_second": results["requests"]["requests_per_second"],
                    "error_rate": results["requests"]["error_rate"],
                }
            )

        if "database" in results and results["database"]:
            report_data.update(
                {
                    "total_queries": results["database"]["total_queries"],
                    "avg_query_time": results["database"]["avg_time"],
                    "slow_queries_count": len(results["database"]["slow_queries"]),
                }
            )

        return PerformanceReporter.generate_report(
            test_name, report_data, level, operation_type
        )

    def save_results(self, filepath: str):
        """Save results to JSON file"""
        # Convert datetime objects to strings for JSON serialization
        serializable_results = {}
        for test_name, results in self.results.items():
            serializable_results[test_name] = self._make_serializable(results)

        with open(filepath, "w") as f:
            json.dump(
                {
                    "timestamp": datetime.now().isoformat(),
                    "results": serializable_results,
                },
                f,
                indent=2,
            )

    def _make_serializable(self, obj):
        """Make object JSON serializable"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj


class AsyncPerformanceTestRunner:
    """Async version of performance test runner"""

    def __init__(self):
        self.request_tracker = RequestTracker()
        self.results: Dict[str, Any] = {}

    async def run_concurrent_requests(
        self,
        request_func: Callable,
        concurrent_users: int,
        duration_seconds: int,
        test_name: str = "async_test",
    ) -> Dict[str, Any]:
        """Run concurrent requests for specified duration"""
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(concurrent_users)

        async def worker():
            """Worker coroutine"""
            while time.perf_counter() < end_time:
                async with semaphore:
                    request_id = self.request_tracker.start_request("GET", "/test")
                    try:
                        await request_func()
                        self.request_tracker.end_request(request_id, 200)
                    except Exception as e:
                        self.request_tracker.end_request(request_id, 500, error=str(e))

                    # Small delay to prevent overwhelming
                    await asyncio.sleep(0.01)

        # Start workers
        workers = [asyncio.create_task(worker()) for _ in range(concurrent_users)]

        # Wait for duration
        await asyncio.sleep(duration_seconds)

        # Cancel workers
        for worker in workers:
            worker.cancel()

        # Wait for workers to finish
        await asyncio.gather(*workers, return_exceptions=True)

        # Get statistics
        stats = self.request_tracker.get_statistics()
        self.results[test_name] = stats

        return stats


# Utility functions
def create_performance_test_data(
    db_session: Session, level: PerformanceLevel
) -> Dict[str, Any]:
    """Create test data for performance tests"""
    from datetime import date, timedelta
    from decimal import Decimal

    from app.models.client import Client
    from app.models.project import Project, TimeEntry
    from app.models.user import User

    config = PerformanceConfig.get_test_data_config(level)

    # Create users
    users = []
    for i in range(config["users_count"]):
        user = User(
            email=f"perf_user_{i}@example.com",
            username=f"perf_user_{i}",
            first_name=f"Perf{i}",
            last_name="User",
            is_active=True,
            is_verified=True,
        )
        users.append(user)

    db_session.add_all(users)
    db_session.commit()

    # Create clients and projects
    projects = []
    for user in users:
        for j in range(config["projects_per_user"]):
            client = Client(
                user_id=user.id,
                name=f"Perf Client {user.id}-{j}",
                email=f"perf_client_{user.id}_{j}@example.com",
                company=f"Perf Company {user.id}-{j}",
            )
            db_session.add(client)
            db_session.flush()

            project = Project(
                user_id=user.id,
                client_id=client.id,
                title=f"Perf Project {user.id}-{j}",
                description=f"Performance test project {user.id}-{j}",
                status="active",
                hourly_rate=Decimal("100.00"),
            )
            projects.append(project)

    db_session.add_all(projects)
    db_session.commit()

    # Create time entries
    time_entries = []
    for project in projects:
        for k in range(config["time_entries_per_project"]):
            entry = TimeEntry(
                user_id=project.user_id,
                project_id=project.id,
                description=f"Perf time entry {k}",
                duration_hours=Decimal("2.0"),
                work_date=date.today() - timedelta(days=k),
                is_billable=True,
                status="completed",
            )
            time_entries.append(entry)

    db_session.add_all(time_entries)
    db_session.commit()

    return {
        "users": users,
        "projects": projects,
        "time_entries": time_entries,
        "config": config,
    }


def cleanup_performance_test_data(db_session: Session, test_data: Dict[str, Any]):
    """Clean up performance test data"""
    # Delete in reverse order to respect foreign keys
    for entry in test_data.get("time_entries", []):
        db_session.delete(entry)

    for project in test_data.get("projects", []):
        db_session.delete(project)

    # Delete clients (they should be deleted with projects due to cascade)
    # But let's be explicit
    for user in test_data.get("users", []):
        # Delete user's clients
        clients = db_session.query(Client).filter(Client.user_id == user.id).all()
        for client in clients:
            db_session.delete(client)

        db_session.delete(user)

    db_session.commit()
