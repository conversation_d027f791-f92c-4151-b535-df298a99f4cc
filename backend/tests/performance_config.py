"""Performance testing configuration and thresholds"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict


class PerformanceLevel(Enum):
    """Performance test levels"""

    SMOKE = "smoke"  # Quick validation tests
    LOAD = "load"  # Normal expected load
    STRESS = "stress"  # High load testing
    SPIKE = "spike"  # Sudden load increases
    ENDURANCE = "endurance"  # Long-running tests


@dataclass
class PerformanceThreshold:
    """Performance threshold configuration"""

    max_response_time_ms: float
    max_p95_response_time_ms: float
    max_p99_response_time_ms: float
    min_requests_per_second: float
    max_error_rate_percent: float
    max_memory_usage_mb: float


class PerformanceConfig:
    """Performance testing configuration"""

    # Default thresholds for different test levels
    THRESHOLDS = {
        PerformanceLevel.SMOKE: PerformanceThreshold(
            max_response_time_ms=100.0,
            max_p95_response_time_ms=150.0,
            max_p99_response_time_ms=200.0,
            min_requests_per_second=50.0,
            max_error_rate_percent=1.0,
            max_memory_usage_mb=512.0,
        ),
        PerformanceLevel.LOAD: PerformanceThreshold(
            max_response_time_ms=200.0,
            max_p95_response_time_ms=300.0,
            max_p99_response_time_ms=500.0,
            min_requests_per_second=100.0,
            max_error_rate_percent=2.0,
            max_memory_usage_mb=1024.0,
        ),
        PerformanceLevel.STRESS: PerformanceThreshold(
            max_response_time_ms=500.0,
            max_p95_response_time_ms=1000.0,
            max_p99_response_time_ms=2000.0,
            min_requests_per_second=50.0,
            max_error_rate_percent=5.0,
            max_memory_usage_mb=2048.0,
        ),
        PerformanceLevel.SPIKE: PerformanceThreshold(
            max_response_time_ms=1000.0,
            max_p95_response_time_ms=2000.0,
            max_p99_response_time_ms=5000.0,
            min_requests_per_second=25.0,
            max_error_rate_percent=10.0,
            max_memory_usage_mb=2048.0,
        ),
        PerformanceLevel.ENDURANCE: PerformanceThreshold(
            max_response_time_ms=300.0,
            max_p95_response_time_ms=500.0,
            max_p99_response_time_ms=1000.0,
            min_requests_per_second=75.0,
            max_error_rate_percent=3.0,
            max_memory_usage_mb=1536.0,
        ),
    }

    # Test configuration for different endpoints
    ENDPOINT_CONFIG = {
        "/auth/login": {
            "concurrent_users": {
                PerformanceLevel.SMOKE: 5,
                PerformanceLevel.LOAD: 25,
                PerformanceLevel.STRESS: 100,
                PerformanceLevel.SPIKE: 200,
                PerformanceLevel.ENDURANCE: 50,
            },
            "duration_seconds": {
                PerformanceLevel.SMOKE: 30,
                PerformanceLevel.LOAD: 300,
                PerformanceLevel.STRESS: 600,
                PerformanceLevel.SPIKE: 120,
                PerformanceLevel.ENDURANCE: 3600,
            },
            "ramp_up_seconds": {
                PerformanceLevel.SMOKE: 5,
                PerformanceLevel.LOAD: 30,
                PerformanceLevel.STRESS: 60,
                PerformanceLevel.SPIKE: 10,
                PerformanceLevel.ENDURANCE: 120,
            },
        },
        "/projects/": {
            "concurrent_users": {
                PerformanceLevel.SMOKE: 3,
                PerformanceLevel.LOAD: 15,
                PerformanceLevel.STRESS: 75,
                PerformanceLevel.SPIKE: 150,
                PerformanceLevel.ENDURANCE: 30,
            },
            "duration_seconds": {
                PerformanceLevel.SMOKE: 30,
                PerformanceLevel.LOAD: 300,
                PerformanceLevel.STRESS: 600,
                PerformanceLevel.SPIKE: 120,
                PerformanceLevel.ENDURANCE: 3600,
            },
            "ramp_up_seconds": {
                PerformanceLevel.SMOKE: 5,
                PerformanceLevel.LOAD: 30,
                PerformanceLevel.STRESS: 60,
                PerformanceLevel.SPIKE: 10,
                PerformanceLevel.ENDURANCE: 120,
            },
        },
        "/time-entries/": {
            "concurrent_users": {
                PerformanceLevel.SMOKE: 3,
                PerformanceLevel.LOAD: 20,
                PerformanceLevel.STRESS: 80,
                PerformanceLevel.SPIKE: 160,
                PerformanceLevel.ENDURANCE: 40,
            },
            "duration_seconds": {
                PerformanceLevel.SMOKE: 30,
                PerformanceLevel.LOAD: 300,
                PerformanceLevel.STRESS: 600,
                PerformanceLevel.SPIKE: 120,
                PerformanceLevel.ENDURANCE: 3600,
            },
            "ramp_up_seconds": {
                PerformanceLevel.SMOKE: 5,
                PerformanceLevel.LOAD: 30,
                PerformanceLevel.STRESS: 60,
                PerformanceLevel.SPIKE: 10,
                PerformanceLevel.ENDURANCE: 120,
            },
        },
        "/invoices/": {
            "concurrent_users": {
                PerformanceLevel.SMOKE: 2,
                PerformanceLevel.LOAD: 10,
                PerformanceLevel.STRESS: 50,
                PerformanceLevel.SPIKE: 100,
                PerformanceLevel.ENDURANCE: 20,
            },
            "duration_seconds": {
                PerformanceLevel.SMOKE: 30,
                PerformanceLevel.LOAD: 300,
                PerformanceLevel.STRESS: 600,
                PerformanceLevel.SPIKE: 120,
                PerformanceLevel.ENDURANCE: 3600,
            },
            "ramp_up_seconds": {
                PerformanceLevel.SMOKE: 5,
                PerformanceLevel.LOAD: 30,
                PerformanceLevel.STRESS: 60,
                PerformanceLevel.SPIKE: 10,
                PerformanceLevel.ENDURANCE: 120,
            },
        },
    }

    # Database operation thresholds
    DATABASE_THRESHOLDS = {
        "simple_query_ms": {
            PerformanceLevel.SMOKE: 10.0,
            PerformanceLevel.LOAD: 20.0,
            PerformanceLevel.STRESS: 50.0,
            PerformanceLevel.SPIKE: 100.0,
            PerformanceLevel.ENDURANCE: 30.0,
        },
        "complex_query_ms": {
            PerformanceLevel.SMOKE: 50.0,
            PerformanceLevel.LOAD: 100.0,
            PerformanceLevel.STRESS: 250.0,
            PerformanceLevel.SPIKE: 500.0,
            PerformanceLevel.ENDURANCE: 150.0,
        },
        "bulk_operation_ms": {
            PerformanceLevel.SMOKE: 100.0,
            PerformanceLevel.LOAD: 200.0,
            PerformanceLevel.STRESS: 500.0,
            PerformanceLevel.SPIKE: 1000.0,
            PerformanceLevel.ENDURANCE: 300.0,
        },
        "transaction_ms": {
            PerformanceLevel.SMOKE: 20.0,
            PerformanceLevel.LOAD: 40.0,
            PerformanceLevel.STRESS: 100.0,
            PerformanceLevel.SPIKE: 200.0,
            PerformanceLevel.ENDURANCE: 60.0,
        },
    }

    # Memory usage thresholds (in MB)
    MEMORY_THRESHOLDS = {
        "baseline_mb": 256,
        "max_increase_mb": {
            PerformanceLevel.SMOKE: 128,
            PerformanceLevel.LOAD: 256,
            PerformanceLevel.STRESS: 512,
            PerformanceLevel.SPIKE: 1024,
            PerformanceLevel.ENDURANCE: 384,
        },
        "max_total_mb": {
            PerformanceLevel.SMOKE: 512,
            PerformanceLevel.LOAD: 1024,
            PerformanceLevel.STRESS: 2048,
            PerformanceLevel.SPIKE: 3072,
            PerformanceLevel.ENDURANCE: 1536,
        },
    }

    # Test data configuration
    TEST_DATA_CONFIG = {
        "users_count": {
            PerformanceLevel.SMOKE: 10,
            PerformanceLevel.LOAD: 100,
            PerformanceLevel.STRESS: 500,
            PerformanceLevel.SPIKE: 1000,
            PerformanceLevel.ENDURANCE: 200,
        },
        "projects_per_user": {
            PerformanceLevel.SMOKE: 2,
            PerformanceLevel.LOAD: 5,
            PerformanceLevel.STRESS: 10,
            PerformanceLevel.SPIKE: 15,
            PerformanceLevel.ENDURANCE: 8,
        },
        "time_entries_per_project": {
            PerformanceLevel.SMOKE: 10,
            PerformanceLevel.LOAD: 50,
            PerformanceLevel.STRESS: 100,
            PerformanceLevel.SPIKE: 200,
            PerformanceLevel.ENDURANCE: 75,
        },
    }

    @classmethod
    def get_threshold(cls, level: PerformanceLevel) -> PerformanceThreshold:
        """Get performance threshold for a specific level"""
        return cls.THRESHOLDS[level]

    @classmethod
    def get_endpoint_config(
        cls, endpoint: str, level: PerformanceLevel
    ) -> Dict[str, Any]:
        """Get endpoint configuration for a specific level"""
        if endpoint not in cls.ENDPOINT_CONFIG:
            # Use default configuration
            endpoint = "/projects/"

        config = cls.ENDPOINT_CONFIG[endpoint]
        return {
            "concurrent_users": config["concurrent_users"][level],
            "duration_seconds": config["duration_seconds"][level],
            "ramp_up_seconds": config["ramp_up_seconds"][level],
        }

    @classmethod
    def get_database_threshold(cls, operation: str, level: PerformanceLevel) -> float:
        """Get database operation threshold for a specific level"""
        return cls.DATABASE_THRESHOLDS.get(operation, {}).get(level, 1000.0)

    @classmethod
    def get_memory_threshold(cls, metric: str, level: PerformanceLevel) -> float:
        """Get memory threshold for a specific level"""
        if metric == "baseline_mb":
            return cls.MEMORY_THRESHOLDS["baseline_mb"]
        return cls.MEMORY_THRESHOLDS.get(metric, {}).get(level, 2048.0)

    @classmethod
    def get_test_data_config(cls, level: PerformanceLevel) -> Dict[str, int]:
        """Get test data configuration for a specific level"""
        return {
            "users_count": cls.TEST_DATA_CONFIG["users_count"][level],
            "projects_per_user": cls.TEST_DATA_CONFIG["projects_per_user"][level],
            "time_entries_per_project": cls.TEST_DATA_CONFIG[
                "time_entries_per_project"
            ][level],
        }


class PerformanceReporter:
    """Performance test result reporter"""

    @staticmethod
    def validate_results(
        results: Dict[str, Any], level: PerformanceLevel, operation_type: str = "api"
    ) -> Dict[str, bool]:
        """Validate performance results against thresholds"""
        threshold = PerformanceConfig.get_threshold(level)
        validation_results = {}

        if operation_type == "api":
            # Validate API performance metrics
            if "mean_response_time" in results:
                validation_results["response_time"] = (
                    results["mean_response_time"] * 1000
                    <= threshold.max_response_time_ms
                )

            if "p95_response_time" in results:
                validation_results["p95_response_time"] = (
                    results["p95_response_time"] * 1000
                    <= threshold.max_p95_response_time_ms
                )

            if "p99_response_time" in results:
                validation_results["p99_response_time"] = (
                    results["p99_response_time"] * 1000
                    <= threshold.max_p99_response_time_ms
                )

            if "requests_per_second" in results:
                validation_results["requests_per_second"] = (
                    results["requests_per_second"] >= threshold.min_requests_per_second
                )

            if "error_rate" in results:
                validation_results["error_rate"] = (
                    results["error_rate"] <= threshold.max_error_rate_percent
                )

        elif operation_type == "database":
            # Validate database performance metrics
            if "mean_query_time" in results:
                db_threshold = PerformanceConfig.get_database_threshold(
                    "simple_query_ms", level
                )
                validation_results["query_time"] = (
                    results["mean_query_time"] * 1000 <= db_threshold
                )

        elif operation_type == "memory":
            # Validate memory usage
            if "memory_usage_mb" in results:
                memory_threshold = PerformanceConfig.get_memory_threshold(
                    "max_total_mb", level
                )
                validation_results["memory_usage"] = (
                    results["memory_usage_mb"] <= memory_threshold
                )

        return validation_results

    @staticmethod
    def generate_report(
        test_name: str,
        results: Dict[str, Any],
        level: PerformanceLevel,
        operation_type: str = "api",
    ) -> str:
        """Generate a performance test report"""
        validation_results = PerformanceReporter.validate_results(
            results, level, operation_type
        )

        report = []
        report.append(f"Performance Test Report: {test_name}")
        report.append(f"Test Level: {level.value.upper()}")
        report.append(f"Operation Type: {operation_type.upper()}")
        report.append("=" * 50)

        # Add results
        report.append("\nResults:")
        for key, value in results.items():
            if isinstance(value, float):
                if "time" in key.lower():
                    report.append(f"  {key}: {value*1000:.2f}ms")
                else:
                    report.append(f"  {key}: {value:.2f}")
            else:
                report.append(f"  {key}: {value}")

        # Add validation results
        report.append("\nValidation:")
        all_passed = True
        for metric, passed in validation_results.items():
            status = "PASS" if passed else "FAIL"
            report.append(f"  {metric}: {status}")
            if not passed:
                all_passed = False

        overall_status = "PASS" if all_passed else "FAIL"
        report.append(f"\nOverall Status: {overall_status}")

        return "\n".join(report)


# Environment-specific configurations
ENVIRONMENT_CONFIG = {
    "development": {
        "base_url": "http://localhost:8000",
        "database_url": "postgresql://localhost/devhq_test",
        "default_level": PerformanceLevel.SMOKE,
        "parallel_tests": 2,
    },
    "staging": {
        "base_url": "https://staging.devhq.com",
        "database_url": "postgresql://staging-db/devhq",
        "default_level": PerformanceLevel.LOAD,
        "parallel_tests": 4,
    },
    "production": {
        "base_url": "https://api.devhq.com",
        "database_url": "postgresql://prod-db/devhq",
        "default_level": PerformanceLevel.ENDURANCE,
        "parallel_tests": 8,
    },
}


def get_environment_config(env: str = "development") -> Dict[str, Any]:
    """Get configuration for specific environment"""
    return ENVIRONMENT_CONFIG.get(env, ENVIRONMENT_CONFIG["development"])
