import pytest
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User, UserSettings


@pytest.mark.asyncio
class TestUserProfile:
    async def test_get_profile(
        self,
        async_client: AsyncClient,
        authenticated_client: AsyncClient,
        test_user: User,
    ):
        """Test retrieving user profile."""
        # Act - Unauthenticated request should fail
        unauth_response = await async_client.get("/api/v1/users/me")

        # Assert
        assert unauth_response.status_code == 401

        # Act - Authenticated request should succeed
        auth_response = await authenticated_client.get("/api/v1/users/me")

        # Assert
        assert auth_response.status_code == 200
        json_response = auth_response.json()

        # Check response structure
        assert "data" in json_response
        user_data = json_response["data"]

        # Verify user data matches test user
        assert user_data["id"] == str(test_user.id)
        assert user_data["email"] == test_user.email
        assert user_data["first_name"] == test_user.first_name
        assert user_data["last_name"] == test_user.last_name
        assert user_data["full_name"] == f"{test_user.first_name} {test_user.last_name}"
        assert "is_active" in user_data
        assert "is_verified" in user_data
        assert "created_at" in user_data
        assert "updated_at" in user_data

    async def test_update_profile(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        db_session: AsyncSession,
    ):
        """Test updating user profile."""
        # Arrange
        update_data = {
            "first_name": "Updated",
            "last_name": "Name",
            "phone": "+9876543210",
            "bio": "Updated bio information",
        }

        # Act
        response = await authenticated_client.put("/api/v1/users/me", json=update_data)

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure
        assert "data" in json_response
        user_data = json_response["data"]

        # Verify updated fields
        assert user_data["first_name"] == update_data["first_name"]
        assert user_data["last_name"] == update_data["last_name"]
        assert user_data["phone"] == update_data["phone"]
        assert user_data["bio"] == update_data["bio"]
        assert (
            user_data["full_name"]
            == f"{update_data['first_name']} {update_data['last_name']}"
        )

        # Verify database was updated
        result = await db_session.execute(
            text("SELECT * FROM users WHERE id = :user_id"),
            {"user_id": str(test_user.id)},
        )
        updated_user = result.mappings().first()
        assert updated_user["first_name"] == update_data["first_name"]
        assert updated_user["last_name"] == update_data["last_name"]
        assert updated_user["phone"] == update_data["phone"]
        assert updated_user["bio"] == update_data["bio"]

    async def test_partial_update_profile(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        db_session: AsyncSession,
    ):
        """Test partial update of user profile."""
        # Arrange - Store original values
        original_first_name = test_user.first_name
        original_last_name = test_user.last_name

        # Only update phone and bio
        update_data = {"phone": "+1122334455", "bio": "Partially updated bio"}

        # Act
        response = await authenticated_client.put("/api/v1/users/me", json=update_data)

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure
        assert "data" in json_response
        user_data = json_response["data"]

        # Verify only specified fields were updated
        assert user_data["first_name"] == original_first_name  # Unchanged
        assert user_data["last_name"] == original_last_name  # Unchanged
        assert user_data["phone"] == update_data["phone"]  # Updated
        assert user_data["bio"] == update_data["bio"]  # Updated

        # Verify database was updated correctly
        result = await db_session.execute(
            text("SELECT * FROM users WHERE id = :user_id"),
            {"user_id": str(test_user.id)},
        )
        updated_user = result.mappings().first()
        assert updated_user["first_name"] == original_first_name
        assert updated_user["last_name"] == original_last_name
        assert updated_user["phone"] == update_data["phone"]
        assert updated_user["bio"] == update_data["bio"]


@pytest.mark.asyncio
class TestUserSettings:
    async def test_get_settings(
        self, authenticated_client: AsyncClient, test_user: User
    ):
        """Test retrieving user settings."""
        # Act
        response = await authenticated_client.get("/api/v1/users/me/settings")

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure
        assert "data" in json_response
        settings_data = json_response["data"]

        # Verify settings fields exist
        assert "theme" in settings_data
        assert "language" in settings_data
        assert "timezone" in settings_data
        assert "date_format" in settings_data
        assert "time_format" in settings_data
        assert "default_currency" in settings_data
        assert "default_hourly_rate" in settings_data
        assert "invoice_prefix" in settings_data
        assert "invoice_number_start" in settings_data
        assert "payment_terms_days" in settings_data
        assert "email_notifications" in settings_data
        assert "push_notifications" in settings_data
        assert "marketing_emails" in settings_data

    async def test_update_settings(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        db_session: AsyncSession,
    ):
        """Test updating user settings."""
        # Arrange
        settings_update = {
            "theme": "dark",
            "language": "fr",
            "timezone": "Europe/Paris",
            "date_format": "DD/MM/YYYY",
            "time_format": "HH:mm",
            "default_currency": "EUR",
            "default_hourly_rate": 75.0,
            "invoice_prefix": "INV",
            "invoice_number_start": 1000,
            "payment_terms_days": 30,
            "email_notifications": True,
            "push_notifications": False,
            "marketing_emails": False,
        }

        # Act
        response = await authenticated_client.put(
            "/api/v1/users/me/settings", json=settings_update
        )

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure
        assert "data" in json_response
        settings_data = json_response["data"]

        # Verify all settings were updated
        for key, value in settings_update.items():
            assert settings_data[key] == value

        # Verify database was updated
        result = await db_session.execute(
            text(
                "SELECT * FROM user_settings WHERE user_id = :user_id AND deleted_at IS NULL"
            ),
            {"user_id": str(test_user.id)},
        )
        user_settings = result.mappings().first()
        assert user_settings is not None
        for key, value in settings_update.items():
            assert user_settings[key] == value

    async def test_partial_update_settings(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        db_session: AsyncSession,
    ):
        """Test partial update of user settings."""
        # Arrange - First get current settings
        get_response = await authenticated_client.get("/api/v1/users/me/settings")
        assert get_response.status_code == 200
        current_settings = get_response.json()["data"]

        # Only update a few settings
        partial_update = {
            "theme": "light",
            "default_hourly_rate": 100.0,
            "marketing_emails": True,
        }

        # Act
        response = await authenticated_client.put(
            "/api/v1/users/me/settings", json=partial_update
        )

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure
        assert "data" in json_response
        settings_data = json_response["data"]

        # Verify only specified settings were updated
        for key, value in partial_update.items():
            assert settings_data[key] == value

        # Verify other settings remained unchanged
        for key, value in current_settings.items():
            if key not in partial_update:
                assert settings_data[key] == value


@pytest.mark.asyncio
class TestAccountManagement:
    async def test_delete_account(
        self,
        authenticated_client: AsyncClient,
        test_user: User,
        db_session: AsyncSession,
    ):
        """Test deleting user account."""
        # Act
        response = await authenticated_client.delete("/api/v1/users/me")

        # Assert
        assert response.status_code == 200
        json_response = response.json()

        # Check response structure
        assert "data" in json_response
        assert json_response["data"]["success"] is True

        # Verify user was soft-deleted in database
        result = await db_session.execute(
            text("SELECT * FROM users WHERE id = :user_id"),
            {"user_id": str(test_user.id)},
        )
        deleted_user = result.mappings().first()
        assert deleted_user is not None
        assert deleted_user["deleted_at"] is not None

        # Verify user settings were also soft-deleted
        settings_result = await db_session.execute(
            text("SELECT * FROM user_settings WHERE user_id = :user_id"),
            {"user_id": str(test_user.id)},
        )
        user_settings = settings_result.mappings().first()
        if user_settings:
            assert user_settings["deleted_at"] is not None

        # Verify authentication no longer works
        auth_check_response = await authenticated_client.get("/api/v1/users/me")
        assert auth_check_response.status_code == 401
