#!/usr/bin/env python3
"""Performance test runner script"""

import argparse
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.database import get_database_url
from tests.performance_config import (PerformanceConfig, PerformanceLevel,
                                      get_environment_config)
from tests.performance_monitor import (PerformanceTestRunner,
                                       cleanup_performance_test_data,
                                       create_performance_test_data)


class PerformanceTestSuite:
    """Main performance test suite runner"""

    def __init__(
        self,
        environment: str = "development",
        level: PerformanceLevel = PerformanceLevel.SMOKE,
    ):
        self.environment = environment
        self.level = level
        self.env_config = get_environment_config(environment)
        self.results: Dict[str, Any] = {}

        # Setup database
        self.engine = create_engine(get_database_url())
        self.SessionLocal = sessionmaker(bind=self.engine)

        # Setup performance runner
        self.perf_runner = PerformanceTestRunner(self.engine)

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests"""
        print(
            f"Running performance tests - Environment: {self.environment}, Level: {self.level.value}"
        )
        print("=" * 80)

        test_results = {}

        # Run different test categories
        test_categories = [
            ("database", self.run_database_tests),
            ("api", self.run_api_tests),
            ("load", self.run_load_tests),
            ("memory", self.run_memory_tests),
        ]

        for category, test_func in test_categories:
            print(f"\nRunning {category.upper()} tests...")
            try:
                category_results = test_func()
                test_results[category] = category_results
                print(f"✓ {category.upper()} tests completed")
            except Exception as e:
                print(f"✗ {category.upper()} tests failed: {e}")
                test_results[category] = {"error": str(e)}

        self.results = test_results
        return test_results

    def run_database_tests(self) -> Dict[str, Any]:
        """Run database performance tests"""
        results = {}

        with self.SessionLocal() as db_session:
            # Create test data
            test_data = create_performance_test_data(db_session, self.level)

            try:
                # Test simple queries
                with self.perf_runner.monitor_performance("simple_queries") as monitor:
                    self._run_simple_query_tests(db_session, test_data)

                results["simple_queries"] = self.perf_runner.results["simple_queries"]

                # Test complex queries
                with self.perf_runner.monitor_performance("complex_queries") as monitor:
                    self._run_complex_query_tests(db_session, test_data)

                results["complex_queries"] = self.perf_runner.results["complex_queries"]

                # Test bulk operations
                with self.perf_runner.monitor_performance("bulk_operations") as monitor:
                    self._run_bulk_operation_tests(db_session, test_data)

                results["bulk_operations"] = self.perf_runner.results["bulk_operations"]

            finally:
                # Clean up test data
                cleanup_performance_test_data(db_session, test_data)

        return results

    def run_api_tests(self) -> Dict[str, Any]:
        """Run API performance tests using pytest"""
        results = {}

        # Run pytest with performance markers
        test_files = ["tests/test_performance.py", "tests/test_benchmarks.py"]

        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"Running {test_file}...")

                # Run pytest and capture results
                exit_code = pytest.main(
                    [
                        test_file,
                        "-v",
                        "--tb=short",
                        f"--performance-level={self.level.value}",
                        "--disable-warnings",
                    ]
                )

                results[test_file] = {
                    "exit_code": exit_code,
                    "status": "passed" if exit_code == 0 else "failed",
                }

        return results

    def run_load_tests(self) -> Dict[str, Any]:
        """Run load tests using Locust"""
        results = {}

        try:
            # Check if locust is available
            import subprocess

            # Run locust tests programmatically
            locust_file = "tests/test_load_testing.py"
            if os.path.exists(locust_file):
                config = PerformanceConfig.get_endpoint_config("/projects/", self.level)

                cmd = [
                    "locust",
                    "-f",
                    locust_file,
                    "--headless",
                    "-u",
                    str(config["concurrent_users"]),
                    "-r",
                    str(config["concurrent_users"] // 10),  # Spawn rate
                    "-t",
                    f"{config['duration_seconds']}s",
                    "--host",
                    self.env_config["base_url"],
                    "--csv",
                    "performance_results",
                ]

                print(f"Running: {' '.join(cmd)}")
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=config["duration_seconds"] + 60,
                )

                results["locust"] = {
                    "exit_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                }

                # Parse CSV results if available
                csv_file = "performance_results_stats.csv"
                if os.path.exists(csv_file):
                    results["csv_results"] = self._parse_locust_csv(csv_file)

        except ImportError:
            results["error"] = "Locust not installed. Install with: pip install locust"
        except subprocess.TimeoutExpired:
            results["error"] = "Load test timed out"
        except Exception as e:
            results["error"] = f"Load test failed: {e}"

        return results

    def run_memory_tests(self) -> Dict[str, Any]:
        """Run memory performance tests"""
        results = {}

        # Run memory benchmark from test_benchmarks.py
        exit_code = pytest.main(
            [
                "tests/test_benchmarks.py::TestMemoryBenchmarks::test_memory_usage_benchmark",
                "-v",
                "--tb=short",
                "--disable-warnings",
            ]
        )

        results["memory_benchmark"] = {
            "exit_code": exit_code,
            "status": "passed" if exit_code == 0 else "failed",
        }

        return results

    def _run_simple_query_tests(self, db_session, test_data):
        """Run simple database query tests"""
        from app.models.client import Client
        from app.models.user import User

        # Test user queries
        for _ in range(100):
            user = (
                db_session.query(User)
                .filter(User.id == test_data["users"][0].id)
                .first()
            )

        # Test client queries
        for _ in range(100):
            clients = (
                db_session.query(Client)
                .filter(Client.user_id == test_data["users"][0].id)
                .all()
            )

    def _run_complex_query_tests(self, db_session, test_data):
        """Run complex database query tests"""
        from sqlalchemy import func

        from app.models.client import Client
        from app.models.project import Project
        from app.models.user import User

        # Test complex joins
        for _ in range(50):
            results = (
                db_session.query(Project)
                .join(Client)
                .join(User)
                .filter(User.is_active == True)
                .all()
            )

        # Test aggregations
        for _ in range(50):
            stats = (
                db_session.query(
                    Project.status,
                    func.count(Project.id).label("count"),
                    func.avg(Project.hourly_rate).label("avg_rate"),
                )
                .group_by(Project.status)
                .all()
            )

    def _run_bulk_operation_tests(self, db_session, test_data):
        """Run bulk operation tests"""
        from datetime import date
        from decimal import Decimal

        from app.core.database_utils import BulkOperationManager
        from app.models.project import TimeEntry

        bulk_manager = BulkOperationManager()

        # Test bulk inserts
        for _ in range(10):
            entries = []
            for i in range(50):
                entry = TimeEntry(
                    user_id=test_data["users"][0].id,
                    project_id=test_data["projects"][0].id,
                    description=f"Bulk test entry {i}",
                    duration_hours=Decimal("1.0"),
                    work_date=date.today(),
                    is_billable=True,
                    status="completed",
                )
                entries.append(entry)

            created_entries = bulk_manager.bulk_create(db_session, entries)

            # Clean up immediately
            for entry in created_entries:
                db_session.delete(entry)
            db_session.commit()

    def _parse_locust_csv(self, csv_file: str) -> Dict[str, Any]:
        """Parse Locust CSV results"""
        import csv

        results = []
        with open(csv_file, "r") as f:
            reader = csv.DictReader(f)
            for row in reader:
                results.append(row)

        if not results:
            return {}

        # Calculate summary statistics
        total_requests = sum(int(row.get("Request Count", 0)) for row in results)
        total_failures = sum(int(row.get("Failure Count", 0)) for row in results)
        avg_response_time = sum(
            float(row.get("Average Response Time", 0)) for row in results
        ) / len(results)

        return {
            "total_requests": total_requests,
            "total_failures": total_failures,
            "failure_rate": (total_failures / total_requests * 100)
            if total_requests > 0
            else 0,
            "avg_response_time": avg_response_time,
            "endpoints": results,
        }

    def generate_report(self) -> str:
        """Generate comprehensive performance report"""
        if not self.results:
            return "No test results available"

        report = []
        report.append("PERFORMANCE TEST REPORT")
        report.append("=" * 50)
        report.append(f"Environment: {self.environment}")
        report.append(f"Test Level: {self.level.value.upper()}")
        report.append(f"Timestamp: {datetime.now().isoformat()}")
        report.append("")

        # Summary
        total_tests = 0
        passed_tests = 0

        for category, results in self.results.items():
            report.append(f"{category.upper()} TESTS:")
            report.append("-" * 30)

            if isinstance(results, dict) and "error" in results:
                report.append(f"  ERROR: {results['error']}")
                total_tests += 1
            else:
                for test_name, test_result in results.items():
                    total_tests += 1
                    if isinstance(test_result, dict):
                        if (
                            test_result.get("status") == "passed"
                            or test_result.get("exit_code") == 0
                        ):
                            report.append(f"  ✓ {test_name}: PASSED")
                            passed_tests += 1
                        else:
                            report.append(f"  ✗ {test_name}: FAILED")
                    else:
                        report.append(f"  - {test_name}: {test_result}")
                        passed_tests += 1

            report.append("")

        # Overall summary
        report.append("SUMMARY:")
        report.append("-" * 30)
        report.append(f"Total Tests: {total_tests}")
        report.append(f"Passed: {passed_tests}")
        report.append(f"Failed: {total_tests - passed_tests}")
        report.append(
            f"Success Rate: {(passed_tests / total_tests * 100):.1f}%"
            if total_tests > 0
            else "Success Rate: N/A"
        )

        return "\n".join(report)

    def save_results(self, output_dir: str = "performance_results"):
        """Save test results to files"""
        os.makedirs(output_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save JSON results
        json_file = os.path.join(output_dir, f"performance_results_{timestamp}.json")
        with open(json_file, "w") as f:
            json.dump(
                {
                    "environment": self.environment,
                    "level": self.level.value,
                    "timestamp": datetime.now().isoformat(),
                    "results": self.results,
                },
                f,
                indent=2,
            )

        # Save text report
        report_file = os.path.join(output_dir, f"performance_report_{timestamp}.txt")
        with open(report_file, "w") as f:
            f.write(self.generate_report())

        print(f"Results saved to:")
        print(f"  JSON: {json_file}")
        print(f"  Report: {report_file}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Run performance tests")
    parser.add_argument(
        "--environment",
        choices=["development", "staging", "production"],
        default="development",
        help="Test environment",
    )
    parser.add_argument(
        "--level",
        choices=[level.value for level in PerformanceLevel],
        default=PerformanceLevel.SMOKE.value,
        help="Performance test level",
    )
    parser.add_argument(
        "--category",
        choices=["all", "database", "api", "load", "memory"],
        default="all",
        help="Test category to run",
    )
    parser.add_argument(
        "--output-dir",
        default="performance_results",
        help="Output directory for results",
    )
    parser.add_argument(
        "--report-only",
        action="store_true",
        help="Only generate report from existing results",
    )

    args = parser.parse_args()

    # Convert level string to enum
    level = PerformanceLevel(args.level)

    # Create test suite
    suite = PerformanceTestSuite(args.environment, level)

    if args.report_only:
        # Load existing results and generate report
        try:
            latest_results = find_latest_results(args.output_dir)
            if latest_results:
                suite.results = latest_results["results"]
                print(suite.generate_report())
            else:
                print("No existing results found")
        except Exception as e:
            print(f"Error loading results: {e}")
        return

    # Run tests
    start_time = time.time()

    try:
        if args.category == "all":
            suite.run_all_tests()
        elif args.category == "database":
            suite.results["database"] = suite.run_database_tests()
        elif args.category == "api":
            suite.results["api"] = suite.run_api_tests()
        elif args.category == "load":
            suite.results["load"] = suite.run_load_tests()
        elif args.category == "memory":
            suite.results["memory"] = suite.run_memory_tests()

        end_time = time.time()

        # Generate and display report
        print("\n" + "=" * 80)
        print(suite.generate_report())
        print(f"\nTotal execution time: {end_time - start_time:.2f} seconds")

        # Save results
        suite.save_results(args.output_dir)

    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nError running tests: {e}")
        sys.exit(1)


def find_latest_results(output_dir: str) -> Dict[str, Any]:
    """Find the latest results file"""
    if not os.path.exists(output_dir):
        return None

    json_files = [
        f
        for f in os.listdir(output_dir)
        if f.startswith("performance_results_") and f.endswith(".json")
    ]

    if not json_files:
        return None

    # Sort by filename (which includes timestamp)
    latest_file = sorted(json_files)[-1]

    with open(os.path.join(output_dir, latest_file), "r") as f:
        return json.load(f)


if __name__ == "__main__":
    main()
