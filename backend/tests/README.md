# DevHQ Backend Testing

This directory contains comprehensive tests for the DevHQ backend application.

## Quick Start

```bash
# Install test dependencies
make install-test-deps

# Run all tests
make test

# Run tests with coverage
make test-coverage

# List available test suites
make list-suites

# Run tests with SQLite (faster for development)
USE_POSTGRES_TESTS=false python -m pytest tests/

# Run tests with PostgreSQL (more accurate for production)
USE_POSTGRES_TESTS=true python -m pytest tests/

# Run tests with Docker PostgreSQL instance
USE_POSTGRES_TESTS=true USE_DOCKER_DB=true python -m pytest tests/

# Run tests with file-based SQLite (for debugging)
USE_POSTGRES_TESTS=false USE_FILE_DB=true python -m pytest tests/
```

## Test Organization

- `tests/` - Test files organized by functionality
- `scripts/` - Test runner and utility scripts
- `docs/testing-guide.md` - Comprehensive testing documentation
- `requirements-test.txt` - Test-specific dependencies
- `Makefile` - Test automation commands

## Database Configuration

The test suite supports multiple database configurations to accommodate different testing needs:

### Environment Variables

- `USE_POSTGRES_TESTS` - Set to `true` to use PostgreSQL, `false` to use SQLite (default: `true`)
- `USE_DOCKER_DB` - Set to `true` to use Docker PostgreSQL instance, `false` to use local PostgreSQL (default: `true`)
- `USE_FILE_DB` - Set to `true` to use file-based SQLite database, `false` to use in-memory SQLite (default: `false`)

### Database Options

1. **PostgreSQL with Docker** (most accurate for production)
   - Uses a dedicated PostgreSQL instance in Docker
   - Provides full compatibility with production database
   - Requires Docker to be running
   - Slower than SQLite but more accurate

2. **PostgreSQL Local** (for CI/CD environments)
   - Uses a local PostgreSQL instance
   - Requires PostgreSQL to be installed locally
   - Good for CI/CD pipelines without Docker

3. **SQLite In-Memory** (fastest for development)
   - Uses an in-memory SQLite database
   - Very fast but may miss PostgreSQL-specific issues
   - Good for rapid development and testing
   - Database is recreated for each test

4. **SQLite File-Based** (for debugging)
   - Uses a file-based SQLite database
   - Allows inspection of database state after tests
   - Good for debugging database-related issues

## Available Test Suites

- **auth** - Authentication and authorization tests
- **timer** - Time tracking functionality tests
- **project** - Project management tests
- **client** - Client management tests
- **invoice** - Invoice processing tests
- **analytics** - Analytics and reporting tests
- **workflow** - End-to-end business workflow tests
- **models** - Database model validation tests
- **services** - Business logic service tests
- **integration** - Component integration tests

## Running Tests

### Using Make

```bash
# Run specific test suite
make test-suite SUITE=auth

# Run tests matching pattern
make test-pattern PATTERN=timer
```

### Running Tests Incrementally

To address failing tests incrementally, you can run tests in smaller batches:

```bash
# Run only unit tests
python -m pytest tests/unit/

# Run only integration tests
python -m pytest tests/integration/

# Run tests for a specific module
python -m pytest tests/unit/test_auth.py

# Run a specific test function
python -m pytest tests/unit/test_auth.py::test_login

# Run tests with verbose output
python -m pytest tests/unit/ -v

# Run tests and stop on first failure
python -m pytest tests/unit/ -x

# Run failed tests first, then the rest
python -m pytest tests/unit/ --failed-first

# Run only previously failed tests
python -m pytest tests/unit/ --last-failed
```

# Run smoke tests
make test-smoke

# Run a single test file
make test-single FILE=test_auth_endpoints.py
```

### Using pytest

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_auth_endpoints.py

# Run tests matching pattern
pytest -k "timer"

# Run with coverage
pytest --cov=app --cov-report=html
```

## Test Infrastructure

- **Fixtures**: Reusable test data and setup in `conftest.py`
- **Utilities**: Helper functions in `test_utils.py`
- **Configuration**: pytest configuration in `pyproject.toml`
- **Dependencies**: Managed in `requirements-test.txt`

## Documentation

See `docs/testing-guide.md` for comprehensive testing documentation including:
- Test writing guidelines
- Best practices
- Common patterns
- Debugging tips
- Coverage analysis