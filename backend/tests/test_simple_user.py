import asyncio

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth import get_password_hash
from app.models.user import User
# Import conftest components directly
from tests.conftest import Base, test_engine


@pytest.mark.asyncio
async def test_manual_db_session():
    """Test user creation with manually created session from conftest components."""

    try:
        # Create tables manually
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # Create session manually
        from sqlalchemy.orm import sessionmaker

        async_session = sessionmaker(
            test_engine, class_=AsyncSession, expire_on_commit=False
        )

        async with async_session() as session:
            # Create user exactly like our working example
            user = User(
                email="<EMAIL>",
                password_hash=get_password_hash("password123"),
                first_name="Manual",
                last_name="Test",
                phone="+1234567890",
                bio="Manual test user",
                is_active=True,
                is_verified=True,
            )

            print(f"Creating user: {user.email}")
            print(f"  Phone: {user.phone}")
            print(f"  Bio: {user.bio}")
            print(f"  Avatar URL: {user.avatar_url}")
            print(f"  Email verified at: {user.email_verified_at}")

            session.add(user)
            await session.commit()
            await session.refresh(user)

            print(f"✅ Manual user created successfully with ID: {user.id}")
            print(f"   Email: {user.email}")
            print(f"   Name: {user.full_name}")

            # Verify the user was created
            assert user.id is not None
            assert user.email == "<EMAIL>"
            assert user.full_name == "Manual Test"

        # Clean up tables
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

    except Exception as e:
        print(f"❌ Error in manual test: {e}")
        raise


@pytest.mark.asyncio
async def test_db_session_fixture(db_session: AsyncSession):
    """Test user creation using the db_session fixture from conftest.py."""

    # Create user using the fixture
    user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("password123"),
        first_name="Fixture",
        last_name="Test",
        phone="+1234567890",
        bio="Fixture test user",
        is_active=True,
        is_verified=True,
    )

    print(f"Creating user with fixture: {user.email}")

    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)

    print(f"✅ Fixture user created successfully with ID: {user.id}")
    print(f"   Email: {user.email}")
    print(f"   Name: {user.full_name}")

    assert user.id is not None
    assert user.email == "<EMAIL>"
    assert user.full_name == "Fixture Test"
