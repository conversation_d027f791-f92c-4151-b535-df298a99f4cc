"""Tests for payment processing and webhook handling"""

import json
import uuid
from datetime import datetime
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import HTTPException
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.payment_gateways.base import PaymentResult, PaymentStatus
from app.core.payment_service_enhanced import EnhancedPaymentService
from app.core.webhook_processor import WebhookProcessingError, WebhookProcessor
from app.models import Invoice, PaymentTransaction, User


@pytest.mark.asyncio
class TestPaymentProcessing:
    """Test payment processing functionality"""

    @pytest.fixture
    def payment_service(self, sync_db_session):
        """Create payment service instance"""
        return EnhancedPaymentService(sync_db_session)

    @pytest.fixture
    def webhook_processor(self, sync_db_session):
        """Create webhook processor instance"""
        return WebhookProcessor(sync_db_session)

    async def test_payment_verification_success(self, db_session, payment_service):
        """Test successful payment verification"""

        with patch.object(payment_service, "gateways") as mock_gateways:
            mock_gateway = AsyncMock()
            mock_gateway.verify_payment.return_value = PaymentResult(
                reference="test_ref_123",
                status=PaymentStatus.SUCCESS,
                amount=Decimal("500.00"),
                currency="NGN",
                gateway_response={"status": "success"},
            )
            mock_gateways = {"paystack": mock_gateway}
            payment_service.gateways = mock_gateways

            result = await payment_service.verify_payment("test_ref_123", "paystack")

            assert result.reference == "test_ref_123"
            assert result.status == PaymentStatus.SUCCESS
            assert result.amount == Decimal("500.00")
            assert result.currency == "NGN"
            mock_gateway.verify_payment.assert_called_once_with("test_ref_123")

    async def test_payment_verification_failed(self, db_session, payment_service):
        """Test failed payment verification"""

        with patch.object(payment_service, "gateways") as mock_gateways:
            mock_gateway = AsyncMock()
            mock_gateway.verify_payment.return_value = PaymentResult(
                reference="test_ref_123",
                status=PaymentStatus.FAILED,
                amount=Decimal("500.00"),
                currency="NGN",
                gateway_response={"status": "failed", "message": "Insufficient funds"},
            )
            mock_gateways = {"paystack": mock_gateway}
            payment_service.gateways = mock_gateways

            result = await payment_service.verify_payment("test_ref_123", "paystack")

            assert result.reference == "test_ref_123"
            assert result.status == PaymentStatus.FAILED
            assert result.amount == Decimal("500.00")
            assert result.currency == "NGN"

    async def test_payment_verification_invalid_gateway(
        self, db_session, payment_service
    ):
        """Test payment verification with invalid gateway"""

        with pytest.raises(ValueError, match="Gateway invalid_gateway not available"):
            await payment_service.verify_payment("test_ref_123", "invalid_gateway")

    def test_webhook_signature_verification_paystack(self, payment_service):
        """Test Paystack webhook signature verification"""
        payload = '{"event":"charge.success","data":{"reference":"test_ref"}}'
        signature = "valid_signature"

        with patch.object(payment_service, "gateways") as mock_gateways:
            mock_gateway = MagicMock()
            mock_gateway.verify_webhook_signature.return_value = True
            mock_gateways = {"paystack": mock_gateway}
            payment_service.gateways = mock_gateways

            result = payment_service.verify_webhook_signature(
                payload, signature, "paystack"
            )

            assert result is True
            mock_gateway.verify_webhook_signature.assert_called_once_with(
                payload, signature
            )

    def test_webhook_signature_verification_invalid(self, payment_service):
        """Test invalid webhook signature verification"""
        payload = '{"event":"charge.success","data":{"reference":"test_ref"}}'
        signature = "invalid_signature"

        with patch.object(payment_service, "gateways") as mock_gateways:
            mock_gateway = MagicMock()
            mock_gateway.verify_webhook_signature.return_value = False
            mock_gateways = {"paystack": mock_gateway}
            payment_service.gateways = mock_gateways

            result = payment_service.verify_webhook_signature(
                payload, signature, "paystack"
            )

            assert result is False

    def test_webhook_signature_verification_unknown_gateway(self, payment_service):
        """Test webhook signature verification for unknown gateway"""
        payload = '{"event":"charge.success","data":{"reference":"test_ref"}}'
        signature = "signature"

        result = payment_service.verify_webhook_signature(payload, signature, "unknown")

        assert result is False

    async def test_process_webhook_paystack_success(
        self, db_session, payment_service, sample_paystack_webhook_payload
    ):
        """Test successful Paystack webhook processing"""

        with patch.object(payment_service, "verify_payment") as mock_verify:
            mock_verify.return_value = PaymentResult(
                reference="test_ref_123",
                status=PaymentStatus.SUCCESS,
                amount=Decimal("500.00"),
                currency="NGN",
                gateway_response={"status": "success"},
            )

            result = await payment_service.process_webhook(
                sample_paystack_webhook_payload, "paystack"
            )

            assert result["reference"] == "test_ref_123"
            assert result["status"] == "success"
            assert result["amount"] == Decimal("500.00")
            assert result["currency"] == "NGN"
            mock_verify.assert_called_once_with("test_ref_123", "paystack")

    async def test_process_webhook_dpo_success(
        self, db_session, payment_service, sample_dpo_webhook_payload
    ):
        """Test successful DPO webhook processing"""

        with patch.object(payment_service, "verify_payment") as mock_verify:
            mock_verify.return_value = PaymentResult(
                reference="test_ref_456",
                status=PaymentStatus.SUCCESS,
                amount=Decimal("500.00"),
                currency="KES",
                gateway_response={"status": "success"},
            )

            result = await payment_service.process_webhook(
                sample_dpo_webhook_payload, "dpo"
            )

            assert result["reference"] == "test_ref_456"
            assert result["status"] == "success"
            assert result["amount"] == Decimal("500.00")
            assert result["currency"] == "KES"
            mock_verify.assert_called_once_with("test_ref_456", "dpo")

    async def test_process_webhook_missing_reference(self, db_session, payment_service):
        """Test webhook processing with missing reference"""

        payload = {"event": "charge.success", "data": {}}

        with pytest.raises(ValueError, match="No reference found in webhook payload"):
            await payment_service.process_webhook(payload, "paystack")


@pytest.mark.asyncio
class TestWebhookProcessor:
    """Test webhook processor functionality"""

    @pytest.fixture
    def webhook_processor(self, sync_db_session):
        """Create webhook processor instance"""
        return WebhookProcessor(sync_db_session)

    async def test_paystack_webhook_processing_success(
        self, db_session, webhook_processor, sample_paystack_webhook_payload
    ):
        """Test successful Paystack webhook processing through WebhookProcessor"""

        payload = json.dumps(sample_paystack_webhook_payload)
        signature = "valid_signature"
        request_id = str(uuid.uuid4())

        with patch.object(
            webhook_processor.payment_service, "verify_webhook_signature"
        ) as mock_verify_sig, patch.object(
            webhook_processor, "_process_paystack_event"
        ) as mock_process_event:
            mock_verify_sig.return_value = True
            mock_process_event.return_value = {
                "reference": "test_ref_123",
                "status": "success",
                "amount": Decimal("500.00"),
            }

            result = await webhook_processor.process_paystack_webhook(
                payload, signature, request_id
            )

            assert result["status"] == "success"
            assert result["request_id"] == request_id
            assert result["event_type"] == "charge.success"
            assert result["data"]["reference"] == "test_ref_123"
            mock_verify_sig.assert_called_once_with(payload, signature, "paystack")
            mock_process_event.assert_called_once()

    async def test_paystack_webhook_invalid_signature(self, webhook_processor):
        """Test Paystack webhook with invalid signature"""
        payload = '{"event":"charge.success","data":{"reference":"test_ref"}}'
        signature = "invalid_signature"

        with patch.object(
            webhook_processor.payment_service, "verify_webhook_signature"
        ) as mock_verify_sig:
            mock_verify_sig.return_value = False

            with pytest.raises(
                WebhookProcessingError, match="Invalid webhook signature"
            ):
                await webhook_processor.process_paystack_webhook(payload, signature)

    async def test_paystack_webhook_invalid_json(self, webhook_processor):
        """Test Paystack webhook with invalid JSON"""
        payload = "invalid json"
        signature = "valid_signature"

        with patch.object(
            webhook_processor.payment_service, "verify_webhook_signature"
        ) as mock_verify_sig:
            mock_verify_sig.return_value = True

            with pytest.raises(WebhookProcessingError, match="Invalid JSON payload"):
                await webhook_processor.process_paystack_webhook(payload, signature)

    async def test_paystack_webhook_missing_event_type(self, webhook_processor):
        """Test Paystack webhook with missing event type"""
        payload = '{"data":{"reference":"test_ref"}}'
        signature = "valid_signature"

        with patch.object(
            webhook_processor.payment_service, "verify_webhook_signature"
        ) as mock_verify_sig:
            mock_verify_sig.return_value = True

            with pytest.raises(WebhookProcessingError, match="Missing event type"):
                await webhook_processor.process_paystack_webhook(payload, signature)

    async def test_dpo_webhook_processing_success(
        self, db_session, webhook_processor, sample_dpo_webhook_payload
    ):
        """Test successful DPO webhook processing through WebhookProcessor"""
        request_id = str(uuid.uuid4())

        with patch.object(
            webhook_processor.payment_service, "process_webhook"
        ) as mock_process:
            mock_process.return_value = {
                "reference": "test_ref_456",
                "status": "success",
                "amount": Decimal("500.00"),
            }

            result = await webhook_processor.process_dpo_webhook(
                sample_dpo_webhook_payload, request_id
            )

            assert result["status"] == "success"
            assert result["request_id"] == request_id
            assert result["reference"] == "test_ref_456"
            mock_process.assert_called_once_with(
                payload=sample_dpo_webhook_payload, gateway_name="dpo"
            )

    async def test_dpo_webhook_invalid_format(self, webhook_processor):
        """Test DPO webhook with invalid format"""
        payload = "not a dict"
        request_id = str(uuid.uuid4())

        with pytest.raises(WebhookProcessingError, match="Invalid payload format"):
            await webhook_processor.process_dpo_webhook(payload, request_id)

    async def test_dpo_webhook_missing_reference(self, webhook_processor):
        """Test DPO webhook with missing reference"""
        payload = {"status": "success"}
        request_id = str(uuid.uuid4())

        with pytest.raises(
            WebhookProcessingError, match="Missing transaction reference"
        ):
            await webhook_processor.process_dpo_webhook(payload, request_id)


class TestWebhookEndpoints:
    """Test webhook API endpoints"""

    async def test_paystack_webhook_endpoint_success(self, async_client: AsyncClient):
        """Test successful Paystack webhook endpoint"""
        payload = {
            "event": "charge.success",
            "data": {
                "reference": "test_ref_123",
                "status": "success",
                "amount": 5000000,
                "currency": "NGN",
            },
        }
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_request_id",
                "event_type": "charge.success",
                "data": {"reference": "test_ref_123"},
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert data["event_type"] == "charge.success"

    async def test_paystack_webhook_endpoint_invalid_signature(
        self, async_client: AsyncClient
    ):
        """Test Paystack webhook endpoint with invalid signature"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "invalid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = (
                WebhookProcessingError(
                    "Invalid webhook signature", error_code="INVALID_SIGNATURE"
                )
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 400
            data = response.json()
            assert "Invalid webhook signature" in data["detail"]

    async def test_dpo_webhook_endpoint_success(self, async_client: AsyncClient):
        """Test successful DPO webhook endpoint"""
        payload = {
            "TransactionRef": "test_ref_456",
            "TransactionStatus": "1",
            "Amount": "500.00",
            "Currency": "KES",
        }

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_dpo_webhook.return_value = {
                "status": "success",
                "request_id": "test_request_id",
                "reference": "test_ref_456",
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post("/webhooks/dpo", json=payload)

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert data["reference"] == "test_ref_456"

    async def test_webhook_endpoint_internal_error(self, async_client: AsyncClient):
        """Test webhook endpoint with internal error"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = Exception(
                "Database error"
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200  # Returns 200 to prevent retries
            data = response.json()
            assert data["status"] == "error"
            assert data["message"] == "Internal server error"
            assert "request_id" in data


@pytest.mark.asyncio
class TestPaymentIntegration:
    """Test payment integration scenarios"""

    async def test_end_to_end_payment_flow(
        self, db_session: AsyncSession, test_user: User, test_client, sync_db_session
    ):
        """Test complete end-to-end payment flow"""

        # Create test invoice
        invoice = Invoice(
            id=str(uuid.uuid4()),
            user_id=test_user.id,
            client_id=test_client.id,
            invoice_number="INV-001",
            total_amount=Decimal("500.00"),
            currency="NGN",
            status="pending",
            payment_reference="test_ref_123",
        )
        db_session.add(invoice)
        await db_session.commit()

        # Mock payment service
        payment_service = EnhancedPaymentService(sync_db_session)

        with patch.object(payment_service, "verify_payment") as mock_verify:
            mock_verify.return_value = PaymentResult(
                reference="test_ref_123",
                status=PaymentStatus.SUCCESS,
                amount=Decimal("500.00"),
                currency="NGN",
                gateway_response={"status": "success"},
            )

            # Process webhook
            webhook_payload = {
                "event": "charge.success",
                "data": {
                    "reference": "test_ref_123",
                    "status": "success",
                    "amount": 50000,  # 500.00 in kobo
                    "currency": "NGN",
                },
            }

            result = await payment_service.process_webhook(webhook_payload, "paystack")

            assert result["reference"] == "test_ref_123"
            assert result["status"] == "success"
            assert result["amount"] == Decimal("500.00")
            assert result["currency"] == "NGN"

    async def test_idempotent_webhook_processing(
        self, db_session: AsyncSession, sync_db_session
    ):
        """Test that webhook processing is idempotent"""

        payment_service = EnhancedPaymentService(sync_db_session)

        with patch.object(payment_service, "verify_payment") as mock_verify:
            mock_verify.return_value = PaymentResult(
                reference="test_ref_123",
                status=PaymentStatus.SUCCESS,
                amount=Decimal("500.00"),
                currency="NGN",
                gateway_response={"status": "success"},
            )

            webhook_payload = {
                "event": "charge.success",
                "data": {
                    "reference": "test_ref_123",
                    "status": "success",
                    "amount": 50000,
                    "currency": "NGN",
                },
            }

            # Process webhook twice
            result1 = await payment_service.process_webhook(webhook_payload, "paystack")
            result2 = await payment_service.process_webhook(webhook_payload, "paystack")

            # Results should be identical
            assert result1 == result2
            assert mock_verify.call_count == 2  # Should be called each time

    async def test_multi_currency_payment_processing(
        self, db_session: AsyncSession, sync_db_session
    ):
        """Test payment processing with multiple currencies"""

        payment_service = EnhancedPaymentService(sync_db_session)

        currencies = [
            ("NGN", Decimal("50000.00")),
            ("KES", Decimal("5000.00")),
            ("USD", Decimal("500.00")),
            ("GHS", Decimal("3000.00")),
            ("ZAR", Decimal("7500.00")),
        ]

        for currency, amount in currencies:
            with patch.object(payment_service, "verify_payment") as mock_verify:
                mock_verify.return_value = PaymentResult(
                    reference=f"test_ref_{currency}",
                    status=PaymentStatus.SUCCESS,
                    amount=amount,
                    currency=currency,
                    gateway_response={"status": "success"},
                )

                webhook_payload = {
                    "event": "charge.success",
                    "data": {
                        "reference": f"test_ref_{currency}",
                        "status": "success",
                        "amount": int(amount * 100),  # Convert to smallest unit
                        "currency": currency,
                    },
                }

                result = await payment_service.process_webhook(
                    webhook_payload, "paystack"
                )

                assert result["reference"] == f"test_ref_{currency}"
                assert result["status"] == "success"
                assert result["amount"] == amount
                assert result["currency"] == currency
