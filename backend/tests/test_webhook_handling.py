"""Tests for webhook handling endpoints and processing"""

import json
import uuid
from datetime import datetime
from decimal import Decimal
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest
from fastapi import HTTPException
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.webhook_processor import WebhookProcessingError, WebhookProcessor
from app.models import Invoice, PaymentTransaction, User


class TestWebhookSecurity:
    """Test webhook security and signature verification"""

    async def test_paystack_webhook_missing_signature(self, async_client: AsyncClient):
        """Test Paystack webhook without signature header"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}

        response = await async_client.post("/webhooks/paystack", json=payload)

        # Should return 503 for missing signature with retry-after header
        assert response.status_code == 503

    async def test_paystack_webhook_with_signature(self, async_client: AsyncClient):
        """Test Paystack webhook with signature header"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "sha256=test_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "event_type": "charge.success",
                "data": {"reference": "test_ref_123"},
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"

    async def test_webhook_signature_verification_failure(
        self, async_client: AsyncClient
    ):
        """Test webhook with invalid signature"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "invalid_signature"}

        with patch(
            "app.core.webhook_processor.WebhookProcessor"
        ) as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = (
                WebhookProcessingError(
                    "Invalid webhook signature", error_code="INVALID_SIGNATURE"
                )
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 400
            data = response.json()
            assert "Invalid webhook signature" in data["detail"]


class TestWebhookErrorHandling:
    """Test webhook error handling scenarios"""

    async def test_webhook_invalid_json_payload(self, async_client: AsyncClient):
        """Test webhook with invalid JSON payload"""
        headers = {"x-paystack-signature": "valid_signature"}

        with patch(
            "app.core.webhook_processor.WebhookProcessor"
        ) as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = (
                WebhookProcessingError(
                    "Invalid JSON payload", error_code="INVALID_JSON"
                )
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack",
                content="invalid json",
                headers={"content-type": "application/json", **headers},
            )

            assert response.status_code == 400

    async def test_webhook_missing_event_type(self, async_client: AsyncClient):
        """Test webhook with missing event type"""
        payload = {"data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch(
            "app.core.webhook_processor.WebhookProcessor"
        ) as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = (
                WebhookProcessingError(
                    "Missing event type", error_code="MISSING_EVENT_TYPE"
                )
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 400

    async def test_webhook_retryable_error(self, async_client: AsyncClient):
        """Test webhook with retryable error"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            error = WebhookProcessingError(
                "Database temporarily unavailable",
                error_code="DATABASE_ERROR",
                retry_after=300,
            )
            print(f"Created error with retry_after: {error.retry_after}")
            mock_processor.process_paystack_webhook.side_effect = error
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            print(f"Response content: {response.content}")
            assert response.status_code == 503
            assert "Retry-After" in response.headers
            assert response.headers["Retry-After"] == "300"

    async def test_webhook_non_retryable_error(self, async_client: AsyncClient):
        """Test webhook with non-retryable error"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = (
                WebhookProcessingError(
                    "Invalid payment reference", error_code="INVALID_REFERENCE"
                )
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200  # Returns 200 to prevent retries
            data = response.json()
            assert data["status"] == "error"
            assert data["error_code"] == "INVALID_REFERENCE"

    async def test_webhook_unexpected_error(self, async_client: AsyncClient):
        """Test webhook with unexpected error"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = Exception(
                "Unexpected error"
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200  # Returns 200 to prevent retries
            data = response.json()
            assert data["status"] == "error"
            assert data["message"] == "Internal server error"
            assert "request_id" in data


class TestWebhookEventTypes:
    """Test different webhook event types"""

    async def test_charge_success_event(self, async_client: AsyncClient):
        """Test charge.success webhook event"""
        payload = {
            "event": "charge.success",
            "data": {
                "reference": "test_ref_123",
                "status": "success",
                "amount": 5000000,  # 50,000 NGN in kobo
                "currency": "NGN",
                "customer": {"email": "<EMAIL>"},
            },
        }
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "event_type": "charge.success",
                "data": {
                    "reference": "test_ref_123",
                    "status": "success",
                    "amount": Decimal("500.00"),
                },
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["event_type"] == "charge.success"
            assert data["data"]["reference"] == "test_ref_123"

    async def test_charge_failed_event(self, async_client: AsyncClient):
        """Test charge.failed webhook event"""
        payload = {
            "event": "charge.failed",
            "data": {
                "reference": "test_ref_123",
                "status": "failed",
                "amount": 5000000,
                "currency": "NGN",
                "gateway_response": "Insufficient funds",
            },
        }
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "event_type": "charge.failed",
                "data": {"reference": "test_ref_123", "status": "failed"},
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["event_type"] == "charge.failed"

    async def test_unsupported_event_type(self, async_client: AsyncClient):
        """Test unsupported webhook event type"""
        payload = {"event": "unsupported.event", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "event_type": "unsupported.event",
                "data": {"message": "Event type not processed"},
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200
            # Should still return success even for unsupported events


class TestDPOWebhookHandling:
    """Test DPO webhook handling"""

    async def test_dpo_webhook_success(self, async_client: AsyncClient):
        """Test successful DPO webhook processing"""
        payload = {
            "TransactionRef": "test_ref_456",
            "TransactionStatus": "1",  # Success
            "Amount": "500.00",
            "Currency": "KES",
            "TransactionDate": "2023-01-01 12:00:00",
            "CustomerEmail": "<EMAIL>",
            "PaymentMethod": "MPESA",
        }

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_dpo_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "reference": "test_ref_456",
                "data": {
                    "reference": "test_ref_456",
                    "status": "success",
                    "amount": Decimal("500.00"),
                    "currency": "KES",
                },
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post("/webhooks/dpo", json=payload)

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert data["reference"] == "test_ref_456"

    async def test_dpo_webhook_failed_transaction(self, async_client: AsyncClient):
        """Test DPO webhook with failed transaction"""
        payload = {
            "TransactionRef": "test_ref_456",
            "TransactionStatus": "0",  # Failed
            "Amount": "500.00",
            "Currency": "KES",
            "ErrorMessage": "Payment failed",
        }

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_dpo_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "reference": "test_ref_456",
                "data": {
                    "reference": "test_ref_456",
                    "status": "failed",
                    "error": "Payment failed",
                },
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post("/webhooks/dpo", json=payload)

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"  # Webhook processed successfully
            assert data["data"]["status"] == "failed"  # Payment failed

    async def test_dpo_webhook_missing_reference(self, async_client: AsyncClient):
        """Test DPO webhook with missing transaction reference"""
        payload = {"TransactionStatus": "1", "Amount": "500.00", "Currency": "KES"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_dpo_webhook.side_effect = WebhookProcessingError(
                "Missing transaction reference", error_code="MISSING_REFERENCE"
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post("/webhooks/dpo", json=payload)

            assert response.status_code == 400  # Returns 400 for missing reference
            data = response.json()
            assert data["error_code"] == "BAD_REQUEST"


class TestWebhookLogging:
    """Test webhook logging and monitoring"""

    async def test_webhook_request_logging(self, async_client: AsyncClient):
        """Test that webhook requests are properly logged"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch(
            "app.routers.webhooks.WebhookProcessor"
        ) as mock_processor_class, patch("app.routers.webhooks.logger") as mock_logger:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "event_type": "charge.success",
                "data": {"reference": "test_ref_123"},
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200

            # Verify logging calls
            assert mock_logger.info.called
            log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
            assert any("Received Paystack webhook" in call for call in log_calls)
            assert any("processed successfully" in call for call in log_calls)

    async def test_webhook_error_logging(self, async_client: AsyncClient):
        """Test that webhook errors are properly logged"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "invalid_signature"}

        with patch(
            "app.routers.webhooks.WebhookProcessor"
        ) as mock_processor_class, patch("app.routers.webhooks.logger") as mock_logger:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.side_effect = (
                WebhookProcessingError(
                    "Invalid webhook signature", error_code="INVALID_SIGNATURE"
                )
            )
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 400

            # Verify error logging
            assert mock_logger.warning.called
            warning_calls = [
                call.args[0] for call in mock_logger.warning.call_args_list
            ]
            assert any("Webhook processing error" in call for call in warning_calls)


class TestWebhookPerformance:
    """Test webhook performance and timing"""

    async def test_webhook_processing_time_logging(self, async_client: AsyncClient):
        """Test that webhook processing time is logged"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch(
            "app.routers.webhooks.WebhookProcessor"
        ) as mock_processor_class, patch("app.routers.webhooks.logger") as mock_logger:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "event_type": "charge.success",
                "data": {"reference": "test_ref_123"},
            }
            mock_processor_class.return_value = mock_processor

            response = await async_client.post(
                "/webhooks/paystack", json=payload, headers=headers
            )

            assert response.status_code == 200

            # Verify processing time is logged
            log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
            assert any("Processing time:" in call for call in log_calls)

    async def test_webhook_concurrent_processing(self, async_client: AsyncClient):
        """Test webhook handling under concurrent requests"""
        payload = {"event": "charge.success", "data": {"reference": "test_ref_123"}}
        headers = {"x-paystack-signature": "valid_signature"}

        with patch("app.routers.webhooks.WebhookProcessor") as mock_processor_class:
            mock_processor = AsyncMock()
            mock_processor.process_paystack_webhook.return_value = {
                "status": "success",
                "request_id": "test_id",
                "event_type": "charge.success",
                "data": {"reference": "test_ref_123"},
            }
            mock_processor_class.return_value = mock_processor

            # Send multiple concurrent requests
            import asyncio

            tasks = []
            for i in range(5):
                task = async_client.post(
                    "/webhooks/paystack",
                    json={**payload, "data": {"reference": f"test_ref_{i}"}},
                    headers=headers,
                )
                tasks.append(task)

            responses = await asyncio.gather(*tasks)

            # All requests should succeed
            for response in responses:
                assert response.status_code == 200
                data = response.json()
                assert data["status"] == "success"
