# Production Environment variables for DevHQ Backend
# Copy this file to .env and fill in your production values.
# DO NOT COMMIT THE .env FILE.

# Application Environment
ENVIRONMENT=production

# Debug mode (should be false in production)
DEBUG=false

# Database Configuration
# Use your production database URL
DATABASE_URL=************************************************/your-database-name

# Generate a secure secret key for production: openssl rand -hex 32
SECRET_KEY=your-production-super-secret-key-here

# Redis Configuration
REDIS_URL=redis://your-redis-host:6379/0

# Paystack payment gateway configuration (REQUIRED for production)
PAYSTACK_SECRET_KEY=sk_live_your_production_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_live_your_production_paystack_public_key

# Cloudinary configuration (REQUIRED for production)
CLOUDINARY_CLOUD_NAME=your_production_cloud_name
CLOUDINARY_API_KEY=your_production_api_key
CLOUDINARY_API_SECRET=your_production_api_secret

# Email configuration (REQUIRED for production)
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-production-email-password

# Sentry monitoring (REQUIRED for production)
SENTRY_DSN=https://your-production-sentry-dsn.ingest.sentry.io/your-project-id

# CORS Origins (comma-separated list) - specify your production domains
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Performance and caching settings
REDIS_CACHE_TTL=3600
API_RATE_LIMIT=1000/minute