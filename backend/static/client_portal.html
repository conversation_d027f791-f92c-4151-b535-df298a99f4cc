<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevHQ - Real-time Client Portal</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-connected {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-disconnected {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .card h3 {
            color: #2d3748;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-progress {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .timeline {
            max-height: 400px;
            overflow-y: auto;
        }

        .timeline-item {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            margin-top: 4px;
            flex-shrink: 0;
        }

        .timeline-content h4 {
            color: #2d3748;
            margin-bottom: 5px;
        }

        .timeline-content p {
            color: #718096;
            font-size: 0.9rem;
        }

        .notifications {
            max-height: 300px;
            overflow-y: auto;
        }

        .notification {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 12px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        .notification.info {
            background: rgba(66, 153, 225, 0.1);
            border-color: #4299e1;
            color: #2b6cb0;
        }

        .notification.success {
            background: rgba(72, 187, 120, 0.1);
            border-color: #48bb78;
            color: #2f855a;
        }

        .notification.warning {
            background: rgba(237, 137, 54, 0.1);
            border-color: #ed8936;
            color: #c05621;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .input:focus {
            outline: none;
            border-color: #667eea;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DevHQ Client Portal</h1>
            <p style="color: #718096; margin-bottom: 20px;">Real-time project collaboration with instant updates</p>
            <div id="connectionStatus" class="status-indicator status-disconnected">
                <div class="status-dot"></div>
                <span>Connecting...</span>
            </div>
        </div>

        <div class="dashboard">
            <!-- File Upload Card -->
            <div class="card">
                <h3>
                    <div class="card-icon">📁</div>
                    File Upload
                </h3>
                <div class="upload-area" id="uploadArea">
                    <div style="font-size: 2rem; margin-bottom: 10px;">☁️</div>
                    <p><strong>Drop files here</strong> or click to browse</p>
                    <p style="color: #718096; font-size: 0.9rem; margin-top: 10px;">
                        Supports images, documents, and archives up to 50MB
                    </p>
                    <input type="file" id="fileInput" style="display: none;" multiple>
                </div>
                <div class="upload-progress" id="uploadProgress">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span id="uploadFileName">Uploading...</span>
                        <span id="uploadPercent">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>

            <!-- Project Timeline Card -->
            <div class="card">
                <h3>
                    <div class="card-icon">📅</div>
                    Project Timeline
                </h3>
                <div class="timeline" id="timeline">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h4>Project Started</h4>
                            <p>Your project has been initiated and is ready for development.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Notifications Card -->
            <div class="card">
                <h3>
                    <div class="card-icon">🔔</div>
                    Live Updates
                </h3>
                <div class="notifications" id="notifications">
                    <div class="notification info">
                        <strong>Welcome!</strong> You're now connected to real-time updates.
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card">
                <h3>
                    <div class="card-icon">⚡</div>
                    Quick Actions
                </h3>
                <div style="display: flex; flex-direction: column; gap: 15px;">
                    <button class="btn" onclick="sendTestMessage()">
                        Send Test Message
                    </button>
                    <button class="btn" onclick="requestUpdate()">
                        Request Project Update
                    </button>
                    <input type="text" class="input" id="commentInput" placeholder="Add a comment...">
                    <button class="btn" onclick="addComment()">
                        Add Comment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection
        let socket = null;
        let isConnected = false;

        // Initialize WebSocket connection
        function initializeWebSocket() {
            // In a real implementation, you'd get the auth token from the portal URL
            const authToken = 'demo_token_' + Math.random().toString(36).substr(2, 9);
            
            socket = io('/ws', {
                auth: {
                    token: authToken
                },
                transports: ['websocket', 'polling']
            });

            socket.on('connect', () => {
                isConnected = true;
                updateConnectionStatus(true);
                addNotification('Connected to real-time updates!', 'success');
                
                // Join demo rooms
                socket.emit('join_room', {
                    room_type: 'client',
                    room_id: 'demo_client_123'
                });
            });

            socket.on('disconnect', () => {
                isConnected = false;
                updateConnectionStatus(false);
                addNotification('Disconnected from real-time updates', 'warning');
            });

            socket.on('connection_established', (data) => {
                console.log('Connection established:', data);
            });

            socket.on('room_joined', (data) => {
                addNotification(`Joined ${data.room_type} room: ${data.room_id}`, 'info');
            });

            // Real-time event handlers
            socket.on('file_upload_progress', handleUploadProgress);
            socket.on('project_updated', handleProjectUpdate);
            socket.on('approval_created', handleApprovalCreated);
            socket.on('notification', handleNotification);
            socket.on('client_activity', handleClientActivity);

            socket.on('error', (error) => {
                console.error('WebSocket error:', error);
                addNotification('Connection error occurred', 'warning');
            });
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            const statusText = statusEl.querySelector('span');
            
            if (connected) {
                statusEl.className = 'status-indicator status-connected';
                statusText.textContent = 'Connected';
            } else {
                statusEl.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Disconnected';
            }
        }

        // Add notification to the feed
        function addNotification(message, type = 'info') {
            const notificationsEl = document.getElementById('notifications');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> ${message}`;
            
            notificationsEl.insertBefore(notification, notificationsEl.firstChild);
            
            // Remove old notifications (keep last 10)
            while (notificationsEl.children.length > 10) {
                notificationsEl.removeChild(notificationsEl.lastChild);
            }
        }

        // Handle file upload progress
        function handleUploadProgress(data) {
            const progressEl = document.getElementById('uploadProgress');
            const fileNameEl = document.getElementById('uploadFileName');
            const percentEl = document.getElementById('uploadPercent');
            const progressFill = document.getElementById('progressFill');
            
            if (data.status === 'uploading' || data.status === 'processing') {
                progressEl.style.display = 'block';
                fileNameEl.textContent = data.message || 'Uploading...';
                percentEl.textContent = `${data.progress}%`;
                progressFill.style.width = `${data.progress}%`;
            } else if (data.status === 'completed') {
                percentEl.textContent = '100%';
                progressFill.style.width = '100%';
                addNotification('File uploaded successfully!', 'success');
                setTimeout(() => {
                    progressEl.style.display = 'none';
                }, 2000);
            } else if (data.status === 'error') {
                addNotification(`Upload failed: ${data.message}`, 'warning');
                progressEl.style.display = 'none';
            }
        }

        // Handle project updates
        function handleProjectUpdate(data) {
            addNotification(`Project updated: ${data.changes ? Object.keys(data.changes).join(', ') : 'status changed'}`, 'info');
            
            // Add to timeline
            addTimelineItem('Project Updated', `Status: ${data.status}, Progress: ${data.completion_percentage}%`);
        }

        // Handle approval creation
        function handleApprovalCreated(data) {
            addNotification(`New approval required: ${data.title}`, 'info');
            addTimelineItem('Approval Required', data.description || data.title);
        }

        // Handle general notifications
        function handleNotification(data) {
            addNotification(data.message, data.type || 'info');
        }

        // Handle client activity
        function handleClientActivity(data) {
            console.log('Client activity:', data);
        }

        // Add item to timeline
        function addTimelineItem(title, description) {
            const timelineEl = document.getElementById('timeline');
            const item = document.createElement('div');
            item.className = 'timeline-item';
            item.innerHTML = `
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <h4>${title}</h4>
                    <p>${description}</p>
                    <small style="color: #a0aec0;">${new Date().toLocaleString()}</small>
                </div>
            `;
            
            timelineEl.insertBefore(item, timelineEl.firstChild);
            
            // Keep last 10 items
            while (timelineEl.children.length > 10) {
                timelineEl.removeChild(timelineEl.lastChild);
            }
        }

        // File upload handling
        function setupFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            uploadArea.addEventListener('click', () => fileInput.click());

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });

            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
        }

        // Handle file uploads
        function handleFiles(files) {
            for (let file of files) {
                simulateFileUpload(file);
            }
        }

        // Simulate file upload with progress
        function simulateFileUpload(file) {
            const progressEl = document.getElementById('uploadProgress');
            const fileNameEl = document.getElementById('uploadFileName');
            const percentEl = document.getElementById('uploadPercent');
            const progressFill = document.getElementById('progressFill');
            
            progressEl.style.display = 'block';
            fileNameEl.textContent = file.name;
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    addNotification(`File "${file.name}" uploaded successfully!`, 'success');
                    addTimelineItem('File Uploaded', `Uploaded: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
                    setTimeout(() => {
                        progressEl.style.display = 'none';
                    }, 2000);
                }
                
                percentEl.textContent = `${Math.round(progress)}%`;
                progressFill.style.width = `${progress}%`;
                
                // Emit progress event
                if (socket && isConnected) {
                    socket.emit('file_upload_progress', {
                        filename: file.name,
                        progress: Math.round(progress),
                        status: progress < 100 ? 'uploading' : 'completed'
                    });
                }
            }, 200);
        }

        // Quick action functions
        function sendTestMessage() {
            if (socket && isConnected) {
                socket.emit('test_message', {
                    message: 'Hello from client portal!',
                    timestamp: new Date().toISOString()
                });
                addNotification('Test message sent!', 'success');
            } else {
                addNotification('Not connected to server', 'warning');
            }
        }

        function requestUpdate() {
            addNotification('Project update requested', 'info');
            addTimelineItem('Update Requested', 'Client requested a project status update');
        }

        function addComment() {
            const commentInput = document.getElementById('commentInput');
            const comment = commentInput.value.trim();
            
            if (comment) {
                addNotification(`Comment added: "${comment}"`, 'success');
                addTimelineItem('Comment Added', comment);
                commentInput.value = '';
            }
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setupFileUpload();
            initializeWebSocket();
            
            // Add some demo timeline items
            setTimeout(() => {
                addTimelineItem('Project Milestone', 'Initial design phase completed');
            }, 2000);
            
            setTimeout(() => {
                addTimelineItem('Developer Update', 'Backend API development in progress');
            }, 4000);
        });
    </script>
</body>
</html>