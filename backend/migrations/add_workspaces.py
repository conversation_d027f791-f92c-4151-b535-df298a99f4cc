"""
Database migration to add workspace functionality
Run this script to add workspace tables and update existing tables
"""

import asyncio
import os
from datetime import datetime, timezone
from uuid import uuid4

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Get database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://devhq_user:devhq_password@localhost:5433/devhq")
# Convert to asyncpg format if needed
if DATABASE_URL.startswith("postgresql://"):
    DATABASE_URL = DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://", 1)


async def run_migration():
    """Run the workspace migration"""
    
    # Create async engine
    engine = create_async_engine(
        DATABASE_URL,
        echo=True,
        future=True
    )
    
    # Create async session factory
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with engine.begin() as conn:
        print("Creating workspace tables...")
        
        # Create workspace table
        await conn.execute(text("""
            CREATE TABLE IF NOT EXISTS workspaces (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(200) NOT NULL,
                description TEXT,
                is_active BOOLEAN NOT NULL DEFAULT true,
                is_default BOOLEAN NOT NULL DEFAULT false,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                deleted_at TIMESTAMP WITH TIME ZONE
            );
        """))
        
        # Create indexes for workspace table
        await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_workspaces_user_id ON workspaces(user_id);"))
        await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_workspaces_user_id_is_active ON workspaces(user_id, is_active);"))
        await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_workspaces_user_id_is_default ON workspaces(user_id, is_default);"))
        await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_workspaces_deleted_at ON workspaces(deleted_at);"))
        
        # Add workspace_id to users table (for active workspace)
        await conn.execute(text("""
            ALTER TABLE users 
            ADD COLUMN IF NOT EXISTS active_workspace_id UUID REFERENCES workspaces(id);
        """))
        
        # Add workspace_id to projects table
        await conn.execute(text("""
            ALTER TABLE projects 
            ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id);
        """))
        
        # Add workspace_id to clients table
        await conn.execute(text("""
            ALTER TABLE clients 
            ADD COLUMN IF NOT EXISTS workspace_id UUID REFERENCES workspaces(id);
        """))
        
        # Create indexes for new foreign keys
        await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_users_active_workspace_id ON users(active_workspace_id);"))
        await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_projects_workspace_id ON projects(workspace_id);"))
        await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_clients_workspace_id ON clients(workspace_id);"))
        
        print("Workspace tables created successfully!")
    
    # Create default workspaces for existing users
    async with async_session() as session:
        print("Creating default workspaces for existing users...")
        
        # Get all users without workspaces
        result = await session.execute(text("""
            SELECT id, first_name, last_name 
            FROM users 
            WHERE id NOT IN (SELECT DISTINCT user_id FROM workspaces WHERE deleted_at IS NULL)
        """))
        users_without_workspaces = result.fetchall()
        
        for user_row in users_without_workspaces:
            user_id = user_row[0]
            first_name = user_row[1]
            last_name = user_row[2]
            
            # Create default workspace
            workspace_id = uuid4()
            workspace_name = f"{first_name}'s Workspace"
            
            await session.execute(text("""
                INSERT INTO workspaces (id, user_id, name, description, is_active, is_default, created_at, updated_at)
                VALUES (:id, :user_id, :name, :description, true, true, :created_at, :updated_at)
            """), {
                "id": workspace_id,
                "user_id": user_id,
                "name": workspace_name,
                "description": "Default workspace for organizing your projects",
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            })
            
            # Set as active workspace for user
            await session.execute(text("""
                UPDATE users SET active_workspace_id = :workspace_id WHERE id = :user_id
            """), {
                "workspace_id": workspace_id,
                "user_id": user_id
            })
            
            print(f"Created default workspace for user {first_name} {last_name}")
        
        await session.commit()
    
    # Migrate existing projects and clients to default workspaces
    async with async_session() as session:
        print("Migrating existing projects to workspaces...")
        
        # Update projects to use user's default workspace
        await session.execute(text("""
            UPDATE projects 
            SET workspace_id = (
                SELECT id FROM workspaces 
                WHERE workspaces.user_id = projects.user_id 
                AND workspaces.is_default = true 
                AND workspaces.deleted_at IS NULL
                LIMIT 1
            )
            WHERE workspace_id IS NULL
        """))
        
        print("Migrating existing clients to workspaces...")
        
        # Update clients to use user's default workspace
        await session.execute(text("""
            UPDATE clients 
            SET workspace_id = (
                SELECT id FROM workspaces 
                WHERE workspaces.user_id = clients.user_id 
                AND workspaces.is_default = true 
                AND workspaces.deleted_at IS NULL
                LIMIT 1
            )
            WHERE workspace_id IS NULL
        """))
        
        await session.commit()
    
    # Make workspace_id NOT NULL after migration
    async with engine.begin() as conn:
        print("Making workspace_id columns NOT NULL...")
        
        await conn.execute(text("""
            ALTER TABLE projects 
            ALTER COLUMN workspace_id SET NOT NULL;
        """))
        
        await conn.execute(text("""
            ALTER TABLE clients 
            ALTER COLUMN workspace_id SET NOT NULL;
        """))
    
    await engine.dispose()
    print("Migration completed successfully!")


if __name__ == "__main__":
    asyncio.run(run_migration())
