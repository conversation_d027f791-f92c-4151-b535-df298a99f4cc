#!/usr/bin/env python3
import asyncio
import sys
import os
from datetime import datetime, timezone

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from sqlalchemy import select
from app.models.user import User, UserSession, UserSettings
from app.database import get_async_db

async def check_sessions():
    async for db in get_async_db():
        # Get all users
        result = await db.execute(select(User))
        users = result.scalars().all()

        print(f"Found {len(users)} users:")
        for user in users:
            print(f"  - {user.email} (ID: {user.id})")
            print(f"    Company: {user.company_name}")
            print(f"    First Name: {user.first_name}")
            print(f"    Last Name: {user.last_name}")
            print(f"    Phone: {user.phone}")
            print(f"    Bio: {user.bio}")
            print()

        # Get all user settings
        result = await db.execute(select(UserSettings))
        settings = result.scalars().all()

        print(f"Found {len(settings)} user settings:")
        for setting in settings:
            print(f"  - User ID: {setting.user_id}")
            print(f"    Currency: {setting.default_currency}")
            print(f"    Hourly Rate: {setting.default_hourly_rate}")
            print(f"    Theme: {setting.theme}")
            print(f"    Language: {setting.language}")
            print()

        # Get all sessions
        result = await db.execute(select(UserSession))
        sessions = result.scalars().all()

        print(f"Found {len(sessions)} sessions:")
        for session in sessions:
            is_expired = session.expires_at < datetime.now(timezone.utc)
            print(f"  - Token: {session.refresh_token[:20]}...")
            print(f"    User ID: {session.user_id}")
            print(f"    Active: {session.is_active}")
            print(f"    Expires: {session.expires_at}")
            print(f"    Expired: {is_expired}")
            print(f"    Device: {session.device_info}")
            print()

        break

if __name__ == "__main__":
    asyncio.run(check_sessions())
