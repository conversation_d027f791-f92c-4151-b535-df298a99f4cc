# DevHQ Backend - Production Fly.io Configuration

app = "devhq-backend"
primary_region = "jnb"  # Johannesburg for African users

[build]
  dockerfile = "Dockerfile"
  target = "production"

[env]
  ENVIRONMENT = "production"
  DEBUG = "false"
  PORT = "8000"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/health"

[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512

[[vm.http_service.concurrency]]
  type = "connections"
  hard_limit = 1000
  soft_limit = 800

[metrics]
  port = 9091
  path = "/metrics"

[deploy]
  release_command = "python -m alembic upgrade head"
  strategy = "rolling"

[[services]]
  protocol = "tcp"
  internal_port = 8000
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [services.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 800

  [[services.tcp_checks]]
    grace_period = "10s"
    interval = "15s"
    restart_limit = 0
    timeout = "2s"

  [[services.http_checks]]
    interval = "30s"
    grace_period = "10s"
    method = "GET"
    path = "/health"
    protocol = "http"
    restart_limit = 0
    timeout = "5s"
    tls_skip_verify = false

    [services.http_checks.headers]
      User-Agent = "Fly-Health-Check"