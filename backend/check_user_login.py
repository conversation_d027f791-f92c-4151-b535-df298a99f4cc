import asyncio
from app.database import get_async_db
from app.models.user import User
from sqlalchemy import select
from app.core.auth import verify_password

async def check_user_login():
    async for db in get_async_db():
        # Get user
        result = await db.execute(select(User).where(User.email == '<EMAIL>'))
        user = result.scalar_one_or_none()
        
        if user:
            print(f"User found: {user.email}")
            print(f"User active: {user.is_active}")
            print(f"User verified: {user.is_verified}")
            print(f"Password hash exists: {bool(user.password_hash)}")
            
            # Test different passwords
            test_passwords = ['password123', 'Password123!', 'newpassword123', 'StrongPassword123!']
            for pwd in test_passwords:
                is_valid = user.verify_password(pwd)
                print(f"Password '{pwd}': {is_valid}")
        else:
            print("User not found")
        break

if __name__ == "__main__":
    asyncio.run(check_user_login())