# Test environment configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/devhq_test
REDIS_URL=redis://localhost:6379/1
SECRET_KEY=test-secret-key-for-testing-only
ENVIRONMENT=testing
DEBUG=true

# Payment gateway test settings
PAYSTACK_SECRET_KEY=sk_test_dummy_key
PAYSTACK_PUBLIC_KEY=pk_test_dummy_key
DPO_COMPANY_TOKEN=test_company_token
DPO_TEST_MODE=true

# Email settings (disabled for tests)
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=

# Monitoring (disabled for tests)
SENTRY_DSN=