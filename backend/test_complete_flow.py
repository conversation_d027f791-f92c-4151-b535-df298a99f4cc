#!/usr/bin/env python3
"""
Comprehensive test of the verification process with a real user
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment
os.environ.setdefault('ENVIRONMENT', 'development')

import asyncio
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from app.config import settings
from app.models.user import User
from app.core.auth import create_verification_token

def test_complete_verification_flow():
    """Test the complete verification flow with a real user"""
    print("Testing complete verification flow...")
    
    # Create database connection
    engine = create_engine(settings.database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Create a test user
        print("Creating test user...")
        user = User(
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            is_active=True,
            is_verified=False
        )
        user.set_password("TestPassword123!")
        
        db.add(user)
        db.flush()  # Get the user ID without committing
        
        user_id = str(user.id)
        print(f"Created user with ID: {user_id}")
        
        # Create verification token for this user
        token = create_verification_token(user_id)
        print(f"Created verification token: {token}")
        print(f"Token length: {len(token)}")
        
        # Test token verification
        from app.core.auth import verify_token
        verified_user_id = verify_token(token, "email_verification")
        print(f"Token verification result: {verified_user_id}")
        
        if verified_user_id != user_id:
            print("❌ Token verification failed!")
            return
            
        print("✅ Token verification successful!")
        
        # Test finding user in database
        found_user = db.query(User).filter(User.id == user_id).first()
        print(f"Found user in database: {found_user}")
        
        if not found_user:
            print("❌ Could not find user in database!")
            return
            
        print("✅ User found in database!")
        
        # Test updating user verification status
        found_user.is_verified = True
        found_user.email_verified_at = "2025-01-01T00:00:00Z"
        db.commit()
        
        print("✅ User verification status updated successfully!")
        
        print("\n🎉 Complete verification flow test successful!")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        # Clean up - delete test user
        try:
            if 'user_id' in locals():
                db.query(User).filter(User.id == user_id).delete()
                db.commit()
                print("🧹 Cleaned up test user")
        except:
            pass
        db.close()

if __name__ == "__main__":
    test_complete_verification_flow()