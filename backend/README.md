# DevHQ Backend

Enterprise-ready backend API for developer business management platform.

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL 15+
- Docker (for database)

### Development Setup

**Option 1: Automated Setup (Recommended)**
```bash
cd backend
./start_dev_server.sh  # Does everything automatically!
```

**Option 2: Manual Setup**
```bash
cd backend
./setup_dev.sh         # First time setup
./start_dev_server.sh   # Start development server
```

### Access Points
- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc  
- **Health Check**: http://localhost:8000/health

## 📁 Project Structure

```
backend/
├── app/                    # Main application package
│   ├── core/              # Core utilities and services
│   ├── models/            # SQLAlchemy database models
│   ├── schemas/           # Pydantic schemas
│   ├── routers/           # FastAPI route handlers
│   ├── config.py          # Configuration settings
│   ├── database.py        # Database connection setup
│   ├── dependencies.py    # FastAPI dependencies
│   └── main.py            # FastAPI application entry point
├── alembic/               # Database migrations
├── docs/                  # Documentation
├── scripts/               # Utility scripts
├── tests/                 # Comprehensive test suite
├── *.sh                   # Development scripts
├── requirements.txt       # Production dependencies
├── .env                   # Environment configuration
└── Dockerfile            # Container configuration
```

## 🧪 Testing

### Test Database Options

The test suite supports multiple database backends:

- **SQLite** (in-memory or file-based) - Fast and simple
- **PostgreSQL** (local or Docker) - Matches production environment

### Running Tests with Docker PostgreSQL

```bash
# Setup the Docker test database
./scripts/setup_test_db.bat  # Windows
python scripts/setup_test_db.py  # Linux/macOS

# Run tests with Docker PostgreSQL
set USE_POSTGRES_TESTS=true  # Windows
set USE_DOCKER_DB=true       # Windows
pytest

# Or on Linux/macOS
USE_POSTGRES_TESTS=true USE_DOCKER_DB=true pytest
```

For more details, see [Docker Test Setup](docs/docker_test_setup.md).

### Running All Tests

```bash
# Run all tests (recommended)
./run_tests.sh

# Or manually with virtual environment
source venv/bin/activate
python -m pytest tests/ -v
```

## 🗄️ Database

### Migrations

```bash
# Apply migrations (recommended)
./run_alembic.sh upgrade head

# Create new migration
./run_alembic.sh revision --autogenerate -m "Description of changes"

# Check current migration status
./run_alembic.sh current

# Rollback migration
./run_alembic.sh downgrade -1
```

### Models

Current models implemented:
- **User**: User authentication and profile
- **UserSettings**: User preferences and configuration  
- **UserSession**: JWT refresh token management
- **Client**: Client/customer management
- **Project**: Project management with milestones
- **TimeEntry**: Time tracking and billing
- **Invoice**: Professional invoicing system
- **ClientApproval**: Client approval workflows
- **Activity**: Audit trail logging

## 🔧 Development Tools

### Code Quality

```bash
# Format code
black .

# Sort imports
isort .

# Type checking
mypy .

# Security linting
bandit -r app/

# Run all quality checks
pre-commit run --all-files
```

### Environment Variables

The `.env` file is automatically created by setup scripts. Current configuration:

```env
# Database (PostgreSQL on Docker)
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/devhq_dev

# Security
SECRET_KEY=dev-secret-key-change-in-production

# Services
REDIS_URL=redis://localhost:6379/0
ENVIRONMENT=development
DEBUG=true
```

## 🚀 Deployment

### Docker

```bash
# Build image
docker build -t devhq-backend .

# Run container
docker run -p 8000:8000 devhq-backend
```

### Production

The application is configured for deployment on Fly.io with:
- Multi-stage Docker builds
- Health checks
- Security headers
- Structured logging
- Error monitoring

## 📚 API Features

### API Usage Guide (Users & Clients)

Base URL: /api/v1

Authentication: Bearer token in Authorization header.

Users
- GET /users/me -> returns current user profile
- PUT /users/me (body: partial UserProfileUpdate) -> updates profile
- PUT /users/me/avatar (multipart file) -> uploads avatar
- GET /users/me/settings -> returns or creates default settings
- PUT /users/me/settings (body: partial UserSettingsUpdate) -> updates settings
- DELETE /users/me -> soft-delete account

Clients
- POST /clients/ (body: ClientCreate) -> create a client
- GET /clients/ -> list clients with filters and sorting
  Query params:
  - q: search term applied to name, email, company
  - is_active: true|false
  - company: substring match (case-insensitive)
  - email: substring match (case-insensitive)
  - sort_by: one of name|email|company|created_at|updated_at (default created_at)
  - sort_order: asc|desc (default desc)
  - page: 1..n (default 1)
  - per_page: 1..100 (default 20)
- GET /clients/{id} -> get a client
- PUT /clients/{id} (body: ClientUpdate) -> update a client
- DELETE /clients/{id} -> soft-delete a client

Activity Logs 
- GET /activity/ -> list activity logs for current user with filtering and pagination
  Query params:
  - entity_type: filter by entity type (client, user)
  - action: filter by action (create, update, delete)
  - q: search in details field
  - sort_order: asc|desc (default desc)
  - page: 1..n (default 1)
  - per_page: 1..100 (default 25)
- Automatically created for: profile updates, account delete, client create/update/delete


### ✅ Implemented Features
- **Core System**: Health checks, configuration, error handling
- **Authentication**: JWT-based auth with refresh tokens
- **User Management**: Profiles, settings, account management
- **Client Management**: Full CRUD with advanced filtering
- **Project Management**: Projects with milestone tracking
- **Time Tracking**: Advanced timer system with analytics
- **Invoicing**: Professional invoice generation with payment integration
- **Client Approvals**: Workflow system for deliverable approval
- **Activity Logging**: Comprehensive audit trails
- **Analytics**: Productivity insights and reporting

### 🚀 Advanced Features
- **Smart Timer**: Real-time tracking with conflict resolution
- **Client Portal**: Token-based access without accounts
- **Payment Integration**: Paystack payment processing
- **Bulk Operations**: Mass time entry management
- **AI Analytics**: Productivity suggestions and insights

## 🤝 Contributing

1. Follow the existing code style
2. Write tests for new features
3. Update documentation
4. Run quality checks before committing

## 📄 License

This project is licensed under the MIT License.