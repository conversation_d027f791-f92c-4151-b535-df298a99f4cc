#!/usr/bin/env python3
"""
Test script to debug email verification issues
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment
os.environ.setdefault('ENVIRONMENT', 'development')

from app.core.auth import create_verification_token, verify_token

def test_token_verification():
    """Test token creation and verification"""
    print("Testing token creation and verification...")
    
    # Create a test token
    user_id = "test-user-123"
    token = create_verification_token(user_id)
    print(f"Created token for user {user_id}")
    print(f"Token: {token}")
    print(f"Token length: {len(token)}")
    
    # Try to verify the token
    print("\nVerifying token...")
    verified_user_id = verify_token(token, "email_verification")
    
    if verified_user_id:
        print(f"✅ Token verified successfully! User ID: {verified_user_id}")
    else:
        print("❌ Token verification failed!")
        
    return token, verified_user_id

def decode_token_manually(token):
    """Manually decode token to see its contents"""
    try:
        from jose import jwt
        from app.config import settings
        
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        print(f"Token payload: {payload}")
        return payload
    except Exception as e:
        print(f"Error decoding token: {e}")
        return None

if __name__ == "__main__":
    token, user_id = test_token_verification()
    
    print("\nToken details:")
    decode_token_manually(token)