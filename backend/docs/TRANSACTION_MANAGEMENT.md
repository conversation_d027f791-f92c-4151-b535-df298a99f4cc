# Transaction Management System

This document describes the comprehensive transaction management system implemented in the Dev HQ backend application. The system provides robust data consistency, foreign key constraint management, and advanced transaction patterns.

## Overview

The transaction management system consists of several key components:

1. **TransactionManager** - Core transaction handling with savepoints
2. **Database Utilities** - Foreign key management and data validation
3. **Decorators and Context Managers** - Convenient transaction patterns
4. **Integration Examples** - Real-world usage in services

## Core Components

### TransactionManager

The `TransactionManager` class provides low-level transaction control with support for nested transactions using savepoints.

```python
from app.core.transaction_manager import TransactionManager

tx_manager = TransactionManager(db_session)

# Basic transaction
with tx_manager.transaction() as tx_db:
    # Database operations
    user = User(email="<EMAIL>")
    tx_db.add(user)
    # Auto-commits on successful exit

# Nested transaction with savepoint
with tx_manager.transaction() as tx_db:
    user = User(email="<EMAIL>")
    tx_db.add(user)
    
    with tx_manager.savepoint() as sp:
        try:
            # Risky operation
            risky_operation(tx_db)
        except Exception:
            sp.rollback()  # Only rollback savepoint
    
    # Main transaction continues
```

### Context Manager: db_transaction

A convenient context manager for simple transaction handling:

```python
from app.core.transaction_manager import db_transaction

with db_transaction(db_session) as tx_db:
    client = Client(name="New Client")
    tx_db.add(client)
    # Auto-commits on success, auto-rollbacks on exception
```

### Decorators

#### @transactional

Decorator for automatic transaction management:

```python
from app.core.transaction_manager import transactional

@transactional(rollback_on_exception=True)
def create_user_with_settings(db: Session, user_data: dict) -> User:
    user = User(**user_data)
    db.add(user)
    db.flush()
    
    settings = UserSettings(user_id=user.id)
    db.add(settings)
    
    return user
```

#### @atomic

Shorthand decorator for atomic operations:

```python
from app.core.transaction_manager import atomic

@atomic
def update_project_status(db: Session, project_id: uuid.UUID, status: str) -> Project:
    project = db.query(Project).get(project_id)
    project.status = status
    return project
```

## Database Utilities

### ForeignKeyManager

Manages foreign key relationships and constraints:

```python
from app.core.database_utils import ForeignKeyManager

fk_manager = ForeignKeyManager()

# Validate foreign key exists
if fk_manager.validate_foreign_key_exists(db, User, user_id):
    # Proceed with operation
    pass

# Find records referencing a foreign key
referencing_records = fk_manager.find_referencing_records(db, Client, client_id)

# Safe deletion with reference checking
fk_manager.safe_delete(db, client, check_references=True)
```

### DataIntegrityValidator

Validates data integrity and constraints:

```python
from app.core.database_utils import DataIntegrityValidator

validator = DataIntegrityValidator()

# Validate foreign key constraints
validator.validate_foreign_key_constraints(db, project)

# Check data consistency
consistency_report = validator.check_data_consistency(db)
```

### BulkOperationManager

Handles bulk operations with validation:

```python
from app.core.database_utils import BulkOperationManager

bulk_manager = BulkOperationManager()

# Bulk create with validation
time_entries = [TimeEntry(...) for _ in range(100)]
created_entries = bulk_manager.bulk_create(db, time_entries)

# Bulk update with validation
updates = [{'id': entry.id, 'status': 'completed'} for entry in entries]
bulk_manager.bulk_update(db, TimeEntry, updates)
```

## Service Integration Examples

### Project Service

The `ProjectService` demonstrates comprehensive transaction management:

```python
class ProjectService:
    def __init__(self, db: Session):
        self.db = db
        self.fk_manager = ForeignKeyManager()
        self.validator = DataIntegrityValidator()

    @transactional(rollback_on_exception=True)
    def create_project(self, user_id: uuid.UUID, project_data: ProjectCreate) -> Project:
        # Validate foreign key references
        if not self.fk_manager.validate_foreign_key_exists(self.db, Client, project_data.client_id):
            raise NotFoundError("Client not found")
        
        # Create project
        project = Project(**project_data.model_dump(), user_id=user_id)
        self.db.add(project)
        self.db.flush()
        
        # Validate data integrity
        self.validator.validate_foreign_key_constraints(self.db, project)
        
        return project

    def update_project(self, user_id: uuid.UUID, project_id: uuid.UUID, project_data: ProjectUpdate) -> Project:
        with db_transaction(self.db) as tx_db:
            project = tx_db.query(Project).filter(...).first()
            if not project:
                raise NotFoundError("Project not found")
            
            # Update fields
            for field, value in project_data.model_dump(exclude_unset=True).items():
                setattr(project, field, value)
            
            # Validate if foreign keys were updated
            if any(field.endswith('_id') for field in project_data.model_dump(exclude_unset=True).keys()):
                self.validator.validate_foreign_key_constraints(tx_db, project)
            
            return project
```

### Invoice Service

The `InvoiceService` demonstrates advanced transaction patterns:

```python
class InvoiceService:
    @atomic
    def create_invoice_from_time_entries(
        self,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        time_entry_ids: List[uuid.UUID],
        invoice_data: InvoiceCreate
    ) -> Invoice:
        # Validate prerequisites
        self._validate_invoice_prerequisites(user_id, project_id, time_entry_ids)
        
        # Complex operation with nested savepoint
        with self.tx_manager.savepoint() as sp:
            try:
                # Create invoice
                invoice = self._create_invoice_record(user_id, project, invoice_data)
                
                # Create invoice items
                invoice_items = self._create_invoice_items_from_time_entries(
                    invoice.id, time_entries
                )
                
                # Update time entries
                self._mark_time_entries_as_invoiced(time_entries, invoice.id)
                
                # Calculate totals
                self._calculate_invoice_totals(invoice, invoice_items)
                
                # Final validation
                self._validate_invoice_integrity(invoice)
                
                return invoice
                
            except Exception as e:
                sp.rollback()
                raise BusinessLogicError(f"Invoice creation failed: {str(e)}")
```

## Authentication Integration

The authentication system has been updated to use transaction management:

```python
# User registration with transaction management
@router.post("/register")
def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    with db_transaction(db) as tx_db:
        # Check if user exists
        existing_user = tx_db.query(User).filter(User.email == user_data.email).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="Email already registered")
        
        # Create user
        user = User(**user_data.model_dump())
        tx_db.add(user)
        tx_db.flush()
        
        # Create default settings
        user_settings = UserSettings(user_id=user.id)
        tx_db.add(user_settings)
        
        # Generate tokens and send verification email
        # ...
        
        return {"message": "User registered successfully"}

# Refresh token management
def create_refresh_token(db: Session, user_id: uuid.UUID) -> str:
    with db_transaction(db) as tx_db:
        # Create session record
        session = UserSession(
            user_id=user_id,
            refresh_token=token,
            expires_at=expires_at
        )
        tx_db.add(session)
        
        return token
```

## Error Handling

The system provides comprehensive error handling:

```python
try:
    with db_transaction(db) as tx_db:
        # Database operations
        pass
except IntegrityError as e:
    # Handle constraint violations
    logger.error(f"Database integrity error: {e}")
    raise ValidationError("Data integrity constraint violated")
except Exception as e:
    # Handle other errors
    logger.error(f"Transaction failed: {e}")
    raise BusinessLogicError(f"Operation failed: {str(e)}")
```

## Event Listeners

The system includes SQLAlchemy event listeners for transaction logging:

```python
@event.listens_for(Session, "after_transaction_end")
def log_transaction_end(session, transaction):
    if hasattr(transaction, '_trae_tx_id'):
        logger.info(
            f"Transaction {transaction._trae_tx_id} ended",
            extra={
                "transaction_id": transaction._trae_tx_id,
                "status": "committed" if transaction._parent is None else "rolled_back"
            }
        )
```

## Testing

Comprehensive tests are provided in `tests/test_transaction_management.py`:

```python
def test_transaction_rollback_on_exception(db_session, sample_user):
    """Test transaction rollback on exception"""
    with pytest.raises(ValueError):
        with db_transaction(db_session) as tx_db:
            client = Client(user_id=sample_user.id, name="Test Client")
            tx_db.add(client)
            raise ValueError("Simulated error")
    
    # Verify rollback occurred
    saved_client = db_session.query(Client).filter_by(name="Test Client").first()
    assert saved_client is None
```

## Best Practices

### 1. Use Appropriate Transaction Patterns

- **Simple operations**: Use `@atomic` decorator
- **Complex operations**: Use `@transactional` decorator
- **Manual control**: Use `db_transaction` context manager
- **Nested operations**: Use `TransactionManager` with savepoints

### 2. Validate Foreign Keys

Always validate foreign key references before creating relationships:

```python
if not fk_manager.validate_foreign_key_exists(db, User, user_id):
    raise NotFoundError("User not found")
```

### 3. Handle Exceptions Properly

Use specific exception types and provide meaningful error messages:

```python
try:
    with db_transaction(db) as tx_db:
        # Operations
        pass
except IntegrityError:
    raise ValidationError("Data integrity constraint violated")
except Exception as e:
    raise BusinessLogicError(f"Operation failed: {str(e)}")
```

### 4. Use Bulk Operations for Performance

For large datasets, use bulk operations:

```python
bulk_manager.bulk_create(db, large_list_of_records)
```

### 5. Validate Data Integrity

Always validate data integrity after complex operations:

```python
validator.validate_foreign_key_constraints(db, record)
```

## Performance Considerations

1. **Transaction Scope**: Keep transactions as short as possible
2. **Bulk Operations**: Use bulk operations for large datasets
3. **Read-Only Transactions**: Use read-only transactions for analytics
4. **Connection Pooling**: Monitor connection pool usage
5. **Query Optimization**: Use appropriate indexes and query patterns

## Monitoring and Logging

The system provides comprehensive logging:

- Transaction start/end events
- Foreign key validation results
- Data integrity check results
- Performance metrics
- Error tracking

## Migration Guide

To migrate existing code to use the transaction management system:

1. **Replace manual commits**:
   ```python
   # Before
   db.add(record)
   db.commit()
   
   # After
   with db_transaction(db) as tx_db:
       tx_db.add(record)
   ```

2. **Add foreign key validation**:
   ```python
   # Before
   project = Project(client_id=client_id)
   
   # After
   if not fk_manager.validate_foreign_key_exists(db, Client, client_id):
       raise NotFoundError("Client not found")
   project = Project(client_id=client_id)
   ```

3. **Use decorators for service methods**:
   ```python
   # Before
   def create_project(self, data):
       project = Project(**data)
       self.db.add(project)
       self.db.commit()
       return project
   
   # After
   @transactional(rollback_on_exception=True)
   def create_project(self, data):
       project = Project(**data)
       self.db.add(project)
       return project
   ```

This transaction management system provides a robust foundation for data consistency and integrity in the Dev HQ application.