# Performance Testing Guide

This document provides a comprehensive guide to the performance testing system implemented for the DevHQ backend application.

## Overview

The performance testing system includes:

- **Benchmark Tests**: Measure baseline performance of individual operations
- **Load Tests**: Test system behavior under expected load
- **Stress Tests**: Test system limits and breaking points
- **Endurance Tests**: Test system stability over extended periods
- **Memory Tests**: Monitor memory usage and detect leaks
- **Database Performance Tests**: Measure query performance and optimization

## Test Structure

### Test Files

```
backend/tests/
├── test_performance.py          # Core performance tests
├── test_benchmarks.py           # Detailed benchmark tests
├── test_load_testing.py         # Locust-based load tests
├── performance_config.py        # Configuration and thresholds
├── performance_monitor.py       # Monitoring utilities
└── run_performance_tests.py     # Test runner script
```

### Performance Levels

The system supports five performance test levels:

1. **SMOKE** - Quick validation tests (30 seconds)
2. **LOAD** - Normal expected load (5 minutes)
3. **STRESS** - High load testing (10 minutes)
4. **SPIKE** - Sudden load increases (2 minutes)
5. **ENDURANCE** - Long-running tests (1 hour)

## Configuration

### Performance Thresholds

Thresholds are defined in `performance_config.py`:

```python
from tests.performance_config import PerformanceLevel, PerformanceConfig

# Get thresholds for a specific level
threshold = PerformanceConfig.get_threshold(PerformanceLevel.LOAD)
print(f"Max response time: {threshold.max_response_time_ms}ms")
```

### Environment Configuration

Supported environments:
- **development**: Local testing with minimal load
- **staging**: Pre-production testing with moderate load
- **production**: Production-like testing with full load

## Running Performance Tests

### Quick Start

```bash
# Install dependencies
pip install -r requirements-test.txt

# Run smoke tests (quick validation)
python tests/run_performance_tests.py --level smoke

# Run load tests
python tests/run_performance_tests.py --level load --category api

# Run all tests
python tests/run_performance_tests.py --level load
```

### Command Line Options

```bash
python tests/run_performance_tests.py [OPTIONS]

Options:
  --environment {development,staging,production}  Test environment
  --level {smoke,load,stress,spike,endurance}     Performance test level
  --category {all,database,api,load,memory}       Test category to run
  --output-dir DIR                                Output directory for results
  --report-only                                   Generate report from existing results
```

### Examples

```bash
# Run database performance tests only
python tests/run_performance_tests.py --category database --level load

# Run stress tests for staging environment
python tests/run_performance_tests.py --environment staging --level stress

# Run endurance tests and save to custom directory
python tests/run_performance_tests.py --level endurance --output-dir ./endurance_results

# Generate report from existing results
python tests/run_performance_tests.py --report-only --output-dir ./performance_results
```

## Test Categories

### 1. Database Performance Tests

Tests database operations including:

- **Simple Queries**: Basic SELECT operations
- **Complex Queries**: JOINs and aggregations
- **Bulk Operations**: Batch inserts and updates
- **Transaction Performance**: Commit/rollback operations

```python
# Example: Run database benchmarks
pytest tests/test_benchmarks.py::TestDatabaseBenchmarks -v
```

### 2. API Performance Tests

Tests REST API endpoints:

- **Authentication**: Login and token validation
- **CRUD Operations**: Create, read, update, delete
- **Pagination**: Large dataset handling
- **Concurrent Requests**: Multiple simultaneous users

```python
# Example: Run API performance tests
pytest tests/test_performance.py::TestPerformance -v
```

### 3. Load Testing

Uses Locust for realistic load simulation:

- **User Simulation**: Different user behavior patterns
- **Ramp-up Testing**: Gradual load increase
- **Sustained Load**: Constant load over time
- **Mixed Workloads**: Different user types

```python
# Example: Run Locust load tests
locust -f tests/test_load_testing.py --headless -u 50 -r 10 -t 300s
```

### 4. Memory Testing

Monitors memory usage:

- **Memory Leaks**: Long-running operation monitoring
- **Peak Usage**: Maximum memory consumption
- **Garbage Collection**: Memory cleanup efficiency

```python
# Example: Run memory tests
pytest tests/test_benchmarks.py::TestMemoryBenchmarks -v
```

## Monitoring and Metrics

### System Monitoring

The `SystemMonitor` class tracks:

- CPU usage percentage
- Memory consumption (RSS/VMS)
- Disk I/O (read/write bytes)
- Network I/O (sent/received bytes)

### Database Monitoring

The `DatabaseMonitor` class tracks:

- Query execution time
- Number of queries
- Slow query detection
- Transaction performance

### Request Tracking

The `RequestTracker` class monitors:

- Response times (mean, median, percentiles)
- Request/response sizes
- Error rates
- Requests per second

## Performance Thresholds

### Response Time Thresholds

| Level | Max Response Time | P95 | P99 |
|-------|------------------|-----|-----|
| Smoke | 100ms | 150ms | 200ms |
| Load | 200ms | 300ms | 500ms |
| Stress | 500ms | 1000ms | 2000ms |
| Spike | 1000ms | 2000ms | 5000ms |
| Endurance | 300ms | 500ms | 1000ms |

### Database Query Thresholds

| Operation | Smoke | Load | Stress | Endurance |
|-----------|-------|------|--------|----------|
| Simple Query | 10ms | 20ms | 50ms | 30ms |
| Complex Query | 50ms | 100ms | 250ms | 150ms |
| Bulk Operation | 100ms | 200ms | 500ms | 300ms |
| Transaction | 20ms | 40ms | 100ms | 60ms |

### Memory Thresholds

| Level | Max Total Memory | Max Increase |
|-------|-----------------|-------------|
| Smoke | 512MB | 128MB |
| Load | 1024MB | 256MB |
| Stress | 2048MB | 512MB |
| Endurance | 1536MB | 384MB |

## Test Data Management

### Automatic Test Data Creation

The system automatically creates test data based on performance level:

```python
from tests.performance_monitor import create_performance_test_data

# Create test data for load testing
test_data = create_performance_test_data(db_session, PerformanceLevel.LOAD)
# Creates: 100 users, 5 projects per user, 50 time entries per project
```

### Test Data Cleanup

Automatic cleanup ensures no test data pollution:

```python
from tests.performance_monitor import cleanup_performance_test_data

# Clean up test data
cleanup_performance_test_data(db_session, test_data)
```

## Results and Reporting

### Result Files

Test results are saved in multiple formats:

```
performance_results/
├── performance_results_20240115_143022.json  # Detailed JSON results
├── performance_report_20240115_143022.txt    # Human-readable report
└── performance_results_stats.csv             # Locust CSV results
```

### JSON Result Structure

```json
{
  "environment": "development",
  "level": "load",
  "timestamp": "2024-01-15T14:30:22",
  "results": {
    "database": {
      "simple_queries": {
        "duration": 10.5,
        "system": {
          "cpu_usage_percent": 25.3,
          "memory_usage_mb": 256.7
        },
        "database": {
          "total_queries": 1000,
          "avg_time": 0.015
        }
      }
    }
  }
}
```

### Report Generation

```bash
# Generate report from latest results
python tests/run_performance_tests.py --report-only

# Generate report from specific directory
python tests/run_performance_tests.py --report-only --output-dir ./custom_results
```

## Integration with CI/CD

### GitHub Actions Example

```yaml
name: Performance Tests

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  workflow_dispatch:

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-test.txt
      
      - name: Run performance tests
        run: |
          cd backend
          python tests/run_performance_tests.py --level smoke --category api
      
      - name: Upload results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: backend/performance_results/
```

### Jenkins Pipeline Example

```groovy
pipeline {
    agent any
    
    triggers {
        cron('H 2 * * *')  // Daily at 2 AM
    }
    
    stages {
        stage('Performance Tests') {
            steps {
                sh '''
                    cd backend
                    python -m venv venv
                    source venv/bin/activate
                    pip install -r requirements.txt
                    pip install -r requirements-test.txt
                    python tests/run_performance_tests.py --level load
                '''
            }
        }
        
        stage('Archive Results') {
            steps {
                archiveArtifacts artifacts: 'backend/performance_results/**/*'
                
                // Fail build if performance degrades
                script {
                    def report = readFile('backend/performance_results/performance_report_*.txt')
                    if (report.contains('FAILED')) {
                        error('Performance tests failed')
                    }
                }
            }
        }
    }
}
```

## Best Practices

### 1. Test Environment Setup

- Use dedicated test databases
- Ensure consistent hardware resources
- Isolate performance tests from other processes
- Use realistic data volumes

### 2. Test Design

- Start with smoke tests for quick feedback
- Gradually increase load levels
- Test realistic user scenarios
- Include error conditions

### 3. Monitoring

- Monitor system resources during tests
- Track database performance metrics
- Log slow operations for analysis
- Set up alerts for threshold violations

### 4. Result Analysis

- Compare results over time
- Identify performance regressions
- Analyze bottlenecks and optimization opportunities
- Document performance improvements

## Troubleshooting

### Common Issues

#### 1. High Memory Usage

```python
# Check for memory leaks
pytest tests/test_benchmarks.py::TestMemoryBenchmarks::test_memory_usage_benchmark -v

# Monitor memory during specific operations
from tests.performance_monitor import SystemMonitor

monitor = SystemMonitor()
monitor.start_monitoring()
# ... run operations ...
metrics = monitor.stop_monitoring()
print(f"Peak memory: {metrics.memory_peak_mb}MB")
```

#### 2. Slow Database Queries

```python
# Enable query monitoring
from tests.performance_monitor import DatabaseMonitor

db_monitor = DatabaseMonitor(engine)
db_monitor.start_monitoring()
# ... run database operations ...
metrics = db_monitor.stop_monitoring()

# Check slow queries
for query in metrics['slow_queries']:
    print(f"Slow query: {query['statement']} ({query['duration']*1000:.2f}ms)")
```

#### 3. High Error Rates

```python
# Check request tracking for errors
from tests.performance_monitor import RequestTracker

tracker = RequestTracker()
# ... make requests ...
stats = tracker.get_statistics()
print(f"Error rate: {stats['error_rate']:.2f}%")
```

### Performance Optimization Tips

1. **Database Optimization**
   - Add appropriate indexes
   - Optimize query patterns
   - Use connection pooling
   - Implement query caching

2. **API Optimization**
   - Implement response caching
   - Use pagination for large datasets
   - Optimize serialization
   - Add request rate limiting

3. **System Optimization**
   - Monitor and tune garbage collection
   - Optimize memory allocation
   - Use async operations where appropriate
   - Implement proper error handling

## Advanced Usage

### Custom Performance Tests

```python
from tests.performance_monitor import PerformanceTestRunner
from tests.performance_config import PerformanceLevel

# Create custom performance test
runner = PerformanceTestRunner(db_engine)

with runner.monitor_performance("custom_test") as monitor:
    # Your custom operations here
    for i in range(1000):
        # Simulate work
        time.sleep(0.001)

# Validate results
validation = runner.validate_performance(
    "custom_test", 
    PerformanceLevel.LOAD, 
    "api"
)

# Generate report
report = runner.generate_report(
    "custom_test", 
    PerformanceLevel.LOAD, 
    "api"
)
print(report)
```

### Async Performance Testing

```python
import asyncio
from tests.performance_monitor import AsyncPerformanceTestRunner

async def async_operation():
    # Simulate async work
    await asyncio.sleep(0.01)
    return "result"

# Run async performance test
runner = AsyncPerformanceTestRunner()
results = await runner.run_concurrent_requests(
    async_operation,
    concurrent_users=50,
    duration_seconds=60,
    test_name="async_test"
)

print(f"Requests per second: {results['requests_per_second']:.2f}")
```

## Conclusion

This performance testing system provides comprehensive coverage of:

- Database performance monitoring
- API endpoint benchmarking
- Load and stress testing
- Memory usage tracking
- Automated reporting and validation

Regular performance testing helps ensure the DevHQ application maintains optimal performance as it scales and evolves.

For questions or issues, please refer to the troubleshooting section or contact the development team.