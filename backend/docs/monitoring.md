# DevHQ Backend Monitoring & Observability

This document provides a comprehensive guide to the monitoring and observability system implemented in the DevHQ backend.

## Overview

The DevHQ backend includes a comprehensive monitoring system built on OpenTelemetry that provides:

- **Distributed Tracing**: Track requests across services and components
- **Metrics Collection**: Monitor performance, business, and infrastructure metrics
- **Structured Logging**: Consistent, searchable log format
- **Alerting**: Automated notifications for critical issues
- **Dashboards**: Pre-configured visualizations for key metrics

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Application   │───▶│  OpenTelemetry   │───▶│   Observability │
│   Components    │    │     SDK          │    │    Backend      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Decorators    │    │    Middleware    │    │   Dashboards    │
│   & Helpers     │    │   & Exporters    │    │   & Alerts      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Components

### 1. Core Monitoring Service (`monitoring.py`)

The main monitoring service that initializes OpenTelemetry and manages telemetry data.

```python
from app.core.monitoring import monitoring_service

# Service is automatically initialized and instrumented in main.py
```

### 2. Configuration (`monitoring_config.py`)

Environment-specific monitoring configuration that adapts based on deployment environment.

```python
from app.core.monitoring_config import monitoring_config

# Get current configuration
config = monitoring_config
print(f"Tracing enabled: {config.tracing.enabled}")
print(f"Environment: {config.tracing.environment}")
```

### 3. Middleware (`monitoring_middleware.py`)

Automatic request monitoring for all HTTP endpoints.

```python
# Automatically added to FastAPI app - no manual setup required
# Tracks:
# - Request duration
# - Response status codes
# - Request/response sizes
# - Error rates
```

### 4. Decorators (`monitoring_decorators.py`)

Decorators for monitoring specific operations.

```python
from app.core.monitoring_decorators import (
    monitor_api_endpoint,
    monitor_service_operation,
    monitor_database_operation,
    monitor_cache_operation
)

@monitor_api_endpoint
async def create_invoice(invoice_data: InvoiceCreate):
    # Automatically tracked with metrics and tracing
    pass

@monitor_service_operation
async def process_payment(payment_data: PaymentData):
    # Service operation monitoring
    pass

@monitor_database_operation
def mark_as_paid(self):
    # Database operation tracking
    pass

@monitor_cache_operation
async def get_cached_data(key: str):
    # Cache operation monitoring
    pass
```

## Metrics

### HTTP Metrics

- `http_requests_total`: Total number of HTTP requests
- `http_request_duration_seconds`: Request duration histogram
- `http_request_size_bytes`: Request size histogram
- `http_response_size_bytes`: Response size histogram

### Business Metrics

- `invoices_created_total`: Total invoices created
- `payment_transactions_total`: Total payment transactions
- `timer_sessions_total`: Total timer sessions
- `payment_processing_duration_seconds`: Payment processing time

### Infrastructure Metrics

- `database_operations_total`: Database operation count
- `database_operations_duration_seconds`: Database operation duration
- `cache_operations_total`: Cache operation count
- `active_websocket_connections`: Active WebSocket connections

### Custom Metrics

Add custom metrics using the monitoring service:

```python
from app.core.monitoring import monitoring_service

# Increment a counter
monitoring_service.record_metric(
    'custom_operation_total',
    1,
    {'operation': 'custom_task', 'status': 'success'}
)

# Record a duration
with monitoring_service.time_operation('custom_duration_seconds'):
    # Your operation here
    pass
```

## Tracing

### Automatic Tracing

The following are automatically traced:

- HTTP requests (via FastAPI instrumentation)
- Database queries (via SQLAlchemy instrumentation)
- Redis operations (via Redis instrumentation)
- HTTP client requests (via HTTPX instrumentation)

### Manual Tracing

Add custom spans for detailed tracing:

```python
from app.core.monitoring import monitoring_service

@monitoring_service.trace_operation("custom_operation")
async def custom_operation():
    # Automatically traced
    pass

# Or use context manager
with monitoring_service.start_span("manual_span") as span:
    span.set_attribute("custom.attribute", "value")
    # Your operation here
```

## Logging

### Structured Logging

All logs use structured format with consistent fields:

```python
import structlog

logger = structlog.get_logger(__name__)

# Standard logging
logger.info("Operation completed", 
           operation="create_invoice", 
           invoice_id="inv_123",
           duration=0.5)

# Error logging
logger.error("Payment failed", 
            payment_id="pay_456", 
            error="Gateway timeout",
            gateway="paystack")
```

### Log Levels by Environment

- **Development**: DEBUG level, console output
- **Staging**: INFO level, structured JSON
- **Production**: WARNING level, structured JSON with sampling

## Dashboards

Pre-configured dashboards are available in `monitoring_dashboard.py`:

### 1. Overview Dashboard
- Request rate and response times
- Error rates and active users
- Database and cache performance
- Payment success rates

### 2. API Performance Dashboard
- Request duration by endpoint
- Request volume and error rates
- Slowest endpoints
- Database query performance

### 3. Business Metrics Dashboard
- Invoice creation rates
- Payment volume and gateway distribution
- Timer session activity
- Revenue trends

### 4. Infrastructure Dashboard
- CPU and memory usage
- Database connection pools
- Cache performance

### 5. Error Tracking Dashboard
- Error rate trends
- Error distribution by status code
- Payment and database failures
- Top error endpoints

### Exporting Dashboard Configurations

```python
from app.core.monitoring_dashboard import MonitoringDashboards

# Export for Grafana
dashboard = MonitoringDashboards.get_overview_dashboard()
config = MonitoringDashboards.export_dashboard_config(dashboard)
```

## Alerting

Automated alerts are configured in `monitoring_alerts.py`:

### Critical Alerts
- High error rate (>5%)
- Service downtime
- Database connection pool exhaustion
- Payment processing failures

### Warning Alerts
- High response times
- Low cache hit rates
- High memory usage
- Unusual request volumes

### Business Alerts
- Low invoice creation rates
- Payment gateway timeouts
- WebSocket connection drops

### Alert Configuration

```python
from app.core.monitoring_alerts import MonitoringAlerts, alert_manager

# Get all alerts
alerts = MonitoringAlerts.get_all_alerts()

# Trigger custom alert
alert_manager.trigger_alert(
    "High Error Rate",
    current_value=0.08,  # 8% error rate
    context={"endpoint": "/api/payments"}
)

# Export Prometheus rules
prometheus_config = MonitoringAlerts.export_prometheus_rules()
```

## Environment Configuration

### Development
```bash
# Minimal monitoring for development
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_SERVICE_NAME=devhq-backend-dev
ENVIRONMENT=development
```

### Staging
```bash
# Full monitoring for staging
OTEL_EXPORTER_OTLP_ENDPOINT=https://staging-otel.devhq.com
OTEL_EXPORTER_OTLP_HEADERS=authorization=Bearer staging_token
OTEL_SERVICE_NAME=devhq-backend-staging
ENVIRONMENT=staging
```

### Production
```bash
# Production monitoring with sampling
OTEL_EXPORTER_OTLP_ENDPOINT=https://otel.devhq.com
OTEL_EXPORTER_OTLP_HEADERS=authorization=Bearer prod_token
OTEL_SERVICE_NAME=devhq-backend
ENVIRONMENT=production
```

## Integration with External Tools

### Prometheus + Grafana

1. Configure Prometheus to scrape metrics:
```yaml
scrape_configs:
  - job_name: 'devhq-backend'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

2. Import dashboard configurations from `monitoring_dashboard.py`

### Jaeger Tracing

1. Set up Jaeger collector:
```bash
docker run -d --name jaeger \
  -p 16686:16686 \
  -p 14250:14250 \
  jaegertracing/all-in-one:latest
```

2. Configure endpoint:
```bash
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:14250
```

### ELK Stack (Elasticsearch, Logstash, Kibana)

1. Configure structured logging output to Logstash
2. Use Kibana for log analysis and visualization

## Best Practices

### 1. Metric Naming
- Use descriptive names with units: `http_request_duration_seconds`
- Include relevant labels: `{method="POST", endpoint="/api/invoices"}`
- Follow Prometheus naming conventions

### 2. Tracing
- Add meaningful span names and attributes
- Don't trace every small operation (avoid noise)
- Include business context in spans

### 3. Logging
- Use structured logging with consistent field names
- Include correlation IDs for request tracking
- Log at appropriate levels (avoid debug logs in production)

### 4. Alerting
- Set meaningful thresholds based on SLAs
- Include runbook links in alert messages
- Use different notification channels for different severities

### 5. Performance
- Use sampling for high-volume traces
- Batch metric exports
- Monitor the monitoring system itself

## Troubleshooting

### Common Issues

1. **No metrics appearing**
   - Check OTEL endpoint configuration
   - Verify network connectivity
   - Check service logs for export errors

2. **High memory usage**
   - Reduce trace sampling rate
   - Increase export intervals
   - Check for metric cardinality explosion

3. **Missing traces**
   - Verify instrumentation is enabled
   - Check sampling configuration
   - Ensure proper span context propagation

### Debug Mode

Enable debug logging for troubleshooting:

```python
import logging
logging.getLogger("opentelemetry").setLevel(logging.DEBUG)
```

## Performance Impact

The monitoring system is designed to have minimal performance impact:

- **Tracing**: ~1-5ms overhead per request (with sampling)
- **Metrics**: ~0.1-1ms overhead per operation
- **Logging**: ~0.1-0.5ms overhead per log entry

Total overhead in production: **<2% of request time**

## Security Considerations

- Never log sensitive data (passwords, tokens, PII)
- Use secure transport (HTTPS/TLS) for telemetry data
- Implement proper authentication for monitoring endpoints
- Regularly rotate API keys and tokens
- Monitor access to observability data

## Maintenance

### Regular Tasks

1. **Weekly**:
   - Review alert thresholds and adjust as needed
   - Check dashboard accuracy and relevance
   - Monitor storage usage for metrics/traces

2. **Monthly**:
   - Update monitoring dependencies
   - Review and optimize metric cardinality
   - Analyze monitoring system performance

3. **Quarterly**:
   - Review and update SLAs/SLIs
   - Conduct monitoring system health checks
   - Update runbooks and documentation

## Support

For monitoring-related issues:

1. Check the application logs for monitoring errors
2. Verify configuration settings
3. Consult this documentation
4. Contact the DevOps team for infrastructure issues

## References

- [OpenTelemetry Documentation](https://opentelemetry.io/docs/)
- [Prometheus Best Practices](https://prometheus.io/docs/practices/)
- [Grafana Dashboard Design](https://grafana.com/docs/grafana/latest/best-practices/)
- [Structured Logging with Structlog](https://www.structlog.org/)