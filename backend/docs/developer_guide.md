# 🛠️ DevHQ Backend - Developer Guide

## 📚 Service Layer Architecture

The DevHQ backend follows a service-oriented architecture where business logic is encapsulated in service classes rather than being implemented directly in API routers. This separation provides several benefits:

- **Testability**: Business logic can be tested independently of HTTP requests
- **Reusability**: Services can be used across different parts of the application
- **Maintainability**: Clear separation of concerns makes code easier to understand and modify
- **Consistency**: Standardized patterns for common operations

## 🏗️ Service Layer Structure

```
app/
├── services/
│   ├── user_service.py      # User management operations
│   ├── client_service.py    # Client management operations
│   ├── project_service.py   # Project management operations
│   └── ...                  # Other service modules
```

## 🎯 Service Design Principles

### 1. **Single Responsibility**
Each service class has a single, well-defined purpose:
- `UserService`: User profile, settings, and account management
- `ClientService`: Client CRUD operations and related functionality
- `ProjectService`: Project, milestone, and time tracking operations

### 2. **Dependency Injection**
Services receive their dependencies (primarily database sessions) through constructor injection:

```python
# Example service usage
def some_endpoint(db: Session = Depends(get_db)):
    user_service = UserService(db)
    user = user_service.get_user_profile(user_id)
    return user
```

### 3. **Explicit Error Handling**
Services raise specific exceptions that can be caught and handled appropriately:

```python
from app.core.exceptions import NotFoundError, ConflictError

# In service method
if not user:
    raise NotFoundError("User not found")
```

## 📖 Service API Documentation

### UserService

#### Initialization
```python
from app.services.user_service import UserService

user_service = UserService(db_session)
```

#### Key Methods
- `get_user_profile(user_id)` - Retrieve user profile
- `update_user_profile(user_id, profile_data)` - Update user profile
- `upload_user_avatar(user_id, file_bytes, content_type)` - Upload avatar
- `get_user_settings(user_id)` - Get or create user settings
- `update_user_settings(user_id, settings_data)` - Update user settings
- `delete_user_account(user_id)` - Soft delete user account

### ClientService

#### Initialization
```python
from app.services.client_service import ClientService

client_service = ClientService(db_session)
```

#### Key Methods
- `create_client(user_id, client_data)` - Create new client
- `get_client(user_id, client_id)` - Retrieve client by ID
- `list_clients(**filters)` - List clients with filtering/pagination
- `update_client(user_id, client_id, client_data)` - Update client
- `delete_client(user_id, client_id)` - Soft delete client
- `get_client_statistics(user_id, client_id)` - Get client metrics

### ProjectService

#### Initialization
```python
from app.services.project_service import ProjectService

project_service = ProjectService(db_session)
```

#### Key Methods
- `create_project(user_id, project_data)` - Create new project
- `list_projects(**filters)` - List projects with filtering/pagination
- `get_project(user_id, project_id)` - Retrieve project with relations
- `update_project(user_id, project_id, project_data)` - Update project
- `delete_project(user_id, project_id)` - Soft delete project
- `get_project_overview(user_id, project_id)` - Get project summary
- `get_project_metrics(user_id, project_id)` - Get project analytics

## 🧪 Testing Services

Services are designed to be easily testable. Here's an example test pattern:

```python
def test_user_service_get_profile(db_session, sample_user):
    # Arrange
    user_service = UserService(db_session)
    
    # Act
    user = user_service.get_user_profile(sample_user.id)
    
    # Assert
    assert user.id == sample_user.id
    assert user.email == sample_user.email
```

## 🔄 Best Practices for Adding New Services

1. **Create a new service class** in `/app/services/`
2. **Follow the naming convention**: `{Domain}Service`
3. **Inject dependencies** through constructor
4. **Use type hints** for all parameters and return values
5. **Add comprehensive docstrings** with examples
6. **Raise appropriate exceptions** for error conditions
7. **Write unit tests** for service methods

## 📝 Example: Creating a New Service

```python
# app/services/invoice_service.py
from sqlalchemy.orm import Session
from app.core.exceptions import NotFoundError
from app.models.invoice import Invoice

class InvoiceService:
    """Service for managing invoice operations"""
    
    def __init__(self, db: Session):
        """
        Initialize InvoiceService with database session
        
        Args:
            db (Session): Database session
        """
        self.db = db
    
    def get_invoice(self, user_id: uuid.UUID, invoice_id: uuid.UUID) -> Invoice:
        """
        Get invoice by ID for the specified user
        
        Args:
            user_id (uuid.UUID): ID of user who owns the invoice
            invoice_id (uuid.UUID): ID of invoice to retrieve
            
        Returns:
            Invoice: Invoice object
            
        Raises:
            NotFoundError: If invoice is not found
        """
        invoice = (
            self.db.query(Invoice)
            .filter(
                Invoice.id == invoice_id,
                Invoice.user_id == user_id,
                Invoice.deleted_at.is_(None),
            )
            .first()
        )
        if not invoice:
            raise NotFoundError("Invoice not found")
        return invoice
```

## 🎯 Key Benefits Achieved

1. **Separation of Concerns**: HTTP layer handles requests, services handle business logic
2. **Improved Testability**: Services can be tested without HTTP framework
3. **Better Code Organization**: Related functionality grouped in service classes
4. **Enhanced Documentation**: Comprehensive docstrings with examples
5. **Consistent Error Handling**: Standardized exception patterns
6. **Scalability**: Easy to add new features and services