# Docker Database Setup for Tests

This guide explains how to configure and use Docker PostgreSQL for running tests in the DevHQ backend.

## Overview

The DevHQ backend tests can be configured to use either:

1. **SQLite** (in-memory or file-based) - Fast and simple, good for quick test runs
2. **PostgreSQL** (local or Docker) - More realistic, matches production environment

## Prerequisites

- Docker and Docker Compose installed
- Python 3.9+ with pip

## Setup Options

### Environment Variables

The test configuration is controlled by these environment variables:

- `USE_POSTGRES_TESTS`: Set to `true` to use PostgreSQL, `false` to use SQLite (default: `true`)
- `USE_DOCKER_DB`: Set to `true` to use Docker PostgreSQL, `false` to use local PostgreSQL (default: `true`)
- `USE_FILE_DB`: Set to `true` to use file-based SQLite (only applies when `USE_POSTGRES_TESTS=false`)

## Quick Setup

### Windows

```powershell
# From the backend directory
.\scripts\setup_test_db.bat

# Run tests with Docker PostgreSQL
set USE_POSTGRES_TESTS=true
set USE_DOCKER_DB=true
pytest
```

### Linux/macOS

```bash
# From the backend directory
python scripts/setup_test_db.py

# Run tests with Docker PostgreSQL
USE_POSTGRES_TESTS=true USE_DOCKER_DB=true pytest
```

## Manual Setup

1. Start the PostgreSQL Docker container:

   ```bash
   # From the project root
   docker-compose -f docker-compose.dev.yml up -d postgres
   ```

2. Create the test database:

   ```bash
   # Using psql (requires PostgreSQL client)
   PGPASSWORD=devhq_password psql -h localhost -p 5433 -U devhq_user -d postgres -c "CREATE DATABASE devhq_test;"
   ```

3. Run tests with Docker PostgreSQL:

   ```bash
   # Windows
   set USE_POSTGRES_TESTS=true
   set USE_DOCKER_DB=true
   pytest

   # Linux/macOS
   USE_POSTGRES_TESTS=true USE_DOCKER_DB=true pytest
   ```

## Troubleshooting

### Connection Issues

If you encounter connection issues:

1. Verify the Docker container is running:
   ```bash
   docker ps | grep postgres
   ```

2. Check the container logs:
   ```bash
   docker logs devhq-postgres
   ```

3. Verify the port mapping:
   ```bash
   docker port devhq-postgres
   ```

### Database Reset

To reset the test database:

```bash
# Windows
set PGPASSWORD=devhq_password
psql -h localhost -p 5433 -U devhq_user -d postgres -c "DROP DATABASE IF EXISTS devhq_test; CREATE DATABASE devhq_test;"

# Linux/macOS
PGPASSWORD=devhq_password psql -h localhost -p 5433 -U devhq_user -d postgres -c "DROP DATABASE IF EXISTS devhq_test; CREATE DATABASE devhq_test;"
```

## CI/CD Integration

For CI/CD environments, use the `test-with-docker.sh` script in the project root:

```bash
# From the project root
./scripts/test-with-docker.sh
```

This script creates a temporary Docker Compose configuration specifically for testing.