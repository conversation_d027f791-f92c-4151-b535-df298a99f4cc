# DevHQ Backend Testing Guide

## Overview

This document provides guidelines and instructions for testing the DevHQ backend application. The testing framework is built on pytest with additional tools for coverage analysis, mocking, and test automation.

## Test Organization

Tests are organized into the following categories:

1. **Unit Tests** - Test individual functions and methods
2. **Integration Tests** - Test interactions between components
3. **End-to-End Tests** - Test complete user workflows
4. **Model Tests** - Test database models and relationships
5. **API Endpoint Tests** - Test REST API endpoints
6. **Service Tests** - Test business logic services

## Test Structure

```
tests/
├── conftest.py                 # Test configuration and fixtures
├── test_smoke.py              # Basic smoke tests
├── test_test_infrastructure.py # Test infrastructure validation
├── test_*.py                  # Individual test files
├── test_*_endpoints.py        # API endpoint tests
├── test_*_models.py           # Model tests
├── test_*_service.py          # Service tests
└── test_*_workflow.py         # Workflow tests
```

## Running Tests

### Using Makefile (Recommended)

```bash
# Run all tests
make test

# Run tests with verbose output
make test-verbose

# Run specific test suite
make test-suite SUITE=auth

# Run tests with coverage
make test-coverage

# List available test suites
make list-suites

# Run smoke tests
make test-smoke

# Run a single test file
make test-single FILE=test_auth_endpoints.py

# Run tests matching pattern
make test-pattern PATTERN=timer
```

### Using Test Runner Script

```bash
# Run all tests
python scripts/run_tests.py

# Run specific test suite
python scripts/run_tests.py --suite auth

# Run tests with coverage
python scripts/run_tests.py --coverage

# List available test suites
python scripts/run_tests.py --list-suites
```

### Using pytest Directly

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_auth_endpoints.py

# Run tests matching pattern
pytest -k "timer"

# Run tests with coverage
pytest --cov=app --cov-report=html
```

## Test Fixtures

The test suite provides several reusable fixtures defined in `conftest.py`:

- `db_session` - Database session for testing
- `client` - Test client for API requests
- `test_user` - Pre-created test user
- `test_client` - Pre-created test client
- `test_project` - Pre-created test project
- `auth_headers` - Authentication headers for API requests

## Writing Tests

### Test File Structure

```python
"""
Description of what is being tested
"""

import pytest
from fastapi.testclient import TestClient


class TestFeatureName:
    """Test cases for Feature Name functionality."""

    def test_specific_behavior(self, client: TestClient, auth_headers):
        """Test specific behavior with description."""
        # Arrange - Set up test data
        
        # Act - Perform the action being tested
        response = client.get("/api/v1/endpoint", headers=auth_headers)
        
        # Assert - Verify the expected outcome
        assert response.status_code == 200
        data = response.json()
        assert "expected_field" in data
```

### Best Practices

1. **Use Descriptive Test Names**: Test names should clearly describe what is being tested and the expected outcome.

2. **Follow AAA Pattern**: 
   - Arrange: Set up test data and preconditions
   - Act: Execute the code under test
   - Assert: Verify the expected outcomes

3. **Use Fixtures**: Leverage existing fixtures to reduce boilerplate code.

4. **Test One Thing**: Each test should focus on a single behavior or scenario.

5. **Use Appropriate Assertions**: Use specific assertions that clearly express what is being verified.

6. **Clean Up**: Ensure tests clean up after themselves to avoid side effects.

## Test Categories

### Unit Tests

Unit tests focus on individual functions and methods without external dependencies.

```python
def test_calculate_billable_amount(self):
    """Test billable amount calculation."""
    # Test the specific function logic
    result = calculate_billable_amount(hours=2.5, rate=100)
    assert result == 250.00
```

### Integration Tests

Integration tests verify that different components work together correctly.

```python
def test_user_registration_flow(self, client: TestClient):
    """Test complete user registration flow."""
    # Test the integration between multiple components
    response = client.post("/api/v1/auth/register", json=registration_data)
    assert response.status_code == 201
    
    # Verify database state
    user = db.query(User).filter_by(email="<EMAIL>").first()
    assert user is not None
    assert user.is_active is True
```

### API Endpoint Tests

API endpoint tests verify that REST endpoints behave correctly.

```python
def test_get_user_profile(self, client: TestClient, auth_headers):
    """Test getting user profile information."""
    response = client.get("/api/v1/users/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
```

## Test Data Management

### Test Factories

For complex test data, use factories to create consistent test objects.

```python
def create_test_user(db_session, **overrides):
    """Create a test user with default values."""
    defaults = {
        "email": f"test-{uuid.uuid4().hex[:8]}@example.com",
        "first_name": "Test",
        "last_name": "User",
        "password": "TestPass123!",
        "is_active": True,
        "is_verified": True
    }
    defaults.update(overrides)
    
    user = User(**defaults)
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user
```

### Test Database

Tests use an in-memory SQLite database for isolation and speed. Each test runs in a transaction that is rolled back after the test completes.

## Coverage Analysis

Coverage reports help identify untested code paths:

```bash
# Generate coverage report
make test-coverage

# Or with pytest directly
pytest --cov=app --cov-report=html
```

Reports are generated in the `htmlcov/` directory.

## Continuous Integration

Tests are automatically run in CI pipelines. All tests must pass before merging changes.

## Debugging Tests

### Verbose Output

Use `-v` flag for verbose test output:

```bash
pytest -v
```

### Debugging with Print Statements

Add print statements for debugging (they will only appear with -s flag):

```bash
pytest -s
```

### Interactive Debugging

Use pytest with pdb for interactive debugging:

```bash
pytest --pdb
```

## Performance Considerations

1. **Mock External Services**: Use mocks for external API calls and services.
2. **Use In-Memory Databases**: Tests use SQLite in-memory databases for speed.
3. **Parallel Test Execution**: Tests can be run in parallel where possible.
4. **Avoid Heavy Fixtures**: Keep fixtures lightweight and focused.

## Common Test Patterns

### Testing Authentication

```python
def test_protected_endpoint_requires_auth(self, client: TestClient):
    """Test that protected endpoint requires authentication."""
    response = client.get("/api/v1/protected")
    assert response.status_code == 401
```

### Testing Validation

```python
def test_invalid_input_validation(self, client: TestClient, auth_headers):
    """Test validation of invalid input."""
    response = client.post("/api/v1/users", json={"email": "invalid-email"})
    assert response.status_code == 422
```

### Testing Error Conditions

```python
def test_resource_not_found(self, client: TestClient, auth_headers):
    """Test handling of non-existent resources."""
    response = client.get("/api/v1/users/non-existent-id")
    assert response.status_code == 404
```

## Test Maintenance

Regular test maintenance tasks include:

1. **Removing Obsolete Tests**: Remove tests for deprecated features
2. **Updating Flaky Tests**: Fix tests that intermittently fail
3. **Adding New Tests**: Add tests for new features
4. **Improving Coverage**: Increase test coverage for critical paths
5. **Refactoring Duplication**: Remove duplicated test code

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure test files are in the Python path
2. **Database Locks**: Use transactions to avoid database locking issues
3. **Async/Await Issues**: Use appropriate async test patterns
4. **Timeout Errors**: Increase timeout values for slow tests

### Getting Help

If you encounter issues with tests:

1. Check the test output for specific error messages
2. Verify that all dependencies are installed
3. Ensure the test database is accessible
4. Consult the #testing channel in Slack
5. Review the pytest documentation