# 🌐 DevHQ Backend - API Endpoint Examples\n\n## 📚 Overview\n\nThis document provides practical examples for using the DevHQ Backend API endpoints. All examples assume you have a valid JWT access token.\n\n## 🔐 Authentication\n\n### User Registration\n```bash\ncurl -X POST \"http://localhost:8000/api/v1/auth/register\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePass123!\",\n    \"first_name\": \"John\",\n    \"last_name\": \"Doe\",\n    \"phone\": \"+1234567890\"\n  }'\n```\n\n### User Login\n```bash\ncurl -X POST \"http://localhost:8000/api/v1/auth/login\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePass123!\"\n  }'\n```\n\n### Refresh Token\n```bash\ncurl -X POST \"http://localhost:8000/api/v1/auth/refresh\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"refresh_token\": \"YOUR_REFRESH_TOKEN\"\n  }'\n```\n\n## 👤 User Management\n\n### Get User Profile\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/users/me\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Update User Profile\n```bash\ncurl -X PUT \"http://localhost:8000/api/v1/users/me\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"first_name\": \"Jane\",\n    \"last_name\": \"Smith\",\n    \"phone\": \"+0987654321\"\n  }'\n```\n\n### Get User Settings\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/users/me/settings\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Update User Settings\n```bash\ncurl -X PUT \"http://localhost:8000/api/v1/users/me/settings\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"theme\": \"dark\",\n    \"language\": \"en\",\n    \"default_currency\": \"USD\",\n    \"default_hourly_rate\": 75.00\n  }'\n```\n\n## 🏢 Client Management\n\n### Create Client\n```bash\ncurl -X POST \"http://localhost:8000/api/v1/clients/\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"name\": \"Acme Corporation\",\n    \"email\": \"<EMAIL>\",\n    \"company\": \"Acme Corp\",\n    \"phone\": \"+1234567890\"\n  }'\n```\n\n### List Clients\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/clients/?page=1&per_page=20&is_active=true\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Get Client Details\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/clients/{client_id}\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Update Client\n```bash\ncurl -X PUT \"http://localhost:8000/api/v1/clients/{client_id}\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"name\": \"Acme Corporation Updated\",\n    \"email\": \"<EMAIL>\"\n  }'\n```\n\n### Delete Client\n```bash\ncurl -X DELETE \"http://localhost:8000/api/v1/clients/{client_id}\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n## 📁 Project Management\n\n### Create Project\n```bash\ncurl -X POST \"http://localhost:8000/api/v1/projects/\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"title\": \"Website Redesign\",\n    \"client_id\": \"CLIENT_UUID\",\n    \"billing_type\": \"time_and_materials\",\n    \"hourly_rate\": 75.00,\n    \"estimated_hours\": 40.00\n  }'\n```\n\n### List Projects\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/projects/?page=1&per_page=20&status=active\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Get Project Details\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/projects/{project_id}\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Update Project\n```bash\ncurl -X PUT \"http://localhost:8000/api/v1/projects/{project_id}\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"title\": \"Website Redesign - Updated\",\n    \"status\": \"in_progress\"\n  }'\n```\n\n### Delete Project\n```bash\ncurl -X DELETE \"http://localhost:8000/api/v1/projects/{project_id}\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n## ⏱️ Time Tracking\n\n### Start Timer\n```bash\ncurl -X POST \"http://localhost:8000/api/v1/timer/start\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\n    \"project_id\": \"PROJECT_UUID\",\n    \"description\": \"Working on homepage design\",\n    \"task_name\": \"Design\"\n  }'\n```\n\n### Stop Timer\n```bash\ncurl -X POST \"http://localhost:8000/api/v1/timer/stop\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Get Timer Status\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/timer/status\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n## 📊 Analytics\n\n### Get Project Overview\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/projects/{project_id}/overview\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Get Project Metrics\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/projects/{project_id}/metrics\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n### Get Client Statistics\n```bash\ncurl -X GET \"http://localhost:8000/api/v1/clients/{client_id}/statistics\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```\n\n## 🛡️ Error Handling\n\nThe API returns standardized error responses:\n\n```json\n{\n  \"error\": \"Validation error\",\n  \"details\": [\n    {\n      \"type\": \"value_error\",\n      \"loc\": [\"body\", \"email\"],\n      \"msg\": \"value is not a valid email address\"\n    }\n  ]\n}\n```\n\nCommon HTTP status codes:\n- `200`: Success\n- `201`: Created\n- `400`: Bad Request\n- `401`: Unauthorized\n- `403`: Forbidden\n- `404`: Not Found\n- `409`: Conflict\n- `422`: Unprocessable Entity\n- `500`: Internal Server Error\n\n## 🔧 Health Check Endpoints\n\n### Basic Health Check\n```bash\ncurl -X GET \"http://localhost:8000/health\"\n```\n\n### Configuration Health Check\n```bash\ncurl -X GET \"http://localhost:8000/config/health\"\n```\n\n## 📱 Rate Limiting\n\nAPI endpoints are rate-limited to prevent abuse. Current limits:\n- `100 requests per minute` for most endpoints\n- `10 requests per minute` for authentication endpoints\n\nExceeding rate limits will return a `429 Too Many Requests` response.\n\n## 🔁 Best Practices\n\n1. **Always use HTTPS** in production\n2. **Store refresh tokens securely** (not in localStorage)\n3. **Handle token expiration** gracefully\n4. **Use appropriate HTTP methods** (GET, POST, PUT, DELETE)\n5. **Implement proper error handling** in your client applications\n6. **Cache responses** when appropriate to reduce API calls\n7. **Use pagination** for list endpoints to improve performance\n8. **Filter and sort** data on the server side when possible\n\n## 🎯 Example Workflow\n\nHere's a complete workflow for creating a client and project:\n\n```bash\n# 1. Create client\nCLIENT_ID=$(curl -X POST \"http://localhost:8000/api/v1/clients/\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d '{\"name\": \"New Client\", \"email\": \"<EMAIL>\"}' \\\n  | jq -r '.id')\n\n# 2. Create project for client\nPROJECT_ID=$(curl -X POST \"http://localhost:8000/api/v1/projects/\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d \"{\\\"title\\\": \\\"New Project\\\", \\\"client_id\\\": \\\"$CLIENT_ID\\\", \\\"billing_type\\\": \\\"time_and_materials\\\"}\" \\\n  | jq -r '.id')\n\n# 3. Start timer on project\ncurl -X POST \"http://localhost:8000/api/v1/timer/start\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\" \\\n  -d \"{\\\"project_id\\\": \\\"$PROJECT_ID\\\", \\\"description\\\": \\\"Working on project\\\"}\"\n\n# 4. Stop timer after work\ncurl -X POST \"http://localhost:8000/api/v1/timer/stop\" \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\"\n```