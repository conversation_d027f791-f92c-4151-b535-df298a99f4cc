#!/bin/bash

# DevHQ Backend Test Runner
# Simple script to run tests locally with proper Python path

echo "🧪 Running DevHQ Backend Tests"
echo "================================"

# Ensure we're in the backend directory
cd "$(dirname "$0")"

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Virtual environment not detected. Please activate it first:"
    echo "   source venv/bin/activate"
    exit 1
fi

# Set Python path and run tests
echo "📍 Setting Python path and running tests..."
PYTHONPATH=. python -m pytest tests/ -v --tb=short

echo ""
echo "✅ Test run complete!"
echo ""
echo "💡 Tips:"
echo "   - Run specific test: PYTHONPATH=. python -m pytest tests/test_main.py -v"
echo "   - Run with coverage: PYTHONPATH=. python -m pytest tests/ --cov=app --cov-report=html"
echo "   - Skip warnings: PYTHONPATH=. python -m pytest tests/ -v --disable-warnings"