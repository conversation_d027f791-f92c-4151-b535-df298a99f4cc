#!/usr/bin/env python3
"""
Cleanup script to remove unverified user accounts
Removes accounts that have not been verified within 24 hours of creation
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta, timezone

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django-like environment
os.environ.setdefault('ENVIRONMENT', 'development')

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Import settings - this should work now that we've set up the path
try:
    from app.config import settings
except ImportError as e:
    print(f"Error importing settings: {e}")
    print("Make sure you're running this script from the backend directory")
    sys.exit(1)

def cleanup_unverified_accounts():
    """Remove unverified accounts older than 24 hours"""
    print("🧹 Starting database cleanup for unverified accounts...")
    
    try:
        # Create database engine
        engine = create_engine(settings.database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # Calculate cutoff time (24 hours ago)
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            # Delete unverified accounts older than 24 hours
            result = db.execute(
                text("""
                    DELETE FROM users 
                    WHERE is_verified = false 
                    AND created_at < :cutoff_time
                """),
                {"cutoff_time": cutoff_time}
            )
            
            deleted_count = result.rowcount
            db.commit()
            
            print(f"✅ Cleaned up {deleted_count} unverified accounts older than 24 hours")
            return deleted_count
            
        except Exception as e:
            print(f"❌ Error during cleanup: {e}")
            db.rollback()
            raise
        finally:
            db.close()
            
    except Exception as e:
        print(f"💥 Cleanup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    cleanup_unverified_accounts()
    print("✨ Cleanup completed successfully.")