#!/bin/bash

# DevHQ Backend Development Setup Script
echo "🚀 Setting up DevHQ Backend Development Environment..."

# Check if Python 3.11+ is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing Python dependencies..."
pip install -r requirements-dev.txt

# Copy environment file
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️ Please update .env file with your actual configuration values"
fi

echo "✅ Development environment setup complete!"
echo ""
echo "🔧 Next steps:"
echo "1. Start PostgreSQL database:"
echo "   Option A (Recommended): ./start_dev_server.sh  # Does everything automatically"
echo "   Option B (Manual): docker run --name devhq-postgres -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=devhq_dev -p 5433:5432 -d postgres:15"
echo "   Option C (Docker Compose): docker-compose up postgres -d  # Uses different credentials"
echo ""
echo "2. Run database migrations: ./run_alembic.sh upgrade head"
echo "3. Start development server: ./start_dev_server.sh"
echo ""
echo "📚 Useful commands:"
echo "- Run tests: ./run_tests.sh"
echo "- Database migrations: ./run_alembic.sh upgrade head"
echo "- Create migration: ./run_alembic.sh revision --autogenerate -m 'description'"