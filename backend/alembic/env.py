"""
Alembic environment configuration for DevHQ Backend
"""

import os
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config, pool

from alembic import context

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    # Try to load .env from backend directory first
    backend_env = os.path.join(os.path.dirname(os.path.dirname(__file__)), ".env")
    if os.path.exists(backend_env):
        load_dotenv(backend_env)
    else:
        # Fallback to current directory
        load_dotenv()
except ImportError:
    # python-dotenv not available, skip loading
    pass

# Add the parent directory to sys.path to import our app
backend_dir = os.path.dirname(os.path.dirname(__file__))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

# Also add the current working directory if we're running from project root
current_dir = os.getcwd()
backend_path = os.path.join(current_dir, "backend")
if os.path.exists(backend_path) and backend_path not in sys.path:
    sys.path.insert(0, backend_path)

# Ensure the 'app' package is importable by explicitly adding backend_dir and its 'app' child
app_path_explicit = os.path.join(backend_dir, "app")
if os.path.exists(app_path_explicit) and app_path_explicit not in sys.path:
    sys.path.insert(0, backend_dir)

# Additional fallback: if we're in a CI environment, try to find the backend directory
if "GITHUB_ACTIONS" in os.environ or "CI" in os.environ:
    # Try to find backend directory relative to current working directory
    possible_paths = [
        current_dir,  # If we're already in backend
        os.path.join(current_dir, "backend"),  # If we're in project root
        os.path.dirname(current_dir),  # If we're in a subdirectory
    ]

    for path in possible_paths:
        app_path = os.path.join(path, "app")
        if os.path.exists(app_path) and os.path.isdir(app_path):
            if path not in sys.path:
                sys.path.insert(0, path)
            break

# Set required environment variables if not present (for CI/migrations)
if not os.getenv("SECRET_KEY"):
    os.environ["SECRET_KEY"] = os.getenv(
        "SECRET_KEY", "migration-secret-key-placeholder"
    )
if not os.getenv("DATABASE_URL"):
    # Use CI-provided DATABASE_URL if set, otherwise fallback
    os.environ["DATABASE_URL"] = os.getenv(
        "DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/devhq_test"
    )

# Debug information for CI
if "GITHUB_ACTIONS" in os.environ or "CI" in os.environ:
    print(f"DEBUG: Current working directory: {os.getcwd()}")
    print(f"DEBUG: __file__ location: {__file__}")
    print(f"DEBUG: backend_dir: {backend_dir}")
    print(f"DEBUG: sys.path: {sys.path[:3]}...")  # Show first 3 entries
    print(f"DEBUG: Looking for app directory...")
    for path in sys.path[:5]:
        app_path = os.path.join(path, "app")
        print(f"DEBUG: Checking {app_path}: {os.path.exists(app_path)}")

# Try to import with error handling
try:
    # Ensure backend directory is on sys.path in CI and local
    backend_root = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(backend_root, ".."))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    if backend_root not in sys.path:
        sys.path.insert(0, backend_root)

    from app import models  # Import all models to register with Base.metadata
    from app.config import settings
    from app.models.base import Base
except ImportError as e:
    print(f"Import error: {e}")
    print("Using fallback configuration for CI/CD environment")

    # Create a minimal settings object for migrations
    class FallbackSettings:
        database_url = os.getenv(
            "DATABASE_URL",
            "postgresql://devhq_user:devhq_password@localhost:5433/devhq",
        )

    settings = FallbackSettings()

    # Import Base and models with error handling
    try:
        from app.models import user
        from app.models.base import Base
    except ImportError:
        print("Warning: Could not import models. Using fallback Base.")
        # Create a minimal Base for basic migrations
        from sqlalchemy.ext.declarative import declarative_base

        Base = declarative_base()

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Set the database URL from our settings
config.set_main_option("sqlalchemy.url", settings.database_url)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
