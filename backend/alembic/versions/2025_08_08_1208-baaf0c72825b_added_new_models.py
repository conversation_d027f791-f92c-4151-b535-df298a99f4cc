"""Added new models

Revision ID: baaf0c72825b
Revises: 202508080000
Create Date: 2025-08-08 12:08:22.922905

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "baaf0c72825b"
down_revision = "202508080000"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_clients_user_id"), "clients", ["user_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_clients_user_id"), table_name="clients")
    # ### end Alembic commands ###
