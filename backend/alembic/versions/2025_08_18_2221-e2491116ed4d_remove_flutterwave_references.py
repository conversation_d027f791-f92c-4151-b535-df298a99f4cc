"""remove_flutterwave_references

Revision ID: e2491116ed4d
Revises: 4aa58859c609
Create Date: 2025-08-18 22:21:42.295246

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "e2491116ed4d"
down_revision = "4aa58859c609"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Remove flutterwave_subaccount_id column from users table if it exists
    # Check if column exists before dropping
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    columns = [col['name'] for col in inspector.get_columns('users')]
    
    if 'flutterwave_subaccount_id' in columns:
        op.drop_column("users", "flutterwave_subaccount_id")


def downgrade() -> None:
    # Add back flutterwave_subaccount_id column
    op.add_column(
        "users",
        sa.Column("flutterwave_subaccount_id", sa.String(length=100), nullable=True),
    )
