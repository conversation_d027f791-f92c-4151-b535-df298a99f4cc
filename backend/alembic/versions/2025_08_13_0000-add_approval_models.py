"""add approval models

Revision ID: 2025_08_13_0000
Revises: 2025_08_12_0000
Create Date: 2025-08-13 00:00:00.000000

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "add_approval_models"
down_revision = "add_invoice_models"
branch_labels = None
depends_on = None


def upgrade():
    # Create client_approvals table
    op.create_table(
        "client_approvals",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("approvable_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("approvable_type", sa.String(length=50), nullable=False),
        sa.Column("client_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("project_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("requested_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("viewed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("approved_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("revision_requested_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("priority", sa.String(length=20), nullable=False),
        sa.Column("due_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("approval_token", sa.String(length=255), nullable=False),
        sa.Column("version_number", sa.Integer(), nullable=False),
        sa.Column("previous_approval_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column(
            "context_data", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
        sa.Column("client_instructions", sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(
            ["client_id"],
            ["clients.id"],
        ),
        sa.ForeignKeyConstraint(
            ["previous_approval_id"],
            ["client_approvals.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_client_approvals_approvable_id"),
        "client_approvals",
        ["approvable_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_approvals_approvable_type"),
        "client_approvals",
        ["approvable_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_approvals_approval_token"),
        "client_approvals",
        ["approval_token"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_approvals_client_id"),
        "client_approvals",
        ["client_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_approvals_id"), "client_approvals", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_client_approvals_priority"),
        "client_approvals",
        ["priority"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_approvals_project_id"),
        "client_approvals",
        ["project_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_approvals_status"), "client_approvals", ["status"], unique=False
    )
    op.create_index(
        op.f("ix_client_approvals_user_id"),
        "client_approvals",
        ["user_id"],
        unique=False,
    )
    op.create_unique_constraint(None, "client_approvals", ["approval_token"])

    # Create client_feedback table
    op.create_table(
        "client_feedback",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("approval_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("content", sa.Text(), nullable=False),
        sa.Column("feedback_type", sa.String(length=50), nullable=False),
        sa.Column("submitted_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("is_addressed", sa.Boolean(), nullable=False),
        sa.Column("developer_response", sa.Text(), nullable=True),
        sa.Column("responded_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("client_name", sa.String(length=200), nullable=False),
        sa.Column("client_email", sa.String(length=255), nullable=False),
        sa.Column("urgency_level", sa.String(length=20), nullable=True),
        sa.Column("category", sa.String(length=100), nullable=True),
        sa.ForeignKeyConstraint(
            ["approval_id"],
            ["client_approvals.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_client_feedback_approval_id"),
        "client_feedback",
        ["approval_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_feedback_feedback_type"),
        "client_feedback",
        ["feedback_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_client_feedback_id"), "client_feedback", ["id"], unique=False
    )

    # Create approval_activities table
    op.create_table(
        "approval_activities",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("approval_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("action", sa.String(length=50), nullable=False),
        sa.Column("actor_type", sa.String(length=20), nullable=False),
        sa.Column("actor_name", sa.String(length=200), nullable=False),
        sa.Column("details", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("timestamp", sa.DateTime(timezone=True), nullable=False),
        sa.Column("ip_address", sa.String(length=45), nullable=True),
        sa.Column("user_agent", sa.String(length=500), nullable=True),
        sa.ForeignKeyConstraint(
            ["approval_id"],
            ["client_approvals.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_approval_activities_action"),
        "approval_activities",
        ["action"],
        unique=False,
    )
    op.create_index(
        op.f("ix_approval_activities_approval_id"),
        "approval_activities",
        ["approval_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_approval_activities_id"), "approval_activities", ["id"], unique=False
    )


def downgrade():
    # Drop tables in reverse order
    op.drop_index(op.f("ix_approval_activities_id"), table_name="approval_activities")
    op.drop_index(
        op.f("ix_approval_activities_approval_id"), table_name="approval_activities"
    )
    op.drop_index(
        op.f("ix_approval_activities_action"), table_name="approval_activities"
    )
    op.drop_table("approval_activities")

    op.drop_index(op.f("ix_client_feedback_id"), table_name="client_feedback")
    op.drop_index(
        op.f("ix_client_feedback_feedback_type"), table_name="client_feedback"
    )
    op.drop_index(op.f("ix_client_feedback_approval_id"), table_name="client_feedback")
    op.drop_table("client_feedback")

    op.drop_constraint(None, "client_approvals", type_="unique")
    op.drop_index(op.f("ix_client_approvals_user_id"), table_name="client_approvals")
    op.drop_index(op.f("ix_client_approvals_status"), table_name="client_approvals")
    op.drop_index(op.f("ix_client_approvals_project_id"), table_name="client_approvals")
    op.drop_index(op.f("ix_client_approvals_priority"), table_name="client_approvals")
    op.drop_index(op.f("ix_client_approvals_id"), table_name="client_approvals")
    op.drop_index(op.f("ix_client_approvals_client_id"), table_name="client_approvals")
    op.drop_index(
        op.f("ix_client_approvals_approval_token"), table_name="client_approvals"
    )
    op.drop_index(
        op.f("ix_client_approvals_approvable_type"), table_name="client_approvals"
    )
    op.drop_index(
        op.f("ix_client_approvals_approvable_id"), table_name="client_approvals"
    )
    op.drop_table("client_approvals")
