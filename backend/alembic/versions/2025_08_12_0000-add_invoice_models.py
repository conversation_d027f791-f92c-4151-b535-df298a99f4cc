"""Add invoice models for professional billing

Revision ID: add_invoice_models
Revises: 3a04378d9af1
Create Date: 2025-08-12 00:00:00.000000

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "add_invoice_models"
down_revision = "3a04378d9af1"
branch_labels = None
depends_on = None


def upgrade():
    # Create invoices table
    op.create_table(
        "invoices",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("invoice_number", sa.String(length=50), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("issue_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("due_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("sent_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("viewed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("paid_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("client_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("project_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("subtotal", sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column("tax_rate", sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column("tax_amount", sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column("discount_amount", sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column("total_amount", sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column("currency", sa.String(length=3), nullable=False),
        sa.Column("exchange_rate", sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column("payment_link", sa.String(length=500), nullable=True),
        sa.Column("payment_token", sa.String(length=255), nullable=True),
        sa.Column("payment_reference", sa.String(length=255), nullable=True),
        sa.Column(
            "billing_address", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
        sa.Column(
            "company_details", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("terms_and_conditions", sa.Text(), nullable=True),
        sa.Column("footer_text", sa.Text(), nullable=True),
        sa.Column("pdf_generated", sa.Boolean(), nullable=False),
        sa.Column("pdf_url", sa.String(length=500), nullable=True),
        sa.Column("template_id", sa.String(length=50), nullable=True),
        sa.Column("payment_terms_days", sa.Integer(), nullable=False),
        sa.Column("late_fee_rate", sa.Numeric(precision=5, scale=2), nullable=True),
        sa.ForeignKeyConstraint(
            ["client_id"],
            ["clients.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_invoices_client_id"), "invoices", ["client_id"], unique=False
    )
    op.create_index(op.f("ix_invoices_id"), "invoices", ["id"], unique=False)
    op.create_index(
        op.f("ix_invoices_invoice_number"), "invoices", ["invoice_number"], unique=True
    )
    op.create_index(
        op.f("ix_invoices_project_id"), "invoices", ["project_id"], unique=False
    )
    op.create_index(op.f("ix_invoices_status"), "invoices", ["status"], unique=False)
    op.create_index(op.f("ix_invoices_user_id"), "invoices", ["user_id"], unique=False)
    op.create_index(
        op.f("ix_invoices_payment_token"), "invoices", ["payment_token"], unique=True
    )

    # Create invoice_items table
    op.create_table(
        "invoice_items",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("invoice_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column("quantity", sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column("unit_price", sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column("total_price", sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column("item_type", sa.String(length=50), nullable=False),
        sa.Column("time_entry_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("milestone_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("billing_status", sa.String(length=50), nullable=False),
        sa.Column("hours_worked", sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column("hourly_rate", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("work_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("sequence_number", sa.Integer(), nullable=False),
        sa.Column("group_name", sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(
            ["invoice_id"],
            ["invoices.id"],
        ),
        sa.ForeignKeyConstraint(
            ["milestone_id"],
            ["project_milestones.id"],
        ),
        sa.ForeignKeyConstraint(
            ["time_entry_id"],
            ["time_entries.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_invoice_items_billing_status"),
        "invoice_items",
        ["billing_status"],
        unique=False,
    )
    op.create_index(op.f("ix_invoice_items_id"), "invoice_items", ["id"], unique=False)
    op.create_index(
        op.f("ix_invoice_items_invoice_id"),
        "invoice_items",
        ["invoice_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_invoice_items_item_type"), "invoice_items", ["item_type"], unique=False
    )
    op.create_index(
        op.f("ix_invoice_items_milestone_id"),
        "invoice_items",
        ["milestone_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_invoice_items_time_entry_id"),
        "invoice_items",
        ["time_entry_id"],
        unique=False,
    )

    # Create payment_transactions table
    op.create_table(
        "payment_transactions",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("invoice_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("transaction_reference", sa.String(length=255), nullable=False),
        sa.Column("payment_method", sa.String(length=50), nullable=False),
        sa.Column("payment_gateway", sa.String(length=50), nullable=False),
        sa.Column("amount", sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column("currency", sa.String(length=3), nullable=False),
        sa.Column("exchange_rate", sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column(
            "gateway_response", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
        sa.Column("gateway_transaction_id", sa.String(length=255), nullable=True),
        sa.Column("initiated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("failed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("gateway_fee", sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column("net_amount", sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column("customer_email", sa.String(length=255), nullable=True),
        sa.Column("customer_phone", sa.String(length=50), nullable=True),
        sa.ForeignKeyConstraint(
            ["invoice_id"],
            ["invoices.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_payment_transactions_gateway_transaction_id"),
        "payment_transactions",
        ["gateway_transaction_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_payment_transactions_id"), "payment_transactions", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_payment_transactions_invoice_id"),
        "payment_transactions",
        ["invoice_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_payment_transactions_status"),
        "payment_transactions",
        ["status"],
        unique=False,
    )
    op.create_index(
        op.f("ix_payment_transactions_transaction_reference"),
        "payment_transactions",
        ["transaction_reference"],
        unique=True,
    )

    # Set default values for new columns
    op.execute("UPDATE invoices SET status = 'draft' WHERE status IS NULL")
    op.execute("UPDATE invoices SET subtotal = 0.00 WHERE subtotal IS NULL")
    op.execute("UPDATE invoices SET tax_rate = 0.00 WHERE tax_rate IS NULL")
    op.execute("UPDATE invoices SET tax_amount = 0.00 WHERE tax_amount IS NULL")
    op.execute(
        "UPDATE invoices SET discount_amount = 0.00 WHERE discount_amount IS NULL"
    )
    op.execute("UPDATE invoices SET total_amount = 0.00 WHERE total_amount IS NULL")
    op.execute("UPDATE invoices SET currency = 'USD' WHERE currency IS NULL")
    op.execute("UPDATE invoices SET pdf_generated = false WHERE pdf_generated IS NULL")
    op.execute(
        "UPDATE invoices SET payment_terms_days = 30 WHERE payment_terms_days IS NULL"
    )

    op.execute("UPDATE invoice_items SET quantity = 1.00 WHERE quantity IS NULL")
    op.execute("UPDATE invoice_items SET item_type = 'custom' WHERE item_type IS NULL")
    op.execute(
        "UPDATE invoice_items SET billing_status = 'billed' WHERE billing_status IS NULL"
    )
    op.execute(
        "UPDATE invoice_items SET sequence_number = 1 WHERE sequence_number IS NULL"
    )

    op.execute(
        "UPDATE payment_transactions SET payment_gateway = 'paystack' WHERE payment_gateway IS NULL"
    )
    op.execute(
        "UPDATE payment_transactions SET status = 'pending' WHERE status IS NULL"
    )


def downgrade():
    # Drop tables in reverse order
    op.drop_table("payment_transactions")
    op.drop_table("invoice_items")
    op.drop_table("invoices")
