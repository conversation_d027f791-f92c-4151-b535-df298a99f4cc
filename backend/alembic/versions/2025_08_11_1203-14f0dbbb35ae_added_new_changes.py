"""Added new changes

Revision ID: 14f0dbbb35ae
Revises: add_advanced_timer_fields
Create Date: 2025-08-11 12:03:30.991372

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "14f0dbbb35ae"
down_revision = "add_advanced_timer_fields"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_time_entries_last_heartbeat", table_name="time_entries")
    op.drop_index("ix_time_entries_timer_device_id", table_name="time_entries")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "ix_time_entries_timer_device_id",
        "time_entries",
        ["timer_device_id"],
        unique=False,
    )
    op.create_index(
        "ix_time_entries_last_heartbeat",
        "time_entries",
        ["last_heartbeat"],
        unique=False,
    )
    # ### end Alembic commands ###
