"""Rename client column to client_name to avoid conflicts

Revision ID: d2dbc7a9c304
Revises: f8be954ce15e
Create Date: 2025-09-02 18:52:25.323748

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd2dbc7a9c304'
down_revision = 'f8be954ce15e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.alter_column('projects', 'client', new_column_name='client_name')


def downgrade() -> None:
    op.alter_column('projects', 'client_name', new_column_name='client')