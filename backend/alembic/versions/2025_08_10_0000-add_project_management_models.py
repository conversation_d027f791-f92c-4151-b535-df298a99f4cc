"""Add project management models

Revision ID: 2025_08_10_0000
Revises: d034e7e8aa01
Create Date: 2025-08-10 00:00:00.000000

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "2025_08_10_0000"
down_revision = "d034e7e8aa01"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Create projects table
    op.create_table(
        "projects",
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("client_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("billing_type", sa.String(length=50), nullable=False),
        sa.Column("total_budget", sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column("estimated_hours", sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column("hourly_rate", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("start_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("end_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("deadline", sa.DateTime(timezone=True), nullable=True),
        sa.Column("is_billable", sa.Boolean(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["client_id"], ["clients.id"]),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_projects_billing_type"), "projects", ["billing_type"], unique=False
    )
    op.create_index(
        op.f("ix_projects_client_id"), "projects", ["client_id"], unique=False
    )
    op.create_index(op.f("ix_projects_id"), "projects", ["id"], unique=False)
    op.create_index(op.f("ix_projects_status"), "projects", ["status"], unique=False)
    op.create_index(op.f("ix_projects_title"), "projects", ["title"], unique=False)
    op.create_index(op.f("ix_projects_user_id"), "projects", ["user_id"], unique=False)

    # Create project_milestones table
    op.create_table(
        "project_milestones",
        sa.Column("project_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("title", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("estimated_hours", sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column("payment_amount", sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column("due_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("sequence_number", sa.Integer(), nullable=False),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["project_id"], ["projects.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_project_milestones_id"), "project_milestones", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_project_milestones_project_id"),
        "project_milestones",
        ["project_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_project_milestones_status"),
        "project_milestones",
        ["status"],
        unique=False,
    )

    # Create time_entries table
    op.create_table(
        "time_entries",
        sa.Column("project_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("duration_minutes", sa.Integer(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("task_name", sa.String(length=255), nullable=True),
        sa.Column("hourly_rate", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("billable_amount", sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column("is_billable", sa.Boolean(), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("work_date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["project_id"], ["projects.id"]),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_time_entries_id"), "time_entries", ["id"], unique=False)
    op.create_index(
        op.f("ix_time_entries_project_id"), "time_entries", ["project_id"], unique=False
    )
    op.create_index(
        op.f("ix_time_entries_status"), "time_entries", ["status"], unique=False
    )
    op.create_index(
        op.f("ix_time_entries_user_id"), "time_entries", ["user_id"], unique=False
    )
    op.create_index(
        op.f("ix_time_entries_work_date"), "time_entries", ["work_date"], unique=False
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Drop time_entries table
    op.drop_index(op.f("ix_time_entries_work_date"), table_name="time_entries")
    op.drop_index(op.f("ix_time_entries_user_id"), table_name="time_entries")
    op.drop_index(op.f("ix_time_entries_status"), table_name="time_entries")
    op.drop_index(op.f("ix_time_entries_project_id"), table_name="time_entries")
    op.drop_index(op.f("ix_time_entries_id"), table_name="time_entries")
    op.drop_table("time_entries")

    # Drop project_milestones table
    op.drop_index(op.f("ix_project_milestones_status"), table_name="project_milestones")
    op.drop_index(
        op.f("ix_project_milestones_project_id"), table_name="project_milestones"
    )
    op.drop_index(op.f("ix_project_milestones_id"), table_name="project_milestones")
    op.drop_table("project_milestones")

    # Drop projects table
    op.drop_index(op.f("ix_projects_user_id"), table_name="projects")
    op.drop_index(op.f("ix_projects_title"), table_name="projects")
    op.drop_index(op.f("ix_projects_status"), table_name="projects")
    op.drop_index(op.f("ix_projects_id"), table_name="projects")
    op.drop_index(op.f("ix_projects_client_id"), table_name="projects")
    op.drop_index(op.f("ix_projects_billing_type"), table_name="projects")
    op.drop_table("projects")

    # ### end Alembic commands ###
