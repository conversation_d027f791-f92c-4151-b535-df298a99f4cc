"""Add comprehensive database indexes for query optimization

Revision ID: add_query_optimization_indexes
Revises: multi_gateway_revenue
Create Date: 2025-01-20 00:00:00.000000

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "add_query_optimization_indexes"
down_revision = "800011175a58"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Add strategic database indexes for query optimization.
    These indexes are designed to optimize the most frequently accessed data patterns.
    """

    # ========================================
    # USER-RELATED OPTIMIZATIONS
    # ========================================

    # Optimize user authentication queries
    op.create_index(
        "ix_users_email_is_active", "users", ["email", "is_active"], unique=False
    )

    # Optimize user session cleanup queries
    op.create_index(
        "ix_user_sessions_expires_at_is_active",
        "user_sessions",
        ["expires_at", "is_active"],
        unique=False,
    )

    # Optimize user settings lookups
    op.create_index(
        "ix_user_settings_user_id_theme",
        "user_settings",
        ["user_id", "theme"],
        unique=False,
    )

    # ========================================
    # PROJECT-RELATED OPTIMIZATIONS
    # ========================================

    # Optimize project dashboard queries (most common)
    op.create_index(
        "ix_projects_user_id_is_active_status",
        "projects",
        ["user_id", "is_active", "status"],
        unique=False,
    )

    # Optimize project filtering by date ranges
    op.create_index(
        "ix_projects_user_id_start_date",
        "projects",
        ["user_id", "start_date"],
        unique=False,
    )

    op.create_index(
        "ix_projects_user_id_deadline",
        "projects",
        ["user_id", "deadline"],
        unique=False,
    )

    # Optimize project search by title (using B-tree for UUID compatibility)
    op.create_index(
        "ix_projects_user_id_title", "projects", ["user_id", "title"], unique=False
    )

    # ========================================
    # TIME ENTRY OPTIMIZATIONS
    # ========================================

    # Optimize time tracking dashboard queries
    op.create_index(
        "ix_time_entries_user_id_is_timer_active",
        "time_entries",
        ["user_id", "is_timer_active"],
        unique=False,
    )

    # Optimize time entry reporting by date ranges
    op.create_index(
        "ix_time_entries_user_id_work_date_status",
        "time_entries",
        ["user_id", "work_date", "status"],
        unique=False,
    )

    # Optimize billable time calculations
    op.create_index(
        "ix_time_entries_project_id_is_billable_billing_status",
        "time_entries",
        ["project_id", "is_billable", "billing_status"],
        unique=False,
    )

    # Optimize time entry analytics
    op.create_index(
        "ix_time_entries_user_id_created_at",
        "time_entries",
        ["user_id", "created_at"],
        unique=False,
    )

    # ========================================
    # CLIENT-RELATED OPTIMIZATIONS
    # ========================================

    # Optimize client search functionality (using B-tree for UUID compatibility)
    op.create_index(
        "ix_clients_user_id_name", "clients", ["user_id", "name"], unique=False
    )

    # Optimize client portal access
    op.create_index(
        "ix_clients_portal_enabled_is_active",
        "clients",
        ["portal_enabled", "is_active"],
        unique=False,
    )

    # ========================================
    # INVOICE OPTIMIZATIONS
    # ========================================

    # Optimize invoice dashboard queries
    op.create_index(
        "ix_invoices_user_id_status_due_date",
        "invoices",
        ["user_id", "status", "due_date"],
        unique=False,
    )

    # Optimize overdue invoice queries
    op.create_index(
        "ix_invoices_status_due_date", "invoices", ["status", "due_date"], unique=False
    )

    # Optimize payment processing queries
    op.create_index(
        "ix_invoices_payment_gateway_settlement_status",
        "invoices",
        ["payment_gateway", "settlement_status"],
        unique=False,
    )

    # Optimize revenue analytics
    op.create_index(
        "ix_invoices_user_id_paid_at", "invoices", ["user_id", "paid_at"], unique=False
    )

    # ========================================
    # MILESTONE OPTIMIZATIONS
    # ========================================

    # Optimize milestone tracking
    op.create_index(
        "ix_project_milestones_project_id_status_due_date",
        "project_milestones",
        ["project_id", "status", "due_date"],
        unique=False,
    )

    # Optimize milestone billing
    op.create_index(
        "ix_project_milestones_billing_status_completed_at",
        "project_milestones",
        ["billing_status", "completed_at"],
        unique=False,
    )

    # ========================================
    # ACTIVITY LOG OPTIMIZATIONS
    # ========================================

    # Optimize activity feed queries
    op.create_index(
        "ix_activity_logs_user_id_created_at_action",
        "activity_logs",
        ["user_id", "created_at", "action"],
        unique=False,
    )

    # Optimize entity-specific activity queries
    op.create_index(
        "ix_activity_logs_entity_type_entity_id_created_at",
        "activity_logs",
        ["entity_type", "entity_id", "created_at"],
        unique=False,
    )

    # ========================================
    # PAYMENT TRANSACTION OPTIMIZATIONS
    # ========================================

    # Optimize payment tracking
    op.create_index(
        "ix_payment_transactions_invoice_id_status",
        "payment_transactions",
        ["invoice_id", "status"],
        unique=False,
    )

    # Optimize payment analytics
    op.create_index(
        "ix_payment_transactions_payment_gateway_initiated_at",
        "payment_transactions",
        ["payment_gateway", "initiated_at"],
        unique=False,
    )

    # ========================================
    # SOFT DELETE OPTIMIZATIONS
    # ========================================

    # Add partial indexes for soft delete pattern (only index non-deleted records)
    op.create_index(
        "ix_projects_user_id_active",
        "projects",
        ["user_id"],
        unique=False,
        postgresql_where=sa.text("deleted_at IS NULL"),
    )

    op.create_index(
        "ix_clients_user_id_active",
        "clients",
        ["user_id"],
        unique=False,
        postgresql_where=sa.text("deleted_at IS NULL"),
    )

    op.create_index(
        "ix_time_entries_project_id_active",
        "time_entries",
        ["project_id"],
        unique=False,
        postgresql_where=sa.text("deleted_at IS NULL"),
    )

    op.create_index(
        "ix_invoices_user_id_active",
        "invoices",
        ["user_id"],
        unique=False,
        postgresql_where=sa.text("deleted_at IS NULL"),
    )


def downgrade() -> None:
    """
    Remove the optimization indexes.
    """

    # Remove soft delete partial indexes
    op.drop_index("ix_invoices_user_id_active", table_name="invoices")
    op.drop_index("ix_time_entries_project_id_active", table_name="time_entries")
    op.drop_index("ix_clients_user_id_active", table_name="clients")
    op.drop_index("ix_projects_user_id_active", table_name="projects")

    # Remove payment transaction indexes
    op.drop_index(
        "ix_payment_transactions_payment_gateway_initiated_at",
        table_name="payment_transactions",
    )
    op.drop_index(
        "ix_payment_transactions_invoice_id_status", table_name="payment_transactions"
    )

    # Remove activity log indexes
    op.drop_index(
        "ix_activity_logs_entity_type_entity_id_created_at", table_name="activity_logs"
    )
    op.drop_index(
        "ix_activity_logs_user_id_created_at_action", table_name="activity_logs"
    )

    # Remove milestone indexes
    op.drop_index(
        "ix_project_milestones_billing_status_completed_at",
        table_name="project_milestones",
    )
    op.drop_index(
        "ix_project_milestones_project_id_status_due_date",
        table_name="project_milestones",
    )

    # Remove invoice indexes
    op.drop_index("ix_invoices_user_id_paid_at", table_name="invoices")
    op.drop_index(
        "ix_invoices_payment_gateway_settlement_status", table_name="invoices"
    )
    op.drop_index("ix_invoices_status_due_date", table_name="invoices")
    op.drop_index("ix_invoices_user_id_status_due_date", table_name="invoices")

    # Remove client indexes
    op.drop_index("ix_clients_portal_enabled_is_active", table_name="clients")
    op.drop_index("ix_clients_user_id_name", table_name="clients")

    # Remove time entry indexes
    op.drop_index("ix_time_entries_user_id_created_at", table_name="time_entries")
    op.drop_index(
        "ix_time_entries_project_id_is_billable_billing_status",
        table_name="time_entries",
    )
    op.drop_index("ix_time_entries_user_id_work_date_status", table_name="time_entries")
    op.drop_index("ix_time_entries_user_id_is_timer_active", table_name="time_entries")

    # Remove project indexes
    op.drop_index("ix_projects_user_id_title", table_name="projects")
    op.drop_index("ix_projects_user_id_deadline", table_name="projects")
    op.drop_index("ix_projects_user_id_start_date", table_name="projects")
    op.drop_index("ix_projects_user_id_is_active_status", table_name="projects")

    # Remove user-related indexes
    op.drop_index("ix_user_settings_user_id_theme", table_name="user_settings")
    op.drop_index("ix_user_sessions_expires_at_is_active", table_name="user_sessions")
    op.drop_index("ix_users_email_is_active", table_name="users")
