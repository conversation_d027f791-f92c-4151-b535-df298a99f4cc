"""Add OAuth fields to User model

Revision ID: 71d2df5e5654
Revises: e2491116ed4d
Create Date: 2025-09-01 17:53:56.473831

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '71d2df5e5654'
down_revision = 'e2491116ed4d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('payment_error_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('gateway', sa.String(length=50), nullable=False),
    sa.Column('error_type', sa.String(length=50), nullable=False),
    sa.Column('severity', sa.String(length=20), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('reference', sa.String(length=255), nullable=True),
    sa.Column('retryable', sa.<PERSON>(), nullable=False),
    sa.Column('retry_after', sa.Integer(), nullable=True),
    sa.Column('gateway_response', sa.JSON(), nullable=True),
    sa.Column('context', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payment_error_logs_created_at'), 'payment_error_logs', ['created_at'], unique=False)
    op.create_index(op.f('ix_payment_error_logs_error_type'), 'payment_error_logs', ['error_type'], unique=False)
    op.create_index(op.f('ix_payment_error_logs_gateway'), 'payment_error_logs', ['gateway'], unique=False)
    op.create_index(op.f('ix_payment_error_logs_reference'), 'payment_error_logs', ['reference'], unique=False)
    op.create_index(op.f('ix_payment_error_logs_severity'), 'payment_error_logs', ['severity'], unique=False)
    op.drop_index('ix_activity_logs_entity_type_entity_id_created_at', table_name='activity_logs')
    op.drop_index('ix_activity_logs_user_id_created_at_action', table_name='activity_logs')
    op.create_index('ix_activity_logs_entity_type_entity_id', 'activity_logs', ['entity_type', 'entity_id'], unique=False)
    op.create_index('ix_activity_logs_user_id_action', 'activity_logs', ['user_id', 'action'], unique=False)
    op.create_index('ix_activity_logs_user_id_entity_type', 'activity_logs', ['user_id', 'entity_type'], unique=False)
    op.create_index('ix_client_approvals_approvable_type_id', 'client_approvals', ['approvable_type', 'approvable_id'], unique=False)
    op.create_index('ix_client_approvals_client_id_status', 'client_approvals', ['client_id', 'status'], unique=False)
    op.create_index('ix_client_approvals_project_id_status', 'client_approvals', ['project_id', 'status'], unique=False)
    op.create_index('ix_client_approvals_user_id_status', 'client_approvals', ['user_id', 'status'], unique=False)
    op.drop_index('ix_clients_portal_enabled_is_active', table_name='clients')
    op.drop_index('ix_clients_user_id_active', table_name='clients', postgresql_where='(deleted_at IS NULL)')
    op.create_index('ix_clients_user_id_company', 'clients', ['user_id', 'company'], unique=False)
    op.create_index('ix_clients_user_id_email', 'clients', ['user_id', 'email'], unique=False)
    op.create_index('ix_clients_user_id_is_active', 'clients', ['user_id', 'is_active'], unique=False)
    op.drop_index('ix_invoices_payment_gateway_settlement_status', table_name='invoices')
    op.drop_index('ix_invoices_status_due_date', table_name='invoices')
    op.drop_index('ix_invoices_user_id_active', table_name='invoices', postgresql_where='(deleted_at IS NULL)')
    op.drop_index('ix_invoices_user_id_paid_at', table_name='invoices')
    op.drop_index('ix_invoices_user_id_status_due_date', table_name='invoices')
    op.create_index('ix_invoices_client_id_status', 'invoices', ['client_id', 'status'], unique=False)
    op.create_index('ix_invoices_project_id_status', 'invoices', ['project_id', 'status'], unique=False)
    op.create_index('ix_invoices_user_id_due_date', 'invoices', ['user_id', 'due_date'], unique=False)
    op.create_index('ix_invoices_user_id_status', 'invoices', ['user_id', 'status'], unique=False)
    op.drop_index('ix_payment_transactions_invoice_id_status', table_name='payment_transactions')
    op.drop_index('ix_payment_transactions_payment_gateway_initiated_at', table_name='payment_transactions')
    op.drop_index('ix_project_milestones_billing_status_completed_at', table_name='project_milestones')
    op.drop_index('ix_project_milestones_project_id_status_due_date', table_name='project_milestones')
    op.drop_index('ix_projects_user_id_active', table_name='projects', postgresql_where='(deleted_at IS NULL)')
    op.drop_index('ix_projects_user_id_deadline', table_name='projects')
    op.drop_index('ix_projects_user_id_is_active_status', table_name='projects')
    op.drop_index('ix_projects_user_id_start_date', table_name='projects')
    op.drop_index('ix_projects_user_id_title', table_name='projects')
    op.create_index('ix_projects_client_id_status', 'projects', ['client_id', 'status'], unique=False)
    op.create_index('ix_projects_user_id_billing_type', 'projects', ['user_id', 'billing_type'], unique=False)
    op.create_index('ix_projects_user_id_status', 'projects', ['user_id', 'status'], unique=False)
    op.drop_index('ix_time_entries_project_id_active', table_name='time_entries', postgresql_where='(deleted_at IS NULL)')
    op.drop_index('ix_time_entries_project_id_is_billable_billing_status', table_name='time_entries')
    op.drop_index('ix_time_entries_user_id_created_at', table_name='time_entries')
    op.drop_index('ix_time_entries_user_id_is_timer_active', table_name='time_entries')
    op.drop_index('ix_time_entries_user_id_work_date_status', table_name='time_entries')
    op.create_index('ix_time_entries_project_id_status', 'time_entries', ['project_id', 'status'], unique=False)
    op.create_index('ix_time_entries_project_id_work_date', 'time_entries', ['project_id', 'work_date'], unique=False)
    op.create_index('ix_time_entries_user_id_status', 'time_entries', ['user_id', 'status'], unique=False)
    op.create_index('ix_time_entries_user_id_work_date', 'time_entries', ['user_id', 'work_date'], unique=False)
    op.add_column('user_payout_settings', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.create_index(op.f('ix_user_payout_settings_id'), 'user_payout_settings', ['id'], unique=False)
    op.drop_index('ix_user_sessions_expires_at_is_active', table_name='user_sessions')
    op.create_index('ix_user_sessions_is_active', 'user_sessions', ['is_active'], unique=False)
    op.create_index('ix_user_sessions_user_id', 'user_sessions', ['user_id'], unique=False)
    op.drop_index('ix_user_settings_user_id_theme', table_name='user_settings')
    op.create_index('ix_user_settings_user_id', 'user_settings', ['user_id'], unique=False)
    op.add_column('users', sa.Column('oauth_provider', sa.String(length=50), nullable=True))
    op.add_column('users', sa.Column('oauth_id', sa.String(length=255), nullable=True))
    op.drop_index('ix_users_email_is_active', table_name='users')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_users_email_is_active', 'users', ['email', 'is_active'], unique=False)
    op.drop_column('users', 'oauth_id')
    op.drop_column('users', 'oauth_provider')
    op.drop_index('ix_user_settings_user_id', table_name='user_settings')
    op.create_index('ix_user_settings_user_id_theme', 'user_settings', ['user_id', 'theme'], unique=False)
    op.drop_index('ix_user_sessions_user_id', table_name='user_sessions')
    op.drop_index('ix_user_sessions_is_active', table_name='user_sessions')
    op.create_index('ix_user_sessions_expires_at_is_active', 'user_sessions', ['expires_at', 'is_active'], unique=False)
    op.drop_index(op.f('ix_user_payout_settings_id'), table_name='user_payout_settings')
    op.drop_column('user_payout_settings', 'deleted_at')
    op.drop_index('ix_time_entries_user_id_work_date', table_name='time_entries')
    op.drop_index('ix_time_entries_user_id_status', table_name='time_entries')
    op.drop_index('ix_time_entries_project_id_work_date', table_name='time_entries')
    op.drop_index('ix_time_entries_project_id_status', table_name='time_entries')
    op.create_index('ix_time_entries_user_id_work_date_status', 'time_entries', ['user_id', 'work_date', 'status'], unique=False)
    op.create_index('ix_time_entries_user_id_is_timer_active', 'time_entries', ['user_id', 'is_timer_active'], unique=False)
    op.create_index('ix_time_entries_user_id_created_at', 'time_entries', ['user_id', 'created_at'], unique=False)
    op.create_index('ix_time_entries_project_id_is_billable_billing_status', 'time_entries', ['project_id', 'is_billable', 'billing_status'], unique=False)
    op.create_index('ix_time_entries_project_id_active', 'time_entries', ['project_id'], unique=False, postgresql_where='(deleted_at IS NULL)')
    op.drop_index('ix_projects_user_id_status', table_name='projects')
    op.drop_index('ix_projects_user_id_billing_type', table_name='projects')
    op.drop_index('ix_projects_client_id_status', table_name='projects')
    op.create_index('ix_projects_user_id_title', 'projects', ['user_id', 'title'], unique=False)
    op.create_index('ix_projects_user_id_start_date', 'projects', ['user_id', 'start_date'], unique=False)
    op.create_index('ix_projects_user_id_is_active_status', 'projects', ['user_id', 'is_active', 'status'], unique=False)
    op.create_index('ix_projects_user_id_deadline', 'projects', ['user_id', 'deadline'], unique=False)
    op.create_index('ix_projects_user_id_active', 'projects', ['user_id'], unique=False, postgresql_where='(deleted_at IS NULL)')
    op.create_index('ix_project_milestones_project_id_status_due_date', 'project_milestones', ['project_id', 'status', 'due_date'], unique=False)
    op.create_index('ix_project_milestones_billing_status_completed_at', 'project_milestones', ['billing_status', 'completed_at'], unique=False)
    op.create_index('ix_payment_transactions_payment_gateway_initiated_at', 'payment_transactions', ['payment_gateway', 'initiated_at'], unique=False)
    op.create_index('ix_payment_transactions_invoice_id_status', 'payment_transactions', ['invoice_id', 'status'], unique=False)
    op.drop_index('ix_invoices_user_id_status', table_name='invoices')
    op.drop_index('ix_invoices_user_id_due_date', table_name='invoices')
    op.drop_index('ix_invoices_project_id_status', table_name='invoices')
    op.drop_index('ix_invoices_client_id_status', table_name='invoices')
    op.create_index('ix_invoices_user_id_status_due_date', 'invoices', ['user_id', 'status', 'due_date'], unique=False)
    op.create_index('ix_invoices_user_id_paid_at', 'invoices', ['user_id', 'paid_at'], unique=False)
    op.create_index('ix_invoices_user_id_active', 'invoices', ['user_id'], unique=False, postgresql_where='(deleted_at IS NULL)')
    op.create_index('ix_invoices_status_due_date', 'invoices', ['status', 'due_date'], unique=False)
    op.create_index('ix_invoices_payment_gateway_settlement_status', 'invoices', ['payment_gateway', 'settlement_status'], unique=False)
    op.drop_index('ix_clients_user_id_is_active', table_name='clients')
    op.drop_index('ix_clients_user_id_email', table_name='clients')
    op.drop_index('ix_clients_user_id_company', table_name='clients')
    op.create_index('ix_clients_user_id_active', 'clients', ['user_id'], unique=False, postgresql_where='(deleted_at IS NULL)')
    op.create_index('ix_clients_portal_enabled_is_active', 'clients', ['portal_enabled', 'is_active'], unique=False)
    op.drop_index('ix_client_approvals_user_id_status', table_name='client_approvals')
    op.drop_index('ix_client_approvals_project_id_status', table_name='client_approvals')
    op.drop_index('ix_client_approvals_client_id_status', table_name='client_approvals')
    op.drop_index('ix_client_approvals_approvable_type_id', table_name='client_approvals')
    op.drop_index('ix_activity_logs_user_id_entity_type', table_name='activity_logs')
    op.drop_index('ix_activity_logs_user_id_action', table_name='activity_logs')
    op.drop_index('ix_activity_logs_entity_type_entity_id', table_name='activity_logs')
    op.create_index('ix_activity_logs_user_id_created_at_action', 'activity_logs', ['user_id', 'created_at', 'action'], unique=False)
    op.create_index('ix_activity_logs_entity_type_entity_id_created_at', 'activity_logs', ['entity_type', 'entity_id', 'created_at'], unique=False)
    op.drop_index(op.f('ix_payment_error_logs_severity'), table_name='payment_error_logs')
    op.drop_index(op.f('ix_payment_error_logs_reference'), table_name='payment_error_logs')
    op.drop_index(op.f('ix_payment_error_logs_gateway'), table_name='payment_error_logs')
    op.drop_index(op.f('ix_payment_error_logs_error_type'), table_name='payment_error_logs')
    op.drop_index(op.f('ix_payment_error_logs_created_at'), table_name='payment_error_logs')
    op.drop_table('payment_error_logs')
    # ### end Alembic commands ###