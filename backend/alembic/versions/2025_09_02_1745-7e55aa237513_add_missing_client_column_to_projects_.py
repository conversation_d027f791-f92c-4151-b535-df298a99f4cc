"""Add missing client column to projects table

Revision ID: 7e55aa237513
Revises: ec8e41efc433
Create Date: 2025-09-02 17:45:40.190935

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7e55aa237513'
down_revision = 'ec8e41efc433'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column('projects', sa.Column('client', sa.String(length=255), nullable=True))


def downgrade() -> None:
    op.drop_column('projects', 'client')