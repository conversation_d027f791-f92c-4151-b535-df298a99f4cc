"""enhance_activity_logging_for_audit_trail

Revision ID: enhance_activity_logging
Revises: cb8c96bf0456
Create Date: 2025-08-17 00:00:00.000000

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "enhance_activity_logging"
down_revision = "cb8c96bf0456"
branch_labels = None
depends_on = None


def upgrade():
    # Add new columns to activity_logs table
    op.add_column(
        "activity_logs",
        sa.Column("client_id", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.add_column("activity_logs", sa.Column("description", sa.Text(), nullable=True))
    op.add_column(
        "activity_logs", sa.Column("ip_address", sa.String(length=45), nullable=True)
    )
    op.add_column(
        "activity_logs", sa.Column("user_agent", sa.String(length=500), nullable=True)
    )
    op.add_column(
        "activity_logs",
        sa.Column("extra_data", postgresql.JSON(astext_type=sa.Text()), nullable=True),
    )

    # Modify existing columns
    op.alter_column("activity_logs", "user_id", nullable=True)
    op.alter_column("activity_logs", "action", type_=sa.String(length=100))

    # Add foreign key constraint for client_id
    op.create_foreign_key(
        "fk_activity_logs_client_id", "activity_logs", "clients", ["client_id"], ["id"]
    )

    # Add indexes for better performance
    op.create_index("ix_activity_logs_client_id", "activity_logs", ["client_id"])


def downgrade():
    # Remove indexes
    op.drop_index("ix_activity_logs_client_id", "activity_logs")

    # Remove foreign key constraint
    op.drop_constraint(
        "fk_activity_logs_client_id", "activity_logs", type_="foreignkey"
    )

    # Remove new columns
    op.drop_column("activity_logs", "extra_data")
    op.drop_column("activity_logs", "user_agent")
    op.drop_column("activity_logs", "ip_address")
    op.drop_column("activity_logs", "description")
    op.drop_column("activity_logs", "client_id")

    # Revert column changes
    op.alter_column("activity_logs", "user_id", nullable=False)
    op.alter_column("activity_logs", "action", type_=sa.String(length=50))
