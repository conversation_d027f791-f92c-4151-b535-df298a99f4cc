"""Add multi-gateway and platform revenue features

Revision ID: add_multi_gateway_and_platform_revenue
Revises: cb8c96bf0456
Create Date: 2025-08-18 00:00:00.000000

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "multi_gateway_revenue"
down_revision = "cb8c96bf0456"
branch_labels = None
depends_on = None


def upgrade():
    # Add new fields to invoices table
    op.add_column(
        "invoices", sa.Column("payment_gateway", sa.String(length=50), nullable=True)
    )
    op.add_column(
        "invoices",
        sa.Column(
            "settlement_type",
            sa.String(length=20),
            nullable=False,
            server_default="standard",
        ),
    )
    op.add_column(
        "invoices",
        sa.Column(
            "settlement_preference",
            sa.String(length=20),
            nullable=False,
            server_default="auto",
        ),
    )
    op.add_column(
        "invoices",
        sa.Column(
            "instant_settlement_fee", sa.Numeric(precision=10, scale=2), nullable=True
        ),
    )
    op.add_column(
        "invoices",
        sa.Column(
            "platform_fee_amount", sa.Numeric(precision=10, scale=2), nullable=True
        ),
    )
    op.add_column(
        "invoices",
        sa.Column(
            "gateway_fee_amount", sa.Numeric(precision=10, scale=2), nullable=True
        ),
    )
    op.add_column(
        "invoices",
        sa.Column(
            "net_payout_amount", sa.Numeric(precision=10, scale=2), nullable=True
        ),
    )
    op.add_column(
        "invoices",
        sa.Column(
            "settlement_status",
            sa.String(length=20),
            nullable=False,
            server_default="pending",
        ),
    )
    op.add_column(
        "invoices", sa.Column("settled_at", sa.DateTime(timezone=True), nullable=True)
    )

    # Create user_payout_settings table
    op.create_table(
        "user_payout_settings",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "platform_fee_rate",
            sa.Numeric(precision=5, scale=4),
            nullable=False,
            server_default="0.025",
        ),
        sa.Column(
            "fee_tier", sa.String(length=20), nullable=False, server_default="starter"
        ),
        sa.Column(
            "payout_method",
            sa.String(length=50),
            nullable=False,
            server_default="bank_transfer",
        ),
        sa.Column(
            "payout_frequency",
            sa.String(length=20),
            nullable=False,
            server_default="weekly",
        ),
        sa.Column(
            "minimum_payout_amount",
            sa.Numeric(precision=10, scale=2),
            nullable=False,
            server_default="100.00",
        ),
        sa.Column(
            "instant_settlement_enabled",
            sa.Boolean(),
            nullable=False,
            server_default="false",
        ),
        sa.Column("paystack_subaccount_id", sa.String(length=100), nullable=True),
        sa.Column(
            "payout_bank_account", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
        sa.Column(
            "payout_mobile_money", postgresql.JSON(astext_type=sa.Text()), nullable=True
        ),
        sa.Column(
            "auto_settlement", sa.Boolean(), nullable=False, server_default="true"
        ),
        sa.Column("settlement_currency", sa.String(length=3), nullable=True),
        sa.Column(
            "total_revenue_earned",
            sa.Numeric(precision=15, scale=2),
            nullable=False,
            server_default="0.00",
        ),
        sa.Column(
            "total_fees_paid",
            sa.Numeric(precision=15, scale=2),
            nullable=False,
            server_default="0.00",
        ),
        sa.Column("last_payout_date", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create indexes
    op.create_index(
        "ix_user_payout_settings_user_id",
        "user_payout_settings",
        ["user_id"],
        unique=False,
    )
    op.create_index(
        "ix_invoices_payment_gateway", "invoices", ["payment_gateway"], unique=False
    )
    op.create_index(
        "ix_invoices_settlement_status", "invoices", ["settlement_status"], unique=False
    )
    op.create_index("ix_invoices_settled_at", "invoices", ["settled_at"], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index("ix_invoices_settled_at", table_name="invoices")
    op.drop_index("ix_invoices_settlement_status", table_name="invoices")
    op.drop_index("ix_invoices_payment_gateway", table_name="invoices")
    op.drop_index("ix_user_payout_settings_user_id", table_name="user_payout_settings")

    # Drop table
    op.drop_table("user_payout_settings")

    # Remove columns from invoices
    op.drop_column("invoices", "settled_at")
    op.drop_column("invoices", "settlement_status")
    op.drop_column("invoices", "net_payout_amount")
    op.drop_column("invoices", "gateway_fee_amount")
    op.drop_column("invoices", "platform_fee_amount")
    op.drop_column("invoices", "instant_settlement_fee")
    op.drop_column("invoices", "settlement_preference")
    op.drop_column("invoices", "settlement_type")
    op.drop_column("invoices", "payment_gateway")
