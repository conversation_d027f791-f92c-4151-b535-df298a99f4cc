"""Add advanced timer fields to time_entries

Revision ID: add_advanced_timer_fields
Revises: ca49acb9f732
Create Date: 2025-08-11 00:00:00.000000

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "add_advanced_timer_fields"
down_revision = "ca49acb9f732"
branch_labels = None
depends_on = None


def upgrade():
    # Add advanced tracking fields to time_entries table
    op.add_column(
        "time_entries",
        sa.Column("timer_started_from", sa.String(length=50), nullable=True),
    )
    op.add_column(
        "time_entries",
        sa.Column(
            "productivity_score", sa.Numeric(precision=3, scale=1), nullable=True
        ),
    )
    op.add_column(
        "time_entries",
        sa.Column("break_duration_minutes", sa.Integer(), nullable=True, default=0),
    )
    op.add_column("time_entries", sa.Column("tags", sa.Text(), nullable=True))
    op.add_column("time_entries", sa.Column("mood_rating", sa.Integer(), nullable=True))
    op.add_column("time_entries", sa.Column("focus_level", sa.Integer(), nullable=True))
    op.add_column(
        "time_entries",
        sa.Column("interruption_count", sa.Integer(), nullable=True, default=0),
    )

    # Add timer state management fields
    op.add_column(
        "time_entries",
        sa.Column("is_timer_active", sa.Boolean(), nullable=False, default=False),
    )
    op.add_column(
        "time_entries",
        sa.Column("timer_device_id", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "time_entries",
        sa.Column("last_heartbeat", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column(
        "time_entries",
        sa.Column("timer_paused_at", sa.DateTime(timezone=True), nullable=True),
    )
    op.add_column(
        "time_entries",
        sa.Column("total_pause_duration", sa.Integer(), nullable=True, default=0),
    )

    # Create indexes for performance
    op.create_index(
        "ix_time_entries_is_timer_active", "time_entries", ["is_timer_active"]
    )
    op.create_index(
        "ix_time_entries_timer_device_id", "time_entries", ["timer_device_id"]
    )
    op.create_index(
        "ix_time_entries_last_heartbeat", "time_entries", ["last_heartbeat"]
    )


def downgrade():
    # Remove indexes
    op.drop_index("ix_time_entries_last_heartbeat", table_name="time_entries")
    op.drop_index("ix_time_entries_timer_device_id", table_name="time_entries")
    op.drop_index("ix_time_entries_is_timer_active", table_name="time_entries")

    # Remove columns
    op.drop_column("time_entries", "total_pause_duration")
    op.drop_column("time_entries", "timer_paused_at")
    op.drop_column("time_entries", "last_heartbeat")
    op.drop_column("time_entries", "timer_device_id")
    op.drop_column("time_entries", "is_timer_active")
    op.drop_column("time_entries", "interruption_count")
    op.drop_column("time_entries", "focus_level")
    op.drop_column("time_entries", "mood_rating")
    op.drop_column("time_entries", "tags")
    op.drop_column("time_entries", "break_duration_minutes")
    op.drop_column("time_entries", "productivity_score")
    op.drop_column("time_entries", "timer_started_from")
