"""Add indexes for payment gateway and payout optimization

Revision ID: add_payment_indexes_optimization
Revises: multi_gateway_revenue
Create Date: 2025-08-18 15:30:00.000000

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "add_payment_indexes_optimization"
down_revision = "multi_gateway_revenue"
branch_labels = None
depends_on = None


def upgrade():
    # Add indexes to invoices table for payment optimization
    # Note: ix_invoices_payment_gateway, ix_invoices_settlement_status, and ix_invoices_settled_at
    # are already created in the multi_gateway_revenue migration
    op.create_index("ix_invoices_payment_reference", "invoices", ["payment_reference"])
    op.create_index("ix_invoices_currency", "invoices", ["currency"])
    op.create_index(
        "ix_invoices_user_id_created_at", "invoices", ["user_id", "created_at"]
    )
    op.create_index(
        "ix_invoices_status_created_at", "invoices", ["status", "created_at"]
    )
    op.create_index(
        "ix_invoices_currency_created_at", "invoices", ["currency", "created_at"]
    )

    # Add indexes to user_payout_settings table for payout optimization
    op.create_index(
        "ix_user_payout_settings_fee_tier", "user_payout_settings", ["fee_tier"]
    )
    op.create_index(
        "ix_user_payout_settings_payout_method",
        "user_payout_settings",
        ["payout_method"],
    )
    op.create_index(
        "ix_user_payout_settings_last_payout_date",
        "user_payout_settings",
        ["last_payout_date"],
    )
    op.create_index(
        "ix_user_payout_settings_total_revenue_earned",
        "user_payout_settings",
        ["total_revenue_earned"],
    )


def downgrade():
    # Remove indexes from user_payout_settings table
    op.drop_index(
        "ix_user_payout_settings_total_revenue_earned",
        table_name="user_payout_settings",
    )
    op.drop_index(
        "ix_user_payout_settings_last_payout_date", table_name="user_payout_settings"
    )
    op.drop_index(
        "ix_user_payout_settings_payout_method", table_name="user_payout_settings"
    )
    op.drop_index("ix_user_payout_settings_fee_tier", table_name="user_payout_settings")

    # Remove indexes from invoices table
    op.drop_index("ix_invoices_currency_created_at", table_name="invoices")
    op.drop_index("ix_invoices_status_created_at", table_name="invoices")
    op.drop_index("ix_invoices_user_id_created_at", table_name="invoices")
    op.drop_index("ix_invoices_currency", table_name="invoices")
    op.drop_index("ix_invoices_payment_reference", table_name="invoices")
    # Note: ix_invoices_settled_at, ix_invoices_settlement_status, and ix_invoices_payment_gateway
    # are handled by the multi_gateway_revenue migration
