"""Fix datetime and numeric field types

Revision ID: fix_datetime_fields
Revises: 2025_08_13_0000-add_approval_models
Create Date: 2025-08-15 00:00:00.000000

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "fix_datetime_fields"
down_revision = "add_approval_models"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Fix datetime and numeric field types"""

    # Fix User model datetime fields
    op.alter_column(
        "users",
        "email_verified_at",
        existing_type=sa.String(),
        type_=sa.DateTime(timezone=True),
        existing_nullable=True,
        postgresql_using="email_verified_at::timestamp with time zone",
    )

    # Fix UserSession datetime fields
    op.alter_column(
        "user_sessions",
        "expires_at",
        existing_type=sa.String(),
        type_=sa.DateTime(timezone=True),
        existing_nullable=False,
        postgresql_using="expires_at::timestamp with time zone",
    )

    # Fix UserSettings numeric fields
    op.alter_column(
        "user_settings",
        "invoice_number_start",
        existing_type=sa.String(),
        type_=sa.Integer(),
        existing_nullable=False,
        postgresql_using="invoice_number_start::integer",
    )

    op.alter_column(
        "user_settings",
        "payment_terms_days",
        existing_type=sa.String(),
        type_=sa.Integer(),
        existing_nullable=False,
        postgresql_using="payment_terms_days::integer",
    )


def downgrade() -> None:
    """Revert datetime and numeric field types"""

    # Revert UserSettings numeric fields
    op.alter_column(
        "user_settings",
        "payment_terms_days",
        existing_type=sa.Integer(),
        type_=sa.String(),
        existing_nullable=False,
        postgresql_using="payment_terms_days::text",
    )

    op.alter_column(
        "user_settings",
        "invoice_number_start",
        existing_type=sa.Integer(),
        type_=sa.String(),
        existing_nullable=False,
        postgresql_using="invoice_number_start::text",
    )

    # Revert UserSession datetime fields
    op.alter_column(
        "user_sessions",
        "expires_at",
        existing_type=sa.DateTime(timezone=True),
        type_=sa.String(),
        existing_nullable=False,
        postgresql_using="expires_at::text",
    )

    # Revert User datetime fields
    op.alter_column(
        "users",
        "email_verified_at",
        existing_type=sa.DateTime(timezone=True),
        type_=sa.String(),
        existing_nullable=True,
        postgresql_using="email_verified_at::text",
    )
