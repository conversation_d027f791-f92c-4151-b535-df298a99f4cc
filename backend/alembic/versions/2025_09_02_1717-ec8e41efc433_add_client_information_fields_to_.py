"""Add client information fields to projects table

Revision ID: ec8e41efc433
Revises: fcb74351de41
Create Date: 2025-09-02 17:17:12.013178

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ec8e41efc433'
down_revision = 'fcb74351de41'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('projects', sa.Column('contact_person', sa.String(length=255), nullable=True))
    op.add_column('projects', sa.Column('client_email', sa.String(length=255), nullable=True))
    op.add_column('projects', sa.Column('client_phone', sa.String(length=50), nullable=True))
    op.add_column('projects', sa.Column('client_address', sa.Text(), nullable=True))
    op.add_column('projects', sa.Column('client_industry', sa.String(length=255), nullable=True))
    op.add_column('projects', sa.Column('client_company_size', sa.String(length=50), nullable=True))
    op.add_column('projects', sa.Column('client_priority', sa.String(length=20), nullable=True))
    op.add_column('projects', sa.Column('client_status', sa.String(length=50), nullable=True))
    op.add_column('projects', sa.Column('client_notes', sa.Text(), nullable=True))
    op.drop_index('ix_users_active_workspace_id', table_name='users')
    op.drop_constraint('users_active_workspace_id_fkey', 'users', type_='foreignkey')
    op.drop_index('ix_workspaces_deleted_at', table_name='workspaces')
    op.drop_index('ix_workspaces_user_id', table_name='workspaces')
    op.drop_index('ix_workspaces_user_id_is_active', table_name='workspaces')
    op.drop_index('ix_workspaces_user_id_is_default', table_name='workspaces')
    op.drop_constraint('workspaces_user_id_fkey', 'workspaces', type_='foreignkey')
    op.create_foreign_key(None, 'workspaces', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'workspaces', type_='foreignkey')
    op.create_foreign_key('workspaces_user_id_fkey', 'workspaces', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_index('ix_workspaces_user_id_is_default', 'workspaces', ['user_id', 'is_default'], unique=False)
    op.create_index('ix_workspaces_user_id_is_active', 'workspaces', ['user_id', 'is_active'], unique=False)
    op.create_index('ix_workspaces_user_id', 'workspaces', ['user_id'], unique=False)
    op.create_index('ix_workspaces_deleted_at', 'workspaces', ['deleted_at'], unique=False)
    op.create_foreign_key('users_active_workspace_id_fkey', 'users', 'workspaces', ['active_workspace_id'], ['id'])
    op.create_index('ix_users_active_workspace_id', 'users', ['active_workspace_id'], unique=False)
    op.drop_column('projects', 'client_notes')
    op.drop_column('projects', 'client_status')
    op.drop_column('projects', 'client_priority')
    op.drop_column('projects', 'client_company_size')
    op.drop_column('projects', 'client_industry')
    op.drop_column('projects', 'client_address')
    op.drop_column('projects', 'client_phone')
    op.drop_column('projects', 'client_email')
    op.drop_column('projects', 'contact_person')
    # ### end Alembic commands ###