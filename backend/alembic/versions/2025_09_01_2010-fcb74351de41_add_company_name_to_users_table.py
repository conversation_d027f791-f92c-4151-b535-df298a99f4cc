"""Add company_name to users table

Revision ID: fcb74351de41
Revises: 71d2df5e5654
Create Date: 2025-09-01 20:10:10.732963

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fcb74351de41'
down_revision = '71d2df5e5654'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('company_name', sa.String(length=200), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'company_name')
    # ### end Alembic commands ###