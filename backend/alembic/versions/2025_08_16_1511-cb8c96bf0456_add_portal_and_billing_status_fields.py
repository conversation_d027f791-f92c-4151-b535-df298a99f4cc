"""Add portal and billing status fields

Revision ID: cb8c96bf0456
Revises: fix_datetime_fields
Create Date: 2025-08-16 15:11:09.714170

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "cb8c96bf0456"
down_revision = "fix_datetime_fields"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_activity_logs_action", table_name="activity_logs")
    op.drop_constraint(
        "client_approvals_approval_token_key", "client_approvals", type_="unique"
    )
    op.drop_index("ix_client_approvals_approval_token", table_name="client_approvals")
    op.create_index(
        op.f("ix_client_approvals_approval_token"),
        "client_approvals",
        ["approval_token"],
        unique=True,
    )
    op.add_column("clients", sa.Column("portal_enabled", sa.<PERSON>(), nullable=False))
    op.add_column(
        "clients",
        sa.Column("portal_access_token", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "clients",
        sa.Column("portal_passcode_hash", sa.String(length=255), nullable=True),
    )
    op.add_column(
        "clients", sa.Column("portal_passcode_enabled", sa.Boolean(), nullable=False)
    )
    op.create_index(
        op.f("ix_clients_portal_access_token"),
        "clients",
        ["portal_access_token"],
        unique=True,
    )
    op.drop_index("ix_invoices_payment_token", table_name="invoices")
    op.create_unique_constraint(None, "invoices", ["payment_token"])
    op.add_column(
        "project_milestones",
        sa.Column("is_client_visible", sa.Boolean(), nullable=False),
    )
    op.add_column(
        "project_milestones",
        sa.Column("billing_status", sa.String(length=50), nullable=False),
    )
    op.create_index(
        op.f("ix_project_milestones_billing_status"),
        "project_milestones",
        ["billing_status"],
        unique=False,
    )
    op.add_column(
        "projects", sa.Column("currency", sa.String(length=3), nullable=False)
    )
    op.add_column(
        "time_entries",
        sa.Column("billing_status", sa.String(length=50), nullable=False),
    )
    op.create_index(
        op.f("ix_time_entries_billing_status"),
        "time_entries",
        ["billing_status"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_time_entries_billing_status"), table_name="time_entries")
    op.drop_column("time_entries", "billing_status")
    op.drop_column("projects", "currency")
    op.drop_index(
        op.f("ix_project_milestones_billing_status"), table_name="project_milestones"
    )
    op.drop_column("project_milestones", "billing_status")
    op.drop_column("project_milestones", "is_client_visible")
    op.drop_constraint(None, "invoices", type_="unique")
    op.create_index(
        "ix_invoices_payment_token", "invoices", ["payment_token"], unique=False
    )
    op.drop_index(op.f("ix_clients_portal_access_token"), table_name="clients")
    op.drop_column("clients", "portal_passcode_enabled")
    op.drop_column("clients", "portal_passcode_hash")
    op.drop_column("clients", "portal_access_token")
    op.drop_column("clients", "portal_enabled")
    op.drop_index(
        op.f("ix_client_approvals_approval_token"), table_name="client_approvals"
    )
    op.create_index(
        "ix_client_approvals_approval_token",
        "client_approvals",
        ["approval_token"],
        unique=False,
    )
    op.create_unique_constraint(
        "client_approvals_approval_token_key",
        "client_approvals",
        ["approval_token"],
        postgresql_nulls_not_distinct=False,
    )
    op.create_index(
        "ix_activity_logs_action", "activity_logs", ["action"], unique=False
    )
    # ### end Alembic commands ###
