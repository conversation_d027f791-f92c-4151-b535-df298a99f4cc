# 📧 Gmail SMTP Setup Guide for DevHQ

## 🚀 Quick Setup (5 minutes)

### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Security](https://myaccount.google.com/security)
2. Click "2-Step Verification"
3. Follow the setup process (you'll need your phone)

### Step 2: Generate App Password
1. Go to [Google Account Security](https://myaccount.google.com/security)
2. Click "2-Step Verification"
3. Scroll down and click "App passwords"
4. Select "Mail" and "Other (Custom name)"
5. Enter "DevHQ Backend" as the name
6. Click "Generate"
7. **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

### Step 3: Update DevHQ Configuration
Edit `backend/.env` and replace:
```env
SMTP_USER=<EMAIL>
SMTP_PASSWORD=abcd efgh ijkl mnop
```

Also update `backend/app/config.py`:
```python
from_email: str = "<EMAIL>"
```

### Step 4: Test Email Sending
```bash
cd backend
source venv/bin/activate
python3 -c "
from app.core.email import email_service
result = email_service.send_verification_email('<EMAIL>', 'Test User', 'test-token')
print(f'Email sent: {result}')
"
```

## ⚠️ Important Notes

### Gmail Limits
- **500 emails per day** (perfect for development)
- **Rate limit**: ~1 email per second
- **Recipients**: Max 500 per day

### Security
- ✅ **Use App Passwords** (never your regular Gmail password)
- ✅ **Keep credentials secure** (don't commit to git)
- ✅ **Rotate passwords** regularly

### Troubleshooting

**"Username and Password not accepted"**
- ✅ Make sure 2FA is enabled
- ✅ Use App Password (not regular password)
- ✅ Check email address is correct

**"Less secure app access"**
- ✅ This is outdated - use App Passwords instead
- ✅ App Passwords are more secure than "less secure apps"

**Emails going to spam**
- ✅ Normal for development emails
- ✅ Check recipient's spam folder
- ✅ Add your Gmail to recipient's contacts

## 🧪 Testing Commands

### Test Verification Email
```bash
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "first_name": "Test",
    "last_name": "User"
  }'
```

### Test Password Reset
```bash
curl -X POST http://localhost:8000/api/v1/auth/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

## 🎯 Expected Results

**Successful Setup:**
```
✅ Email sent: True
📧 Real email delivered to inbox
🔗 Verification link works
```

**Failed Setup:**
```
❌ Email sent: False
🚫 SMTP authentication error
```

## 🚀 Ready for Production?

**Gmail is perfect for:**
- ✅ Development and testing
- ✅ Small applications (<500 emails/day)
- ✅ Personal projects

**Consider upgrading to:**
- 📈 **SendGrid** for higher volume
- 📈 **Resend** for developer experience
- 📈 **AWS SES** for cost efficiency

## 🔧 Quick Commands

**Check current config:**
```bash
cd backend && source venv/bin/activate && python3 -c "
from app.config import settings
print(f'SMTP Host: {settings.smtp_host}')
print(f'SMTP User: {settings.smtp_user}')
print(f'From Email: {settings.from_email}')
"
```

**Test SMTP connection:**
```bash
cd backend && source venv/bin/activate && python3 -c "
import smtplib
try:
    server = smtplib.SMTP('smtp.gmail.com', 587)
    server.starttls()
    server.login('<EMAIL>', 'your-app-password')
    print('✅ SMTP connection successful!')
    server.quit()
except Exception as e:
    print(f'❌ SMTP connection failed: {e}')
"
```