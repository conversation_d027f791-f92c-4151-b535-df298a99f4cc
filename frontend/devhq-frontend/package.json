{"name": "devhq-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.15", "@tanstack/react-query": "^5.28.3", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "decimal.js": "^10.6.0", "framer-motion": "^11.18.2", "lucide-react": "^0.358.0", "next": "^14.2.32", "next-themes": "^0.2.1", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^9.9.0", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-paystack": "^6.0.0", "react-phone-input-2": "^2.15.1", "react-progress": "^0.0.12", "react-toast": "^1.0.3", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}