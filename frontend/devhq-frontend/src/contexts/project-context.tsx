/** @format */

"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { useParams } from "next/navigation";

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface TimeEntry {
  id: string;
  description: string;
  startTime: Date;
  endTime: Date | null;
  durationMinutes: number;
  isBillable: boolean;
  billableAmount: number;
  date: string;
  taskId?: string;
  milestoneId?: string;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: "todo" | "in_progress" | "done";
  priority: "low" | "medium" | "high";
  estimatedHours?: number;
  actualHours?: number;
  assignee?: string;
  milestoneId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Milestone {
  id: string;
  name: string;
  description?: string;
  status: "not_started" | "in_progress" | "completed";
  progress: number;
  startDate: Date;
  dueDate: Date;
  completedDate?: Date;
  estimatedHours: number;
  actualHours: number;
  allocatedBudget: number;
  spentBudget: number;
  deliverables: Deliverable[];
}

export interface Deliverable {
  id: string;
  name: string;
  status: "not_started" | "in_progress" | "completed";
  assignee: string;
  milestoneId: string;
}

export interface ProjectFile {
  id: string;
  name: string;
  type: string;
  size: number;
  category: "design" | "document" | "code" | "image" | "other";
  description?: string;
  uploadedBy: string;
  uploadedAt: Date;
  version: number;
  isSharedWithClient: boolean;
  url?: string;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  category: "meeting" | "decision" | "idea" | "issue" | "other";
  tags: string[];
  author: string;
  isPinned: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  type: "email" | "call" | "meeting" | "message";
  subject?: string;
  content: string;
  sender: string;
  isFromClient: boolean;
  timestamp: Date;
}

export interface Approval {
  id: string;
  deliverableTitle: string;
  description: string;
  status: "pending" | "approved" | "revision_requested";
  version: number;
  submittedAt: Date;
  approvedAt?: Date;
  clientFeedback?: string;
  attachments: string[];
}

export interface Expense {
  id: string;
  description: string;
  amount: number;
  category: "software" | "hardware" | "travel" | "materials" | "other";
  date: Date;
  isReimbursable: boolean;
}

export interface ProjectData {
  id: string;
  name: string;
  description?: string;
  client: string;
  status: "active" | "completed" | "on_hold" | "cancelled";
  startDate: Date;
  endDate?: Date;
  totalBudget: number;
  hourlyRate: number;

  // Feature data
  timeEntries: TimeEntry[];
  tasks: Task[];
  milestones: Milestone[];
  files: ProjectFile[];
  notes: Note[];
  messages: Message[];
  approvals: Approval[];
  expenses: Expense[];

  // Calculated fields
  totalHours: number;
  billableHours: number;
  totalRevenue: number;
  spentBudget: number;
  remainingBudget: number;
}

// ============================================================================
// CONTEXT DEFINITION
// ============================================================================

interface ProjectContextType {
  // Project data
  project: ProjectData | null;
  isLoading: boolean;

  // Time tracking
  currentTimer: { taskId?: string; startTime: Date } | null;
  startTimer: (taskId?: string) => void;
  stopTimer: (description: string) => void;
  addTimeEntry: (entry: Omit<TimeEntry, "id">) => void;
  updateTimeEntry: (id: string, updates: Partial<TimeEntry>) => void;
  deleteTimeEntry: (id: string) => void;

  // Task management
  addTask: (task: Omit<Task, "id" | "createdAt" | "updatedAt">) => void;
  updateTask: (id: string, updates: Partial<Task>) => void;
  deleteTask: (id: string) => void;

  // Milestone management
  addMilestone: (milestone: Omit<Milestone, "id">) => void;
  updateMilestone: (id: string, updates: Partial<Milestone>) => void;
  deleteMilestone: (id: string) => void;

  // File management
  addFile: (file: Omit<ProjectFile, "id">) => void;
  updateFile: (id: string, updates: Partial<ProjectFile>) => void;
  deleteFile: (id: string) => void;

  // Note management
  addNote: (note: Omit<Note, "id" | "createdAt" | "updatedAt">) => void;
  updateNote: (id: string, updates: Partial<Note>) => void;
  deleteNote: (id: string) => void;

  // Communication
  addMessage: (message: Omit<Message, "id">) => void;

  // Approvals
  addApproval: (approval: Omit<Approval, "id">) => void;
  updateApproval: (id: string, updates: Partial<Approval>) => void;

  // Expenses
  addExpense: (expense: Omit<Expense, "id">) => void;
  updateExpense: (id: string, updates: Partial<Expense>) => void;
  deleteExpense: (id: string) => void;

  // Project management
  updateProject: (updates: Partial<ProjectData>) => void;

  // Utility functions
  refreshProject: () => void;
  calculateProjectMetrics: () => void;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

// ============================================================================
// PROVIDER COMPONENT
// ============================================================================

export function ProjectProvider({ children }: { children: ReactNode }) {
  const params = useParams() as { workspaceId: string; projectId: string };
  const { workspaceId, projectId } = params;

  const [project, setProject] = useState<ProjectData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentTimer, setCurrentTimer] = useState<{
    taskId?: string;
    startTime: Date;
  } | null>(null);

  // Storage key for this project
  const storageKey = `project_${workspaceId}_${projectId}`;

  // ============================================================================
  // DATA PERSISTENCE
  // ============================================================================

  const saveProject = (updatedProject: ProjectData) => {
    setProject(updatedProject);
    localStorage.setItem(storageKey, JSON.stringify(updatedProject));
  };

  const loadProject = () => {
    setIsLoading(true);
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const projectData = JSON.parse(stored);
        // Convert date strings back to Date objects
        projectData.startDate = new Date(projectData.startDate);
        if (projectData.endDate)
          projectData.endDate = new Date(projectData.endDate);

        // Convert dates in nested objects
        projectData.timeEntries =
          projectData.timeEntries?.map((entry: any) => ({
            ...entry,
            startTime: new Date(entry.startTime),
            endTime: entry.endTime ? new Date(entry.endTime) : null,
          })) || [];

        projectData.tasks =
          projectData.tasks?.map((task: any) => ({
            ...task,
            createdAt: new Date(task.createdAt),
            updatedAt: new Date(task.updatedAt),
          })) || [];

        projectData.milestones =
          projectData.milestones?.map((milestone: any) => ({
            ...milestone,
            startDate: new Date(milestone.startDate),
            dueDate: new Date(milestone.dueDate),
            completedDate:
              milestone.completedDate ?
                new Date(milestone.completedDate)
              : undefined,
          })) || [];

        projectData.files =
          projectData.files?.map((file: any) => ({
            ...file,
            uploadedAt: new Date(file.uploadedAt),
          })) || [];

        projectData.notes =
          projectData.notes?.map((note: any) => ({
            ...note,
            createdAt: new Date(note.createdAt),
            updatedAt: new Date(note.updatedAt),
          })) || [];

        projectData.messages =
          projectData.messages?.map((message: any) => ({
            ...message,
            timestamp: new Date(message.timestamp),
          })) || [];

        projectData.approvals =
          projectData.approvals?.map((approval: any) => ({
            ...approval,
            submittedAt: new Date(approval.submittedAt),
            approvedAt:
              approval.approvedAt ? new Date(approval.approvedAt) : undefined,
          })) || [];

        projectData.expenses =
          projectData.expenses?.map((expense: any) => ({
            ...expense,
            date: new Date(expense.date),
          })) || [];

        setProject(projectData);
      } else {
        // Create initial project data if none exists
        createInitialProject();
      }
    } catch (error) {
      console.error("Error loading project:", error);
      createInitialProject();
    } finally {
      setIsLoading(false);
    }
  };

  const createInitialProject = () => {
    const initialProject: ProjectData = {
      id: projectId,
      name: "New Project",
      client: "Client Name",
      status: "active",
      startDate: new Date(),
      totalBudget: 10000,
      hourlyRate: 100,
      timeEntries: [],
      tasks: [],
      milestones: [],
      files: [],
      notes: [],
      messages: [],
      approvals: [],
      expenses: [],
      totalHours: 0,
      billableHours: 0,
      totalRevenue: 0,
      spentBudget: 0,
      remainingBudget: 10000,
    };
    saveProject(initialProject);
  };

  // ============================================================================
  // CALCULATION FUNCTIONS
  // ============================================================================

  const calculateProjectMetrics = () => {
    if (!project) return;

    const totalHours = project.timeEntries.reduce(
      (sum, entry) => sum + entry.durationMinutes / 60,
      0
    );
    const billableHours = project.timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.durationMinutes / 60, 0);
    const totalRevenue = project.timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.billableAmount, 0);
    const spentBudget =
      project.expenses.reduce((sum, expense) => sum + expense.amount, 0) +
      totalRevenue;
    const remainingBudget = project.totalBudget - spentBudget;

    const updatedProject = {
      ...project,
      totalHours,
      billableHours,
      totalRevenue,
      spentBudget,
      remainingBudget,
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // TIME TRACKING FUNCTIONS
  // ============================================================================

  const startTimer = (taskId?: string) => {
    setCurrentTimer({
      taskId,
      startTime: new Date(),
    });
  };

  const stopTimer = (description: string) => {
    if (!currentTimer || !project) return;

    const endTime = new Date();
    const durationMinutes = Math.round(
      (endTime.getTime() - currentTimer.startTime.getTime()) / (1000 * 60)
    );
    const billableAmount = (durationMinutes / 60) * project.hourlyRate;

    const newEntry: TimeEntry = {
      id: `time_${Date.now()}`,
      description,
      startTime: currentTimer.startTime,
      endTime,
      durationMinutes,
      isBillable: true,
      billableAmount,
      date: currentTimer.startTime.toISOString().split("T")[0],
      taskId: currentTimer.taskId,
    };

    addTimeEntry(newEntry);
    setCurrentTimer(null);
  };

  const addTimeEntry = (entry: Omit<TimeEntry, "id">) => {
    if (!project) return;

    const newEntry: TimeEntry = {
      ...entry,
      id: `time_${Date.now()}`,
    };

    const timeEntries = [...project.timeEntries, newEntry];

    // Calculate metrics
    const totalHours = timeEntries.reduce(
      (sum, entry) => sum + entry.durationMinutes / 60,
      0
    );
    const billableHours = timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.durationMinutes / 60, 0);
    const totalRevenue = timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.billableAmount, 0);
    const spentBudget =
      project.expenses.reduce((sum, expense) => sum + expense.amount, 0) +
      totalRevenue;
    const remainingBudget = project.totalBudget - spentBudget;

    const updatedProject = {
      ...project,
      timeEntries,
      totalHours,
      billableHours,
      totalRevenue,
      spentBudget,
      remainingBudget,
    };

    saveProject(updatedProject);
  };

  const updateTimeEntry = (id: string, updates: Partial<TimeEntry>) => {
    if (!project) return;

    const timeEntries = project.timeEntries.map((entry) =>
      entry.id === id ? { ...entry, ...updates } : entry
    );

    // Calculate metrics
    const totalHours = timeEntries.reduce(
      (sum, entry) => sum + entry.durationMinutes / 60,
      0
    );
    const billableHours = timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.durationMinutes / 60, 0);
    const totalRevenue = timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.billableAmount, 0);
    const spentBudget =
      project.expenses.reduce((sum, expense) => sum + expense.amount, 0) +
      totalRevenue;
    const remainingBudget = project.totalBudget - spentBudget;

    const updatedProject = {
      ...project,
      timeEntries,
      totalHours,
      billableHours,
      totalRevenue,
      spentBudget,
      remainingBudget,
    };

    saveProject(updatedProject);
  };

  const deleteTimeEntry = (id: string) => {
    if (!project) return;

    const timeEntries = project.timeEntries.filter((entry) => entry.id !== id);

    // Calculate metrics
    const totalHours = timeEntries.reduce(
      (sum, entry) => sum + entry.durationMinutes / 60,
      0
    );
    const billableHours = timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.durationMinutes / 60, 0);
    const totalRevenue = timeEntries
      .filter((entry) => entry.isBillable)
      .reduce((sum, entry) => sum + entry.billableAmount, 0);
    const spentBudget =
      project.expenses.reduce((sum, expense) => sum + expense.amount, 0) +
      totalRevenue;
    const remainingBudget = project.totalBudget - spentBudget;

    const updatedProject = {
      ...project,
      timeEntries,
      totalHours,
      billableHours,
      totalRevenue,
      spentBudget,
      remainingBudget,
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // TASK MANAGEMENT FUNCTIONS
  // ============================================================================

  const addTask = (task: Omit<Task, "id" | "createdAt" | "updatedAt">) => {
    if (!project) return;

    const newTask: Task = {
      ...task,
      id: `task_${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const updatedProject = {
      ...project,
      tasks: [...project.tasks, newTask],
    };

    saveProject(updatedProject);
  };

  const updateTask = (id: string, updates: Partial<Task>) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      tasks: project.tasks.map((task) =>
        task.id === id ? { ...task, ...updates, updatedAt: new Date() } : task
      ),
    };

    saveProject(updatedProject);
  };

  const deleteTask = (id: string) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      tasks: project.tasks.filter((task) => task.id !== id),
      timeEntries: project.timeEntries.filter((entry) => entry.taskId !== id),
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // MILESTONE MANAGEMENT FUNCTIONS
  // ============================================================================

  const addMilestone = (milestone: Omit<Milestone, "id">) => {
    if (!project) return;

    const newMilestone: Milestone = {
      ...milestone,
      id: `milestone_${Date.now()}`,
    };

    const updatedProject = {
      ...project,
      milestones: [...project.milestones, newMilestone],
    };

    saveProject(updatedProject);
  };

  const updateMilestone = (id: string, updates: Partial<Milestone>) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      milestones: project.milestones.map((milestone) =>
        milestone.id === id ? { ...milestone, ...updates } : milestone
      ),
    };

    saveProject(updatedProject);
  };

  const deleteMilestone = (id: string) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      milestones: project.milestones.filter((milestone) => milestone.id !== id),
      tasks: project.tasks.filter((task) => task.milestoneId !== id),
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // FILE MANAGEMENT FUNCTIONS
  // ============================================================================

  const addFile = (file: Omit<ProjectFile, "id">) => {
    if (!project) return;

    const newFile: ProjectFile = {
      ...file,
      id: `file_${Date.now()}`,
    };

    const updatedProject = {
      ...project,
      files: [...project.files, newFile],
    };

    saveProject(updatedProject);
  };

  const updateFile = (id: string, updates: Partial<ProjectFile>) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      files: project.files.map((file) =>
        file.id === id ? { ...file, ...updates } : file
      ),
    };

    saveProject(updatedProject);
  };

  const deleteFile = (id: string) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      files: project.files.filter((file) => file.id !== id),
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // NOTE MANAGEMENT FUNCTIONS
  // ============================================================================

  const addNote = (note: Omit<Note, "id" | "createdAt" | "updatedAt">) => {
    if (!project) return;

    const newNote: Note = {
      ...note,
      id: `note_${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const updatedProject = {
      ...project,
      notes: [...project.notes, newNote],
    };

    saveProject(updatedProject);
  };

  const updateNote = (id: string, updates: Partial<Note>) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      notes: project.notes.map((note) =>
        note.id === id ? { ...note, ...updates, updatedAt: new Date() } : note
      ),
    };

    saveProject(updatedProject);
  };

  const deleteNote = (id: string) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      notes: project.notes.filter((note) => note.id !== id),
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // COMMUNICATION FUNCTIONS
  // ============================================================================

  const addMessage = (message: Omit<Message, "id">) => {
    if (!project) return;

    const newMessage: Message = {
      ...message,
      id: `message_${Date.now()}`,
    };

    const updatedProject = {
      ...project,
      messages: [...project.messages, newMessage],
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // APPROVAL FUNCTIONS
  // ============================================================================

  const addApproval = (approval: Omit<Approval, "id">) => {
    if (!project) return;

    const newApproval: Approval = {
      ...approval,
      id: `approval_${Date.now()}`,
    };

    const updatedProject = {
      ...project,
      approvals: [...project.approvals, newApproval],
    };

    saveProject(updatedProject);
  };

  const updateApproval = (id: string, updates: Partial<Approval>) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      approvals: project.approvals.map((approval) =>
        approval.id === id ? { ...approval, ...updates } : approval
      ),
    };

    saveProject(updatedProject);
  };

  // ============================================================================
  // EXPENSE FUNCTIONS
  // ============================================================================

  const addExpense = (expense: Omit<Expense, "id">) => {
    if (!project) return;

    const newExpense: Expense = {
      ...expense,
      id: `expense_${Date.now()}`,
    };

    const updatedProject = {
      ...project,
      expenses: [...project.expenses, newExpense],
    };

    saveProject(updatedProject);
    calculateProjectMetrics();
  };

  const updateExpense = (id: string, updates: Partial<Expense>) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      expenses: project.expenses.map((expense) =>
        expense.id === id ? { ...expense, ...updates } : expense
      ),
    };

    saveProject(updatedProject);
    calculateProjectMetrics();
  };

  const deleteExpense = (id: string) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      expenses: project.expenses.filter((expense) => expense.id !== id),
    };

    saveProject(updatedProject);
    calculateProjectMetrics();
  };

  // ============================================================================
  // PROJECT MANAGEMENT FUNCTIONS
  // ============================================================================

  const updateProject = (updates: Partial<ProjectData>) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      ...updates,
    };

    saveProject(updatedProject);
    calculateProjectMetrics();
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const refreshProject = () => {
    loadProject();
  };

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    if (workspaceId && projectId) {
      loadProject();
    }
  }, [workspaceId, projectId]);

  // Remove the automatic calculation effect to prevent infinite loops
  // Calculations will be triggered manually when data changes

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const value: ProjectContextType = {
    // Project data
    project,
    isLoading,

    // Time tracking
    currentTimer,
    startTimer,
    stopTimer,
    addTimeEntry,
    updateTimeEntry,
    deleteTimeEntry,

    // Task management
    addTask,
    updateTask,
    deleteTask,

    // Milestone management
    addMilestone,
    updateMilestone,
    deleteMilestone,

    // File management
    addFile,
    updateFile,
    deleteFile,

    // Note management
    addNote,
    updateNote,
    deleteNote,

    // Communication
    addMessage,

    // Approvals
    addApproval,
    updateApproval,

    // Expenses
    addExpense,
    updateExpense,
    deleteExpense,

    // Project management
    updateProject,

    // Utility functions
    refreshProject,
    calculateProjectMetrics,
  };

  return (
    <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useProject() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error("useProject must be used within a ProjectProvider");
  }
  return context;
}
