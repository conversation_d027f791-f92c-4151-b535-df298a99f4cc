import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define which routes are protected
const protectedRoutes = ['/dashboard', '/projects', '/clients', '/time', '/invoices']
const authRoutes = ['/login', '/register', '/forgot-password']

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for API routes, static files, and other Next.js internals
  if (pathname.startsWith('/api') || 
      pathname.startsWith('/_next') || 
      pathname.startsWith('/favicon.ico')) {
    return NextResponse.next()
  }

  // Check if the route is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  )

  // Check if the route is for authentication
  const isAuthRoute = authRoutes.includes(pathname)

  // For protected routes, we'll let the client-side auth provider handle the redirect
  // This is because we can't access localStorage from server-side middleware
  // The auth provider will redirect to login if no token is found
  
  return NextResponse.next()
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}