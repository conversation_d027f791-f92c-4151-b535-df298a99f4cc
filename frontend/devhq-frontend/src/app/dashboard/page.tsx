/** @format */

"use client";

import React from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import Link from "next/link";
import {
  Building2,
  Users,
  FolderOpen,
  MessageSquare,
  Settings,
  BarChart3,
  CreditCard,
  FileText,
  Clock,
  Target,
  ArrowRight,
  Plus,
  TrendingUp,
  DollarSign,
  CheckCircle,
} from "lucide-react";

export default function DashboardPage() {
  // Mock data for dashboard stats
  const stats = [
    {
      title: "Active Projects",
      value: "12",
      change: "+2 this month",
      icon: <Target className="h-6 w-6" />,
      color: "text-blue-400",
      bgColor: "bg-blue-400/10",
    },
    {
      title: "Total Revenue",
      value: "$24,500",
      change: "+15% this month",
      icon: <DollarSign className="h-6 w-6" />,
      color: "text-green-400",
      bgColor: "bg-green-400/10",
    },
    {
      title: "Hours Tracked",
      value: "342",
      change: "+28 this week",
      icon: <Clock className="h-6 w-6" />,
      color: "text-purple-400",
      bgColor: "bg-purple-400/10",
    },
    {
      title: "Completed Tasks",
      value: "89",
      change: "+12 this week",
      icon: <CheckCircle className="h-6 w-6" />,
      color: "text-orange-400",
      bgColor: "bg-orange-400/10",
    },
  ];

  // Main navigation sections
  const mainSections = [
    {
      title: "Workspaces",
      description: "Manage your project workspaces and teams",
      href: "/workspace",
      icon: <Building2 className="h-8 w-8" />,
      color: "text-blue-400",
      bgColor: "bg-blue-400/10",
      borderColor: "border-blue-400/30",
    },
    {
      title: "Payouts",
      description: "Set up automatic payments from clients",
      href: "/payouts",
      icon: <CreditCard className="h-8 w-8" />,
      color: "text-green-400",
      bgColor: "bg-green-400/10",
      borderColor: "border-green-400/30",
    },
    {
      title: "Analytics",
      description: "View performance metrics and insights",
      href: "/analytics",
      icon: <BarChart3 className="h-8 w-8" />,
      color: "text-purple-400",
      bgColor: "bg-purple-400/10",
      borderColor: "border-purple-400/30",
    },
    {
      title: "Invoices & Payments",
      description: "Manage billing and payment tracking",
      href: "/invoices",
      icon: <CreditCard className="h-8 w-8" />,
      color: "text-yellow-400",
      bgColor: "bg-yellow-400/10",
      borderColor: "border-yellow-400/30",
    },
    {
      title: "Client Management",
      description: "Manage client relationships and communications",
      href: "/clients",
      icon: <Users className="h-8 w-8" />,
      color: "text-orange-400",
      bgColor: "bg-orange-400/10",
      borderColor: "border-orange-400/30",
    },
    {
      title: "Documents",
      description: "Centralized document and file management",
      href: "/documents",
      icon: <FileText className="h-8 w-8" />,
      color: "text-red-400",
      bgColor: "bg-red-400/10",
      borderColor: "border-red-400/30",
    },
  ];

  // Quick actions
  const quickActions = [
    {
      title: "Create New Project",
      href: "/create-project-form",
      icon: <Plus className="h-5 w-5" />,
      color: "text-green-400",
      bgColor: "bg-green-400/10",
    },
    {
      title: "Start Time Tracking",
      href: "/workspace",
      icon: <Clock className="h-5 w-5" />,
      color: "text-blue-400",
      bgColor: "bg-blue-400/10",
    },
    {
      title: "Create Invoice",
      href: "/invoices",
      icon: <CreditCard className="h-5 w-5" />,
      color: "text-yellow-400",
      bgColor: "bg-yellow-400/10",
    },
    {
      title: "View Analytics",
      href: "/analytics",
      icon: <TrendingUp className="h-5 w-5" />,
      color: "text-purple-400",
      bgColor: "bg-purple-400/10",
    },
  ];

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            DEVHQ DASHBOARD
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Welcome back! 👋
              </h2>
              <p className="text-white/60 mt-1">
                Here's what's happening with your projects today.
              </p>
            </div>
            <Link
              href="/workspace"
              className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Quick Start
            </Link>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="border border-white/20 bg-transparent p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`p-2 ${stat.bgColor} ${stat.color}`}>
                  {stat.icon}
                </div>
                <TrendingUp className="h-4 w-4 text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-white/60 mb-1">{stat.title}</div>
                <div className="text-xs text-green-400">{stat.change}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center gap-2 mb-4">
            <Target className="h-5 w-5 text-blue-400" />
            <h3 className="text-lg font-medium text-white">Quick Actions</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.href}
                className={`border border-white/20 p-4 hover:bg-white/5 transition-colors flex items-center gap-3`}
              >
                <div className={`p-2 ${action.bgColor} ${action.color}`}>
                  {action.icon}
                </div>
                <span className="text-white text-sm font-medium">
                  {action.title}
                </span>
              </Link>
            ))}
          </div>
        </div>

        {/* Main Navigation Sections */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-green-400" />
            <h3 className="text-lg font-medium text-white">Main Sections</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mainSections.map((section, index) => (
              <Link
                key={index}
                href={section.href}
                className={`border ${section.borderColor} bg-transparent p-6 hover:bg-white/5 transition-all duration-200 group`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 ${section.bgColor} ${section.color}`}>
                    {section.icon}
                  </div>
                  <ArrowRight className="h-5 w-5 text-white/40 group-hover:text-white/80 transition-colors" />
                </div>
                <div>
                  <h4 className="text-lg font-medium text-white mb-2">
                    {section.title}
                  </h4>
                  <p className="text-sm text-white/60 leading-relaxed">
                    {section.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center gap-2 mb-4">
            <MessageSquare className="h-5 w-5 text-purple-400" />
            <h3 className="text-lg font-medium text-white">Recent Activity</h3>
          </div>
          <div className="space-y-3">
            {[
              {
                action: "Created new project",
                project: "E-commerce Website",
                time: "2 hours ago",
                type: "project",
              },
              {
                action: "Completed milestone",
                project: "Mobile App Design",
                time: "5 hours ago",
                type: "milestone",
              },
              {
                action: "Invoice sent to client",
                project: "Brand Identity Package",
                time: "1 day ago",
                type: "invoice",
              },
              {
                action: "Time tracking started",
                project: "API Development",
                time: "2 days ago",
                type: "time",
              },
            ].map((activity, index) => (
              <div
                key={index}
                className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0"
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`w-2 h-2 rounded-full ${
                      activity.type === "project" ? "bg-blue-400"
                      : activity.type === "milestone" ? "bg-green-400"
                      : activity.type === "invoice" ? "bg-yellow-400"
                      : "bg-purple-400"
                    }`}
                  />
                  <div>
                    <span className="text-white text-sm">
                      {activity.action}
                    </span>
                    <span className="text-white/60 text-sm ml-1">
                      "{activity.project}"
                    </span>
                  </div>
                </div>
                <span className="text-white/40 text-xs">{activity.time}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Settings Link */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-white mb-2">
                Account Settings
              </h3>
              <p className="text-sm text-white/60">
                Manage your profile, preferences, and integrations
              </p>
            </div>
            <Link
              href="/settings"
              className="border border-white/20 bg-transparent px-4 py-2 text-white/60 hover:text-white hover:bg-white/5 flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Settings
            </Link>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
