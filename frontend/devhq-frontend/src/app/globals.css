@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 8%; /* Even darker background for more depth */
  --foreground: 0 0% 95%; /* Slightly softer white */
  --card: 0 0% 12%; /* Slightly lighter card background */
  --card-foreground: 0 0% 95%;
  --popover: #141414;
  --popover-foreground: 0 0% 95%;
  --primary: 142.1 70.6% 45.3%;
  --primary-foreground: 142.1 70.6% 15.3%;
  --secondary: 0 0% 20%; /* Lighter secondary elements */
  --secondary-foreground: 0 0% 95%;
  --muted: 0 0% 25%;
  --muted-foreground: 0 0% 70%;
  --accent: 0 0% 20%;
  --accent-foreground: 0 0% 95%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 95%;
  --border: 0 0% 20%; /* Lighter borders */
  --input: 0 0% 20%;
  --ring: 142.1 70.6% 45.3%;
  --radius: 0.5rem;

  --page-primary: #0d0d0d;

  --page-secondary: #141414
}

/* Subtle background texture */
.bg-textured {
  background-image: 
    radial-gradient(hsl(var(--foreground) / 0.05) 1px, transparent 1px),
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px, 100% 100%;
}

/* Enhanced dark theme styles */
.bg-soft-dark {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--card)) 100%);
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: var(--page-primary);
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--neon-electric) / 0.4);
  border-radius: 5px;
  border: 2px solid hsl(var(--background));
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--neon-electric) / 0.6);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--neon-electric) / 0.4) hsl(var(--background));
}

/* Subtle background texture */
.bg-textured {
  background-image: 
    radial-gradient(hsl(var(--foreground) / 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced dark theme styles */
.bg-soft-dark {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--card)) 100%);
}

/* Selection styling */
::selection {
  background: hsl(var(--neon-electric) / 0.2);
  color: hsl(var(--foreground));
}

/* Focus styles */
.focus-neon:focus-visible {
  outline: none;
  box-shadow: none;
}

/* Enhanced Neon Utility Classes - Inspired by Neon.com */
.text-glow {
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
}

@keyframes text-glow-animation {
  0% {
    text-shadow: 
      0 0 5px hsl(var(--neon-electric)),
      0 0 10px hsl(var(--neon-electric)),
      0 0 15px hsl(var(--neon-electric));
  }
  50% {
    text-shadow: 
      0 0 10px hsl(var(--neon-electric)),
      0 0 20px hsl(var(--neon-electric)),
      0 0 30px hsl(var(--neon-electric));
  }
  100% {
    text-shadow: 
      0 0 5px hsl(var(--neon-electric)),
      0 0 10px hsl(var(--neon-electric)),
      0 0 15px hsl(var(--neon-electric));
  }
}

.animate-text-glow {
  animation: text-glow-animation 2s ease-in-out infinite;
}

.border-glow {
  box-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    inset 0 0 5px currentColor;
}

.border-glow-intense {
  box-shadow: 
    0 0 5px currentColor,
    0 0 15px currentColor,
    0 0 25px currentColor,
    inset 0 0 10px currentColor;
}

.neon-button {
  position: relative;
  background: transparent;
  border: 2px solid hsl(var(--neon-electric));
  color: hsl(var(--neon-electric));
  transition: all 0.3s ease;
}


.neon-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
}


.neon-border {
  border: 1px solid hsl(var(--border));
}

.neon-glow {
  box-shadow: 
    0 0 15px hsl(var(--neon-electric) / 0.2),
    inset 0 0 15px hsl(var(--neon-electric) / 0.1);
}

.neon-glow:hover {
  box-shadow: 
    0 0 25px hsl(var(--neon-electric) / 0.3),
    inset 0 0 25px hsl(var(--neon-electric) / 0.2);
}

.bg-grid {
  background-image: 
    linear-gradient(hsl(var(--border)) 1px, transparent 1px),
    linear-gradient(90deg, hsl(var(--border)) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-grid-glow {
  background-image: 
    linear-gradient(hsl(var(--neon-electric) / 0.1) 1px, transparent 1px),
    linear-gradient(90deg, hsl(var(--neon-electric) / 0.1) 1px, transparent 1px);
  background-size: 30px 30px;
}

/* Neon accent lines */
.neon-accent-line {
  position: relative;
}

.neon-accent-line::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    hsl(var(--neon-electric)), 
    hsl(var(--neon-cyan)), 
    hsl(var(--neon-purple))
  );
  box-shadow: 0 0 10px hsl(var(--neon-electric));
}

/* Pulsing animation for active elements */
@keyframes neon-pulse {
  0%, 100% {
    box-shadow: 
      0 0 5px hsl(var(--neon-electric)),
      0 0 10px hsl(var(--neon-electric)),
      0 0 15px hsl(var(--neon-electric));
  }
  50% {
    box-shadow: 
      0 0 10px hsl(var(--neon-electric)),
      0 0 20px hsl(var(--neon-electric)),
      0 0 30px hsl(var(--neon-electric));
  }
}

.neon-pulse {
  animation: neon-pulse 2s ease-in-out infinite;
}

/* Enhanced focus states */
.neon-focus:focus-visible {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 3px hsl(var(--neon-primary) / 0.5);
}

.focus-neon:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px hsl(var(--neon-electric) / 0.5);
}

/* Accessibility focus styles */
.accessible-focus {
  outline: 2px solid hsl(var(--neon-primary));
  outline-offset: 2px;
}

.accessible-focus-secondary {
  outline: 2px solid hsl(var(--neon-electric));
  outline-offset: 2px;
}

/* Skip to content link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: hsl(var(--neon-primary));
  color: hsl(var(--background));
  padding: 8px;
  z-index: 1000;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 0;
}

/* Ensure links don't have default outlines */
a:focus-visible {
  outline: none;
}

/* Ensure input text is always bright and visible */
input {
  color: hsl(var(--foreground)) !important;
}

input::placeholder {
  color: hsl(var(--muted-foreground)) !important;
}

/* Typography improvements */
h1, h2, h3, h4 {
  letter-spacing: -0.02em;
  font-weight: 700;
}

h1 {
  font-size: 2.5rem;
  line-height: 1.2;
}

h2 {
  font-size: 2rem;
  line-height: 1.3;
}

h3 {
  font-size: 1.75rem;
  line-height: 1.4;
}

h4 {
  font-size: 1.5rem;
  line-height: 1.4;
}

p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Enhanced link styles */
.link {
  color: hsl(var(--neon-electric));
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
}

.link:hover {
  color: hsl(var(--neon-electric) / 0.8);
}

.link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: hsl(var(--neon-electric));
  transition: width 0.3s ease;
}

.link:hover::after {
  width: 100%;
}

/* Card improvements */
.card {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card)) 100%);
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  border-color: hsl(var(--neon-electric) / 0.5);
  transform: translateY(-5px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, hsl(var(--neon-electric) / 0.1), transparent);
  transition: 0.5s;
}

.card:hover::before {
  left: 100%;
}

/* Enhanced card styles */
.neon-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.neon-card:hover {
  border-color: hsl(var(--neon-electric) / 0.5);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Blinking animation for logo */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-blink {
  animation: blink 1.5s infinite alternate;
}

.card-hover:hover {
  border-color: hsl(var(--neon-electric) / 0.5);
  transform: translateY(-2px);
  transition: all 0.2s ease;
}

/* Button improvements */
.btn {
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}


.btn-primary {
  background: hsl(var(--neon-electric));
  color: hsl(var(--background));
  border: none;
  box-shadow: 0 4px 15px hsl(var(--neon-electric) / 0.4);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-outline {
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  background: transparent;
}


.btn-neon {
  background: transparent;
  color: hsl(var(--neon-electric));
  border: 1px solid hsl(var(--neon-electric));
  box-shadow: 0 0 10px hsl(var(--neon-electric) / 0.3);
}


/* Input improvements */
.input {
  background: hsl(var(--secondary));
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.input:focus {
  border-color: hsl(var(--neon-primary));
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 3px hsl(var(--neon-primary) / 0.3);
  outline: none;
  background: hsl(var(--secondary) / 0.8);
}

.textarea {
  background: hsl(var(--secondary));
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  min-height: 100px;
}

.textarea:focus {
  border-color: hsl(var(--neon-primary));
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 3px hsl(var(--neon-primary) / 0.3);
  outline: none;
  background: hsl(var(--secondary) / 0.8);
}

/* Enhanced form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

/* Select improvements */
.select {
  background: hsl(var(--secondary));
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.select:focus {
  border-color: hsl(var(--neon-primary));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 3px hsl(var(--neon-primary) / 0.3);
  outline: none;
}

/* Badge improvements */
.badge {
  font-weight: 500;
  letter-spacing: 0.01em;
}

/* Journey Timeline Styles */
.journey-timeline {
  position: relative;
}

.journey-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background: linear-gradient(to bottom, 
    hsl(var(--neon-electric) / 0.2), 
    hsl(var(--neon-turquoise) / 0.4), 
    hsl(var(--neon-violet) / 0.2));
  transform: translateX(-50%);
}

.journey-step-indicator {
  position: absolute;
  left: 50%;
  width: 24px;
  height: 24px;
  background: hsl(var(--neon-electric));
  border: 4px solid hsl(var(--background));
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 10px hsl(var(--neon-electric));
  z-index: 10;
}

.journey-step-card {
  transition: all 0.3s ease;
}

.journey-step-card:hover {
  transform: translateY(-5px);
  box-shadow: 
    0 10px 25px hsl(var(--neon-electric) / 0.1),
    0 0 15px hsl(var(--neon-electric) / 0.2);
}

/* Floating Elements Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Geometric Patterns */
.geometric-pattern {
  position: absolute;
  border: 1px solid hsl(var(--neon-electric) / 0.2);
  transform-origin: center;
}

/* Gradient Orbs */
.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.1;
}

/* Futuristic Card Styles */
.futuristic-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.futuristic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, hsl(var(--neon-electric) / 0.1), transparent);
  transition: 0.5s;
}

.futuristic-card:hover::before {
  left: 100%;
}

.futuristic-card:hover {
  border-color: hsl(var(--neon-electric) / 0.5);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px hsl(var(--neon-electric) / 0.1);
}

/* Enhanced Card Styles */
.enhanced-card {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card)) 100%);
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.enhanced-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  border-color: hsl(var(--neon-electric) / 0.4);
}

.enhanced-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 10px hsl(var(--neon-electric) / 0.1);
  pointer-events: none;
}

/* Custom Input Focus Style */
.input:focus-visible, .textarea:focus-visible {
  outline: none;
  border-color: hsl(var(--neon-primary));
  box-shadow: none;
  --tw-ring-color: transparent;
}

.dashboard-button-hover-effect {
  position: relative;
  overflow: hidden;
  background: white;
  color: black;
}

.dashboard-button-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%; /* Start off-screen to the left */
  width: 100%;
  height: 100%;
  background: hsl(var(--neon-electric));
  transition: left 0.3s ease-in-out; /* Smooth slide-in transition */
  z-index: 1; /* Above the button background but below text */
}

.dashboard-button-hover-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 3; /* Above everything */
  clip-path: inset(0 100% 0 0); /* Initially hidden */
  transition: clip-path 0.3s ease-in-out;
  white-space: nowrap; /* Prevent text wrapping */
  padding: 0 2rem; /* Default padding for navbar buttons */
}

/* For buttons with arrows (hero and CTA sections) */
.dashboard-button-hover-effect:has(svg)::after {
  gap: 0.75rem; /* Match gap-3 */
  padding: 0 4rem; /* Match button padding for larger buttons */
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='4' cy='12' r='1' fill='white'/%3E%3Ccircle cx='8' cy='12' r='1' fill='white'/%3E%3Ccircle cx='12' cy='12' r='1' fill='white'/%3E%3Ccircle cx='16' cy='12' r='1' fill='white'/%3E%3Ccircle cx='18' cy='10' r='1' fill='white'/%3E%3Ccircle cx='20' cy='12' r='1' fill='white'/%3E%3Ccircle cx='18' cy='14' r='1' fill='white'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 4rem center;
  background-size: 1.75rem 1.75rem;
  padding-right: 6rem; /* Extra space for the larger arrow */
}

/* Hide original content on hover */
.dashboard-button-hover-effect:hover > * {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.dashboard-button-hover-effect:hover::before {
  left: 0; /* Slide in from left to right */
}

.dashboard-button-hover-effect:hover::after {
  clip-path: inset(0 0 0 0); /* Reveal white text as background slides */
}

.dashboard-button-hover-effect:not(:hover)::before {
  left: -100%; /* Slide out to the left when not hovering */
}

.dashboard-button-hover-effect:not(:hover)::after {
  clip-path: inset(0 100% 0 0); /* Hide white text when not hovering */
}

/* Ensure original text appears above the background */
.dashboard-button-hover-effect > * {
  position: relative;
  z-index: 1;
}

/* Watch Demo Button Hover Effect */
.watch-demo-hover-effect {
  position: relative;
  overflow: hidden;
  background: transparent;
  color: white;
  border-width: 2px;
}

.watch-demo-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%; /* Start off-screen to the left */
  width: 100%;
  height: 100%;
  background: white;
  transition: left 0.3s ease-in-out; /* Smooth slide-in transition */
  z-index: 1; /* Above the button background but below text */
}

.watch-demo-hover-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: black;
  z-index: 3; /* Above everything */
  clip-path: inset(0 100% 0 0); /* Initially hidden */
  transition: clip-path 0.3s ease-in-out;
  white-space: nowrap; /* Prevent text wrapping */
  padding: 0 3rem; /* Match button padding */
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolygon points='5,3 19,12 5,21' fill='black'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 3rem center;
  background-size: 1.5rem 1.5rem;
  padding-left: 5rem; /* Extra space for the play icon */
}

/* Hide original content on hover */
.watch-demo-hover-effect:hover > * {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.watch-demo-hover-effect:hover::before {
  left: 0; /* Slide in from left to right */
}

.watch-demo-hover-effect:hover::after {
  clip-path: inset(0 0 0 0); /* Reveal black text as background slides */
}

.watch-demo-hover-effect:not(:hover)::before {
  left: -100%; /* Slide out to the left when not hovering */
}

.watch-demo-hover-effect:not(:hover)::after {
  clip-path: inset(0 100% 0 0); /* Hide black text when not hovering */
}

/* Glassy Navbar Styles */
.glassy-navbar {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glassy-navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  border-radius: inherit;
  pointer-events: none;
}

.glassy-navbar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, rgba(255, 255, 255, 0.1), transparent 50%);
  border-radius: inherit;
  pointer-events: none;
}

/* Glassy Dropdown Styles */
.glassy-dropdown {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glassy-dropdown::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
  border-radius: inherit;
  pointer-events: none;
}

/* Update dashboard button CSS for original navbar button size */
.dashboard-button-hover-effect::after {
  padding: 0 2rem; /* Original padding for navbar button */
}

.bg-sidebar {
  background-color: hsl(var(--background));
}

@keyframes blink-underline {
  0%, 100% { border-bottom: 1px solid transparent; }
  50% { border-bottom: 1px solid white; }
}

.blinking-underline {
  animation: blink-underline 1s infinite;
}