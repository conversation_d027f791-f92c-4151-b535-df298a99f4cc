'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Loader2 } from 'lucide-react'

export default function LoadingPage() {
  const router = useRouter()

  // Optional: Add a timeout or check for authentication status
  // to redirect to dashboard after a short delay or successful auth
  useEffect(() => {
    // This is a placeholder. In a real app, you'd check auth status.
    // For now, just simulate a delay before redirecting to dashboard.
    const timer = setTimeout(() => {
      router.push('/dashboard')
    }, 2000) // Simulate 2-second loading time

    return () => clearTimeout(timer)
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen bg-black text-white">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-10 w-10 animate-spin text-neon-primary" />
        <p className="text-lg font-semibold">Loading...</p>
      </div>
    </div>
  )
}
