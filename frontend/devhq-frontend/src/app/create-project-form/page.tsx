/** @format */

"use client";

import { CalendarIcon, GitHubLogoIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { Square, ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useAuth } from "@/components/providers/auth-provider";
import { projectApi, clientApi, workspaceApi } from "@/lib/api";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function GitHubConnectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const workspaceId = searchParams.get("workspaceId");

  const [visibility, setVisibility] = useState("private");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [actualWorkspaceId, setActualWorkspaceId] = useState<string | null>(
    null
  );

  // Fetch the actual workspace UUID from the workspace alias
  useEffect(() => {
    const fetchWorkspaceId = async () => {
      if (!workspaceId) return;

      try {
        const workspace = await workspaceApi.getWorkspace(workspaceId);
        setActualWorkspaceId(workspace.id);
      } catch (error) {
        console.error("Error fetching workspace:", error);
      }
    };

    fetchWorkspaceId();
  }, [workspaceId]);

  // Check if user signed up with GitHub
  const isGitHubUser = user?.oauth_provider === "github";
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Form states
  const [repoName, setRepoName] = useState("");
  const [repoDescription, setRepoDescription] = useState("");
  const [projectName, setProjectName] = useState("");
  const [clientName, setClientName] = useState("");
  const [company, setCompany] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [billingAddress, setBillingAddress] = useState("");
  const [projectBudget, setProjectBudget] = useState<number | "">("");
  const [dueDate, setDueDate] = useState<Date | undefined>();

  const handleBack = () => {
    if (workspaceId) {
      router.push(`/workspace/${workspaceId}`);
    } else {
      router.back();
    }
  };

  const handleSubmit = async () => {
    if (!workspaceId) {
      console.error("No workspaceId found to save the project.");
      router.push("/dashboard");
      return;
    }

    if (!projectName.trim()) {
      alert("Please enter a project name.");
      return;
    }

    if (!clientName.trim()) {
      alert("Please enter a client name.");
      return;
    }

    setIsSubmitting(true);

    try {
      // First, check if a client with this email already exists
      let clientId: string;

      if (email) {
        // Try to find existing client by email
        try {
          const existingClients = await clientApi.listClients({
            workspace_id: workspaceId,
            email: email,
            per_page: 1,
          });

          if (existingClients.items.length > 0) {
            // Use existing client
            clientId = existingClients.items[0].id;
            console.log("Using existing client:", existingClients.items[0]);
          } else {
            // Create new client
            const clientData = {
              name: clientName,
              email: email,
              phone: phone || undefined,
              company: company || undefined,
              address_line1: billingAddress || undefined,
              workspace_id: actualWorkspaceId || workspaceId,
              is_active: true,
            };
            const createdClient = await clientApi.createClient(clientData);
            clientId = createdClient.id;
            console.log("Created new client:", createdClient);
          }
        } catch (error) {
          console.error("Error checking for existing client:", error);
          // If there's an error checking, try to create a new client anyway
          const clientData = {
            name: clientName,
            email: email,
            phone: phone || undefined,
            company: company || undefined,
            address_line1: billingAddress || undefined,
            workspace_id: actualWorkspaceId || workspaceId,
            is_active: true,
          };
          const createdClient = await clientApi.createClient(clientData);
          clientId = createdClient.id;
        }
      } else {
        // No email provided, create new client
        const clientData = {
          name: clientName,
          phone: phone || undefined,
          company: company || undefined,
          address_line1: billingAddress || undefined,
          workspace_id: actualWorkspaceId || workspaceId,
          is_active: true,
        };
        const createdClient = await clientApi.createClient(clientData);
        clientId = createdClient.id;
      }

      // Then, create the project with the client ID and client information
      const projectData = {
        title: projectName,
        description: `Project for ${clientName}`,
        client_id: clientId,
        workspace_id: actualWorkspaceId || workspaceId,
        status: "draft",
        deadline: dueDate ? format(dueDate, "yyyy-MM-dd") : undefined,
        total_budget: projectBudget ? Number(projectBudget) : undefined,
        currency: "USD",
        is_billable: true,
        is_active: true,
        // Include client information in project fields
        client_name: clientName,
        contact_person: clientName, // Use client name as contact person initially
        client_email: email || undefined,
        client_phone: phone || undefined,
        client_address: billingAddress || undefined,
        client_status: "active",
      };

      const createdProject = await projectApi.createProject(projectData);
      console.log("Project created successfully:", createdProject);

      // Small delay to ensure database transaction is committed
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Redirect to workspace page with refresh parameter
      router.push(`/workspace/${workspaceId}?refresh=true`);
    } catch (error) {
      console.error("Error creating project:", error);
      alert("Failed to create project. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Top Navigation - Only the logo and organization part */}
      <div className="border-b border-border">
        <div className="px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* DevHQ Logo */}
              <div className="flex items-center space-x-2">
                <Square className="h-8 w-8 text-primary border border-neon-electric animate-blink" />
                <div className="text-xl font-bold">DevHQ</div>
              </div>
              {/* Organization */}
              <div className="text-sm text-gray-400">/ codegoddy</div>
            </div>
            <Button
              variant="outline"
              onClick={handleBack}
              className="rounded-none hover:bg-white hover:text-black"
            >
              Back
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex items-center justify-center min-h-[calc(100vh-80px)] px-4">
        <div className="w-full max-w-4xl">
          <div className="space-y-6 mb-8 mt-12">
            <h1 className="text-4xl font-semibold text-white">New Project</h1>
            <p className="text-lg text-white">Source Code</p>
          </div>
          {/* Main Container Box */}
          <div className="border border-white/20 bg-transparent p-8 w-full mx-auto">
            <div className="flex flex-col items-start space-y-4 w-full">
              {/* Repository Section - Only show for GitHub users */}
              {isGitHubUser && (
                <div className="border border-white/20 bg-transparent p-6 w-full">
                  <h3 className="text-lg font-medium text-white mb-2">
                    Repository
                  </h3>
                  <p className="text-sm text-gray-400 mb-4">
                    Create a new GitHub repository for this project
                  </p>
                  <Button
                    onClick={() => router.push("/github-connect-initial")}
                    className="w-full rounded-none bg-white text-black hover:bg-green-500 hover:text-white mb-4"
                  >
                    <GitHubLogoIcon className="h-4 w-4 mr-2" />
                    Connect to GitHub
                  </Button>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Repository Name */}
                    <div>
                      <Label htmlFor="repo-name">Repository Name *</Label>
                      <Input
                        id="repo-name"
                        type="text"
                        placeholder="e.g. my-awesome-project"
                        value={repoName}
                        onChange={(e) => setRepoName(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>

                    {/* Visibility */}
                    <div>
                      <Label htmlFor="repo-visibility">Visibility</Label>
                      <Select
                        value={visibility}
                        onValueChange={(value) => setVisibility(value)}
                      >
                        <SelectTrigger className="bg-transparent border-white/20 text-white rounded-none">
                          <SelectValue placeholder="Select visibility" />
                        </SelectTrigger>
                        <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                          <SelectItem value="private">Private</SelectItem>
                          <SelectItem value="public">Public</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Description */}
                    <div className="md:col-span-2">
                      <Label htmlFor="repo-description">Description</Label>
                      <Textarea
                        id="repo-description"
                        placeholder="Brief description of your repository..."
                        rows={3}
                        value={repoDescription}
                        onChange={(e) => setRepoDescription(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Project Details Section */}
              <div className="border border-white/20 bg-transparent p-6 w-full mt-6">
                {/* Project Name Section */}
                <div>
                  <h3 className="text-lg font-medium text-white mb-2">Name</h3>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-400 flex-shrink-0 w-1/3">
                      Add a unique name for your project
                    </p>
                    <div className="ml-6 flex-1">
                      <Label htmlFor="project-name" className="sr-only">
                        Project Name
                      </Label>
                      <Input
                        id="project-name"
                        type="text"
                        placeholder="e.g. E-commerce Website"
                        value={projectName}
                        onChange={(e) => setProjectName(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>
                  </div>
                </div>

                {/* Client Section */}
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-white mb-2">
                    Client
                  </h3>
                  <p className="text-sm text-gray-400 mb-4">Add client info</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Client Name */}
                    <div>
                      <Label htmlFor="client-name">Client Name *</Label>
                      <Input
                        id="client-name"
                        type="text"
                        placeholder="e.g. John Smith"
                        value={clientName}
                        onChange={(e) => setClientName(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>

                    {/* Company */}
                    <div>
                      <Label htmlFor="company">Company</Label>
                      <Input
                        id="company"
                        type="text"
                        placeholder="e.g. Acme Corporation"
                        value={company}
                        onChange={(e) => setCompany(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="e.g. <EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>

                    {/* Phone */}
                    <div>
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="e.g. +****************"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>

                    {/* Billing Address */}
                    <div className="md:col-span-2">
                      <Label htmlFor="billing-address">Billing Address</Label>
                      <Textarea
                        id="billing-address"
                        placeholder="123 Main St, City, State, ZIP"
                        rows={3}
                        value={billingAddress}
                        onChange={(e) => setBillingAddress(e.target.value)}
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>

                    {/* Project Budget */}
                    <div>
                      <Label htmlFor="project-budget">Project Budget ($)</Label>
                      <Input
                        id="project-budget"
                        type="number"
                        placeholder="e.g. 5000"
                        value={
                          projectBudget === "" || projectBudget === 0 ?
                            ""
                          : projectBudget
                        }
                        onChange={(e) =>
                          setProjectBudget(
                            e.target.value === "" ?
                              ""
                            : parseFloat(e.target.value) || ""
                          )
                        }
                        min="0"
                        step="0.01"
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>

                    {/* Due Date */}
                    <div>
                      <Label htmlFor="due-date">Due Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full justify-start text-left font-normal bg-transparent border border-white/20 text-white hover:bg-transparent hover:text-white",
                              !dueDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {dueDate ?
                              format(dueDate, "PPP")
                            : <span>Pick a date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-auto p-0 bg-black border-white/20"
                          align="start"
                        >
                          <Calendar
                            mode="single"
                            selected={dueDate}
                            onSelect={setDueDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-start space-x-4 mt-8 pt-6 border-t border-white/10">
              <Button
                variant="outline"
                onClick={handleBack}
                className="rounded-none hover:bg-white hover:text-black px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                className="rounded-none bg-green-500 text-white hover:bg-green-600 px-6 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!projectName || !clientName || isSubmitting}
              >
                {isSubmitting ? "Creating Project..." : "Create Project"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
