/** @format */

"use client";

import React, { useState, useEffect, useRef } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { useAuth } from "@/components/providers/auth-provider";
import { userApi, authApi } from "@/lib/api";
import { currencyApi } from "@/lib/api/currency";
import { CurrencyConversionModal } from "@/components/currency/currency-conversion-modal";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Settings,
  User,
  Bell,
  Shield,
  CreditCard,
  Palette,
  Globe,
  Save,
  Eye,
  EyeOff,
  Camera,
  Upload,
  Loader2,
  ChevronDown,
} from "lucide-react";

export default function SettingsPage() {
  const { user, refreshUser } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [activeTab, setActiveTab] = useState("profile");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  // Currency conversion modal state
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [pendingCurrencyChange, setPendingCurrencyChange] = useState<{
    oldCurrency: string;
    newCurrency: string;
  } | null>(null);

  // Profile form state
  const [profileData, setProfileData] = useState({
    first_name: "",
    last_name: "",
    phone: "",
    bio: "",
    company_name: "",
  });

  // Original profile data for change detection
  const [originalProfileData, setOriginalProfileData] = useState({
    first_name: "",
    last_name: "",
    phone: "",
    bio: "",
    company_name: "",
  });

  // Business data state
  const [businessData, setBusinessData] = useState({
    company_name: "",
    hourly_rate: "",
    company_description: "",
    default_currency: "USD",
  });

  // Original business data for change detection
  const [originalBusinessData, setOriginalBusinessData] = useState({
    company_name: "",
    hourly_rate: "",
    company_description: "",
    default_currency: "USD",
  });

  // Settings state
  const [settings, setSettings] = useState<any>(null);

  const [notifications, setNotifications] = useState({
    email: true,
    push: false,
    sms: false,
    projectUpdates: true,
    invoiceReminders: true,
    clientMessages: true,
  });

  // Change detection functions
  const hasProfileChanges = () => {
    return JSON.stringify(profileData) !== JSON.stringify(originalProfileData);
  };

  const hasBusinessChanges = () => {
    return (
      JSON.stringify(businessData) !== JSON.stringify(originalBusinessData)
    );
  };

  const tabs = [
    { id: "profile", label: "Profile", icon: <User className="h-4 w-4" /> },
    {
      id: "notifications",
      label: "Notifications",
      icon: <Bell className="h-4 w-4" />,
    },
    { id: "security", label: "Security", icon: <Shield className="h-4 w-4" /> },
    {
      id: "billing",
      label: "Billing",
      icon: <CreditCard className="h-4 w-4" />,
    },
    {
      id: "appearance",
      label: "Appearance",
      icon: <Palette className="h-4 w-4" />,
    },
    {
      id: "integrations",
      label: "Integrations",
      icon: <Globe className="h-4 w-4" />,
    },
  ];

  // Load user data on component mount
  useEffect(() => {
    if (user) {
      const profileDataFromUser = {
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        phone: user.phone || "",
        bio: user.bio || "",
        company_name: user.company_name || "",
      };
      setProfileData(profileDataFromUser);
      setOriginalProfileData(profileDataFromUser);

      setBusinessData((prev) => ({
        ...prev,
        company_name: user.company_name || "",
      }));
    }
    loadSettings();
  }, [user]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const settingsData = await userApi.getSettings();
      setSettings(settingsData);

      // Update notifications from settings
      if (settingsData) {
        setNotifications((prev) => ({
          ...prev,
          email: settingsData.email_notifications,
          push: settingsData.push_notifications,
        }));

        // Update business data from settings - get fresh user data
        const freshUser = await authApi.getCurrentUser();

        const businessDataFromSettings = {
          company_name: freshUser?.company_name || "",
          default_currency: settingsData.default_currency || "USD",
          hourly_rate: settingsData.default_hourly_rate?.toString() || "",
          company_description: "",
        };
        setBusinessData(businessDataFromSettings);
        setOriginalBusinessData(businessDataFromSettings);
      }
    } catch (error) {
      console.error("Failed to load settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      setMessage({ type: "error", text: "Please select an image file" });
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setMessage({ type: "error", text: "Image must be less than 5MB" });
      return;
    }

    try {
      setIsUploadingAvatar(true);
      setMessage(null);

      await userApi.uploadAvatar(file);
      await refreshUser(); // Refresh user data to get new avatar URL

      setMessage({
        type: "success",
        text: "Profile picture updated successfully!",
      });
    } catch (error: any) {
      console.error("Avatar upload error:", error);
      setMessage({
        type: "error",
        text:
          error.response?.data?.detail || "Failed to upload profile picture",
      });
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  const handleProfileSave = async () => {
    try {
      setIsSaving(true);
      setMessage(null);

      // Include company_name in profile data
      const profileUpdateData = {
        ...profileData,
        company_name: businessData.company_name,
      };

      await userApi.updateProfile(profileUpdateData);
      await refreshUser(); // Refresh user data

      // Update original data to reflect saved state
      setOriginalProfileData({ ...profileData });

      setMessage({ type: "success", text: "Profile updated successfully!" });
    } catch (error: any) {
      console.error("Profile update error:", error);
      setMessage({
        type: "error",
        text: error.response?.data?.detail || "Failed to update profile",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCurrencyChange = (newCurrency: string) => {
    const oldCurrency = businessData.default_currency;

    if (oldCurrency === newCurrency) return;

    // Show conversion modal if currencies are different
    setPendingCurrencyChange({ oldCurrency, newCurrency });
    setShowCurrencyModal(true);
  };

  const handleCurrencyConversionConfirm = async (convertData: boolean) => {
    if (!pendingCurrencyChange) return;

    try {
      setIsSaving(true);
      setMessage(null);

      // Update the business data state
      setBusinessData((prev) => ({
        ...prev,
        default_currency: pendingCurrencyChange.newCurrency,
      }));

      // If user wants to convert existing data, call the currency API
      if (convertData) {
        await currencyApi.updateUserDefaultCurrency({
          new_currency: pendingCurrencyChange.newCurrency,
          convert_existing_data: true,
        });

        setMessage({
          type: "success",
          text: `Currency changed to ${pendingCurrencyChange.newCurrency} and existing data converted!`,
        });
      } else {
        // Just update the settings without conversion
        const settingsUpdate = {
          default_currency: pendingCurrencyChange.newCurrency,
        };

        await userApi.updateSettings(settingsUpdate);

        setMessage({
          type: "success",
          text: `Default currency changed to ${pendingCurrencyChange.newCurrency}`,
        });
      }

      await loadSettings();
    } catch (error: any) {
      console.error("Currency change error:", error);
      setMessage({
        type: "error",
        text: error.response?.data?.detail || "Failed to change currency",
      });

      // Revert the currency change on error
      setBusinessData((prev) => ({
        ...prev,
        default_currency: pendingCurrencyChange.oldCurrency,
      }));
    } finally {
      setIsSaving(false);
      setPendingCurrencyChange(null);
    }
  };

  const handleBusinessSave = async () => {
    try {
      setIsSaving(true);
      setMessage(null);

      // Update user settings with business data
      const settingsUpdate = {
        default_currency: businessData.default_currency,
        default_hourly_rate: parseFloat(businessData.hourly_rate) || 0,
      };

      // Update profile with company name
      const profileUpdate = {
        company_name: businessData.company_name,
      };

      await Promise.all([
        userApi.updateSettings(settingsUpdate),
        userApi.updateProfile(profileUpdate),
      ]);

      await refreshUser();
      await loadSettings();

      // The loadSettings function will now properly update the business data
      // with fresh data from the database, so we don't need to manually update here

      setMessage({
        type: "success",
        text: "Business information updated successfully!",
      });
    } catch (error: any) {
      console.error("Business update error:", error);
      setMessage({
        type: "error",
        text:
          error.response?.data?.detail ||
          "Failed to update business information",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSettingsSave = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      setMessage(null);

      const updatedSettings = {
        ...settings,
        email_notifications: notifications.email,
        push_notifications: notifications.push,
      };

      await userApi.updateSettings(updatedSettings);
      setSettings(updatedSettings);

      setMessage({ type: "success", text: "Settings updated successfully!" });
    } catch (error: any) {
      console.error("Settings update error:", error);
      setMessage({
        type: "error",
        text: error.response?.data?.detail || "Failed to update settings",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const renderProfileTab = () => (
    <div className="space-y-6">
      {/* Profile Picture Section */}
      <div>
        <h3 className="text-lg font-medium text-white mb-4">Profile Picture</h3>
        <div className="flex items-center gap-6">
          <div className="relative">
            <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-600 flex items-center justify-center">
              {user?.avatar_url ?
                <img
                  src={user.avatar_url}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              : <User className="h-12 w-12 text-white/40" />}
            </div>
            {isUploadingAvatar && (
              <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                <Loader2 className="h-6 w-6 text-white animate-spin" />
              </div>
            )}
          </div>
          <div className="space-y-2">
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploadingAvatar}
              className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20 flex items-center gap-2 disabled:opacity-50"
            >
              <Camera className="h-4 w-4" />
              {isUploadingAvatar ? "Uploading..." : "Change Picture"}
            </button>
            <p className="text-sm text-white/60">
              JPG, PNG or GIF. Max size 5MB.
            </p>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleAvatarUpload}
            className="hidden"
          />
        </div>
      </div>

      {/* Personal Information */}
      <div>
        <h3 className="text-lg font-medium text-white mb-4">
          Personal Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              First Name
            </label>
            <input
              type="text"
              value={profileData.first_name}
              onChange={(e) =>
                setProfileData((prev) => ({
                  ...prev,
                  first_name: e.target.value,
                }))
              }
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Last Name
            </label>
            <input
              type="text"
              value={profileData.last_name}
              onChange={(e) =>
                setProfileData((prev) => ({
                  ...prev,
                  last_name: e.target.value,
                }))
              }
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={user?.email || ""}
              disabled
              className="w-full p-3 bg-transparent border border-white/20 text-white/60 cursor-not-allowed"
            />
            <p className="text-xs text-white/40 mt-1">
              Email cannot be changed
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              value={profileData.phone}
              onChange={(e) =>
                setProfileData((prev) => ({ ...prev, phone: e.target.value }))
              }
              placeholder="+****************"
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none"
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-white/70 mb-2">
              Bio
            </label>
            <textarea
              rows={4}
              value={profileData.bio}
              onChange={(e) =>
                setProfileData((prev) => ({ ...prev, bio: e.target.value }))
              }
              placeholder="Tell us about yourself..."
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none resize-none"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-white mb-4">
          Business Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Company Name
            </label>
            <input
              type="text"
              value={businessData.company_name}
              onChange={(e) =>
                setBusinessData((prev) => ({
                  ...prev,
                  company_name: e.target.value,
                }))
              }
              placeholder="Your company or business name"
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Hourly Rate ({businessData.default_currency})
            </label>
            <input
              type="number"
              value={businessData.hourly_rate}
              onChange={(e) =>
                setBusinessData((prev) => ({
                  ...prev,
                  hourly_rate: e.target.value,
                }))
              }
              placeholder="75.00"
              min="0"
              step="0.01"
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Default Currency
            </label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between bg-transparent border border-white/20 text-white hover:bg-white/5 focus:border-green-400 h-12 px-3"
                >
                  <span className="flex items-center gap-2">
                    <span className="font-medium text-green-400">
                      {currencyApi.getCurrencySymbol(
                        businessData.default_currency
                      )}
                    </span>
                    <span>
                      {businessData.default_currency} -{" "}
                      {currencyApi.getCurrencyName(
                        businessData.default_currency
                      )}
                    </span>
                  </span>
                  <ChevronDown className="h-4 w-4 opacity-50" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full bg-black/20 backdrop-blur-md border border-white/10 max-h-64 overflow-y-auto">
                {currencyApi.getPopularCurrencies().map((currency) => (
                  <DropdownMenuItem
                    key={currency.code}
                    onClick={() => handleCurrencyChange(currency.code)}
                    className="cursor-pointer hover:bg-white/10 focus:bg-white/10 text-white px-3 py-2 transition-colors text-sm flex items-center gap-3"
                  >
                    <span className="font-medium text-green-400 min-w-[2rem]">
                      {currency.symbol}
                    </span>
                    <span className="font-medium">{currency.code}</span>
                    <span className="text-white/60">{currency.name}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-white/70 mb-2">
              Company Description
            </label>
            <textarea
              rows={4}
              value={businessData.company_description}
              onChange={(e) =>
                setBusinessData((prev) => ({
                  ...prev,
                  company_description: e.target.value,
                }))
              }
              placeholder="Brief description of your company or services..."
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none resize-none"
            />
          </div>
        </div>

        {/* Business Information Save Button - Only show when there are changes */}
        {hasBusinessChanges() && (
          <div className="mt-8 pt-6 border-t border-white/20">
            <button
              onClick={handleBusinessSave}
              disabled={isSaving}
              className="border border-green-400/30 bg-green-400/10 px-6 py-3 text-green-400 hover:bg-green-400/20 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ?
                <Loader2 className="h-4 w-4 animate-spin" />
              : <Save className="h-4 w-4" />}
              {isSaving ? "Saving..." : "Save Business Information"}
            </button>
          </div>
        )}
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-white mb-4">
          Notification Preferences
        </h3>
        <div className="space-y-4">
          {Object.entries(notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">
                  {key.charAt(0).toUpperCase() +
                    key.slice(1).replace(/([A-Z])/g, " $1")}
                </div>
                <div className="text-white/60 text-sm">
                  {key === "email" && "Receive notifications via email"}
                  {key === "push" && "Browser push notifications"}
                  {key === "sms" && "SMS notifications for urgent updates"}
                  {key === "projectUpdates" && "Updates about project progress"}
                  {key === "invoiceReminders" &&
                    "Payment and invoice reminders"}
                  {key === "clientMessages" && "New messages from clients"}
                </div>
              </div>
              <button
                onClick={() =>
                  setNotifications((prev) => ({ ...prev, [key]: !value }))
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  value ? "bg-green-400" : "bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    value ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-white mb-4">Change Password</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Current Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none pr-10"
              />
              <button
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white"
              >
                {showPassword ?
                  <EyeOff className="h-4 w-4" />
                : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              New Password
            </label>
            <input
              type="password"
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white/70 mb-2">
              Confirm New Password
            </label>
            <input
              type="password"
              className="w-full p-3 bg-transparent border border-white/20 text-white focus:border-green-400 focus:outline-none"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-white mb-4">
          Two-Factor Authentication
        </h3>
        <div className="border border-white/20 bg-transparent p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-white font-medium">Enable 2FA</div>
              <div className="text-white/60 text-sm">
                Add an extra layer of security to your account
              </div>
            </div>
            <button className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20">
              Enable
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBillingTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-white mb-4">
          Subscription Plan
        </h3>
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-white font-medium text-lg">Pro Plan</div>
              <div className="text-white/60">$29/month • Billed monthly</div>
            </div>
            <div className="px-3 py-1 bg-green-400/10 text-green-400 border border-green-400/30 text-sm">
              ACTIVE
            </div>
          </div>
          <div className="space-y-2 text-sm text-white/60">
            <div>✓ Unlimited projects</div>
            <div>✓ Advanced analytics</div>
            <div>✓ Priority support</div>
            <div>✓ Custom integrations</div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-white mb-4">Payment Method</h3>
        <div className="border border-white/20 bg-transparent p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-400/10 text-blue-400 flex items-center justify-center text-sm font-bold">
                ****
              </div>
              <div>
                <div className="text-white font-medium">
                  **** **** **** 4242
                </div>
                <div className="text-white/60 text-sm">Expires 12/25</div>
              </div>
            </div>
            <button className="border border-white/20 bg-transparent px-4 py-2 text-white/60 hover:text-white">
              Update
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTab = () => {
    switch (activeTab) {
      case "profile":
        return renderProfileTab();
      case "notifications":
        return renderNotificationsTab();
      case "security":
        return renderSecurityTab();
      case "billing":
        return renderBillingTab();
      case "appearance":
        return (
          <div className="text-center py-12">
            <Palette className="h-12 w-12 text-white/40 mx-auto mb-4" />
            <div className="text-white/60">Appearance settings coming soon</div>
          </div>
        );
      case "integrations":
        return (
          <div className="text-center py-12">
            <Globe className="h-12 w-12 text-white/40 mx-auto mb-4" />
            <div className="text-white/60">
              Integration settings coming soon
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            ACCOUNT SETTINGS
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">Settings</h2>
              <p className="text-white/60 mt-1">
                Manage your account preferences and configuration.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center gap-3 px-4 py-3 text-left transition-colors ${
                    activeTab === tab.id ?
                      "border-l-2 border-green-400 bg-green-400/10 text-green-400"
                    : "text-white/60 hover:text-white hover:bg-white/5"
                  }`}
                >
                  {tab.icon}
                  <span className="flex-1">{tab.label}</span>
                  {/* Show indicator for unsaved changes */}
                  {tab.id === "profile" &&
                    (hasProfileChanges() || hasBusinessChanges()) && (
                      <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    )}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="border border-white/20 bg-transparent p-6">
              {/* Message Display */}
              {message && (
                <div
                  className={`mb-6 p-4 rounded-md border ${
                    message.type === "success" ?
                      "bg-green-500/10 border-green-500/30 text-green-400"
                    : "bg-red-500/10 border-red-500/30 text-red-400"
                  }`}
                >
                  {message.text}
                </div>
              )}

              {renderTab()}

              {((activeTab === "profile" && hasProfileChanges()) ||
                activeTab === "notifications" ||
                activeTab === "security") && (
                <div className="mt-8 pt-6 border-t border-white/20">
                  <button
                    onClick={
                      activeTab === "profile" ? handleProfileSave
                      : activeTab === "notifications" ?
                        handleSettingsSave
                      : undefined
                    }
                    disabled={isSaving}
                    className="border border-green-400/30 bg-green-400/10 px-6 py-3 text-green-400 hover:bg-green-400/20 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ?
                      <Loader2 className="h-4 w-4 animate-spin" />
                    : <Save className="h-4 w-4" />}
                    {isSaving ? "Saving..." : "Save Changes"}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Currency Conversion Modal */}
      {showCurrencyModal && pendingCurrencyChange && (
        <CurrencyConversionModal
          isOpen={showCurrencyModal}
          onClose={() => {
            setShowCurrencyModal(false);
            setPendingCurrencyChange(null);
            // Revert currency selection
            setBusinessData((prev) => ({
              ...prev,
              default_currency: pendingCurrencyChange.oldCurrency,
            }));
          }}
          oldCurrency={pendingCurrencyChange.oldCurrency}
          newCurrency={pendingCurrencyChange.newCurrency}
          onConfirm={handleCurrencyConversionConfirm}
        />
      )}
    </DashboardLayout>
  );
}
