/** @format */

"use client";

import React, { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import {
  CheckCircle,
  XCircle,
  MessageSquare,
  Calendar,
  User,
  FileText,
  Download,
  Star,
  Clock,
} from "lucide-react";

interface ClientApprovalData {
  id: string;
  deliverableTitle: string;
  description: string;
  version: number;
  submittedAt: Date;
  attachments: string[];
  projectName: string;
  clientName: string;
  status: "pending" | "approved" | "revision_requested";
  clientFeedback?: string;
  approvedAt?: Date;
}

export default function ClientApprovalPage() {
  const params = useParams();
  const { approvalId } = params;
  
  const [approval, setApproval] = useState<ClientApprovalData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [feedback, setFeedback] = useState("");
  const [rating, setRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // In a real app, this would fetch from an API
    // For now, we'll simulate loading approval data
    const loadApprovalData = () => {
      // Simulate API call
      setTimeout(() => {
        const mockApproval: ClientApprovalData = {
          id: approvalId as string,
          deliverableTitle: "Homepage Design Mockups",
          description: "Complete homepage design with responsive layouts for desktop and mobile devices. Includes hero section, navigation, feature highlights, and footer design.",
          version: 2,
          submittedAt: new Date("2024-01-15T14:30:00"),
          attachments: [
            "homepage-desktop.png",
            "homepage-mobile.png", 
            "style-guide.pdf"
          ],
          projectName: "E-commerce Website Redesign",
          clientName: "Acme Corporation",
          status: "pending"
        };
        setApproval(mockApproval);
        setIsLoading(false);
      }, 1000);
    };

    loadApprovalData();
  }, [approvalId]);

  const handleApprove = async () => {
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      if (approval) {
        setApproval({
          ...approval,
          status: "approved",
          approvedAt: new Date(),
          clientFeedback: feedback || undefined
        });
      }
      setIsSubmitting(false);
    }, 1000);
  };

  const handleRequestRevision = async () => {
    if (!feedback.trim()) {
      alert("Please provide feedback for the revision request.");
      return;
    }

    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      if (approval) {
        setApproval({
          ...approval,
          status: "revision_requested",
          clientFeedback: feedback
        });
      }
      setIsSubmitting(false);
    }, 1000);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Loading approval details...</div>
      </div>
    );
  }

  if (!approval) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-4">Approval Not Found</h1>
          <p className="text-white/60">The approval link you're looking for doesn't exist or has expired.</p>
        </div>
      </div>
    );
  }

  const isCompleted = approval.status !== "pending";

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="border-b border-white/20 bg-black/50 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2">
              Client Approval Request
            </h1>
            <p className="text-white/60">
              {approval.projectName} • {approval.clientName}
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-8 space-y-8">
        {/* Status Banner */}
        {isCompleted && (
          <div className={`border p-4 rounded-lg ${
            approval.status === "approved" 
              ? "border-green-400/30 bg-green-400/10" 
              : "border-red-400/30 bg-red-400/10"
          }`}>
            <div className="flex items-center gap-3">
              {approval.status === "approved" ? (
                <CheckCircle className="h-6 w-6 text-green-400" />
              ) : (
                <XCircle className="h-6 w-6 text-red-400" />
              )}
              <div>
                <h3 className={`font-medium ${
                  approval.status === "approved" ? "text-green-400" : "text-red-400"
                }`}>
                  {approval.status === "approved" ? "Approved" : "Revision Requested"}
                </h3>
                <p className="text-white/60 text-sm">
                  {approval.status === "approved" 
                    ? `Approved on ${approval.approvedAt?.toLocaleDateString()}`
                    : "Please review the feedback below"
                  }
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Deliverable Details */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center gap-2 mb-4">
            <FileText className="h-5 w-5 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Deliverable Details</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-white mb-2">
                {approval.deliverableTitle}
              </h3>
              <p className="text-white/60 text-sm leading-relaxed">
                {approval.description}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-white/20">
              <div className="flex items-center gap-2 text-white/60">
                <Calendar className="h-4 w-4" />
                <span className="text-sm">
                  Submitted {approval.submittedAt.toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center gap-2 text-white/60">
                <User className="h-4 w-4" />
                <span className="text-sm">Version {approval.version}</span>
              </div>
              <div className="flex items-center gap-2 text-white/60">
                <Clock className="h-4 w-4" />
                <span className="text-sm">
                  {approval.attachments.length} attachment(s)
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Attachments */}
        {approval.attachments.length > 0 && (
          <div className="border border-white/20 bg-transparent p-6">
            <h3 className="text-lg font-medium text-white mb-4">Attachments</h3>
            <div className="space-y-2">
              {approval.attachments.map((attachment, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border border-white/20 bg-white/5"
                >
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-blue-400" />
                    <span className="text-white">{attachment}</span>
                  </div>
                  <button className="flex items-center gap-2 text-blue-400 hover:text-blue-300">
                    <Download className="h-4 w-4" />
                    <span className="text-sm">Download</span>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Feedback Section */}
        {approval.clientFeedback && (
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <MessageSquare className="h-5 w-5 text-yellow-400" />
              <h3 className="text-lg font-medium text-white">Client Feedback</h3>
            </div>
            <p className="text-white/80 leading-relaxed">{approval.clientFeedback}</p>
          </div>
        )}

        {/* Action Section */}
        {!isCompleted && (
          <div className="border border-white/20 bg-transparent p-6">
            <h3 className="text-lg font-medium text-white mb-4">Your Review</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-white/60 text-sm mb-2">
                  Feedback (optional for approval, required for revision)
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Share your thoughts, suggestions, or revision requests..."
                  className="w-full border border-white/20 bg-transparent px-3 py-2 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
                  rows={4}
                />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={handleApprove}
                  disabled={isSubmitting}
                  className="flex-1 border border-green-400/30 bg-green-400/10 px-6 py-3 text-green-400 hover:bg-green-400/20 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <CheckCircle className="h-5 w-5 mr-2 inline" />
                  {isSubmitting ? "Processing..." : "Approve"}
                </button>
                <button
                  onClick={handleRequestRevision}
                  disabled={isSubmitting}
                  className="flex-1 border border-red-400/30 bg-red-400/10 px-6 py-3 text-red-400 hover:bg-red-400/20 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <XCircle className="h-5 w-5 mr-2 inline" />
                  {isSubmitting ? "Processing..." : "Request Revision"}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="text-center text-white/40 text-sm">
          <p>Powered by DevHQ Project Management</p>
        </div>
      </div>
    </div>
  );
}
