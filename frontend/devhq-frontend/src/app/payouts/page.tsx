/** @format */

"use client";

import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  Wallet,
  CreditCard,
  Shield,
  CheckCircle,
  AlertCircle,
  Clock,
  ExternalLink,
  DollarSign,
  TrendingUp,
  Calendar,
  Info,
  ArrowRight,
} from "lucide-react";

export default function PayoutsPage() {
  const [payoutStatus, setPayoutStatus] = useState<
    "not_setup" | "pending" | "active" | "requires_action"
  >("not_setup");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedGateway, setSelectedGateway] = useState<
    "paystack" | "korapay" | null
  >(null);
  const [showGatewaySelection, setShowGatewaySelection] = useState(false);

  // Payment gateway options
  const paymentGateways = [
    {
      id: "paystack",
      name: "Paystack",
      description: "Market leader in Nigeria, Kenya, Ghana",
      features: [
        "T+2 business days settlement",
        "Instant settlement available (eligible merchants)",
        "Local payment methods (M-Pesa, USSD, Bank Transfer)",
        "Strong fraud protection",
      ],
      fees: {
        standard: "1.5% + ₦100 (Nigeria), 2.9% (Kenya)",
        instant: "2.9% + ₦100 (additional fee for instant)",
      },
      countries: ["Nigeria", "Kenya", "Ghana"],
      logo: "🟢", // In real app, use actual logo
      recommended: true,
    },
    {
      id: "korapay",
      name: "Korapay",
      description: "Fast-growing African payment processor",
      features: [
        "T+1 to T+2 business days settlement",
        "Competitive fees across Africa",
        "Multi-currency support",
        "Strong mobile money integration",
      ],
      fees: {
        standard: "1.4% + local fees",
        instant: "2.5% + local fees",
      },
      countries: ["Nigeria", "Kenya", "Ghana", "South Africa"],
      logo: "🔵", // In real app, use actual logo
      recommended: false,
    },
  ];

  // Mock data for demonstration
  const payoutData = {
    totalEarnings: 45230,
    pendingPayouts: 8500,
    lastPayout: {
      amount: 6200,
      date: "2024-01-15",
      status: "completed",
    },
    recentPayouts: [
      {
        id: "1",
        amount: 6200,
        date: "2024-01-15",
        status: "completed",
        invoice: "INV-001",
      },
      {
        id: "2",
        amount: 4800,
        date: "2024-01-10",
        status: "completed",
        invoice: "INV-002",
      },
      {
        id: "3",
        amount: 3200,
        date: "2024-01-05",
        status: "pending",
        invoice: "INV-003",
      },
    ],
  };

  const handleSetupPayouts = () => {
    setShowGatewaySelection(true);
  };

  const handleGatewaySelection = async (gatewayId: "paystack" | "korapay") => {
    setSelectedGateway(gatewayId);
    setIsLoading(true);

    try {
      // In real implementation, this would call:
      // POST /api/v1/users/me/payouts/onboarding-link
      // with the selected gateway

      const response = await fetch("/api/v1/users/me/payouts/onboarding-link", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ gateway: gatewayId }),
      });

      if (response.ok) {
        const data = await response.json();
        // Redirect to the gateway's hosted onboarding form
        window.location.href = data.onboarding_url;
      } else {
        // For demo purposes, simulate the flow
        const gatewayName = paymentGateways.find(
          (g) => g.id === gatewayId
        )?.name;
        alert(`Redirecting to secure ${gatewayName} onboarding form...`);
        setPayoutStatus("pending");
        setShowGatewaySelection(false);
      }
    } catch (error) {
      console.error("Error setting up payouts:", error);
      // For demo purposes, simulate the flow
      const gatewayName = paymentGateways.find((g) => g.id === gatewayId)?.name;
      alert(`Redirecting to secure ${gatewayName} onboarding form...`);
      setPayoutStatus("pending");
      setShowGatewaySelection(false);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "not_setup":
        return <AlertCircle className="h-5 w-5 text-yellow-400" />;
      case "pending":
        return <Clock className="h-5 w-5 text-blue-400" />;
      case "active":
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "requires_action":
        return <AlertCircle className="h-5 w-5 text-red-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "not_setup":
        return "border-yellow-400/30 bg-yellow-400/10 text-yellow-400";
      case "pending":
        return "border-blue-400/30 bg-blue-400/10 text-blue-400";
      case "active":
        return "border-green-400/30 bg-green-400/10 text-green-400";
      case "requires_action":
        return "border-red-400/30 bg-red-400/10 text-red-400";
      default:
        return "border-gray-400/30 bg-gray-400/10 text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "not_setup":
        return "Not Set Up";
      case "pending":
        return "Pending Verification";
      case "active":
        return "Active";
      case "requires_action":
        return "Action Required";
      default:
        return "Unknown";
    }
  };

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            PAYOUTS & EARNINGS
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Payment Setup
              </h2>
              <p className="text-white/60 mt-1">
                Set up your account to receive payments from clients
                automatically.
              </p>
            </div>
            {payoutStatus === "not_setup" && (
              <button
                onClick={handleSetupPayouts}
                disabled={isLoading}
                className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20 flex items-center gap-2 disabled:opacity-50"
              >
                <Wallet className="h-4 w-4" />
                {isLoading ? "Setting up..." : "Set Up Payouts"}
              </button>
            )}
          </div>
        </div>

        {/* Status Banner */}
        <div className={`border p-6 ${getStatusColor(payoutStatus)}`}>
          <div className="flex items-start gap-4">
            {getStatusIcon(payoutStatus)}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-medium">
                  Payout Status: {getStatusText(payoutStatus)}
                </h3>
              </div>

              {payoutStatus === "not_setup" && (
                <div className="space-y-2">
                  <p className="text-sm opacity-90">
                    Connect your bank account to receive payments automatically
                    when clients pay invoices.
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <Shield className="h-4 w-4" />
                    <span>Secure setup powered by Paystack</span>
                  </div>
                </div>
              )}

              {payoutStatus === "pending" && (
                <div className="space-y-2">
                  <p className="text-sm opacity-90">
                    Your account is being verified. This usually takes 1-2
                    business days.
                  </p>
                  <p className="text-sm opacity-75">
                    You'll receive an email once verification is complete.
                  </p>
                </div>
              )}

              {payoutStatus === "active" && (
                <div className="space-y-2">
                  <p className="text-sm opacity-90">
                    Your payout account is active! Payments will be
                    automatically transferred to your bank account.
                  </p>
                  <div className="flex items-center gap-4 text-sm opacity-75">
                    <span>• Payouts processed within 1-3 business days</span>
                    <span>• Platform fee: 2.9% + $0.30 per transaction</span>
                  </div>
                </div>
              )}

              {payoutStatus === "requires_action" && (
                <div className="space-y-2">
                  <p className="text-sm opacity-90">
                    Additional information required to complete your account
                    setup.
                  </p>
                  <button className="text-sm underline flex items-center gap-1">
                    Complete setup <ExternalLink className="h-3 w-3" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Earnings Overview */}
        {payoutStatus === "active" && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-green-400/10 text-green-400">
                  <DollarSign className="h-5 w-5" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${payoutData.totalEarnings.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Total Earnings</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-blue-400/10 text-blue-400">
                  <Clock className="h-5 w-5" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${payoutData.pendingPayouts.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Pending Payouts</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-purple-400/10 text-purple-400">
                  <TrendingUp className="h-5 w-5" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${payoutData.lastPayout.amount.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Last Payout</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* How It Works */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center gap-2 mb-6">
            <Info className="h-5 w-5 text-blue-400" />
            <h3 className="text-lg font-medium text-white">How Payouts Work</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-400/10 text-blue-400 flex items-center justify-center mx-auto mb-3">
                <CreditCard className="h-6 w-6" />
              </div>
              <h4 className="text-white font-medium mb-2">
                1. Client Pays Invoice
              </h4>
              <p className="text-white/60 text-sm">
                Your client pays an invoice through the secure payment link you
                send them.
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-400/10 text-green-400 flex items-center justify-center mx-auto mb-3">
                <Shield className="h-6 w-6" />
              </div>
              <h4 className="text-white font-medium mb-2">
                2. Payment Processed
              </h4>
              <p className="text-white/60 text-sm">
                Payment is securely processed by Paystack with platform fees
                automatically deducted.
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-400/10 text-purple-400 flex items-center justify-center mx-auto mb-3">
                <Wallet className="h-6 w-6" />
              </div>
              <h4 className="text-white font-medium mb-2">
                3. Money in Your Account
              </h4>
              <p className="text-white/60 text-sm">
                Funds are automatically transferred to your connected bank
                account within 1-3 business days.
              </p>
            </div>
          </div>
        </div>

        {/* Recent Payouts */}
        {payoutStatus === "active" && (
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-6">
              <Calendar className="h-5 w-5 text-purple-400" />
              <h3 className="text-lg font-medium text-white">Recent Payouts</h3>
            </div>

            <div className="space-y-3">
              {payoutData.recentPayouts.map((payout) => (
                <div
                  key={payout.id}
                  className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        payout.status === "completed" ?
                          "bg-green-400"
                        : "bg-blue-400"
                      }`}
                    />
                    <div>
                      <div className="text-white font-medium">
                        ${payout.amount.toLocaleString()}
                      </div>
                      <div className="text-white/60 text-sm">
                        {payout.invoice} •{" "}
                        {new Date(payout.date).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div
                    className={`px-2 py-1 text-xs border ${
                      payout.status === "completed" ?
                        "border-green-400/30 bg-green-400/10 text-green-400"
                      : "border-blue-400/30 bg-blue-400/10 text-blue-400"
                    }`}
                  >
                    {payout.status.toUpperCase()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Gateway Selection Modal */}
        {showGatewaySelection && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-black border border-white/20 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-white">
                    Choose Your Payment Gateway
                  </h3>
                  <button
                    onClick={() => setShowGatewaySelection(false)}
                    className="text-white/60 hover:text-white"
                  >
                    ✕
                  </button>
                </div>

                <p className="text-white/60 mb-6">
                  Select the payment processor that works best for your location
                  and needs. You can change this later in your settings.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {paymentGateways.map((gateway) => (
                    <div
                      key={gateway.id}
                      className={`border p-6 cursor-pointer transition-all ${
                        gateway.recommended ?
                          "border-green-400/30 bg-green-400/5"
                        : "border-white/20 hover:border-white/40"
                      }`}
                      onClick={() =>
                        handleGatewaySelection(
                          gateway.id as "paystack" | "korapay"
                        )
                      }
                    >
                      {gateway.recommended && (
                        <div className="inline-block px-2 py-1 bg-green-400/10 text-green-400 text-xs border border-green-400/30 mb-3">
                          RECOMMENDED
                        </div>
                      )}

                      <div className="flex items-center gap-3 mb-4">
                        <span className="text-2xl">{gateway.logo}</span>
                        <div>
                          <h4 className="text-lg font-medium text-white">
                            {gateway.name}
                          </h4>
                          <p className="text-white/60 text-sm">
                            {gateway.description}
                          </p>
                        </div>
                      </div>

                      <div className="space-y-3 mb-4">
                        <div>
                          <h5 className="text-white font-medium text-sm mb-2">
                            Features:
                          </h5>
                          <ul className="space-y-1">
                            {gateway.features.map((feature, index) => (
                              <li
                                key={index}
                                className="text-white/60 text-sm flex items-center gap-2"
                              >
                                <CheckCircle className="h-3 w-3 text-green-400" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h5 className="text-white font-medium text-sm mb-2">
                            Fees:
                          </h5>
                          <div className="space-y-1">
                            <div className="text-white/60 text-sm">
                              Standard: {gateway.fees.standard}
                            </div>
                            <div className="text-white/60 text-sm">
                              Instant: {gateway.fees.instant}
                            </div>
                          </div>
                        </div>

                        <div>
                          <h5 className="text-white font-medium text-sm mb-2">
                            Available in:
                          </h5>
                          <div className="flex flex-wrap gap-1">
                            {gateway.countries.map((country, index) => (
                              <span
                                key={index}
                                className="px-2 py-1 bg-white/10 text-white/60 text-xs"
                              >
                                {country}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>

                      <button
                        disabled={isLoading}
                        className={`w-full py-3 px-4 text-center transition-colors ${
                          gateway.recommended ?
                            "bg-green-400/10 text-green-400 border border-green-400/30 hover:bg-green-400/20"
                          : "bg-white/5 text-white border border-white/20 hover:bg-white/10"
                        } disabled:opacity-50`}
                      >
                        {isLoading && selectedGateway === gateway.id ?
                          "Setting up..."
                        : `Set Up with ${gateway.name}`}
                      </button>
                    </div>
                  ))}
                </div>

                <div className="mt-6 p-4 border border-blue-400/30 bg-blue-400/10">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-400 mt-0.5" />
                    <div className="text-sm text-blue-400">
                      <p className="font-medium mb-1">Secure Setup Process</p>
                      <p className="opacity-90">
                        You'll be redirected to your chosen payment processor's
                        secure onboarding form. Your financial information is
                        handled directly by them and never stored on DevHQ
                        servers.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Notice */}
        <div className="border border-blue-400/30 bg-blue-400/10 p-4">
          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-blue-400 mt-0.5" />
            <div className="text-sm text-blue-400">
              <p className="font-medium mb-1">Secure & Compliant</p>
              <p className="opacity-90">
                Your financial information is handled by Paystack, a
                PCI-compliant payment processor. DevHQ never stores your bank
                account details or sensitive financial information.
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
