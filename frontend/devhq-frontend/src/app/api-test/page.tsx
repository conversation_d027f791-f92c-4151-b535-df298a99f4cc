/** @format */

"use client";

import { useEffect, useState } from "react";
import { apiClient } from "@/lib/api";

export default function ApiTestPage() {
  const [status, setStatus] = useState("Checking...");
  const [error, setError] = useState("");

  useEffect(() => {
    const checkApi = async () => {
      try {
        const response = await apiClient.get("/auth/me");
        setStatus("Mock API Connection Successful! (Frontend-Only Mode)");
      } catch (err: any) {
        console.error("API Test Error:", err);
        setStatus("API Connection Failed");
        setError(err.message || "Unknown error");
      }
    };

    checkApi();
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 bg-neon-primary rounded-md flex items-center justify-center">
              <div className="w-6 h-6 bg-background rounded-sm"></div>
            </div>
          </div>
          <h1 className="text-2xl font-bold text-neon-primary font-mono">
            DEVHQ
          </h1>
          <p className="text-foreground-secondary mt-2">
            Mock API Connection Test (Frontend-Only)
          </p>
        </div>

        <div className="bg-background-tertiary border border-border rounded-lg p-6">
          <div className="text-center">
            <h2 className="text-lg font-semibold mb-2">{status}</h2>
            {error && <p className="text-neon-error text-sm">{error}</p>}
            <div className="mt-4">
              <div className="w-16 h-16 bg-neon-primary/10 rounded-full flex items-center justify-center mx-auto">
                <div className="w-8 h-8 bg-neon-primary rounded-md animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
