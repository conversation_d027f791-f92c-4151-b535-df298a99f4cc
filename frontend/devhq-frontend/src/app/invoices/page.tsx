/** @format */

"use client";

import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  CreditCard,
  Plus,
  Search,
  Filter,
  Download,
  Send,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  DollarSign,
} from "lucide-react";

export default function InvoicesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock invoice data
  const invoices = [
    {
      id: "INV-001",
      client: "TechCorp Inc.",
      project: "E-commerce Platform",
      amount: 8500,
      status: "paid",
      dueDate: "2024-01-15",
      createdDate: "2024-01-01",
      paidDate: "2024-01-12",
    },
    {
      id: "INV-002",
      client: "StartupXYZ",
      project: "Mobile App Design",
      amount: 6200,
      status: "sent",
      dueDate: "2024-02-01",
      createdDate: "2024-01-15",
      paidDate: null,
    },
    {
      id: "INV-003",
      client: "Creative Agency",
      project: "Brand Identity",
      amount: 4800,
      status: "draft",
      dueDate: "2024-02-15",
      createdDate: "2024-01-20",
      paidDate: null,
    },
    {
      id: "INV-004",
      client: "Local Business",
      project: "Website Redesign",
      amount: 3200,
      status: "overdue",
      dueDate: "2024-01-20",
      createdDate: "2024-01-05",
      paidDate: null,
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case "sent":
        return <Send className="h-4 w-4 text-blue-400" />;
      case "draft":
        return <Clock className="h-4 w-4 text-yellow-400" />;
      case "overdue":
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "border-green-400/30 bg-green-400/10 text-green-400";
      case "sent":
        return "border-blue-400/30 bg-blue-400/10 text-blue-400";
      case "draft":
        return "border-yellow-400/30 bg-yellow-400/10 text-yellow-400";
      case "overdue":
        return "border-red-400/30 bg-red-400/10 text-red-400";
      default:
        return "border-gray-400/30 bg-gray-400/10 text-gray-400";
    }
  };

  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      invoice.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || invoice.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalRevenue = invoices
    .filter((inv) => inv.status === "paid")
    .reduce((sum, inv) => sum + inv.amount, 0);

  const pendingAmount = invoices
    .filter((inv) => inv.status === "sent")
    .reduce((sum, inv) => sum + inv.amount, 0);

  const overdueAmount = invoices
    .filter((inv) => inv.status === "overdue")
    .reduce((sum, inv) => sum + inv.amount, 0);

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            INVOICES & PAYMENTS
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Invoice Management
              </h2>
              <p className="text-white/60 mt-1">
                Create, send, and track your invoices and payments.
              </p>
            </div>
            <button className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20 flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Invoice
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-green-400/10 text-green-400">
                <DollarSign className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  ${totalRevenue.toLocaleString()}
                </div>
                <div className="text-sm text-white/60">Total Revenue</div>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-blue-400/10 text-blue-400">
                <Clock className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  ${pendingAmount.toLocaleString()}
                </div>
                <div className="text-sm text-white/60">Pending Payment</div>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-red-400/10 text-red-400">
                <AlertCircle className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  ${overdueAmount.toLocaleString()}
                </div>
                <div className="text-sm text-white/60">Overdue Amount</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="flex gap-2">
            <button
              onClick={() => setStatusFilter("all")}
              className={`border px-3 py-1 text-xs ${
                statusFilter === "all"
                  ? "border-green-400 bg-green-400/10 text-green-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              All ({invoices.length})
            </button>
            <button
              onClick={() => setStatusFilter("draft")}
              className={`border px-3 py-1 text-xs ${
                statusFilter === "draft"
                  ? "border-yellow-400 bg-yellow-400/10 text-yellow-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              Draft ({invoices.filter((inv) => inv.status === "draft").length})
            </button>
            <button
              onClick={() => setStatusFilter("sent")}
              className={`border px-3 py-1 text-xs ${
                statusFilter === "sent"
                  ? "border-blue-400 bg-blue-400/10 text-blue-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              Sent ({invoices.filter((inv) => inv.status === "sent").length})
            </button>
            <button
              onClick={() => setStatusFilter("paid")}
              className={`border px-3 py-1 text-xs ${
                statusFilter === "paid"
                  ? "border-green-400 bg-green-400/10 text-green-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              Paid ({invoices.filter((inv) => inv.status === "paid").length})
            </button>
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder="Search invoices..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 p-2 pl-10 bg-transparent border border-white/20 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-white/40" />
          </div>
        </div>

        {/* Invoices List */}
        <div className="border border-white/20 bg-transparent">
          <div className="grid grid-cols-12 gap-4 p-4 border-b border-white/20 text-xs text-white/60 uppercase tracking-wider">
            <div className="col-span-2">Invoice ID</div>
            <div className="col-span-2">Client</div>
            <div className="col-span-2">Project</div>
            <div className="col-span-2">Amount</div>
            <div className="col-span-2">Status</div>
            <div className="col-span-2">Actions</div>
          </div>

          <div className="divide-y divide-white/10">
            {filteredInvoices.map((invoice) => (
              <div
                key={invoice.id}
                className="grid grid-cols-12 gap-4 p-4 items-center hover:bg-white/5"
              >
                <div className="col-span-2 text-white font-medium">
                  {invoice.id}
                </div>
                <div className="col-span-2 text-white">{invoice.client}</div>
                <div className="col-span-2 text-white/60">{invoice.project}</div>
                <div className="col-span-2 text-white font-medium">
                  ${invoice.amount.toLocaleString()}
                </div>
                <div className="col-span-2">
                  <div
                    className={`inline-flex items-center gap-2 px-2 py-1 text-xs border ${getStatusColor(
                      invoice.status
                    )}`}
                  >
                    {getStatusIcon(invoice.status)}
                    {invoice.status.toUpperCase()}
                  </div>
                </div>
                <div className="col-span-2 flex items-center gap-2">
                  <button className="border border-white/20 bg-transparent px-2 py-1 text-white/60 hover:text-white">
                    <Eye className="h-4 w-4" />
                  </button>
                  <button className="border border-white/20 bg-transparent px-2 py-1 text-white/60 hover:text-white">
                    <Download className="h-4 w-4" />
                  </button>
                  {invoice.status === "draft" && (
                    <button className="border border-blue-400/30 bg-blue-400/10 px-2 py-1 text-blue-400 hover:bg-blue-400/20">
                      <Send className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
