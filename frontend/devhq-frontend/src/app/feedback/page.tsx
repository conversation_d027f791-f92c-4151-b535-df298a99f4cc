'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useState } from 'react'
import Link from 'next/link'
import { Square, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function FeedbackPage() {
  const [feedbackType, setFeedbackType] = useState('general')
  const [subject, setSubject] = useState('')
  const [message, setMessage] = useState('')
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle feedback submission
    console.log('Feedback submitted:', { feedbackType, subject, message })
    // Show success message or redirect
    alert('Thank you for your feedback! We\'ll get back to you soon.')
    router.push('/dashboard')
  }

  return (
    <div className="flex flex-col min-h-screen bg-black text-white">
      {/* Header with Logo */}
      <header className="sticky top-0 z-50 w-full p-6">
        <div className="flex items-center justify-between">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <Square className="h-8 w-8 text-primary border border-neon-electric animate-blink" />
            <span className="font-bold text-xl">DevHQ</span>
          </Link>
          <Button 
            variant="ghost" 
            onClick={() => router.back()}
            className="flex items-center space-x-2 text-gray-400 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 relative overflow-hidden">
        {/* Hero Background Elements */}
        <div className="absolute inset-0 z-0">
          {/* Subtle radial gradient */}
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(0,255,136,0.08)_0%,rgba(0,0,0,0)_70%)]"></div>
          
          {/* Grid pattern with a tech feel */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(0,255,136,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(0,255,136,0.05)_1px,transparent_1px)] bg-[size:60px_60px] [mask-image:radial-gradient(ellipse_at_center,black_20%,transparent_70%)]"></div>
          
          {/* Floating geometric shapes */}
          <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-green-500/20 rotate-45"></div>
          <div className="absolute top-1/3 right-1/4 w-24 h-24 border border-green-500/20 rounded-full"></div>
          <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-green-500/5 transform rotate-12"></div>
        </div>

        {/* Feedback Form */}
        <div className="relative z-10 flex items-center justify-center min-h-full py-12 px-4">
          <div className="w-full max-w-md">
            <Card className="bg-black/20 backdrop-blur-md border border-white/10 shadow-xl">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-white">Send Feedback</CardTitle>
                <CardDescription className="text-gray-300">
                  Help us improve DevHQ by sharing your thoughts, suggestions, or reporting issues.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Feedback Type */}
                  <div className="space-y-2">
                    <Label htmlFor="feedback-type" className="text-white">Feedback Type</Label>
                    <select
                      id="feedback-type"
                      value={feedbackType}
                      onChange={(e) => setFeedbackType(e.target.value)}
                      className="w-full px-3 py-2 bg-black/30 backdrop-blur-sm border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="general">General Feedback</option>
                      <option value="bug">Bug Report</option>
                      <option value="feature">Feature Request</option>
                      <option value="improvement">Improvement Suggestion</option>
                    </select>
                  </div>

                  {/* Subject */}
                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-white">Subject</Label>
                    <Input
                      id="subject"
                      type="text"
                      placeholder="Brief description of your feedback"
                      value={subject}
                      onChange={(e) => setSubject(e.target.value)}
                      className="bg-black/30 backdrop-blur-sm border-white/20 text-white placeholder-gray-400 focus:border-green-500"
                      required
                    />
                  </div>

                  {/* Message */}
                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-white">Message</Label>
                    <textarea
                      id="message"
                      placeholder="Please provide detailed feedback..."
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      rows={5}
                      className="w-full px-3 py-2 bg-black/30 backdrop-blur-sm border border-white/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                      required
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="flex flex-col space-y-3 pt-4">
                    <Button 
                      type="submit" 
                      className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2"
                    >
                      Send Feedback
                    </Button>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      onClick={() => router.back()}
                      className="w-full text-gray-400 hover:text-white hover:bg-white/10"
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Additional Info */}
            <div className="mt-6 text-center text-gray-400 text-sm">
              <p>Your feedback helps us make DevHQ better for everyone.</p>
              <p className="mt-1">We typically respond within 24-48 hours.</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}