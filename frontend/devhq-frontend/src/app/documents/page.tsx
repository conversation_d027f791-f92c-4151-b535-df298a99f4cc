/** @format */

"use client";

import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  FileText,
  Upload,
  Search,
  Filter,
  Download,
  Eye,
  Share,
  Trash2,
  Folder,
  Image,
  File,
  Archive,
  Calendar,
} from "lucide-react";

export default function DocumentsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Mock document data
  const documents = [
    {
      id: "1",
      name: "Project Proposal - TechCorp.pdf",
      type: "pdf",
      size: "2.4 MB",
      uploadDate: "2024-01-15",
      project: "E-commerce Platform",
      client: "TechCorp Inc.",
      category: "proposal",
    },
    {
      id: "2",
      name: "Brand Guidelines.zip",
      type: "archive",
      size: "15.2 MB",
      uploadDate: "2024-01-12",
      project: "Brand Identity",
      client: "Creative Agency",
      category: "deliverable",
    },
    {
      id: "3",
      name: "Wireframes_v2.fig",
      type: "design",
      size: "8.7 MB",
      uploadDate: "2024-01-10",
      project: "Mobile App Design",
      client: "StartupXYZ",
      category: "design",
    },
    {
      id: "4",
      name: "Contract_Signed.pdf",
      type: "pdf",
      size: "1.1 MB",
      uploadDate: "2024-01-08",
      project: "Website Redesign",
      client: "Local Business",
      category: "contract",
    },
    {
      id: "5",
      name: "Screenshots.zip",
      type: "archive",
      size: "5.3 MB",
      uploadDate: "2024-01-05",
      project: "E-commerce Platform",
      client: "TechCorp Inc.",
      category: "reference",
    },
    {
      id: "6",
      name: "Logo_Final.svg",
      type: "image",
      size: "245 KB",
      uploadDate: "2024-01-03",
      project: "Brand Identity",
      client: "Creative Agency",
      category: "deliverable",
    },
  ];

  const getFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return <FileText className="h-8 w-8 text-red-400" />;
      case "image":
        return <Image className="h-8 w-8 text-green-400" />;
      case "archive":
        return <Archive className="h-8 w-8 text-yellow-400" />;
      case "design":
        return <File className="h-8 w-8 text-purple-400" />;
      default:
        return <File className="h-8 w-8 text-gray-400" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "proposal":
        return "border-blue-400/30 bg-blue-400/10 text-blue-400";
      case "contract":
        return "border-green-400/30 bg-green-400/10 text-green-400";
      case "deliverable":
        return "border-purple-400/30 bg-purple-400/10 text-purple-400";
      case "design":
        return "border-orange-400/30 bg-orange-400/10 text-orange-400";
      case "reference":
        return "border-gray-400/30 bg-gray-400/10 text-gray-400";
      default:
        return "border-gray-400/30 bg-gray-400/10 text-gray-400";
    }
  };

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.client.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "all" || doc.type === typeFilter;
    return matchesSearch && matchesType;
  });

  const totalSize = documents.reduce((sum, doc) => {
    const size = parseFloat(doc.size.split(" ")[0]);
    const unit = doc.size.split(" ")[1];
    return sum + (unit === "MB" ? size : size / 1024);
  }, 0);

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            DOCUMENT MANAGEMENT
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Files & Documents
              </h2>
              <p className="text-white/60 mt-1">
                Centralized storage for all your project files and documents.
              </p>
            </div>
            <button className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20 flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Upload Files
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-blue-400/10 text-blue-400">
                <FileText className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">{documents.length}</div>
                <div className="text-sm text-white/60">Total Files</div>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-green-400/10 text-green-400">
                <Archive className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  {totalSize.toFixed(1)} MB
                </div>
                <div className="text-sm text-white/60">Total Size</div>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-purple-400/10 text-purple-400">
                <Folder className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  {new Set(documents.map((d) => d.project)).size}
                </div>
                <div className="text-sm text-white/60">Projects</div>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-orange-400/10 text-orange-400">
                <Calendar className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  {
                    documents.filter(
                      (d) =>
                        new Date(d.uploadDate) >
                        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    ).length
                  }
                </div>
                <div className="text-sm text-white/60">This Week</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="flex gap-2">
            <button
              onClick={() => setTypeFilter("all")}
              className={`border px-3 py-1 text-xs ${
                typeFilter === "all"
                  ? "border-green-400 bg-green-400/10 text-green-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              All ({documents.length})
            </button>
            <button
              onClick={() => setTypeFilter("pdf")}
              className={`border px-3 py-1 text-xs ${
                typeFilter === "pdf"
                  ? "border-red-400 bg-red-400/10 text-red-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              PDF ({documents.filter((d) => d.type === "pdf").length})
            </button>
            <button
              onClick={() => setTypeFilter("image")}
              className={`border px-3 py-1 text-xs ${
                typeFilter === "image"
                  ? "border-green-400 bg-green-400/10 text-green-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              Images ({documents.filter((d) => d.type === "image").length})
            </button>
            <button
              onClick={() => setTypeFilter("archive")}
              className={`border px-3 py-1 text-xs ${
                typeFilter === "archive"
                  ? "border-yellow-400 bg-yellow-400/10 text-yellow-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              Archives ({documents.filter((d) => d.type === "archive").length})
            </button>
          </div>

          <div className="flex items-center gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64 p-2 pl-10 bg-transparent border border-white/20 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-white/40" />
            </div>
          </div>
        </div>

        {/* Documents Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDocuments.map((doc) => (
            <div
              key={doc.id}
              className="border border-white/20 bg-transparent p-6 hover:bg-white/5 transition-colors"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  {getFileIcon(doc.type)}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-white font-medium truncate">{doc.name}</h3>
                    <p className="text-white/60 text-sm">{doc.size}</p>
                  </div>
                </div>
                <div
                  className={`px-2 py-1 text-xs border ${getCategoryColor(doc.category)}`}
                >
                  {doc.category.toUpperCase()}
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="text-white/60 text-sm">
                  <span className="font-medium">Project:</span> {doc.project}
                </div>
                <div className="text-white/60 text-sm">
                  <span className="font-medium">Client:</span> {doc.client}
                </div>
                <div className="text-white/60 text-sm">
                  <span className="font-medium">Uploaded:</span>{" "}
                  {new Date(doc.uploadDate).toLocaleDateString()}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <button className="border border-white/20 bg-transparent px-2 py-1 text-white/60 hover:text-white">
                    <Eye className="h-4 w-4" />
                  </button>
                  <button className="border border-white/20 bg-transparent px-2 py-1 text-white/60 hover:text-white">
                    <Download className="h-4 w-4" />
                  </button>
                  <button className="border border-white/20 bg-transparent px-2 py-1 text-white/60 hover:text-white">
                    <Share className="h-4 w-4" />
                  </button>
                </div>
                <button className="border border-red-400/30 bg-red-400/10 px-2 py-1 text-red-400 hover:bg-red-400/20">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}
