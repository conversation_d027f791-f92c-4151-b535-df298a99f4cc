/** @format */

"use client";

import { RegisterForm } from "@/components/forms/register-form";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { SiGithub, SiGoogle } from "react-icons/si";
import { oauthApi } from "@/lib/api";
import { useState } from "react";

export default function RegisterPage() {
  const [oauthLoading, setOauthLoading] = useState<string | null>(null);

  async function handleOAuthLogin(provider: "google" | "github") {
    setOauthLoading(provider);

    try {
      const { authorization_url } = await oauthApi.getAuthUrl(provider);
      // Redirect to OAuth provider
      window.location.href = authorization_url;
    } catch (err: any) {
      console.error(`Failed to initiate ${provider} login:`, err);
      setOauthLoading(null);
    }
  }
  return (
    <div className="relative overflow-hidden min-h-screen text-white flex flex-col items-center">
      {/* Futuristic background elements */}

      <div className="w-full max-w-md relative z-10 pt-10">
        <h1 className="text-2xl font-semibold mb-3">Create an account</h1>

        <div className="space-y-3 mb-3 flex flex-col items-center">
          <Button
            onClick={() => handleOAuthLogin("github")}
            disabled={oauthLoading !== null}
            className="w-full bg-transparent border border-white/20 text-white hover:bg-white hover:text-black flex items-center justify-center gap-3 py-3"
          >
            {oauthLoading === "github" ?
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
            : <SiGithub className="w-5 h-5" />}
            Continue with GitHub
          </Button>
          <Button
            onClick={() => handleOAuthLogin("google")}
            disabled={oauthLoading !== null}
            className="w-full bg-transparent border border-white/20 text-white hover:bg-white hover:text-black flex items-center justify-center gap-3 py-3"
          >
            {oauthLoading === "google" ?
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
            : <SiGoogle className="w-5 h-5" />}
            Continue with Google
          </Button>
        </div>

        <div className="text-center mb-3">
          <span className="text-white/70">or</span>
        </div>

        <div className="shadow-lg mb-4">
          <RegisterForm />
        </div>

        <div className="text-left text-sm text-foreground-secondary">
          Already have an account?{" "}
          <Link
            href="/login"
            className="text-green-500 hover:underline font-medium"
          >
            Sign in
          </Link>
        </div>
      </div>
    </div>
  );
}
