'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { apiClient } from '@/lib/api'
import { Button } from '@/components/ui/button'


export default function VerifyEmailPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading')
  const [message, setMessage] = useState('')
  const [email, setEmail] = useState('')
  const searchParams = useSearchParams()
  const router = useRouter()
  const token = searchParams.get('token')
  const resend = searchParams.get('resend')

  useEffect(() => {
    console.log('VerifyEmailPage mounted with token:', token)
    console.log('Resend param:', resend)
    
    if (resend === 'true') {
      setStatus('error')
      setMessage('Please enter your email to resend verification')
      return
    }
    
    if (!token) {
      setStatus('error')
      setMessage('No verification token provided')
      console.error('No token found in URL parameters')
      return
    }

    verifyEmail(token)
  }, [token, resend])

  const verifyEmail = async (token: string) => {
    console.log('Attempting to verify email with token:', token)
    console.log('Token length:', token.length)
    
    try {
      const response = await apiClient.post('/auth/verify-email', {
        token: token
      })

      setStatus('success')
      setMessage('Email verified successfully! Please log in to continue.')
      
      // Redirect to login after 3 seconds with success message
      setTimeout(() => {
        router.push('/login?verified=true')
      }, 3000)
    } catch (error: any) {
      console.error('Email verification error:', error)
      console.error('Error response:', error.response)
      
      if (error.response?.status === 400) {
        setStatus('expired')
        setMessage('This verification link has expired or is invalid.')
      } else {
        setStatus('error')
        setMessage('Failed to verify email. Please try again.')
      }
    }
  }

  const resendVerification = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      console.log('Resending verification for email:', email)
      
      // For resend, we need to make a request without authentication
      // This should be a public endpoint for resending verification
      const response = await apiClient.post('/auth/public-resend-verification', {
        email: email
      })
      
      setStatus('success')
      setMessage('Verification email sent successfully. Please check your inbox.')
    } catch (error: any) {
      console.error('Resend verification error:', error)
      setStatus('error')
      setMessage('Failed to resend verification email. Please try again.')
    }
  }

  return (
    <div className="relative overflow-hidden min-h-screen text-white flex flex-col items-center">
      <div className="w-full max-w-md relative z-10 pt-10">
        <h1 className="text-2xl font-semibold mb-3 text-center">
          {status === 'loading' && 'Verifying Email...'}
          {status === 'success' && '✅ Email Verified!'}
          {status === 'error' && 'Verification Failed'}
          {status === 'expired' && '⏰ Link Expired'}
        </h1>
        <p className="text-sm text-foreground-secondary mb-3 text-center">
          {status === 'loading' && 'Please wait while we verify your email address.'}
          {status === 'success' && 'Welcome to DevHQ! You will be redirected to your dashboard shortly.'}
          {status === 'error' && !email && 'There was a problem verifying your email address.'}
          {status === 'error' && email && 'Enter your email to resend verification.'}
          {status === 'expired' && 'Your verification link has expired.'}
        </p>
        
        <div className="space-y-2">
          {status === 'loading' && (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neon-primary"></div>
            </div>
          )}
          
          {status === 'success' && (
            <div className="text-center space-y-4">
              <p className="text-neon-success font-medium">{message}</p>
              <p className="text-sm text-foreground-muted">
                Redirecting to dashboard...
              </p>
            </div>
          )}
          
          {status === 'error' && !email && (
            <div className="text-center space-y-4">
              <p className="text-neon-error">{message}</p>
              <div className="space-y-2">
                <Button 
                  onClick={() => setEmail('show-form')}
                  variant="neon"
                  className="w-full"
                >
                  Resend Verification Email
                </Button>
                <Button 
                  onClick={() => router.push('/login')}
                  className="w-full bg-green-500 text-black hover:bg-white"
                >
                  Go to Login
                </Button>
              </div>
            </div>
          )}
          
          {status === 'error' && email && (
            <form onSubmit={resendVerification} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-foreground text-sm font-medium">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email === 'show-form' ? '' : email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="border border-white/20 bg-transparent rounded-none focus:border-neon-primary focus:ring-neon-primary/20 w-full px-3 py-2"
                  required
                />
              </div>
              <Button 
                type="submit"
                variant="neon"
                className="w-full"
              >
                Resend Verification
              </Button>
              <Button 
                type="button"
                onClick={() => setEmail('')}
                variant="neon"
                className="w-full"
              >
                Cancel
              </Button>
            </form>
          )}
          
          {status === 'expired' && (
            <div className="text-center space-y-4">
              <p className="text-neon-error">{message}</p>
              <div className="space-y-2">
                <Button 
                  onClick={() => setEmail('show-form')}
                  variant="neon"
                  className="w-full"
                >
                  Resend Verification Email
                </Button>
                <Button 
                  onClick={() => router.push('/login')}
                  className="w-full bg-green-500 text-black hover:bg-white"
                >
                  Go to Login
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}