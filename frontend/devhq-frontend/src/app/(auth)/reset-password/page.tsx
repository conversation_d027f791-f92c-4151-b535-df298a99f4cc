import { ResetPasswordForm } from '@/components/forms/reset-password-form'
import Link from 'next/link'

export default function ResetPasswordPage() {
  return (
    <div className="relative overflow-hidden min-h-screen text-white flex flex-col items-center">
      <div className="w-full max-w-md relative z-10 pt-10">
        <h1 className="text-2xl font-semibold mb-3 text-center">Reset Your Password</h1>
        <p className="text-white/70 mb-6 text-center">
          Enter your new password below.
        </p>
        
        <div className="shadow-lg mb-4">
          <ResetPasswordForm />
        </div>
        
        <div className="text-left text-sm text-foreground-secondary">
          Remembered your password?{' '}
          <Link 
            href="/login" 
            className="text-green-500 hover:underline font-medium"
          >
            Sign in
          </Link>
        </div>
      </div>
    </div>
  )
}