/** @format */

"use client";

import { LoginForm } from "@/components/forms/login-form";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Square } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SiGithub, SiGoogle } from "react-icons/si";
import { oauthApi } from "@/lib/api";

export default function LoginPage() {
  const searchParams = useSearchParams();
  const [showVerificationSuccess, setShowVerificationSuccess] = useState(false);
  const [oauthLoading, setOauthLoading] = useState<string | null>(null);

  useEffect(() => {
    if (searchParams.get("verified") === "true") {
      setShowVerificationSuccess(true);
      // Hide the message after 5 seconds
      setTimeout(() => setShowVerificationSuccess(false), 5000);
    }
  }, [searchParams]);

  async function handleOAuthLogin(provider: "google" | "github") {
    setOauthLoading(provider);

    try {
      const { authorization_url } = await oauthApi.getAuthUrl(provider);
      // Redirect to OAuth provider
      window.location.href = authorization_url;
    } catch (err: any) {
      console.error(`Failed to initiate ${provider} login:`, err);
      setOauthLoading(null);
    }
  }

  return (
    <div className="relative overflow-hidden min-h-screen text-white flex flex-col items-center">
      {/* Futuristic background elements */}

      <div className="w-full max-w-md relative z-10 pt-10">
        <h1 className="text-2xl font-semibold mb-3">Sign in to DevHQ</h1>

        <div className="space-y-3 mb-3 flex flex-col items-center">
          <Button
            onClick={() => handleOAuthLogin("github")}
            disabled={oauthLoading !== null}
            className="w-full bg-transparent border border-white/20 text-white hover:bg-white hover:text-black flex items-center justify-center gap-3 py-3"
          >
            {oauthLoading === "github" ?
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
            : <SiGithub className="w-5 h-5" />}
            Continue with GitHub
          </Button>
          <Button
            onClick={() => handleOAuthLogin("google")}
            disabled={oauthLoading !== null}
            className="w-full bg-transparent border border-white/20 text-white hover:bg-white hover:text-black flex items-center justify-center gap-3 py-3"
          >
            {oauthLoading === "google" ?
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
            : <SiGoogle className="w-5 h-5" />}
            Continue with Google
          </Button>
        </div>

        <div className="text-center mb-3">
          <span className="text-white/70">or</span>
        </div>

        {showVerificationSuccess && (
          <div className="mb-4 p-3 bg-green-500/10 border border-green-500/20">
            <p className="text-green-400 text-sm text-center">
              ✅ Email verified successfully! You can now log in to your
              account.
            </p>
          </div>
        )}

        <div className="shadow-lg">
          <LoginForm />

          <div className="mt-2 mb-4 text-left">
            <Link
              href="/forgot-password"
              className="text-sm text-neon-primary hover:text-neon-primary/80"
            >
              Forgot your password?
            </Link>
          </div>
        </div>

        <div className="text-left text-sm text-foreground-secondary">
          Don't have an account?{" "}
          <Link
            href="/register"
            className="text-green-500 hover:underline font-medium"
          >
            Sign up
          </Link>
        </div>
      </div>
    </div>
  );
}
