import Link from 'next/link'
import { Square } from 'lucide-react'


export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-[#0d0d0d]">
      <div className="container mx-auto px-4 py-4">
        <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity mb-4">
          <Square className="h-10 w-10 text-primary border border-neon-electric animate-blink" />
          <span className="font-bold text-lg">DevHQ</span>
        </Link>
      </div>
      <div className="container mx-auto px-4">
        {children}
      </div>
    </div>
  )
}