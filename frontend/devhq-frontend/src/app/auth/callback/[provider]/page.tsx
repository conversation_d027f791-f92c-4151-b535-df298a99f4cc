'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { oauthApi } from '@/lib/api'

interface OAuthCallbackPageProps {
  params: {
    provider: string
  }
}

export default function OAuthCallbackPage({ params }: OAuthCallbackPageProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setUser } = useAuth()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [error, setError] = useState<string>('')

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        const error = searchParams.get('error')

        if (error) {
          throw new Error(`OAuth error: ${error}`)
        }

        if (!code) {
          throw new Error('No authorization code received')
        }

        if (!['google', 'github'].includes(params.provider)) {
          throw new Error('Invalid OAuth provider')
        }

        // Exchange code for tokens and user info
        const authResponse = await oauthApi.handleCallback(
          params.provider as 'google' | 'github',
          code,
          state || undefined
        )

        // Set user in auth context
        setUser(authResponse.user)
        setStatus('success')

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard')
        }, 2000)

      } catch (err: any) {
        console.error('OAuth callback error:', err)
        setError(err.message || 'Authentication failed')
        setStatus('error')

        // Redirect to login after error
        setTimeout(() => {
          router.push('/login?error=oauth_failed')
        }, 3000)
      }
    }

    handleOAuthCallback()
  }, [params.provider, searchParams, router, setUser])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-neon-primary border-t-transparent mx-auto" />
          <h2 className="text-xl font-semibold text-foreground">
            Completing {params.provider} authentication...
          </h2>
          <p className="text-muted-foreground">
            Please wait while we sign you in.
          </p>
        </div>
      </div>
    )
  }

  if (status === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center mx-auto">
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-foreground">
            Authentication successful!
          </h2>
          <p className="text-muted-foreground">
            Redirecting you to the dashboard...
          </p>
        </div>
      </div>
    )
  }

  if (status === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center mx-auto">
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-foreground">
            Authentication failed
          </h2>
          <p className="text-muted-foreground">
            {error || 'Something went wrong during authentication.'}
          </p>
          <p className="text-sm text-muted-foreground">
            Redirecting you back to login...
          </p>
        </div>
      </div>
    )
  }

  return null
}
