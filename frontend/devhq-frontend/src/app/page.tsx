"use client";

import * as React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CountingStat } from "@/components/ui/CountingStat";
import { ArrowRight, Code, BarChart, Users, DollarSign, Shield, Check, Target, Coffee, Square, Clock, Building2, Globe, Zap, Rocket, Play, Terminal, Cpu, Database } from "lucide-react";
import { DottedArrow } from "@/components/ui/dotted-arrow";
import { SiVercel, SiReplit, SiGithub, SiRetool, SiLinear, SiX, SiLinkedin } from "react-icons/si";
import { motion } from "framer-motion";
import { Navbar } from "@/components/layout/Navbar";
import { NewToDevHQ } from "@/components/layout/NewToDevHQ";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen bg-black text-white">
      <NewToDevHQ />
      <Navbar />

      {/* Main Content */}
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full min-h-screen flex items-center justify-center overflow-hidden bg-black pt-20">
          {/* Futuristic background elements */}
          <div className="absolute inset-0 z-0">
            {/* Subtle radial gradient */}
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(0,255,136,0.08)_0%,rgba(0,0,0,0)_70%)]"></div>
            
            {/* Grid pattern with a tech feel */}
            <div className="absolute inset-0 bg-[linear-gradient(rgba(0,255,136,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(0,255,136,0.05)_1px,transparent_1px)] bg-[size:60px_60px] [mask-image:radial-gradient(ellipse_at_center,black_20%,transparent_70%)]"></div>
            
            {/* Floating geometric shapes */}
            <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-green-500/20 rotate-45"></div>
            <div className="absolute top-1/3 right-1/4 w-24 h-24 border border-green-500/20 rounded-full"></div>
            <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-green-500/5 transform rotate-12"></div>
          </div>

          <div className="container relative z-10 px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
              {/* Left Side - Content */}
              <div className="text-left lg:text-left text-center lg:pl-8">
                {/* Main Headline */}
                <motion.div
                  className="mb-8"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold leading-none tracking-tight mb-6">
                    <span className="block text-white">SHIP</span>
                    <span className="block bg-gradient-to-r from-green-400 to-green-400 bg-clip-text text-transparent">FASTER</span>
                    <span className="block text-white">GET PAID</span>
                  </h1>
                </motion.div>

                {/* Subtitle */}
                <motion.div
                  className="mb-12"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                >
                  <p className="max-w-2xl mx-auto lg:mx-0 text-lg sm:text-xl md:text-2xl font-light text-white/70 leading-relaxed">
                    The all-in-one platform for freelance developers to manage projects, track time, and create invoices effortlessly.
                  </p>
                </motion.div>

                {/* CTA Buttons */}
                <motion.div 
                  className="flex flex-col sm:flex-row items-center lg:items-start justify-center lg:justify-start gap-6 mb-16"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  <Button 
                    asChild 
                    size="lg" 
                    className="dashboard-button-hover-effect text-lg sm:text-xl px-12 py-8 bg-white text-black font-medium"
                  >
                    <Link href="/register" className="flex items-center gap-4" data-text="Get Started for Free">
                      <span>Get Started for Free</span>
                      <DottedArrow className="w-6 h-6 flex-shrink-0" />
                    </Link>
                  </Button>

                  <Button 
                    asChild 
                    size="lg" 
                    className="watch-demo-hover-effect text-lg sm:text-xl px-12 py-8 bg-transparent text-white font-medium border-2 border-white/30"
                  >
                    <Link href="#features" className="flex items-center gap-3" data-text="Watch Demo">
                      <motion.span animate={{ rotate: 0 }} whileHover={{ rotate: 90 }} transition={{ duration: 0.3 }}>
                        <Play className="w-6 h-6" />
                      </motion.span>
                      <span>Watch Demo</span>
                    </Link>
                  </Button>
                </motion.div>
              </div>

              {/* Right Side - Interactive Code Preview */}
              <motion.div
                className="hidden lg:block"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <div className="relative">
                  {/* Glassy Background */}
                  <div className="absolute inset-0 bg-white/5 backdrop-blur-md rounded-xl border border-white/10 shadow-2xl"></div>
                  
                  {/* Terminal Window */}
                  <div className="relative bg-black/20 backdrop-blur-lg border border-white/20 rounded-xl shadow-2xl overflow-hidden">
                    {/* Terminal Header */}
                    <div className="flex items-center justify-between px-4 py-3 border-b border-white/20 bg-white/5">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-red-500 shadow-lg"></div>
                        <div className="w-3 h-3 rounded-full bg-yellow-500 shadow-lg"></div>
                        <div className="w-3 h-3 rounded-full bg-green-500 shadow-lg"></div>
                      </div>
                      <div className="text-white/70 text-sm font-mono font-medium">devhq-terminal</div>
                      <div></div>
                    </div>
                    
                    {/* Terminal Content */}
                    <div className="p-6 font-mono text-sm bg-black/10">
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <span className="text-green-400 font-bold">$</span>
                          <span className="text-white ml-2 font-medium">npm create devhq-project</span>
                        </div>
                        <div className="text-green-400 font-medium">✓ Project created successfully</div>
                        <div className="text-white/80">✓ Time tracking initialized</div>
                        <div className="text-white/80">✓ Invoice templates ready</div>
                        <div className="text-white/80">✓ Client portal configured</div>
                        <div className="flex items-center mt-4">
                          <span className="text-green-400 font-bold">$</span>
                          <span className="text-white ml-2 font-medium">devhq start</span>
                          <span className="animate-pulse text-green-400 ml-1 font-bold">|</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Enhanced Floating Elements */}
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-green-500/30 backdrop-blur-sm rounded-full animate-pulse border border-green-500/50"></div>
                  <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-blue-500/30 backdrop-blur-sm rounded-full animate-pulse delay-1000 border border-blue-500/50"></div>
                  <div className="absolute top-1/2 -right-2 w-4 h-4 bg-purple-500/20 backdrop-blur-sm rounded-full animate-pulse delay-500 border border-purple-500/30"></div>
                </div>
              </motion.div>
            </div>

            {/* Stats Row */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
            >
              {[
                { number: "10K+", label: "Developers", value: 10000 },
                { number: "50M+", label: "Hours Tracked", value: 50000000 },
                { number: "$2.5M+", label: "Revenue Generated", value: 2500000 }
              ].map((stat, i) => (
                <motion.div
                  key={i}
                  className="text-center p-12 rounded-xl"
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="text-3xl md:text-4xl font-bold text-white mb-1">
                    <CountingStat value={stat.value} formatter={(val) => {
                      if (stat.number.includes("K")) return `${(val / 1000).toFixed(0)}K+`;
                      if (stat.number.includes("M")) return `${(val / 1000000).toFixed(1)}M+`;
                      if (stat.number.includes("$")) return `${val.toLocaleString()}`; // For exact dollar amounts
                      return val.toLocaleString();
                    }} />
                  </div>
                  <div className="text-lg text-white font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Trusted By Section */}
        <section className="w-full py-20 bg-black">
          <div className="container">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Trusted by <span className="text-green-400">10,000+</span> Developers
              </h2>
              <p className="text-xl text-white/50 max-w-2xl mx-auto">
                Join top developers at leading companies worldwide
              </p>
            </div>

            {/* Company Logos Grid */}
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center">
                {[
                  { Icon: SiVercel, name: "Vercel" },
                  { Icon: SiReplit, name: "Replit" },
                  { Icon: SiGithub, name: "GitHub" },
                  { Icon: SiRetool, name: "Retool" },
                  { Icon: SiLinear, name: "Linear" }
                ].map((company, index) => (
                  <motion.div
                    key={company.name}
                    className="flex flex-col items-center opacity-70 hover:opacity-100 transition-opacity p-4 rounded-lg hover:bg-black-900/50"
                    whileHover={{ y: -5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <company.Icon className="w-12 h-12 md:w-16 md:h-16 text-white" />
                    <span className="mt-4 text-sm text-white/50">{company.name}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-20 bg-black">
          <div className="container">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Everything You Need in <span className="text-green-400">One Platform</span>
              </h2>
              <p className="text-xl text-white/50 max-w-2xl mx-auto">
                Focus on what you do best. We'll handle the rest.
              </p>
            </div>

            {/* Features Grid */}
            <div className="flex flex-col space-y-20 max-w-6xl mx-auto">
              {[
                {
                  title: "Project Management",
                  description: "Organize tasks, set deadlines, and track progress for all your client projects.",
                  benefit: "Streamline your workflow and hit deadlines with ease.",
                  icon: <Code className="w-6 h-6" />,
                  color: "text-green-400",
                  borderColor: "border-green-500/30"
                },
                {
                  title: "Time Tracking",
                  description: "Log billable hours with a single click. Never lose a minute of your hard work.",
                  benefit: "Maximize your earnings by accurately tracking every hour.",
                  icon: <Clock className="w-6 h-6" />,
                  color: "text-green-400",
                  borderColor: "border-green-500/30"
                },
                {
                  title: "Invoicing",
                  description: "Generate and send professional invoices automatically. Get paid on time.",
                  benefit: "Automate billing and improve your cash flow.",
                  icon: <DollarSign className="w-6 h-6" />,
                  color: "text-purple-400",
                  borderColor: "border-purple-500/30"
                },
                {
                  title: "Client Portal",
                  description: "Give your clients a dedicated portal to view project progress and invoices.",
                  benefit: "Enhance client communication and transparency.",
                  icon: <Users className="w-6 h-6" />,
                  color: "text-green-400",
                  borderColor: "border-green-500/30"
                },
                {
                  title: "Analytics",
                  description: "Understand your business with key metrics on revenue, project profitability, and more.",
                  benefit: "Make data-driven decisions to grow your freelance business.",
                  icon: <BarChart className="w-6 h-6" />,
                  color: "text-orange-400",
                  borderColor: "border-orange-500/30"
                },
                {
                  title: "Secure & Reliable",
                  description: "Your data is safe with us. Industry-standard security practices.",
                  benefit: "Work with peace of mind, knowing your data is protected.",
                  icon: <Shield className="w-6 h-6" />,
                  color: "text-red-400",
                  borderColor: "border-red-500/30"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className={`p-8 rounded-xl transition-all duration-300 group`}
                  initial={{ opacity: 0, y: 20, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, y: 0, x: 0 }}
                  transition={{ duration: 0.7, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  {index % 2 === 0 ? (
                    <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-12">
                      <div className={`w-24 h-24 rounded-lg flex items-center justify-center flex-shrink-0 bg-black ${feature.color}`}>
                        {React.cloneElement(feature.icon, { className: "w-12 h-12" })}
                      </div>
                      <div className="text-center md:text-left">
                        <h3 className="text-4xl font-bold mb-4 text-white">{feature.title}</h3>
                        <p className="text-lg text-white/70 mb-2">{feature.description}</p>
                        <p className="text-base text-white/50">{feature.benefit}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col md:flex-row-reverse items-center md:items-start space-y-6 md:space-y-0 md:space-x-reverse md:space-x-12">
                      <div className={`w-24 h-24 rounded-lg flex items-center justify-center flex-shrink-0 bg-black ${feature.color}`}>
                        {React.cloneElement(feature.icon, { className: "w-12 h-12" })}
                      </div>
                      <div className="text-center md:text-right">
                        <h3 className="text-4xl font-bold mb-4 text-white">{feature.title}</h3>
                        <p className="text-lg text-white/70 mb-2">{feature.description}</p>
                        <p className="text-base text-white/50">{feature.benefit}</p>
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Workflow Section */}
        <section className="w-full py-20 bg-black">
          <div className="container">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  From Chaos to <span className="text-green-400">Clarity</span>
                </h2>
                <p className="text-xl text-white/50 max-w-2xl mx-auto">
                  Stop juggling tools. Start shipping code.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.7 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-2xl font-bold mb-6 text-red-400 flex items-center">
                    <Terminal className="mr-2" />
                    The Developer's Nightmare
                  </h3>
                  <ul className="space-y-4">
                    {[
                      "Lost billable hours",
                      "Scattered project data",
                      "Manual invoice creation",
                      "5+ apps and 20+ tabs",
                      "Endless context switching"
                    ].map((item, i) => (
                      <li key={i} className="flex items-start">
                        <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center mt-1 mr-3 flex-shrink-0">
                          <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        </div>
                        <span className="text-white/70">{item}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>

                <motion.div
                  className="relative"
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.7 }}
                  viewport={{ once: true }}
                >
                  <div className="absolute -inset-4 bg-green-500/10 rounded-2xl blur-xl"></div>
                  <div className="relative bg-black/50 border border-white/10 rounded-2xl p-8 backdrop-blur-sm">
                    <h3 className="text-2xl font-bold mb-6 text-green-400 flex items-center">
                      <Cpu className="mr-2" />
                      The DevHQ Way
                    </h3>
                    <ul className="space-y-4">
                      {[
                        "Unified dashboard",
                        "Smart time tracking",
                        "Automated invoicing",
                        "Client collaboration",
                        "Insightful analytics"
                      ].map((item, i) => (
                        <li key={i} className="flex items-start">
                          <div className="w-5 h-5 rounded-full bg-green-500/20 flex items-center justify-center mt-1 mr-3 flex-shrink-0">
                            <Check className="w-3 h-3 text-green-400" />
                          </div>
                          <span className="text-white/70">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="w-full py-20 bg-black">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Your Best Work <span className="text-green-400">Awaits</span>
              </h2>
              <p className="text-xl text-white/50 mb-8 max-w-2xl mx-auto">
                Focus on what you do best. Let DevHQ handle the rest.
              </p>
              <Button asChild size="lg" className="dashboard-button-hover-effect text-xl px-16 py-10 bg-white text-black font-medium">
                <Link href="/register" className="flex items-center justify-center gap-4" data-text="Start Your Transformation">
                  <span>Start Your Transformation</span>
                  <DottedArrow className="w-7 h-7 flex-shrink-0" />
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t border-white/10 py-12 bg-black">
        <div className="container grid grid-cols-1 gap-8 md:grid-cols-4">
          <div className="flex flex-col gap-4">
            <div className="flex items-center space-x-2">
              <Square className="h-8 w-8 text-primary border border-neon-electric animate-blink" />
              <span className="font-bold text-white">
                DevHQ
              </span>
            </div>
            <p className="text-sm text-white/50">
              The all-in-one platform for freelance developers.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-white/50 hover:text-white/70">
                <SiGithub className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-white/50 hover:text-white/70">
                <SiVercel className="h-5 w-5" />
              </Link>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-8 md:col-span-3 md:grid-cols-4">
            <div>
              <h3 className="font-semibold text-white">Product</h3>
              <ul className="mt-4 space-y-2">
                <li><Link href="#features" className="text-sm text-white/50 hover:text-white/70">Features</Link></li>
                <li><Link href="#" className="text-sm text-white/50 hover:text-white/70">Pricing</Link></li>
                <li><Link href="/login" className="text-sm text-white/50 hover:text-white/70">Login</Link></li>
                <li><Link href="/register" className="text-sm text-white/50 hover:text-white/70">Register</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white">Company</h3>
              <ul className="mt-4 space-y-2">
                <li><Link href="#" className="text-sm text-white/50 hover:text-white/70">About Us</Link></li>
                <li><Link href="#" className="text-sm text-white/50 hover:text-white/70">Blog</Link></li>
                <li><Link href="#" className="text-sm text-white/50 hover:text-white/70">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white">Legal</h3>
              <ul className="mt-4 space-y-2">
                <li><Link href="#" className="text-sm text-white/50 hover:text-white/70">Terms of Service</Link></li>
                <li><Link href="#" className="text-sm text-white/50 hover:text-white/70">Privacy Policy</Link></li>
              </ul>
            </div>
            <div className="md:col-span-1">
              <h3 className="font-semibold text-white">Connect With Us</h3>
              <p className="mt-4 text-sm text-white/50">Follow us on social media for updates and news.</p>
              <div className="mt-4 flex space-x-4">
                <Link href="#" className="text-white/50 hover:text-white/70">
                  <SiX className="h-5 w-5" />
                </Link>
                <Link href="#" className="text-white/50 hover:text-white/70">
                  <SiLinkedin className="h-5 w-5" />
                </Link>
                <Link href="#" className="text-white/50 hover:text-white/70">
                  <SiGithub className="h-5 w-5" />
                </Link>
              </div>
            </div>
            
          </div>
        </div>
        <div className="container mt-8 border-t border-white/10 pt-8 text-center text-sm text-white/50">
          © {new Date().getFullYear()} DevHQ, Inc. All rights reserved.
        </div>
      </footer>
    </div>
  );
}