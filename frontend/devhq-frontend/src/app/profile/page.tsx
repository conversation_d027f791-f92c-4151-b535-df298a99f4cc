/** @format */

"use client";

import React from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { useAuth } from "@/components/providers/auth-provider";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  User,
  Mail,
  Phone,
  Calendar,
  Shield,
  Edit,
  MapPin,
  Briefcase,
  Clock,
} from "lucide-react";
import Link from "next/link";

export default function ProfilePage() {
  const { user } = useAuth();

  // Generate user initials
  const getUserInitials = () => {
    if (user?.first_name && user?.last_name) {
      return `${user.first_name.charAt(0).toUpperCase()}${user.last_name.charAt(0).toUpperCase()}`;
    }
    if (user?.first_name) {
      return user.first_name.charAt(0).toUpperCase();
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return "U";
  };

  // Generate background color based on user initials (matching app's color scheme)
  const getAvatarBgColor = () => {
    const initials = getUserInitials();
    const colors = [
      "bg-blue-400/20 text-blue-400",
      "bg-green-400/20 text-green-400",
      "bg-purple-400/20 text-purple-400",
      "bg-yellow-400/20 text-yellow-400",
      "bg-orange-400/20 text-orange-400",
      "bg-red-400/20 text-red-400",
    ];
    const index = initials.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            USER PROFILE
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">Profile</h2>
              <p className="text-white/60 mt-1">
                View and manage your profile information
              </p>
            </div>
            <Link href="/settings">
              <button className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20 flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Edit Profile
              </button>
            </Link>
          </div>
        </div>

        {/* Profile Card */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center gap-2 mb-6">
            <User className="h-5 w-5 text-blue-400" />
            <h3 className="text-lg font-medium text-white">
              Profile Information
            </h3>
          </div>
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="flex-shrink-0">
              <Avatar className="h-32 w-32 border border-white/20 rounded-full">
                <AvatarImage
                  src={user?.avatar_url}
                  alt={user?.full_name || "User"}
                  className="rounded-full object-cover"
                />
                <AvatarFallback
                  className={`${getAvatarBgColor()} rounded-full text-3xl font-bold border border-white/20`}
                >
                  {getUserInitials()}
                </AvatarFallback>
              </Avatar>
            </div>

            {/* User Info */}
            <div className="flex-1 space-y-4">
              <div>
                <div className="text-2xl font-bold text-white mb-1">
                  {user?.full_name ||
                    `${user?.first_name} ${user?.last_name}` ||
                    "User"}
                </div>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center text-white/60">
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="text-sm">{user?.email}</span>
                  </div>
                  {user?.is_verified && (
                    <div className="flex items-center text-green-400">
                      <Shield className="h-4 w-4 mr-1" />
                      <span className="text-xs">Verified</span>
                    </div>
                  )}
                </div>
              </div>

              {user?.bio && (
                <div>
                  <p className="text-white/60 text-sm leading-relaxed">
                    {user.bio}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Contact Information */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Mail className="h-5 w-5 text-green-400" />
              <h3 className="text-lg font-medium text-white">
                Contact Information
              </h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-white/60">Email</span>
                <span className="text-sm text-white">
                  {user?.email || "Not provided"}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-white/60">Phone</span>
                <span className="text-sm text-white">
                  {user?.phone || "Not provided"}
                </span>
              </div>
            </div>
          </div>

          {/* Account Information */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="h-5 w-5 text-purple-400" />
              <h3 className="text-lg font-medium text-white">
                Account Information
              </h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-white/60">Status</span>
                <span
                  className={`px-2 py-1 text-xs font-medium ${
                    user?.is_active ?
                      "bg-green-400/10 text-green-400"
                    : "bg-red-400/10 text-red-400"
                  }`}
                >
                  {user?.is_active ? "Active" : "Inactive"}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-white/60">Verification</span>
                <span
                  className={`px-2 py-1 text-xs font-medium ${
                    user?.is_verified ?
                      "bg-green-400/10 text-green-400"
                    : "bg-yellow-400/10 text-yellow-400"
                  }`}
                >
                  {user?.is_verified ? "Verified" : "Pending"}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-white/60">Member Since</span>
                <span className="text-sm text-white">
                  {user?.created_at ? formatDate(user.created_at) : "Unknown"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
