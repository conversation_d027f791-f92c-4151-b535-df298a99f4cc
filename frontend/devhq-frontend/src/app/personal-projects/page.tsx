'use client'

import { PersonalProjectsLayout } from '@/components/layout/personal-projects-layout'
import { Button } from '@/components/ui/button'
import { Plus, Search, Github, FileText } from 'lucide-react'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

export default function PersonalProjectsPage() {
  const [activeFilter, setActiveFilter] = useState('All');
  const [isAddProjectModalOpen, setIsAddProjectModalOpen] = useState(false);
  const router = useRouter();

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'In Progress':
        return 'bg-orange-500/20 text-orange-300';
      case 'Completed':
        return 'bg-green-500/20 text-green-300';
      case 'Pending':
        return 'bg-red-500/20 text-red-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  const projectCounts = {
    All: projects.length,
    'In Progress': projects.filter(p => p.status === 'In Progress').length,
    Completed: projects.filter(p => p.status === 'Completed').length,
    Pending: projects.filter(p => p.status === 'Pending').length,
  };

  const filteredProjects = projects.filter(project => {
    if (activeFilter === 'All') {
      return true;
    }
    return project.status === activeFilter;
  });

  const handleGithubProject = () => {
    // Navigate to GitHub connect page or handle GitHub integration
    router.push('/create-project-form');
    setIsAddProjectModalOpen(false);
  };

  const handleManualProject = () => {
    // Handle manual project creation (could open another modal or navigate to a form)
    console.log('Creating manual project');
    setIsAddProjectModalOpen(false);
    // You can add more logic here for manual project creation
  };

  return (
    <PersonalProjectsLayout>
      <div className="flex flex-col h-full">
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          {/* Projects Section */}
          <div className="space-y-4">
            {/* PROJECTS heading */}
            <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">Projects</h1>
            
            {/* Personal Projects heading with Add button */}
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold text-white">Personal Projects</h2>
              <Button 
                className="border border-white/20 bg-transparent hover:bg-white hover:text-black text-white rounded-none py-2 px-4 text-sm"
                onClick={() => setIsAddProjectModalOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Project
              </Button>
            </div>
          </div>
        </div>
        
        {/* Full width border line */}
        <div className="border-b border-white/20 w-full mt-8"></div>
        
        {/* Projects heading after border */}
        <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
          <h1 className="text-xs text-white/70 uppercase tracking-wider">Projects</h1>

          {/* Filter Buttons */}
          <div className="flex mt-4 rounded-none overflow-hidden">
            <Button 
              variant="ghost" 
              className={`border border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === 'All' ? 'bg-green-500/20' : 'bg-transparent hover:bg-white/10'}`}
              onClick={() => setActiveFilter('All')}
            >
              All ({projectCounts.All})
            </Button>
            <Button 
              variant="ghost" 
              className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === 'In Progress' ? 'bg-green-500/20' : 'bg-transparent hover:bg-white/10'}`}
              onClick={() => setActiveFilter('In Progress')}
            >
              In Progress ({projectCounts['In Progress']})
            </Button>
            <Button 
              variant="ghost" 
              className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === 'Completed' ? 'bg-green-500/20' : 'bg-transparent hover:bg-white/10'}`}
              onClick={() => setActiveFilter('Completed')}
            >
              Completed ({projectCounts.Completed})
            </Button>
            <Button 
              variant="ghost" 
              className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === 'Pending' ? 'bg-green-500/20' : 'bg-transparent hover:bg-white/10'}`}
              onClick={() => setActiveFilter('Pending')}
            >
              Pending ({projectCounts.Pending})
            </Button>
          </div>

          <div className="relative mt-4">
            <input 
              type="text" 
              placeholder="Search projects..." 
              className="w-full p-2 pl-10 bg-transparent border border-white/20 text-white placeholder-gray-500 focus:outline-none focus:ring-0 rounded-none"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
          </div>

          {/* Table Headers */}
          <div className="flex font-mono text-xs uppercase text-white border-b border-white/20 pb-2 mt-6">
            <div className="w-1/4">Project Name</div>
            <div className="w-1/4">Client</div>
            <div className="w-1/6">Status</div>
            <div className="w-1/6">Due Date</div>
            <div className="w-1/6">Progress</div>
          </div>

          {/* Project Rows */}
          {filteredProjects.map((project, index) => (
            <div key={index} className="flex text-sm text-white border-b border-white/20 py-4">
              <div 
                className="w-1/4 underline hover:text-green-500 cursor-pointer"
                onClick={() => router.push(`/projects/${project.id}`)}
              >{project.name}</div>
              <div className="w-1/4">{project.client}</div>
              <div className="w-1/6"><span className={`px-2 py-1 text-xs ${getStatusColorClass(project.status)}`}>{project.status}</span></div>
              <div className="w-1/6">{project.dueDate}</div>
              <div className="w-1/6">{project.progress}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Add Project Modal */}
      <Dialog open={isAddProjectModalOpen} onOpenChange={setIsAddProjectModalOpen}>
        <DialogContent className="!rounded-none !border !border-white/20 max-w-md [&>button]:!rounded-none sm:!rounded-none">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Add New Project</DialogTitle>
            <p className="text-sm text-gray-500 mt-2">
              Choose how you'd like to create your project
            </p>
          </DialogHeader>
          
          <div className="space-y-4 mt-6">
            {/* GitHub Option */}
            <Button
              onClick={handleGithubProject}
              className="w-full rounded-none border border-white/20 bg-transparent hover:bg-white hover:text-black text-white p-6 h-auto flex flex-col items-center gap-3"
            >
              <Github className="h-8 w-8" />
              <div className="text-center">
                <div className="font-semibold">Import from GitHub</div>
                <div className="text-sm opacity-70">Connect a GitHub repository</div>
              </div>
            </Button>

            {/* Manual Option */}
            <Button
              onClick={handleManualProject}
              className="w-full rounded-none border border-white/20 bg-transparent hover:bg-white hover:text-black text-white p-6 h-auto flex flex-col items-center gap-3"
            >
              <FileText className="h-8 w-8" />
              <div className="text-center">
                <div className="font-semibold">Create Manually</div>
                <div className="text-sm opacity-70">Set up a project from scratch</div>
              </div>
            </Button>
          </div>

          <div className="flex justify-start mt-6">
            <Button
              variant="outline"
              onClick={() => setIsAddProjectModalOpen(false)}
              className="rounded-none border border-white/20 hover:bg-white hover:text-black"
            >
              Cancel
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </PersonalProjectsLayout>
  )
}

const projects = [
  {
    id: 'devhq-dashboard-redesign',
    name: 'DevHQ Dashboard Redesign',
    client: 'Internal',
    status: 'In Progress',
    dueDate: '2024-12-31',
    progress: '60%',
  },
  {
    id: 'client-portal-development',
    name: 'Client Portal Development',
    client: 'Acme Corp',
    status: 'Completed',
    dueDate: '2024-07-15',
    progress: '100%',
  },
  {
    id: 'mobile-app-integration',
    name: 'Mobile App Integration',
    client: 'Global Solutions',
    status: 'Pending',
    dueDate: '2025-03-01',
    progress: '10%',
  },
]