/** @format */

"use client";

import React, { useState } from "react";
import { WorkspaceLayout } from "@/components/layout/workspace-layout";
import { useProject } from "@/contexts/project-context";
import {
  MessageSquare,
  Send,
  Phone,
  Mail,
  Calendar,
  User,
  Clock,
  Plus,
  Search,
  Video,
  FileText,
} from "lucide-react";

export default function CommunicationPage() {
  const { project, isLoading, addMessage } = useProject();

  // Local state
  const [selectedType, setSelectedType] = useState<
    "all" | "email" | "call" | "meeting" | "message"
  >("all");
  const [showNewMessageForm, setShowNewMessageForm] = useState(false);
  const [newMessage, setNewMessage] = useState({
    type: "message" as "email" | "call" | "meeting" | "message",
    subject: "",
    content: "",
    sender: "You",
    isFromClient: false,
  });

  // Get messages from project context
  const messages = project?.messages || [];

  // Filter messages by type
  const filteredMessages =
    selectedType === "all" ? messages : (
      messages.filter((message) => message.type === selectedType)
    );

  // Search functionality
  const [searchTerm, setSearchTerm] = useState("");
  const searchedMessages =
    searchTerm ?
      filteredMessages.filter(
        (message) =>
          message.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
          message.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          message.sender.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : filteredMessages;

  // Communication management functions
  const handleAddMessage = () => {
    if (newMessage.content.trim()) {
      addMessage({
        type: newMessage.type,
        subject: newMessage.subject || undefined,
        content: newMessage.content,
        sender: newMessage.sender,
        isFromClient: newMessage.isFromClient,
        timestamp: new Date(),
      });

      setNewMessage({
        type: "message",
        subject: "",
        content: "",
        sender: "You",
        isFromClient: false,
      });
      setShowNewMessageForm(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "email":
        return <Mail className="h-4 w-4 text-blue-400" />;
      case "call":
        return <Phone className="h-4 w-4 text-green-400" />;
      case "meeting":
        return <Calendar className="h-4 w-4 text-purple-400" />;
      case "message":
        return <MessageSquare className="h-4 w-4 text-gray-400" />;
      default:
        return <MessageSquare className="h-4 w-4 text-gray-400" />;
    }
  };

  // Calculate metrics
  const totalMessages = messages.length;
  const clientMessages = messages.filter((m) => m.isFromClient).length;
  const recentMessages = messages.filter((m) => {
    const daysDiff =
      (Date.now() - m.timestamp.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff <= 7;
  }).length;

  // Communication types for filtering
  const communicationTypes: Array<{
    key: "all" | "email" | "call" | "meeting" | "message";
    label: string;
    count: number;
  }> = [
    { key: "all", label: "All", count: messages.length },
    {
      key: "email",
      label: "Emails",
      count: messages.filter((m) => m.type === "email").length,
    },
    {
      key: "call",
      label: "Calls",
      count: messages.filter((m) => m.type === "call").length,
    },
    {
      key: "meeting",
      label: "Meetings",
      count: messages.filter((m) => m.type === "meeting").length,
    },
    {
      key: "message",
      label: "Messages",
      count: messages.filter((m) => m.type === "message").length,
    },
  ];

  if (isLoading) {
    return (
      <WorkspaceLayout>
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          <div className="text-white">Loading...</div>
        </div>
      </WorkspaceLayout>
    );
  }

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Navigation */}
        <div className="space-y-4">
          {/* COMMUNICATION HUB heading */}
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            Communication Hub
          </h1>

          {/* Project heading */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h2 className="text-2xl font-semibold text-white">
                Client Communication & Project Updates
              </h2>
            </div>
            <button
              onClick={() => setShowNewMessageForm(true)}
              className="border border-blue-400/30 bg-blue-400/10 px-4 py-2 text-blue-400 hover:bg-blue-400/20"
            >
              <Plus className="h-4 w-4 mr-2 inline" />
              New Message
            </button>
          </div>
        </div>
      </div>

      {/* Communication Content */}
      <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
        <div className="mt-6 space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/20 flex items-center justify-center">
                  <MessageSquare className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {totalMessages}
                  </div>
                  <div className="text-sm text-white/60">Total Messages</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/20 flex items-center justify-center">
                  <User className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {clientMessages}
                  </div>
                  <div className="text-sm text-white/60">From Client</div>
                </div>
              </div>
            </div>
          </div>

          {/* Communication Type Filter */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <MessageSquare className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Communication Types
              </h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {communicationTypes.map((type) => (
                <button
                  key={type.key}
                  onClick={() => setSelectedType(type.key)}
                  className={`border px-3 py-1 text-xs ${
                    selectedType === type.key ?
                      "border-blue-400 bg-blue-400/10 text-blue-400"
                    : "border-white/20 text-white/60 hover:text-white"
                  }`}
                >
                  {type.label} ({type.count})
                </button>
              ))}
            </div>
          </div>

          {/* Search */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Search className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Search Communications
              </h3>
            </div>
            <input
              placeholder="Search messages, subjects, or senders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-transparent border border-white/20 px-3 py-2 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
            />
          </div>

          {/* New Message Form */}
          {showNewMessageForm && (
            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-2 mb-4">
                <Plus className="h-5 w-5 text-green-400" />
                <h3 className="text-lg font-medium text-white">
                  New Communication
                </h3>
              </div>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <select
                    value={newMessage.type}
                    onChange={(e) =>
                      setNewMessage({
                        ...newMessage,
                        type: e.target.value as
                          | "email"
                          | "call"
                          | "meeting"
                          | "message",
                      })
                    }
                    className="border border-white/20 bg-transparent px-3 py-2 text-white focus:border-blue-400 focus:outline-none"
                  >
                    <option value="message">Message</option>
                    <option value="email">Email</option>
                    <option value="call">Call</option>
                    <option value="meeting">Meeting</option>
                  </select>
                  <input
                    type="text"
                    placeholder="Sender name"
                    value={newMessage.sender}
                    onChange={(e) =>
                      setNewMessage({ ...newMessage, sender: e.target.value })
                    }
                    className="border border-white/20 bg-transparent px-3 py-2 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
                  />
                </div>
                {(newMessage.type === "email" ||
                  newMessage.type === "meeting") && (
                  <input
                    type="text"
                    placeholder="Subject"
                    value={newMessage.subject}
                    onChange={(e) =>
                      setNewMessage({ ...newMessage, subject: e.target.value })
                    }
                    className="w-full border border-white/20 bg-transparent px-3 py-2 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
                  />
                )}
                <textarea
                  placeholder="Message content..."
                  value={newMessage.content}
                  onChange={(e) =>
                    setNewMessage({ ...newMessage, content: e.target.value })
                  }
                  className="w-full border border-white/20 bg-transparent px-3 py-2 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
                  rows={4}
                />
                <div className="flex items-center gap-3">
                  <label className="flex items-center gap-2 text-white/60">
                    <input
                      type="checkbox"
                      checked={newMessage.isFromClient}
                      onChange={(e) =>
                        setNewMessage({
                          ...newMessage,
                          isFromClient: e.target.checked,
                        })
                      }
                      className="rounded"
                    />
                    From client
                  </label>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleAddMessage}
                    className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20"
                  >
                    Add Communication
                  </button>
                  <button
                    onClick={() => setShowNewMessageForm(false)}
                    className="border border-white/20 px-4 py-2 text-white/60 hover:text-white"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Messages Timeline */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Communication Timeline
              </h3>
            </div>
            <p className="text-white/60 text-sm mb-6">
              All project communication in chronological order
            </p>
            <div className="space-y-4">
              {searchedMessages.map((message) => (
                <div
                  key={message.id}
                  className="border border-white/20 bg-transparent p-4"
                >
                  <div className="flex items-start gap-3">
                    <div
                      className={`w-10 h-10 flex items-center justify-center ${message.isFromClient ? "bg-blue-500/20" : "bg-green-500/20"}`}
                    >
                      {getTypeIcon(message.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-medium">
                          {message.subject ||
                            `${message.type.charAt(0).toUpperCase() + message.type.slice(1)} from ${message.sender}`}
                        </h3>
                        <div
                          className={`border px-2 py-1 text-xs ${message.isFromClient ? "border-blue-400/30 bg-blue-400/10 text-blue-400" : "border-green-400/30 bg-green-400/10 text-green-400"}`}
                        >
                          {message.isFromClient ? "Client" : "You"}
                        </div>
                      </div>
                      <p className="text-white/60 text-sm mb-3 whitespace-pre-line">
                        {message.content}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-white/60">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {message.sender}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {message.timestamp.toLocaleDateString()} at{" "}
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {searchedMessages.length === 0 && (
                <div className="text-center py-8 text-white/60">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>
                    {messages.length === 0 ?
                      "No communications yet. Start by adding your first message!"
                    : searchTerm ?
                      "No messages match your search criteria."
                    : "No messages in this category."}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </WorkspaceLayout>
  );
}
