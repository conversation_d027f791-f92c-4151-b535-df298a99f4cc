/** @format */

"use client";

import React, { useState } from "react";
import { WorkspaceLayout } from "@/components/layout/workspace-layout";
import { useProject } from "@/contexts/project-context";
import {
  DollarSign,
  TrendingUp,
  Target,
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  Calendar,
  Tag,
  Receipt,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

export default function FinancialsPage() {
  const { project, isLoading, addExpense, updateExpense, deleteExpense } =
    useProject();

  // Local state
  const [selectedCategory, setSelectedCategory] = useState<
    "all" | "software" | "hardware" | "travel" | "materials" | "other"
  >("all");
  const [showNewExpenseForm, setShowNewExpenseForm] = useState(false);
  const [newExpense, setNewExpense] = useState({
    description: "",
    amount: 0,
    category: "other" as
      | "software"
      | "hardware"
      | "travel"
      | "materials"
      | "other",
    isReimbursable: false,
  });

  // Get financial data from project context
  const expenses = project?.expenses || [];
  const totalBudget = project?.totalBudget || 0;
  const totalRevenue = project?.totalRevenue || 0;
  const spentBudget = project?.spentBudget || 0;
  const remainingBudget = project?.remainingBudget || 0;
  const totalHours = project?.totalHours || 0;
  const billableHours = project?.billableHours || 0;
  const hourlyRate = project?.hourlyRate || 0;

  // Filter expenses by category
  const filteredExpenses =
    selectedCategory === "all" ? expenses : (
      expenses.filter((expense) => expense.category === selectedCategory)
    );

  // Sort expenses by date (newest first)
  const sortedExpenses = [...filteredExpenses].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Calculate expense metrics
  const totalExpenses = expenses.reduce(
    (sum, expense) => sum + expense.amount,
    0
  );
  const reimbursableExpenses = expenses
    .filter((e) => e.isReimbursable)
    .reduce((sum, e) => sum + e.amount, 0);
  const netProfit = totalRevenue - totalExpenses;
  const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;
  const budgetUtilization =
    totalBudget > 0 ? (spentBudget / totalBudget) * 100 : 0;

  // Expense management functions
  const handleAddExpense = () => {
    if (newExpense.description.trim() && newExpense.amount > 0) {
      addExpense({
        description: newExpense.description,
        amount: newExpense.amount,
        category: newExpense.category,
        date: new Date(),
        isReimbursable: newExpense.isReimbursable,
      });

      setNewExpense({
        description: "",
        amount: 0,
        category: "other",
        isReimbursable: false,
      });
      setShowNewExpenseForm(false);
    }
  };

  const handleDeleteExpense = (expenseId: string) => {
    if (confirm("Are you sure you want to delete this expense?")) {
      deleteExpense(expenseId);
    }
  };

  // Expense categories for filtering
  const expenseCategories: Array<{
    key: "all" | "software" | "hardware" | "travel" | "materials" | "other";
    label: string;
    count: number;
  }> = [
    { key: "all", label: "All Expenses", count: expenses.length },
    {
      key: "software",
      label: "Software",
      count: expenses.filter((e) => e.category === "software").length,
    },
    {
      key: "hardware",
      label: "Hardware",
      count: expenses.filter((e) => e.category === "hardware").length,
    },
    {
      key: "travel",
      label: "Travel",
      count: expenses.filter((e) => e.category === "travel").length,
    },
    {
      key: "materials",
      label: "Materials",
      count: expenses.filter((e) => e.category === "materials").length,
    },
    {
      key: "other",
      label: "Other",
      count: expenses.filter((e) => e.category === "other").length,
    },
  ];

  if (isLoading) {
    return (
      <WorkspaceLayout>
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          <div className="text-white">Loading...</div>
        </div>
      </WorkspaceLayout>
    );
  }

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Navigation */}
        <div className="space-y-4">
          {/* FINANCIAL DASHBOARD heading */}
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            Financial Dashboard
          </h1>

          {/* Project heading */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h2 className="text-2xl font-semibold text-white">
                Project Financial Health & Profitability
              </h2>
            </div>
            <div
              className={`border px-3 py-1 ${profitMargin > 60 ? "border-green-400/30 bg-green-400/10 text-green-400" : "border-yellow-400/30 bg-yellow-400/10 text-yellow-400"}`}
            >
              <span className="text-sm">
                {profitMargin.toFixed(1)}% Profit Margin
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Financials Content */}
      <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
        <div className="mt-6 space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/20 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${totalRevenue.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Total Revenue</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/20 flex items-center justify-center">
                  <Target className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${remainingBudget.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Remaining Budget</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-500/20 flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${netProfit.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Net Profit</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-yellow-500/20 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    $
                    {billableHours > 0 ?
                      (totalRevenue / billableHours).toFixed(0)
                    : "0"}
                  </div>
                  <div className="text-sm text-white/60">Effective Rate</div>
                </div>
              </div>
            </div>
          </div>

          {/* Budget Analysis */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Budget Analysis
              </h3>
            </div>
            <p className="text-white/60 text-sm mb-6">
              Track spending against your project budget
            </p>
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-white/60">Budget Utilization</span>
                  <span className="text-white">
                    {budgetUtilization.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-white/10 h-3">
                  <div
                    className={`h-3 ${
                      budgetUtilization > 90 ? "bg-red-500"
                      : budgetUtilization > 75 ? "bg-yellow-500"
                      : "bg-green-500"
                    }`}
                    style={{ width: `${Math.min(budgetUtilization, 100)}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-white/20">
                <div className="text-center">
                  <div className="text-xl font-bold text-white">
                    ${spentBudget.toLocaleString()}
                  </div>
                  <div className="text-xs text-white/60">Spent</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-white">
                    ${totalBudget.toLocaleString()}
                  </div>
                  <div className="text-xs text-white/60">Total Budget</div>
                </div>
              </div>

              {budgetUtilization > 85 && (
                <div className="flex items-center gap-2 p-3 bg-yellow-500/10 border border-yellow-500/20">
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span className="text-sm text-yellow-300">
                    Approaching budget limit. Monitor spending carefully.
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Expense Categories Filter */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Tag className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Filter Expenses
              </h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {expenseCategories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedCategory(category.key)}
                  className={`border px-3 py-1 text-xs ${
                    selectedCategory === category.key ?
                      "border-red-400 bg-red-400/10 text-red-400"
                    : "border-white/20 text-white/60 hover:text-white"
                  }`}
                >
                  {category.label} ({category.count})
                </button>
              ))}
            </div>
          </div>

          {/* New Expense Form */}
          {showNewExpenseForm && (
            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-2 mb-4">
                <Plus className="h-5 w-5 text-green-400" />
                <h3 className="text-lg font-medium text-white">
                  Add New Expense
                </h3>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="expense-description">Description</Label>
                  <Input
                    id="expense-description"
                    type="text"
                    placeholder="e.g. Software license, Office supplies"
                    value={newExpense.description}
                    onChange={(e) =>
                      setNewExpense({
                        ...newExpense,
                        description: e.target.value,
                      })
                    }
                    className="bg-transparent border-white/20 text-white rounded-none"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="expense-amount">Amount ($)</Label>
                    <Input
                      id="expense-amount"
                      type="number"
                      placeholder="0.00"
                      value={newExpense.amount === 0 ? "" : newExpense.amount}
                      onChange={(e) =>
                        setNewExpense({
                          ...newExpense,
                          amount:
                            e.target.value === "" ? 0 : Number(e.target.value),
                        })
                      }
                      className="bg-transparent border-white/20 text-white rounded-none"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div>
                    <Label htmlFor="expense-category">Category</Label>
                    <Select
                      value={newExpense.category}
                      onValueChange={(value) =>
                        setNewExpense({
                          ...newExpense,
                          category: value as
                            | "software"
                            | "hardware"
                            | "travel"
                            | "materials"
                            | "other",
                        })
                      }
                    >
                      <SelectTrigger className="bg-transparent border-white/20 text-white rounded-none">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                        <SelectItem value="other">Other</SelectItem>
                        <SelectItem value="software">Software</SelectItem>
                        <SelectItem value="hardware">Hardware</SelectItem>
                        <SelectItem value="travel">Travel</SelectItem>
                        <SelectItem value="materials">Materials</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="reimbursable-expense"
                    checked={newExpense.isReimbursable}
                    onCheckedChange={(checked) =>
                      setNewExpense({
                        ...newExpense,
                        isReimbursable: !!checked,
                      })
                    }
                  />
                  <Label
                    htmlFor="reimbursable-expense"
                    className="text-white/60"
                  >
                    Reimbursable expense
                  </Label>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleAddExpense}
                    className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20"
                  >
                    Add Expense
                  </button>
                  <button
                    onClick={() => setShowNewExpenseForm(false)}
                    className="border border-white/20 px-4 py-2 text-white/60 hover:text-white"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Expenses */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Receipt className="h-5 w-5 text-red-400" />
                <h3 className="text-lg font-medium text-white">
                  Project Expenses
                </h3>
              </div>
              <button
                onClick={() => setShowNewExpenseForm(true)}
                className="border border-red-400/30 bg-red-400/10 px-3 py-1 text-red-400 hover:bg-red-400/20"
              >
                <Plus className="h-4 w-4 mr-1 inline" />
                Add Expense
              </button>
            </div>
            <p className="text-white/60 text-sm mb-6">
              Track costs and reimbursable expenses
            </p>
            <div className="space-y-3">
              {sortedExpenses.map((expense) => (
                <div
                  key={expense.id}
                  className="border border-white/20 bg-transparent p-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-medium">
                          {expense.description}
                        </h3>
                        <div className="border border-blue-400/30 bg-blue-400/10 px-2 py-1 text-xs text-blue-400">
                          {expense.category}
                        </div>
                        {expense.isReimbursable && (
                          <div className="border border-green-400/30 bg-green-400/10 px-2 py-1 text-xs text-green-400">
                            Reimbursable
                          </div>
                        )}
                      </div>
                      <div className="text-sm text-white/60">
                        {expense.date.toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-right mr-4">
                        <div className="text-xl font-bold text-white">
                          ${expense.amount}
                        </div>
                      </div>
                      <button
                        onClick={() => handleDeleteExpense(expense.id)}
                        className="border border-red-400/30 bg-red-400/10 px-3 py-1 text-red-400 hover:bg-red-400/20"
                        title="Delete expense"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {sortedExpenses.length === 0 && (
                <div className="text-center py-8 text-white/60">
                  <Receipt className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>
                    {expenses.length === 0 ?
                      "No expenses yet. Add your first project expense!"
                    : "No expenses match the selected category."}
                  </p>
                </div>
              )}
            </div>

            {expenses.length > 0 && (
              <div className="mt-6 pt-4 border-t border-white/20">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-white">
                      ${totalExpenses}
                    </div>
                    <div className="text-sm text-white/60">Total Expenses</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {expenses.length}
                    </div>
                    <div className="text-sm text-white/60">Line Items</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </WorkspaceLayout>
  );
}
