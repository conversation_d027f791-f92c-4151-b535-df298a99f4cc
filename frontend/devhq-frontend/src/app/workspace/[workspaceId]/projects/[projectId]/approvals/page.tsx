/** @format */

"use client";

import React, { useState } from "react";
import { WorkspaceLayout } from "@/components/layout/workspace-layout";
import { useProject } from "@/contexts/project-context";
import {
  CheckCircle,
  Clock,
  XCircle,
  MessageSquare,
  Upload,
  Eye,
  Plus,
  FileText,
  Calendar,
  AlertCircle,
  Link,
  Copy,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";

export default function ClientApprovalsPage() {
  const { project, isLoading, addApproval, updateApproval } = useProject();
  const { toast } = useToast();

  // Local state
  const [selectedStatus, setSelectedStatus] = useState<
    "all" | "pending" | "approved" | "revision_requested"
  >("all");
  const [showNewApprovalForm, setShowNewApprovalForm] = useState(false);
  const [newApproval, setNewApproval] = useState({
    deliverableTitle: "",
    description: "",
    version: 1,
    attachments: [] as string[],
  });
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
    variant?: "default" | "destructive";
  }>({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: () => {},
  });

  // Get approvals from project context
  const approvals = project?.approvals || [];

  // Filter approvals by status
  const filteredApprovals =
    selectedStatus === "all" ? approvals : (
      approvals.filter((approval) => approval.status === selectedStatus)
    );

  // Sort approvals by submission date (newest first)
  const sortedApprovals = [...filteredApprovals].sort(
    (a, b) =>
      new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()
  );

  // Approval management functions
  const handleAddApproval = () => {
    if (newApproval.deliverableTitle.trim() && newApproval.description.trim()) {
      addApproval({
        deliverableTitle: newApproval.deliverableTitle,
        description: newApproval.description,
        status: "pending",
        version: newApproval.version,
        submittedAt: new Date(),
        attachments: newApproval.attachments,
      });

      setNewApproval({
        deliverableTitle: "",
        description: "",
        version: 1,
        attachments: [],
      });
      setShowNewApprovalForm(false);
    }
  };

  const handleUpdateApprovalStatus = (
    approvalId: string,
    status: "approved" | "revision_requested",
    feedback?: string
  ) => {
    const updates: any = { status };
    if (status === "approved") {
      updates.approvedAt = new Date();
    }
    if (feedback) {
      updates.clientFeedback = feedback;
    }
    updateApproval(approvalId, updates);
  };

  const generateApprovalLink = (approvalId: string) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/client-approval/${approvalId}`;
  };

  const copyApprovalLink = (approvalId: string) => {
    const link = generateApprovalLink(approvalId);
    navigator.clipboard.writeText(link).then(() => {
      toast({
        title: "Approval Link Copied!",
        description: "Share this link with your client for approval.",
        variant: "success",
      });
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-400" />;
      case "revision_requested":
        return <XCircle className="h-5 w-5 text-red-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "text-green-400 border-green-400";
      case "pending":
        return "text-yellow-400 border-yellow-400";
      case "revision_requested":
        return "text-red-400 border-red-400";
      default:
        return "text-gray-400 border-gray-400";
    }
  };

  // Calculate metrics
  const pendingCount = approvals.filter((a) => a.status === "pending").length;
  const approvedCount = approvals.filter((a) => a.status === "approved").length;
  const revisionCount = approvals.filter(
    (a) => a.status === "revision_requested"
  ).length;

  // Status categories for filtering
  const statusCategories: Array<{
    key: "all" | "pending" | "approved" | "revision_requested";
    label: string;
    count: number;
  }> = [
    { key: "all", label: "All Approvals", count: approvals.length },
    { key: "pending", label: "Pending", count: pendingCount },
    { key: "approved", label: "Approved", count: approvedCount },
    {
      key: "revision_requested",
      label: "Needs Revision",
      count: revisionCount,
    },
  ];

  if (isLoading) {
    return (
      <WorkspaceLayout>
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          <div className="text-white">Loading...</div>
        </div>
      </WorkspaceLayout>
    );
  }

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Navigation */}
        <div className="space-y-4">
          {/* CLIENT APPROVALS heading */}
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            Client Approvals
          </h1>

          {/* Project heading */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h2 className="text-2xl font-semibold text-white">
                Deliverable Approval Workflow
              </h2>
            </div>
            <button
              onClick={() => setShowNewApprovalForm(true)}
              className="border border-yellow-400/30 bg-yellow-400/10 px-4 py-2 text-yellow-400 hover:bg-yellow-400/20"
            >
              <Plus className="h-4 w-4 mr-2 inline" />
              Submit for Approval
            </button>
          </div>
        </div>
      </div>

      {/* Approvals Content */}
      <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
        <div className="mt-6 space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-yellow-500/20 flex items-center justify-center">
                  <Clock className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {pendingCount}
                  </div>
                  <div className="text-sm text-white/60">Pending Review</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/20 flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {approvedCount}
                  </div>
                  <div className="text-sm text-white/60">Approved</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-red-500/20 flex items-center justify-center">
                  <XCircle className="h-5 w-5 text-red-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {revisionCount}
                  </div>
                  <div className="text-sm text-white/60">Needs Revision</div>
                </div>
              </div>
            </div>
          </div>

          {/* Status Filter */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Filter by Status
              </h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {statusCategories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedStatus(category.key)}
                  className={`border px-3 py-1 text-xs ${
                    selectedStatus === category.key ?
                      "border-yellow-400 bg-yellow-400/10 text-yellow-400"
                    : "border-white/20 text-white/60 hover:text-white"
                  }`}
                >
                  {category.label} ({category.count})
                </button>
              ))}
            </div>
          </div>

          {/* New Approval Form */}
          {showNewApprovalForm && (
            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-2 mb-4">
                <Plus className="h-5 w-5 text-green-400" />
                <h3 className="text-lg font-medium text-white">
                  Submit New Deliverable
                </h3>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="deliverable-title">Deliverable Title</Label>
                  <Input
                    id="deliverable-title"
                    type="text"
                    placeholder="e.g. Homepage Design Mockups"
                    value={newApproval.deliverableTitle}
                    onChange={(e) =>
                      setNewApproval({
                        ...newApproval,
                        deliverableTitle: e.target.value,
                      })
                    }
                    className="bg-transparent border-white/20 text-white rounded-none"
                  />
                </div>
                <div>
                  <Label htmlFor="deliverable-description">Description</Label>
                  <Textarea
                    id="deliverable-description"
                    placeholder="Detailed description of the deliverable and what needs approval..."
                    value={newApproval.description}
                    onChange={(e) =>
                      setNewApproval({
                        ...newApproval,
                        description: e.target.value,
                      })
                    }
                    className="bg-transparent border-white/20 text-white rounded-none"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="version-number">Version Number</Label>
                    <Input
                      id="version-number"
                      type="number"
                      placeholder="e.g. 1"
                      value={
                        newApproval.version === 0 ? "" : newApproval.version
                      }
                      onChange={(e) =>
                        setNewApproval({
                          ...newApproval,
                          version:
                            e.target.value === "" ? 0 : Number(e.target.value),
                        })
                      }
                      className="bg-transparent border-white/20 text-white rounded-none"
                      min="1"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Upload className="h-4 w-4 text-white/60" />
                    <span className="text-white/60 text-sm">
                      Attachments: {newApproval.attachments.length}
                    </span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleAddApproval}
                    className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20"
                  >
                    Submit for Approval
                  </button>
                  <button
                    onClick={() => setShowNewApprovalForm(false)}
                    className="border border-white/20 px-4 py-2 text-white/60 hover:text-white"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Approvals List */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-5 w-5 text-yellow-400" />
              <h3 className="text-lg font-medium text-white">
                Deliverable Submissions
              </h3>
            </div>
            <p className="text-white/60 text-sm mb-6">
              Track the status of your submitted work and client feedback
            </p>
            <div className="space-y-4">
              {sortedApprovals.map((approval) => (
                <div
                  key={approval.id}
                  className="border border-white/20 bg-transparent p-4"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(approval.status)}
                        <h3 className="text-white font-medium">
                          {approval.deliverableTitle}
                        </h3>
                        <div
                          className={`border px-2 py-1 text-xs ${getStatusColor(approval.status)}`}
                        >
                          {approval.status.replace("_", " ").toUpperCase()}
                        </div>
                        <div className="border border-white/20 px-2 py-1 text-xs text-white/60">
                          v{approval.version}
                        </div>
                      </div>
                      <p className="text-white/60 text-sm mb-3">
                        {approval.description}
                      </p>

                      {/* Attachments */}
                      <div className="flex items-center gap-2 mb-3">
                        <span className="text-xs text-white/60">
                          Attachments:
                        </span>
                        {approval.attachments.map((attachment, index) => (
                          <div
                            key={index}
                            className="border border-white/20 px-2 py-1 text-xs text-white/60"
                          >
                            {attachment}
                          </div>
                        ))}
                      </div>

                      {/* Timeline */}
                      <div className="text-xs text-white/60">
                        Submitted: {approval.submittedAt.toLocaleDateString()}{" "}
                        at {approval.submittedAt.toLocaleTimeString()}
                        {approval.approvedAt && (
                          <span className="ml-4">
                            Approved: {approval.approvedAt.toLocaleDateString()}{" "}
                            at {approval.approvedAt.toLocaleTimeString()}
                          </span>
                        )}
                      </div>

                      {/* Client Feedback */}
                      {approval.clientFeedback && (
                        <div className="mt-3 p-3 bg-red-500/10 border border-red-500/20">
                          <div className="flex items-start gap-2">
                            <MessageSquare className="h-4 w-4 text-red-400 mt-0.5" />
                            <div>
                              <div className="text-sm font-medium text-red-300 mb-1">
                                Client Feedback:
                              </div>
                              <p className="text-sm text-red-200">
                                {approval.clientFeedback}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => copyApprovalLink(approval.id)}
                        className="border border-blue-400/30 bg-blue-400/10 px-3 py-1 text-blue-400 hover:bg-blue-400/20"
                        title="Copy client approval link"
                      >
                        <Link className="h-4 w-4" />
                      </button>
                      <button
                        className="border border-white/20 bg-transparent px-3 py-1 text-white/60 hover:text-white"
                        title="View details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {approval.status === "pending" && (
                        <>
                          <button
                            onClick={() =>
                              handleUpdateApprovalStatus(
                                approval.id,
                                "approved"
                              )
                            }
                            className="border border-green-400/30 bg-green-400/10 px-3 py-1 text-green-400 hover:bg-green-400/20"
                            title="Approve"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => {
                              const feedback = prompt(
                                "Enter feedback for revision:"
                              );
                              if (feedback) {
                                handleUpdateApprovalStatus(
                                  approval.id,
                                  "revision_requested",
                                  feedback
                                );
                              }
                            }}
                            className="border border-red-400/30 bg-red-400/10 px-3 py-1 text-red-400 hover:bg-red-400/20"
                            title="Request revision"
                          >
                            <XCircle className="h-4 w-4" />
                          </button>
                        </>
                      )}
                      {approval.status === "revision_requested" && (
                        <button
                          onClick={() => {
                            // In a real app, this would open a resubmission form
                            toast({
                              title: "Feature Coming Soon",
                              description:
                                "Resubmission functionality will be available soon.",
                              variant: "info",
                            });
                          }}
                          className="border border-blue-400/30 bg-blue-400/10 px-3 py-1 text-blue-400 hover:bg-blue-400/20"
                        >
                          <Upload className="h-4 w-4 mr-2 inline" />
                          Resubmit
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {sortedApprovals.length === 0 && (
              <div className="text-center py-8 text-white/60">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>
                  {approvals.length === 0 ?
                    "No submissions yet. Submit your first deliverable for client review!"
                  : "No approvals match the selected status filter."}
                </p>
              </div>
            )}
          </div>

          {/* Approval Workflow Guide */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-6">
              <CheckCircle className="h-5 w-5 text-yellow-400" />
              <h3 className="text-lg font-medium text-white">
                How Approvals Work
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500/20 flex items-center justify-center mx-auto mb-3">
                  <Upload className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-white font-medium mb-2">1. Submit</h3>
                <p className="text-sm text-white/60">
                  Upload your deliverable and request client approval
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-yellow-500/20 flex items-center justify-center mx-auto mb-3">
                  <Clock className="h-6 w-6 text-yellow-400" />
                </div>
                <h3 className="text-white font-medium mb-2">2. Review</h3>
                <p className="text-sm text-white/60">
                  Client reviews your work and provides feedback
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-500/20 flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
                <h3 className="text-white font-medium mb-2">3. Approve</h3>
                <p className="text-sm text-white/60">
                  Get approval or make revisions based on feedback
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ConfirmationDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog((prev) => ({ ...prev, isOpen: false }))}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        description={confirmDialog.description}
        variant={confirmDialog.variant}
      />
    </WorkspaceLayout>
  );
}
