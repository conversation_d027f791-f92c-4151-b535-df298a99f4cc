/** @format */

"use client";

import React, { useState } from "react";
import { WorkspaceLayout } from "@/components/layout/workspace-layout";
import { useProject } from "@/contexts/project-context";
import {
  Target,
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  DollarSign,
  Plus,
  Play,
  Edit,
  Trash2,
  Users,
  TrendingUp,
  FileText,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { CalendarIcon } from "@radix-ui/react-icons";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

export default function MilestonesPage() {
  const { project, isLoading, addMilestone, updateMilestone, deleteMilestone } =
    useProject();

  // Local state
  const [selectedStatus, setSelectedStatus] = useState<
    "all" | "not_started" | "in_progress" | "completed"
  >("all");
  const [showNewMilestoneForm, setShowNewMilestoneForm] = useState(false);
  const [editingMilestone, setEditingMilestone] = useState<string | null>(null);
  const [newMilestone, setNewMilestone] = useState({
    name: "",
    description: "",
    estimatedHours: 0,
    allocatedBudget: 0,
    startDate: new Date(),
    dueDate: new Date(),
    deliverables: [] as Array<{ name: string; assignee: string }>,
  });
  const [newDeliverable, setNewDeliverable] = useState({
    name: "",
    assignee: "",
  });

  // Get milestones from project context
  const milestones = project?.milestones || [];

  // Filter milestones by status
  const filteredMilestones =
    selectedStatus === "all" ? milestones : (
      milestones.filter((milestone) => milestone.status === selectedStatus)
    );

  // Sort milestones by due date
  const sortedMilestones = [...filteredMilestones].sort(
    (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
  );

  // Milestone management functions
  const handleAddMilestone = () => {
    if (newMilestone.name.trim() && newMilestone.description.trim()) {
      addMilestone({
        name: newMilestone.name,
        description: newMilestone.description,
        status: "not_started",
        startDate: newMilestone.startDate,
        dueDate: newMilestone.dueDate,
        estimatedHours: newMilestone.estimatedHours,
        actualHours: 0,
        allocatedBudget: newMilestone.allocatedBudget,
        spentBudget: 0,
        progress: 0,
        deliverables: newMilestone.deliverables.map((d, index) => ({
          ...d,
          id: `deliverable-${Date.now()}-${index}`,
          milestoneId: `milestone-${Date.now()}`,
          status: "not_started" as const,
        })),
      });

      setNewMilestone({
        name: "",
        description: "",
        estimatedHours: 0,
        allocatedBudget: 0,
        startDate: new Date(),
        dueDate: new Date(),
        deliverables: [],
      });
      setShowNewMilestoneForm(false);
    }
  };

  const handleDeleteMilestone = (milestoneId: string) => {
    if (confirm("Are you sure you want to delete this milestone?")) {
      deleteMilestone(milestoneId);
    }
  };

  const handleUpdateMilestoneStatus = (
    milestoneId: string,
    status: "not_started" | "in_progress" | "completed"
  ) => {
    const updates: any = { status };
    if (status === "completed") {
      updates.completedDate = new Date();
      updates.progress = 100;

      // Auto-generate invoice when milestone is completed
      const milestone = milestones.find((m) => m.id === milestoneId);
      if (milestone && milestone.allocatedBudget > 0) {
        const invoiceData = {
          title: `Invoice for ${milestone.name}`,
          description: `Payment for completed milestone: ${milestone.description}`,
          amount: milestone.allocatedBudget,
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          status: "draft" as const,
          createdAt: new Date(),
          milestoneId: milestoneId,
          items: [
            {
              description: milestone.name,
              quantity: 1,
              rate: milestone.allocatedBudget,
            },
          ],
        };

        // Note: Invoice creation would be handled by the project context
        // For now, we'll just show a success message
        console.log(
          "Milestone completed! Invoice would be created:",
          invoiceData
        );
      }
    } else if (status === "in_progress") {
      updates.completedDate = undefined;
    }
    updateMilestone(milestoneId, updates);
  };

  const handleAddDeliverable = () => {
    if (newDeliverable.name.trim() && newDeliverable.assignee.trim()) {
      setNewMilestone({
        ...newMilestone,
        deliverables: [...newMilestone.deliverables, { ...newDeliverable }],
      });
      setNewDeliverable({ name: "", assignee: "" });
    }
  };

  const handleRemoveDeliverable = (index: number) => {
    setNewMilestone({
      ...newMilestone,
      deliverables: newMilestone.deliverables.filter((_, i) => i !== index),
    });
  };

  // Status categories for filtering
  const statusCategories: Array<{
    key: "all" | "not_started" | "in_progress" | "completed";
    label: string;
    count: number;
  }> = [
    { key: "all", label: "All Milestones", count: milestones.length },
    {
      key: "not_started",
      label: "Not Started",
      count: milestones.filter((m) => m.status === "not_started").length,
    },
    {
      key: "in_progress",
      label: "In Progress",
      count: milestones.filter((m) => m.status === "in_progress").length,
    },
    {
      key: "completed",
      label: "Completed",
      count: milestones.filter((m) => m.status === "completed").length,
    },
  ];

  // Calculate overall project metrics
  const totalEstimatedHours = milestones.reduce(
    (sum, m) => sum + m.estimatedHours,
    0
  );
  const totalActualHours = milestones.reduce(
    (sum, m) => sum + m.actualHours,
    0
  );
  const totalAllocatedBudget = milestones.reduce(
    (sum, m) => sum + m.allocatedBudget,
    0
  );
  const totalSpentBudget = milestones.reduce(
    (sum, m) => sum + m.spentBudget,
    0
  );
  const overallProgress =
    milestones.length > 0 ?
      milestones.reduce((sum, m) => sum + m.progress, 0) / milestones.length
    : 0;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "in_progress":
        return <Play className="h-5 w-5 text-blue-400" />;
      case "overdue":
        return <AlertTriangle className="h-5 w-5 text-red-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400 border-green-400";
      case "in_progress":
        return "text-blue-400 border-blue-400";
      case "overdue":
        return "text-red-400 border-red-400";
      default:
        return "text-gray-400 border-gray-400";
    }
  };

  const getDeliverableStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400 border-green-400";
      case "in_progress":
        return "text-blue-400 border-blue-400";
      default:
        return "text-gray-400 border-gray-400";
    }
  };

  if (isLoading) {
    return (
      <WorkspaceLayout>
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          <div className="text-white">Loading...</div>
        </div>
      </WorkspaceLayout>
    );
  }

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Navigation */}
        <div className="space-y-4">
          {/* PROJECT MILESTONES heading */}
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            Project Milestones
          </h1>

          {/* Project heading */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h2 className="text-2xl font-semibold text-white">
                Project Phases & Deliverable Tracking
              </h2>
            </div>
            <button
              onClick={() => setShowNewMilestoneForm(true)}
              className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20"
            >
              <Plus className="h-4 w-4 mr-2 inline" />
              Add Milestone
            </button>
          </div>
        </div>
      </div>

      {/* Milestones Content */}
      <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
        <div className="mt-6 space-y-6">
          {/* Progress Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/20 flex items-center justify-center">
                  <Target className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {milestones.filter((m) => m.status === "completed").length}/
                    {milestones.length}
                  </div>
                  <div className="text-sm text-white/60">Milestones</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/20 flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {overallProgress.toFixed(0)}%
                  </div>
                  <div className="text-sm text-white/60">Overall Progress</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-500/20 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${totalSpentBudget.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Budget Spent</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-yellow-500/20 flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {
                      milestones.filter((m) => m.status === "in_progress")
                        .length
                    }
                  </div>
                  <div className="text-sm text-white/60">Active</div>
                </div>
              </div>
            </div>
          </div>

          {/* Overall Progress Bar */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Target className="h-5 w-5 text-green-400" />
              <h3 className="text-lg font-medium text-white">
                Project Progress
              </h3>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-white/60">Overall Completion</span>
              <span className="text-white">{overallProgress.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-white/10 h-3">
              <div
                className="h-3 bg-green-500"
                style={{ width: `${overallProgress}%` }}
              ></div>
            </div>
          </div>

          {/* Status Filter */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Target className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Filter by Status
              </h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {statusCategories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedStatus(category.key)}
                  className={`border px-3 py-1 text-xs ${
                    selectedStatus === category.key ?
                      "border-green-400 bg-green-400/10 text-green-400"
                    : "border-white/20 text-white/60 hover:text-white"
                  }`}
                >
                  {category.label} ({category.count})
                </button>
              ))}
            </div>
          </div>

          {/* New Milestone Form */}
          {showNewMilestoneForm && (
            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-2 mb-4">
                <Plus className="h-5 w-5 text-green-400" />
                <h3 className="text-lg font-medium text-white">
                  Create New Milestone
                </h3>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="milestone-name">Milestone Name</Label>
                  <Input
                    id="milestone-name"
                    type="text"
                    placeholder="e.g. Frontend Development Phase"
                    value={newMilestone.name}
                    onChange={(e) =>
                      setNewMilestone({ ...newMilestone, name: e.target.value })
                    }
                    className="bg-transparent border-white/20 text-white rounded-none"
                  />
                </div>
                <div>
                  <Label htmlFor="milestone-description">Description</Label>
                  <Textarea
                    id="milestone-description"
                    placeholder="Detailed description of milestone objectives and deliverables..."
                    value={newMilestone.description}
                    onChange={(e) =>
                      setNewMilestone({
                        ...newMilestone,
                        description: e.target.value,
                      })
                    }
                    rows={3}
                    className="bg-transparent border-white/20 text-white rounded-none"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="milestone-start-date">Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal bg-transparent border border-white/20 text-white hover:bg-transparent hover:text-white rounded-none",
                            !newMilestone.startDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {newMilestone.startDate ?
                            format(newMilestone.startDate, "PPP")
                          : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 bg-black/80 backdrop-blur-md border-white/20 rounded-none"
                        align="start"
                      >
                        <CalendarComponent
                          mode="single"
                          selected={newMilestone.startDate}
                          onSelect={(date) =>
                            setNewMilestone({
                              ...newMilestone,
                              startDate: date || new Date(),
                            })
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <Label htmlFor="milestone-due-date">Due Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal bg-transparent border border-white/20 text-white hover:bg-transparent hover:text-white rounded-none",
                            !newMilestone.dueDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {newMilestone.dueDate ?
                            format(newMilestone.dueDate, "PPP")
                          : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 bg-black/80 backdrop-blur-md border-white/20 rounded-none"
                        align="start"
                      >
                        <CalendarComponent
                          mode="single"
                          selected={newMilestone.dueDate}
                          onSelect={(date) =>
                            setNewMilestone({
                              ...newMilestone,
                              dueDate: date || new Date(),
                            })
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="milestone-hours">Estimated Hours</Label>
                    <Input
                      id="milestone-hours"
                      type="number"
                      placeholder="e.g. 40"
                      value={
                        newMilestone.estimatedHours === 0 ?
                          ""
                        : newMilestone.estimatedHours
                      }
                      onChange={(e) =>
                        setNewMilestone({
                          ...newMilestone,
                          estimatedHours:
                            e.target.value === "" ? 0 : Number(e.target.value),
                        })
                      }
                      min="0"
                      className="bg-transparent border-white/20 text-white rounded-none"
                    />
                  </div>
                  <div>
                    <Label htmlFor="milestone-budget">
                      Allocated Budget ($)
                    </Label>
                    <Input
                      id="milestone-budget"
                      type="number"
                      placeholder="e.g. 5000"
                      value={
                        newMilestone.allocatedBudget === 0 ?
                          ""
                        : newMilestone.allocatedBudget
                      }
                      onChange={(e) =>
                        setNewMilestone({
                          ...newMilestone,
                          allocatedBudget:
                            e.target.value === "" ? 0 : Number(e.target.value),
                        })
                      }
                      min="0"
                      className="bg-transparent border-white/20 text-white rounded-none"
                    />
                  </div>
                </div>

                {/* Deliverables Section */}
                <div className="border-t border-white/20 pt-4">
                  <h4 className="text-white font-medium mb-3">Deliverables</h4>
                  <div className="space-y-2">
                    {newMilestone.deliverables.map((deliverable, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 p-2 border border-white/20"
                      >
                        <span className="flex-1 text-white">
                          {deliverable.name}
                        </span>
                        <span className="text-white/60 text-sm">
                          ({deliverable.assignee})
                        </span>
                        <button
                          onClick={() => handleRemoveDeliverable(index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-1 gap-4 mt-3">
                    <div>
                      <Label htmlFor="deliverable-name">Deliverable Name</Label>
                      <Input
                        id="deliverable-name"
                        type="text"
                        placeholder="e.g. Homepage design mockups"
                        value={newDeliverable.name}
                        onChange={(e) =>
                          setNewDeliverable({
                            ...newDeliverable,
                            name: e.target.value,
                          })
                        }
                        className="bg-transparent border-white/20 text-white rounded-none"
                      />
                    </div>
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Label htmlFor="deliverable-assignee">Assignee</Label>
                        <Input
                          id="deliverable-assignee"
                          type="text"
                          placeholder="e.g. John Doe"
                          value={newDeliverable.assignee}
                          onChange={(e) =>
                            setNewDeliverable({
                              ...newDeliverable,
                              assignee: e.target.value,
                            })
                          }
                          className="bg-transparent border-white/20 text-white rounded-none"
                        />
                      </div>
                      <div className="flex items-end">
                        <button
                          onClick={handleAddDeliverable}
                          className="border border-blue-400/30 bg-blue-400/10 px-3 py-2 text-blue-400 hover:bg-blue-400/20"
                        >
                          <Plus className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={handleAddMilestone}
                    className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20"
                  >
                    Create Milestone
                  </button>
                  <button
                    onClick={() => setShowNewMilestoneForm(false)}
                    className="border border-white/20 px-4 py-2 text-white/60 hover:text-white"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Milestones Timeline */}
          <div className="space-y-6">
            {sortedMilestones.map((milestone, index) => (
              <div
                key={milestone.id}
                className="border border-white/20 bg-transparent p-6"
              >
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500/20 flex items-center justify-center text-green-400 font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="text-white text-lg font-medium flex items-center gap-2">
                        {getStatusIcon(milestone.status)}
                        {milestone.name}
                        <div
                          className={`border px-2 py-1 text-xs ${getStatusColor(milestone.status)}`}
                        >
                          {milestone.status.replace("_", " ").toUpperCase()}
                        </div>
                      </h3>
                      <p className="text-white/60 text-sm mt-1">
                        {milestone.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-right mr-4">
                      <div className="text-2xl font-bold text-white">
                        {milestone.progress}%
                      </div>
                      <div className="text-sm text-white/60">Complete</div>
                    </div>
                    {milestone.status === "not_started" && (
                      <button
                        onClick={() =>
                          handleUpdateMilestoneStatus(
                            milestone.id,
                            "in_progress"
                          )
                        }
                        className="border border-blue-400/30 bg-blue-400/10 px-3 py-1 text-blue-400 hover:bg-blue-400/20"
                        title="Start milestone"
                      >
                        <Play className="h-4 w-4" />
                      </button>
                    )}
                    {milestone.status === "in_progress" && (
                      <button
                        onClick={() =>
                          handleUpdateMilestoneStatus(milestone.id, "completed")
                        }
                        className="border border-green-400/30 bg-green-400/10 px-3 py-1 text-green-400 hover:bg-green-400/20"
                        title="Complete milestone"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={() => setEditingMilestone(milestone.id)}
                      className="border border-white/20 bg-transparent px-3 py-1 text-white/60 hover:text-white"
                      title="Edit milestone"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteMilestone(milestone.id)}
                      className="border border-red-400/30 bg-red-400/10 px-3 py-1 text-red-400 hover:bg-red-400/20"
                      title="Delete milestone"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="w-full bg-white/10 h-2">
                    <div
                      className={`h-2 ${
                        milestone.status === "completed" ? "bg-green-500"
                        : milestone.status === "in_progress" ? "bg-blue-500"
                        : "bg-gray-500"
                      }`}
                      style={{ width: `${milestone.progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Milestone Details */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-white/60">Timeline</div>
                    <div className="text-white text-sm">
                      {milestone.startDate.toLocaleDateString()} -{" "}
                      {milestone.dueDate.toLocaleDateString()}
                    </div>
                    {milestone.completedDate && (
                      <div className="text-green-400 text-xs">
                        Completed:{" "}
                        {milestone.completedDate.toLocaleDateString()}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="text-sm text-white/60">Hours</div>
                    <div className="text-white text-sm">
                      {milestone.actualHours}h / {milestone.estimatedHours}h
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-white/60">Budget</div>
                    <div className="text-white text-sm">
                      ${milestone.spentBudget.toLocaleString()} / $
                      {milestone.allocatedBudget.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-white/60">Deliverables</div>
                    <div className="text-white text-sm">
                      {
                        milestone.deliverables.filter(
                          (d) => d.status === "completed"
                        ).length
                      }{" "}
                      / {milestone.deliverables.length}
                    </div>
                  </div>
                </div>

                {/* Deliverables */}
                <div>
                  <h4 className="text-white font-medium mb-3">Deliverables</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {milestone.deliverables.map((deliverable) => (
                      <div
                        key={deliverable.id}
                        className="border border-white/20 bg-transparent p-3"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h5 className="text-white text-sm font-medium">
                                {deliverable.name}
                              </h5>
                              <div
                                className={`border px-2 py-1 text-xs ${getDeliverableStatusColor(deliverable.status)}`}
                              >
                                {deliverable.status.replace("_", " ")}
                              </div>
                            </div>
                            <div className="text-xs text-white/60">
                              Assigned to: {deliverable.assignee}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}

            {sortedMilestones.length === 0 && (
              <div className="text-center py-8 text-white/60">
                <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>
                  {milestones.length === 0 ?
                    "No milestones yet. Create your first project milestone!"
                  : "No milestones match the selected status filter."}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </WorkspaceLayout>
  );
}
