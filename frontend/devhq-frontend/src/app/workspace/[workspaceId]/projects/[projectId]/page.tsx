/** @format */

"use client";

import React from "react";
import { WorkspaceLayout } from "@/components/layout/workspace-layout";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  FileText,
  Edit,
  Save,
  X,
  CalendarIcon,
  Building,
  Mail,
  Phone,
  MapPin,
  Star,
  UserCheck,
  UserX,
  CheckCircle2,
  Archive,
  Activity,
  DollarSign,
  BarChart3,
  CheckSquare,
  FolderOpen,
  MessageSquare,
  Target,
  StickyNote,
  Users,
  Timer,
} from "lucide-react";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { projectApi, clientApi } from "@/lib/api";

// Import project feature components (kept for reference in other routes)
import { ProjectFinancialDashboard } from "@/components/projects/project-financial-dashboard";
import { ProjectTaskBoard } from "@/components/projects/project-task-board";
import { ProjectFileManager } from "@/components/projects/project-file-manager";
import { ProjectCommunicationLog } from "@/components/projects/project-communication-log";
import { ProjectMilestoneTracker } from "@/components/projects/project-milestone-tracker";
import { ProjectAnalyticsDashboard } from "@/components/projects/project-analytics-dashboard";
import { ProjectNotes } from "@/components/projects/project-notes";
// Removed ProjectFeatureNav from the details page per linked-hub plan
import Link from "next/link";

// Client data is now fetched from the backend API

// Status and priority configs removed - using backend data directly

export default function ProjectDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { workspaceId, projectId } = params;
  const [project, setProject] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedProject, setEditedProject] = useState<any>(null);
  const [clientDetails, setClientDetails] = useState<any>(null);
  const [clientChanges, setClientChanges] = useState<any>({});
  const [hasClientChanges, setHasClientChanges] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const fetchProjectData = async () => {
    if (!projectId || typeof projectId !== "string") return;

    try {
      setLoading(true);
      setError(null);

      // Fetch project details
      const projectData = await projectApi.getProject(projectId);
      setProject(projectData);

      // Fetch client details if project has a client
      if (projectData.client_id) {
        try {
          const clientData = await clientApi.getClient(projectData.client_id);
          setClientDetails(clientData);
        } catch (clientError) {
          console.error("Error fetching client details:", clientError);
          // Don't fail the whole page if client fetch fails
        }
      }
    } catch (err: any) {
      console.error("Error fetching project data:", err);
      let errorMessage = "Failed to load project details";

      if (err.code === "NETWORK_ERROR" || !err.response) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else if (err.response?.status === 404) {
        errorMessage = "Project not found. It may have been deleted.";
      } else if (err.response?.status === 403) {
        errorMessage = "You don't have permission to view this project.";
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjectData();
  }, [projectId]);

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case "In Progress":
        return "bg-orange-500/20 text-orange-300";
      case "Completed":
        return "bg-green-500/20 text-green-300";
      case "Pending":
        return "bg-red-500/20 text-red-300";
      default:
        return "bg-gray-500/20 text-gray-300";
    }
  };

  const handleEditClick = () => {
    setIsEditing(true);
    setEditedProject({ ...project });
  };

  const handleSave = async () => {
    if (!projectId || typeof projectId !== "string" || !editedProject) return;

    try {
      setSaving(true);
      setError(null);

      // Prepare the update data - map frontend fields to backend fields
      const updateData = {
        title: editedProject.name || editedProject.title,
        description: editedProject.description,
        status: editedProject.status,
        total_budget:
          editedProject.totalBudget ?
            Number(editedProject.totalBudget)
          : undefined,
        estimated_hours:
          editedProject.estimatedHours ?
            Number(editedProject.estimatedHours)
          : undefined,
        deadline: editedProject.dueDate || editedProject.deadline,
        start_date: editedProject.startDate || editedProject.start_date,
        end_date: editedProject.endDate || editedProject.end_date,
      };

      // Remove undefined values
      Object.keys(updateData).forEach((key) => {
        if (updateData[key as keyof typeof updateData] === undefined) {
          delete updateData[key as keyof typeof updateData];
        }
      });

      const updatedProject = await projectApi.updateProject(
        projectId,
        updateData
      );
      setProject(updatedProject);
      setIsEditing(false);
      setEditedProject(null);
      setSuccessMessage("Project updated successfully!");

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      console.error("Error updating project:", err);
      setError(err.response?.data?.detail || "Failed to update project");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedProject(null);
  };

  const handleInputChange = (field: string, value: string) => {
    setEditedProject((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleClientFieldChange = (field: string, value: string) => {
    const newChanges = {
      ...clientChanges,
      [field]: value,
    };
    setClientChanges(newChanges);
    setHasClientChanges(true);
  };

  const handleSaveClientChanges = async () => {
    if (!workspaceId || !projectId || !hasClientChanges) return;

    try {
      setSaving(true);
      setError(null);

      // Prepare the update data with client changes
      const updateData = {
        ...clientChanges,
        // Map clientName to the main client field if provided
        ...(clientChanges.clientName && { client: clientChanges.clientName }),
      };

      // Update the project via API
      const updatedProject = await projectApi.updateProject(
        projectId,
        updateData
      );

      // Update local state
      setProject(updatedProject);
      setClientChanges({});
      setHasClientChanges(false);
      setSuccessMessage("Client information updated successfully!");

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);

      // If the project now has a client_id, try to fetch client details
      if (updatedProject.client_id) {
        try {
          const clientData = await clientApi.getClient(
            updatedProject.client_id
          );
          setClientDetails(clientData);
        } catch (clientError) {
          console.error("Error fetching updated client details:", clientError);
          // Don't fail the whole operation if client fetch fails
        }
      }
    } catch (err: any) {
      console.error("Error saving client changes:", err);
      let errorMessage = "Failed to save client information";

      if (err.code === "NETWORK_ERROR" || !err.response) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      }

      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <WorkspaceLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-white">Loading project details...</div>
        </div>
      </WorkspaceLayout>
    );
  }

  if (error) {
    return (
      <WorkspaceLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="text-red-400 mb-4">Error loading project</div>
            <div className="text-white/60 text-sm mb-4">{error}</div>
            <div className="space-x-4">
              <Button
                onClick={fetchProjectData}
                className="bg-blue-600 hover:bg-blue-700 text-white rounded-none"
                disabled={loading}
              >
                {loading ? "Retrying..." : "Retry"}
              </Button>
              <Button
                onClick={() => router.back()}
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10 rounded-none"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </WorkspaceLayout>
    );
  }

  if (!project) {
    return (
      <WorkspaceLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-white">Project not found</div>
        </div>
      </WorkspaceLayout>
    );
  }

  return (
    <WorkspaceLayout>
      <div className="flex flex-col h-full">
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 p-4 rounded-none">
              <div className="flex items-center space-x-2">
                <div className="text-red-400 text-sm font-medium">Error</div>
                <button
                  onClick={() => setError(null)}
                  className="text-red-400 hover:text-red-300 ml-auto"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className="text-red-300 text-sm mt-1">{error}</div>
            </div>
          )}

          {/* Success Display */}
          {successMessage && (
            <div className="bg-green-500/10 border border-green-500/30 p-4 rounded-none">
              <div className="flex items-center space-x-2">
                <div className="text-green-400 text-sm font-medium">
                  Success
                </div>
                <button
                  onClick={() => setSuccessMessage(null)}
                  className="text-green-400 hover:text-green-300 ml-auto"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className="text-green-300 text-sm mt-1">
                {successMessage}
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="space-y-4">
            {/* PROJECT DETAILS heading */}
            <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
              Project Details
            </h1>

            {/* Project heading */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <h2 className="text-2xl font-semibold text-white">
                  {project.title || project.name}
                </h2>
              </div>
            </div>
          </div>
        </div>

        {/* Project Details Content */}
        <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
          <div className="mt-6 space-y-6">
            {/* Project Information */}
            <div className="border border-white/20 bg-transparent p-6">
              <h3 className="text-lg font-medium text-white mb-4">
                Project Overview
              </h3>

              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div className="border border-white/20 p-4">
                  <div className="text-xs text-white/60">Status</div>
                  <div className="text-white mt-1">{project.status || "—"}</div>
                </div>
                <div className="border border-white/20 p-4">
                  <div className="text-xs text-white/60">Deadline</div>
                  <div className="text-white mt-1">
                    {project.deadline ?
                      new Date(project.deadline).toLocaleDateString()
                    : "—"}
                  </div>
                </div>
                <div className="border border-white/20 p-4">
                  <div className="text-xs text-white/60">Progress</div>
                  <div className="text-white mt-1">
                    {project.completion_percentage ?
                      `${project.completion_percentage}%`
                    : "—"}
                  </div>
                </div>
                <div className="border border-white/20 p-4">
                  <div className="text-xs text-white/60">Budget</div>
                  <div className="text-white mt-1">
                    {project.total_budget ?
                      `${project.currency || "$"}${project.total_budget.toLocaleString()}`
                    : "—"}
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="mt-2">
                <h4 className="text-sm font-medium text-white mb-3">
                  Quick Links
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  <Link
                    href={`/workspace/${workspaceId}/projects/${projectId}/milestones`}
                    className="border border-white/20 p-4 block"
                  >
                    <div className="text-white">Milestones</div>
                    <div className="text-white/60 text-xs">
                      Track and review milestones
                    </div>
                  </Link>
                  <Link
                    href={`/workspace/${workspaceId}/projects/${projectId}/tasks`}
                    className="border border-white/20 p-4 block"
                  >
                    <div className="text-white">Delivery (Tasks)</div>
                    <div className="text-white/60 text-xs">
                      Work items linked to milestones
                    </div>
                  </Link>
                  <Link
                    href={`/workspace/${workspaceId}/projects/${projectId}/files`}
                    className="border border-white/20 p-4 block"
                  >
                    <div className="text-white">Files</div>
                    <div className="text-white/60 text-xs">
                      Upload and attach deliverables
                    </div>
                  </Link>
                  <Link
                    href={`/workspace/${workspaceId}/projects/${projectId}/financials`}
                    className="border border-white/20 p-4 block"
                  >
                    <div className="text-white">Financials</div>
                    <div className="text-white/60 text-xs">
                      Budget vs actual, invoicing
                    </div>
                  </Link>
                  <Link
                    href={`/workspace/${workspaceId}/projects/${projectId}/plan`}
                    className="border border-white/20 p-4 block"
                  >
                    <div className="text-white">Plan</div>
                    <div className="text-white/60 text-xs">
                      Define scope and publish milestones
                    </div>
                  </Link>
                  <Link
                    href={`/workspace/${workspaceId}/projects/${projectId}/client`}
                    className="border border-white/20 p-4 block"
                  >
                    <div className="text-white">Client</div>
                    <div className="text-white/60 text-xs">
                      Approvals and client-visible files
                    </div>
                  </Link>
                </div>
              </div>
            </div>

            {/* Project Details Card */}
            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-white">
                  Project Details
                </h3>
                <div className="flex items-center space-x-2">
                  {isEditing ?
                    <>
                      <Button
                        variant="ghost"
                        onClick={handleSave}
                        disabled={saving}
                        className="text-green-400 hover:bg-green-400/10 p-2 rounded-none disabled:opacity-50 flex items-center"
                      >
                        {saving ?
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
                            <span className="ml-2 text-xs">Saving...</span>
                          </>
                        : <Save className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={handleCancel}
                        disabled={saving}
                        className="text-red-400 hover:bg-red-400/10 p-2 rounded-none disabled:opacity-50"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </>
                  : <Button
                      variant="ghost"
                      onClick={handleEditClick}
                      className="text-white hover:bg-white/10 p-2 rounded-none"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  }
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Project Name
                  </label>
                  {isEditing ?
                    <Input
                      value={editedProject?.name || editedProject?.title || ""}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                      className="bg-transparent border-white/20 text-white rounded-none"
                    />
                  : <div className="text-white">
                      {project.title || project.name}
                    </div>
                  }
                </div>
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Status
                  </label>
                  {isEditing ?
                    <Select
                      value={editedProject?.status || ""}
                      onValueChange={(value) =>
                        handleInputChange("status", value)
                      }
                    >
                      <SelectTrigger className="bg-transparent border-white/20 text-white rounded-none">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="on_hold">On Hold</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  : <span
                      className={`px-2 py-1 text-xs ${getStatusColorClass(project.status)}`}
                    >
                      {project.status}
                    </span>
                  }
                </div>
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Deadline
                  </label>
                  {isEditing ?
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal bg-transparent border-white/20 text-white rounded-none hover:bg-white/10"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {editedProject?.dueDate || editedProject?.deadline ?
                            format(
                              new Date(
                                editedProject.dueDate || editedProject.deadline
                              ),
                              "PPP"
                            )
                          : "Pick a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 bg-black/80 backdrop-blur-md border-white/20 rounded-none"
                        align="start"
                      >
                        <Calendar
                          mode="single"
                          selected={
                            editedProject?.dueDate || editedProject?.deadline ?
                              new Date(
                                editedProject.dueDate || editedProject.deadline
                              )
                            : undefined
                          }
                          onSelect={(date) =>
                            handleInputChange(
                              "dueDate",
                              date ? format(date, "yyyy-MM-dd") : ""
                            )
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  : <div className="text-white">
                      {project.deadline ?
                        new Date(project.deadline).toLocaleDateString()
                      : "—"}
                    </div>
                  }
                </div>
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Progress
                  </label>
                  {isEditing ?
                    <Input
                      value={
                        editedProject?.progress ||
                        editedProject?.completion_percentage ||
                        ""
                      }
                      onChange={(e) =>
                        handleInputChange("progress", e.target.value)
                      }
                      className="bg-transparent border-white/20 text-white rounded-none"
                      placeholder="e.g., 75"
                      type="number"
                      min="0"
                      max="100"
                    />
                  : <div className="text-white">
                      {project.completion_percentage ?
                        `${project.completion_percentage}%`
                      : "—"}
                    </div>
                  }
                </div>
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Project Budget
                  </label>
                  {isEditing ?
                    <Input
                      type="number"
                      value={
                        editedProject?.totalBudget ||
                        editedProject?.total_budget ||
                        ""
                      }
                      onChange={(e) =>
                        handleInputChange("totalBudget", e.target.value)
                      }
                      className="bg-transparent border-white/20 text-white rounded-none"
                      placeholder="e.g., 15000"
                      min="0"
                      step="100"
                    />
                  : <div className="text-white">
                      {project.total_budget ?
                        `${project.currency || "$"}${project.total_budget.toLocaleString()}`
                      : "—"}
                    </div>
                  }
                </div>
              </div>

              {/* Client Information Section */}
              <div className="mt-8 pt-6 border-t border-white/20">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-white">
                    Client Information
                  </h4>
                  {hasClientChanges && (
                    <Button
                      onClick={handleSaveClientChanges}
                      disabled={saving}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-none disabled:opacity-50 flex items-center"
                    >
                      {saving ?
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          <span>Saving...</span>
                        </>
                      : <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Client Changes
                        </>
                      }
                    </Button>
                  )}
                </div>
                <div className="space-y-6">
                  {/* Client Header */}
                  <div>
                    <p className="text-xs text-white/50 mb-1">Client Name</p>
                    <Input
                      value={
                        clientChanges.contact_person ||
                        project.contact_person ||
                        ""
                      }
                      onChange={(e) =>
                        handleClientFieldChange(
                          "contact_person",
                          e.target.value
                        )
                      }
                      className="bg-transparent border border-white/30 text-white rounded-none text-lg font-medium focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none"
                      placeholder="Enter client name..."
                    />
                  </div>

                  {/* Client Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Contact Information */}
                    <div className="space-y-4">
                      <h6 className="text-sm font-medium text-white/70 mb-3">
                        Contact Details
                      </h6>
                      <div className="space-y-4">
                        <div>
                          <p className="text-xs text-white/50 mb-1">
                            Contact Person
                          </p>
                          <Input
                            value={
                              clientChanges.contact_person ||
                              project.contact_person ||
                              ""
                            }
                            onChange={(e) =>
                              handleClientFieldChange(
                                "contact_person",
                                e.target.value
                              )
                            }
                            className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none"
                            placeholder="Enter contact person..."
                          />
                        </div>
                        <div>
                          <p className="text-xs text-white/50 mb-1">Email</p>
                          <Input
                            value={
                              clientChanges.client_email ||
                              project.client_email ||
                              ""
                            }
                            onChange={(e) =>
                              handleClientFieldChange(
                                "client_email",
                                e.target.value
                              )
                            }
                            className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none"
                            placeholder="Enter email..."
                            type="email"
                          />
                        </div>
                        <div>
                          <p className="text-xs text-white/50 mb-1">Phone</p>
                          <Input
                            value={
                              clientChanges.client_phone ||
                              project.client_phone ||
                              ""
                            }
                            onChange={(e) =>
                              handleClientFieldChange(
                                "client_phone",
                                e.target.value
                              )
                            }
                            className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none"
                            placeholder="Enter phone number..."
                          />
                        </div>
                        <div>
                          <p className="text-xs text-white/50 mb-1">Address</p>
                          <textarea
                            value={
                              clientChanges.client_address ||
                              project.client_address ||
                              ""
                            }
                            onChange={(e) =>
                              handleClientFieldChange(
                                "client_address",
                                e.target.value
                              )
                            }
                            className="w-full bg-transparent border border-white/30 text-white rounded-none text-sm p-2 resize-none focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none"
                            placeholder="Enter address..."
                            rows={2}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Business Information */}
                    <div className="space-y-4">
                      <h6 className="text-sm font-medium text-white/70 mb-3">
                        Business Information
                      </h6>
                      <div className="space-y-4">
                        <div>
                          <p className="text-xs text-white/50 mb-1">Industry</p>
                          <Input
                            value={
                              clientChanges.client_industry ||
                              project.client_industry ||
                              ""
                            }
                            onChange={(e) =>
                              handleClientFieldChange(
                                "client_industry",
                                e.target.value
                              )
                            }
                            className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none"
                            placeholder="Enter industry..."
                          />
                        </div>
                        <div>
                          <p className="text-xs text-white/50 mb-1">
                            Company Size
                          </p>
                          <Select
                            value={
                              clientChanges.client_company_size ||
                              project.client_company_size ||
                              ""
                            }
                            onValueChange={(value) =>
                              handleClientFieldChange(
                                "client_company_size",
                                value
                              )
                            }
                          >
                            <SelectTrigger className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none">
                              <SelectValue placeholder="Select company size" />
                            </SelectTrigger>
                            <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                              <SelectItem value="Small">
                                Small (1-50 employees)
                              </SelectItem>
                              <SelectItem value="Medium">
                                Medium (51-200 employees)
                              </SelectItem>
                              <SelectItem value="Large">
                                Large (201-1000 employees)
                              </SelectItem>
                              <SelectItem value="Enterprise">
                                Enterprise (1000+ employees)
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <p className="text-xs text-white/50 mb-1">Priority</p>
                          <Select
                            value={
                              clientChanges.client_priority ||
                              project.client_priority ||
                              ""
                            }
                            onValueChange={(value) =>
                              handleClientFieldChange("client_priority", value)
                            }
                          >
                            <SelectTrigger className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none">
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                            <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                              <SelectItem value="low">Low</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div className="space-y-4">
                      <h6 className="text-sm font-medium text-white/70 mb-3">
                        Additional Information
                      </h6>
                      <div className="space-y-4">
                        <div>
                          <p className="text-xs text-white/50 mb-1">
                            Client Status
                          </p>
                          <Select
                            value={
                              clientChanges.client_status ||
                              project.client_status ||
                              ""
                            }
                            onValueChange={(value) =>
                              handleClientFieldChange("client_status", value)
                            }
                          >
                            <SelectTrigger className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="inactive">Inactive</SelectItem>
                              <SelectItem value="completed">
                                Completed
                              </SelectItem>
                              <SelectItem value="archived">Archived</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Client Notes */}
                  <div className="mt-6 pt-4 border-t border-white/10">
                    <h6 className="text-sm font-medium text-white/70 mb-2">
                      Notes
                    </h6>
                    <textarea
                      value={
                        clientChanges.client_notes || project.client_notes || ""
                      }
                      onChange={(e) =>
                        handleClientFieldChange("client_notes", e.target.value)
                      }
                      className="w-full bg-transparent border border-white/30 text-white rounded-none text-sm p-4 resize-none focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none"
                      placeholder="Enter notes about this client..."
                      rows={3}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Full width border line */}
        <div className="border-b border-white/20 w-full mt-8"></div>
      </div>
    </WorkspaceLayout>
  );
}
