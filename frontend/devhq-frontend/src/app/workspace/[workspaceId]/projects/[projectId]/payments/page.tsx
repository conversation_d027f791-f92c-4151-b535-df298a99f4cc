/** @format */

"use client";

import React, { useState } from "react";
import { useParams } from "next/navigation";
import { WorkspaceLayout } from "@/components/layout/workspace-layout";
import { useProject } from "@/contexts/project-context";
import {
  DollarSign,
  CreditCard,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus,
  Send,
  Download,
  Eye,
  Edit,
  Calendar,
  FileText,
  TrendingUp,
  Link,
  Copy,
  ExternalLink,
  Mail,
  Target,
  Percent,
} from "lucide-react";
import SendEmailModal from "@/components/modals/send-email-modal";
import { EnhancedInvoiceCreationModal } from "@/components/modals/enhanced-invoice-creation-modal";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { CalendarIcon } from "@radix-ui/react-icons";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

export default function PaymentsPage() {
  const { project, isLoading } = useProject();
  const { toast } = useToast();

  // Local state
  const [selectedStatus, setSelectedStatus] = useState<
    "all" | "draft" | "sent" | "paid" | "overdue"
  >("all");
  const [showNewInvoiceForm, setShowNewInvoiceForm] = useState(false);
  const [newInvoice, setNewInvoice] = useState({
    title: "",
    description: "",
    amount: 0,
    dueDate: new Date(),
    items: [] as Array<{ description: string; quantity: number; rate: number }>,
  });
  const [newItem, setNewItem] = useState({
    description: "",
    quantity: 1,
    rate: 0,
  });

  // Email modal state
  const [emailModal, setEmailModal] = useState({
    isOpen: false,
    type: "payment_link" as "payment_link" | "invoice_receipt",
    invoiceData: null as any,
  });

  // Mock data for invoices and payments (would come from project context in real app)
  const invoices: any[] = [];
  const payments: any[] = [];

  // Filter invoices by status
  const filteredInvoices =
    selectedStatus === "all" ? invoices : (
      invoices.filter((invoice) => invoice.status === selectedStatus)
    );

  // Sort invoices by creation date (newest first)
  const sortedInvoices = [...filteredInvoices].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  // Calculate metrics
  const totalInvoiced = invoices.reduce((sum, inv) => sum + inv.amount, 0);
  const totalPaid = payments.reduce((sum, pay) => sum + pay.amount, 0);
  const totalOutstanding = invoices
    .filter((inv) => inv.status !== "paid")
    .reduce((sum, inv) => sum + inv.amount, 0);
  const overdueInvoices = invoices.filter(
    (inv) => inv.status !== "paid" && new Date(inv.dueDate) < new Date()
  ).length;

  // Invoice management functions
  const handleAddInvoice = () => {
    if (newInvoice.title.trim() && newInvoice.amount > 0) {
      const totalAmount = newInvoice.items.reduce(
        (sum, item) => sum + item.quantity * item.rate,
        0
      );

      // Note: In a real app, this would call addInvoice from the project context
      console.log("Invoice would be created:", {
        title: newInvoice.title,
        description: newInvoice.description,
        amount: totalAmount || newInvoice.amount,
        dueDate: newInvoice.dueDate,
        status: "draft",
        createdAt: new Date(),
        items: newInvoice.items,
      });

      setNewInvoice({
        title: "",
        description: "",
        amount: 0,
        dueDate: new Date(),
        items: [],
      });
      setShowNewInvoiceForm(false);
    }
  };

  const handleSendInvoice = (invoiceId: string) => {
    // Note: In a real app, this would call updateInvoice from the project context
    console.log("Invoice would be sent:", invoiceId);
    toast({
      title: "Invoice Sent!",
      description: "The invoice has been sent to your client.",
      variant: "success",
    });
  };

  const handleMarkPaid = (invoiceId: string, amount: number) => {
    // Note: In a real app, this would call updateInvoice and addPayment from the project context
    console.log("Invoice would be marked as paid:", invoiceId, amount);
    toast({
      title: "Payment Recorded!",
      description: "The invoice has been marked as paid.",
      variant: "success",
    });
  };

  const handleAddItem = () => {
    if (newItem.description.trim() && newItem.rate > 0) {
      setNewInvoice({
        ...newInvoice,
        items: [...newInvoice.items, { ...newItem }],
      });
      setNewItem({ description: "", quantity: 1, rate: 0 });
    }
  };

  const handleRemoveItem = (index: number) => {
    setNewInvoice({
      ...newInvoice,
      items: newInvoice.items.filter((_, i) => i !== index),
    });
  };

  // Payment link generation functions
  const generatePaymentLink = (invoiceId: string) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/payment/${invoiceId}`;
  };

  const copyPaymentLink = (invoiceId: string) => {
    const link = generatePaymentLink(invoiceId);
    navigator.clipboard.writeText(link).then(() => {
      toast({
        title: "Payment Link Copied!",
        description: "Share this link with your client for easy payment.",
        variant: "success",
      });
    });
  };

  // Email functions
  const handleSendPaymentLink = (invoice: any) => {
    setEmailModal({
      isOpen: true,
      type: "payment_link",
      invoiceData: invoice,
    });
  };

  const handleSendReceipt = (invoice: any) => {
    setEmailModal({
      isOpen: true,
      type: "invoice_receipt",
      invoiceData: invoice,
    });
  };

  const handleEmailSend = async (emailData: {
    clientEmail: string;
    clientName: string;
    customMessage?: string;
  }) => {
    const { invoiceData } = emailModal;

    try {
      // In real implementation, this would call the backend API
      const response = await fetch(
        `/api/v1/invoices/${invoiceData.id}/send-email`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email_type: emailModal.type,
            client_email: emailData.clientEmail,
            client_name: emailData.clientName,
            custom_message: emailData.customMessage,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to send email");
      }

      // For demo purposes, just show success
      console.log("Email sent successfully:", emailData);
    } catch (error) {
      console.error("Error sending email:", error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  // Status categories for filtering
  const statusCategories: Array<{
    key: "all" | "draft" | "sent" | "paid" | "overdue";
    label: string;
    count: number;
  }> = [
    { key: "all", label: "All Invoices", count: invoices.length },
    {
      key: "draft",
      label: "Draft",
      count: invoices.filter((i) => i.status === "draft").length,
    },
    {
      key: "sent",
      label: "Sent",
      count: invoices.filter((i) => i.status === "sent").length,
    },
    {
      key: "paid",
      label: "Paid",
      count: invoices.filter((i) => i.status === "paid").length,
    },
    { key: "overdue", label: "Overdue", count: overdueInvoices },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "sent":
        return <Clock className="h-5 w-5 text-yellow-400" />;
      case "overdue":
        return <AlertTriangle className="h-5 w-5 text-red-400" />;
      default:
        return <FileText className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "text-green-400 border-green-400";
      case "sent":
        return "text-yellow-400 border-yellow-400";
      case "overdue":
        return "text-red-400 border-red-400";
      default:
        return "text-gray-400 border-gray-400";
    }
  };

  if (isLoading) {
    return (
      <WorkspaceLayout>
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          <div className="text-white">Loading...</div>
        </div>
      </WorkspaceLayout>
    );
  }

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Navigation */}
        <div className="space-y-4">
          {/* PAYMENTS & INVOICING heading */}
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            Payments & Invoicing
          </h1>

          {/* Project heading */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <h2 className="text-2xl font-semibold text-white">
                Invoice Management & Payment Tracking
              </h2>
            </div>
            <button
              onClick={() => setShowNewInvoiceForm(true)}
              className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20"
            >
              <Plus className="h-4 w-4 mr-2 inline" />
              Create Invoice
            </button>
          </div>
        </div>
      </div>

      {/* Payments Content */}
      <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
        <div className="mt-6 space-y-6">
          {/* Financial Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/20 flex items-center justify-center">
                  <FileText className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${totalInvoiced.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Total Invoiced</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/20 flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${totalPaid.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Total Paid</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-yellow-500/20 flex items-center justify-center">
                  <Clock className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    ${totalOutstanding.toLocaleString()}
                  </div>
                  <div className="text-sm text-white/60">Outstanding</div>
                </div>
              </div>
            </div>

            <div className="border border-white/20 bg-transparent p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-red-500/20 flex items-center justify-center">
                  <AlertTriangle className="h-5 w-5 text-red-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {overdueInvoices}
                  </div>
                  <div className="text-sm text-white/60">Overdue</div>
                </div>
              </div>
            </div>
          </div>

          {/* Status Filter */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <CreditCard className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">
                Filter by Status
              </h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {statusCategories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedStatus(category.key)}
                  className={`border px-3 py-1 text-xs ${
                    selectedStatus === category.key ?
                      "border-green-400 bg-green-400/10 text-green-400"
                    : "border-white/20 text-white/60 hover:text-white"
                  }`}
                >
                  {category.label} ({category.count})
                </button>
              ))}
            </div>
          </div>

          {/* New Invoice Form */}
          {showNewInvoiceForm && (
            <div className="border border-white/20 bg-transparent p-6">
              <div className="flex items-center gap-2 mb-4">
                <Plus className="h-5 w-5 text-green-400" />
                <h3 className="text-lg font-medium text-white">
                  Create New Invoice
                </h3>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="invoice-title">Invoice Title</Label>
                  <Input
                    id="invoice-title"
                    type="text"
                    placeholder="e.g. Website Development - Phase 1"
                    value={newInvoice.title}
                    onChange={(e) =>
                      setNewInvoice({ ...newInvoice, title: e.target.value })
                    }
                    className="bg-transparent border-white/20 text-white rounded-none"
                  />
                </div>
                <div>
                  <Label htmlFor="invoice-description">Description</Label>
                  <Textarea
                    id="invoice-description"
                    placeholder="Detailed description of work completed..."
                    value={newInvoice.description}
                    onChange={(e) =>
                      setNewInvoice({
                        ...newInvoice,
                        description: e.target.value,
                      })
                    }
                    rows={3}
                    className="bg-transparent border-white/20 text-white rounded-none"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="invoice-amount">Total Amount ($)</Label>
                    <Input
                      id="invoice-amount"
                      type="number"
                      placeholder="e.g. 2500.00"
                      value={newInvoice.amount}
                      onChange={(e) =>
                        setNewInvoice({
                          ...newInvoice,
                          amount: Number(e.target.value),
                        })
                      }
                      min="0"
                      step="0.01"
                      className="bg-transparent border-white/20 text-white rounded-none"
                    />
                  </div>
                  <div>
                    <Label htmlFor="invoice-due-date">Due Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal bg-transparent border border-white/20 text-white hover:bg-transparent hover:text-white rounded-none",
                            !newInvoice.dueDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {newInvoice.dueDate ?
                            format(newInvoice.dueDate, "PPP")
                          : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 bg-black/80 backdrop-blur-md border-white/20 rounded-none"
                        align="start"
                      >
                        <CalendarComponent
                          mode="single"
                          selected={newInvoice.dueDate}
                          onSelect={(date) =>
                            setNewInvoice({
                              ...newInvoice,
                              dueDate: date || new Date(),
                            })
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Line Items */}
                <div className="border-t border-white/20 pt-4">
                  <h4 className="text-white font-medium mb-3">Line Items</h4>
                  <div className="space-y-2">
                    {newInvoice.items.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2 p-2 border border-white/20"
                      >
                        <span className="flex-1 text-white">
                          {item.description}
                        </span>
                        <span className="text-white/60 text-sm">
                          {item.quantity} × ${item.rate}
                        </span>
                        <span className="text-white font-medium">
                          ${(item.quantity * item.rate).toFixed(2)}
                        </span>
                        <button
                          onClick={() => handleRemoveItem(index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                  <div className="mt-3">
                    <div className="grid grid-cols-1 gap-4 mb-4">
                      <div>
                        <Label htmlFor="item-description">
                          Item Description
                        </Label>
                        <Input
                          id="item-description"
                          type="text"
                          placeholder="e.g. Website design and development"
                          value={newItem.description}
                          onChange={(e) =>
                            setNewItem({
                              ...newItem,
                              description: e.target.value,
                            })
                          }
                          className="bg-transparent border-white/20 text-white rounded-none"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="item-quantity">Quantity</Label>
                          <Input
                            id="item-quantity"
                            type="number"
                            placeholder="1"
                            value={
                              newItem.quantity === 0 ? "" : newItem.quantity
                            }
                            onChange={(e) =>
                              setNewItem({
                                ...newItem,
                                quantity:
                                  e.target.value === "" ?
                                    0
                                  : Number(e.target.value),
                              })
                            }
                            min="1"
                            className="bg-transparent border-white/20 text-white rounded-none"
                          />
                        </div>
                        <div>
                          <Label htmlFor="item-rate">Rate ($)</Label>
                          <Input
                            id="item-rate"
                            type="number"
                            placeholder="75.00"
                            value={newItem.rate === 0 ? "" : newItem.rate}
                            onChange={(e) =>
                              setNewItem({
                                ...newItem,
                                rate:
                                  e.target.value === "" ?
                                    0
                                  : Number(e.target.value),
                              })
                            }
                            min="0"
                            step="0.01"
                            className="bg-transparent border-white/20 text-white rounded-none"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <button
                        onClick={handleAddItem}
                        className="border border-blue-400/30 bg-blue-400/10 px-3 py-2 text-blue-400 hover:bg-blue-400/20"
                        title="Add line item"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={handleAddInvoice}
                    className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20"
                  >
                    Create Invoice
                  </button>
                  <button
                    onClick={() => setShowNewInvoiceForm(false)}
                    className="border border-white/20 px-4 py-2 text-white/60 hover:text-white"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Invoices List */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-6">
              <FileText className="h-5 w-5 text-green-400" />
              <h3 className="text-lg font-medium text-white">Invoices</h3>
            </div>
            <div className="space-y-4">
              {sortedInvoices.map((invoice) => (
                <div
                  key={invoice.id}
                  className="border border-white/20 bg-transparent p-4"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(invoice.status)}
                        <h3 className="text-white font-medium">
                          {invoice.title}
                        </h3>
                        {invoice.milestoneId && (
                          <div className="border border-purple-400/30 bg-purple-400/10 px-2 py-1 text-xs text-purple-400">
                            MILESTONE
                          </div>
                        )}
                        <div
                          className={`border px-2 py-1 text-xs ${getStatusColor(invoice.status)}`}
                        >
                          {invoice.status.toUpperCase()}
                        </div>
                      </div>
                      <p className="text-white/60 text-sm mb-3">
                        {invoice.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-white/60">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Due: {new Date(invoice.dueDate).toLocaleDateString()}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />$
                          {invoice.amount.toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => copyPaymentLink(invoice.id)}
                        className="border border-purple-400/30 bg-purple-400/10 px-3 py-1 text-purple-400 hover:bg-purple-400/20"
                        title="Copy payment link for client"
                      >
                        <Link className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleSendPaymentLink(invoice)}
                        className="border border-orange-400/30 bg-orange-400/10 px-3 py-1 text-orange-400 hover:bg-orange-400/20"
                        title="Send payment link via email"
                      >
                        <Mail className="h-4 w-4" />
                      </button>
                      {invoice.status === "draft" && (
                        <button
                          onClick={() => handleSendInvoice(invoice.id)}
                          className="border border-blue-400/30 bg-blue-400/10 px-3 py-1 text-blue-400 hover:bg-blue-400/20"
                          title="Send invoice"
                        >
                          <Send className="h-4 w-4" />
                        </button>
                      )}
                      {invoice.status === "sent" && (
                        <button
                          onClick={() =>
                            handleMarkPaid(invoice.id, invoice.amount)
                          }
                          className="border border-green-400/30 bg-green-400/10 px-3 py-1 text-green-400 hover:bg-green-400/20"
                          title="Mark as paid"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </button>
                      )}
                      <button
                        className="border border-white/20 bg-transparent px-3 py-1 text-white/60 hover:text-white"
                        title="Download PDF"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                      <button
                        className="border border-white/20 bg-transparent px-3 py-1 text-white/60 hover:text-white"
                        title="View details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {sortedInvoices.length === 0 && (
                <div className="text-center py-8 text-white/60">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>
                    {invoices.length === 0 ?
                      "No invoices yet. Create your first invoice!"
                    : "No invoices match the selected status filter."}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Smart Invoice Creation */}
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Target className="h-5 w-5 text-purple-400" />
              <h3 className="text-lg font-medium text-white">
                Smart Invoice Creation
              </h3>
            </div>
            <p className="text-white/60 text-sm mb-4">
              Create invoices using flexible billing scenarios tailored to your project workflow.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => {
                  // In a real implementation, this would open the enhanced modal with the milestone scenario selected
                  alert("This would open the enhanced invoice creation modal with milestone scenario selected");
                }}
                className="border border-white/20 bg-transparent p-4 hover:bg-white/5 text-left"
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-blue-500/20 flex items-center justify-center">
                    <Target className="h-4 w-4 text-blue-400" />
                  </div>
                  <h4 className="font-medium text-white">Milestone Payment</h4>
                </div>
                <p className="text-xs text-white/60">
                  Invoice for completed project milestones or approved deliverables.
                </p>
              </button>
              
              <button
                onClick={() => {
                  // In a real implementation, this would open the enhanced modal with the deposit scenario selected
                  alert("This would open the enhanced invoice creation modal with deposit scenario selected");
                }}
                className="border border-white/20 bg-transparent p-4 hover:bg-white/5 text-left"
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-green-500/20 flex items-center justify-center">
                    <Percent className="h-4 w-4 text-green-400" />
                  </div>
                  <h4 className="font-medium text-white">Upfront Deposit</h4>
                </div>
                <p className="text-xs text-white/60">
                  Request an upfront payment before starting work on the project.
                </p>
              </button>
              
              <button
                onClick={() => {
                  // In a real implementation, this would open the enhanced modal with the time & materials scenario selected
                  alert("This would open the enhanced invoice creation modal with time & materials scenario selected");
                }}
                className="border border-white/20 bg-transparent p-4 hover:bg-white/5 text-left"
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-yellow-500/20 flex items-center justify-center">
                    <Clock className="h-4 w-4 text-yellow-400" />
                  </div>
                  <h4 className="font-medium text-white">Time & Materials</h4>
                </div>
                <p className="text-xs text-white/60">
                  Invoice for tracked time and expenses over a specific period.
                </p>
              </button>
            </div>
            <div className="mt-4">
              <button
                onClick={() => {
                  // In a real implementation, this would open the enhanced modal
                  alert("This would open the enhanced invoice creation modal");
                }}
                className="border border-purple-400/30 bg-purple-400/10 px-4 py-2 text-purple-400 hover:bg-purple-400/20 flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create Smart Invoice
              </button>
            </div>
        </div>
      </div>

      {/* Email Modal */}
      <SendEmailModal

      isOpen={emailModal.isOpen as boolean}
        onClose={() => setEmailModal({ ...emailModal, isOpen: false })}
        emailType={emailModal.type}
        data={{
          projectName: project?.name,
          clientName: emailModal.invoiceData?.clientName,
          amount: emailModal.invoiceData?.amount,
          invoiceNumber: emailModal.invoiceData?.id,
          paymentLink:
            emailModal.invoiceData ?
              generatePaymentLink(emailModal.invoiceData.id)
            : "",
        }}
        onSend={handleEmailSend}
      />
      
            {/* Enhanced Invoice Creation Modal */}
      <EnhancedInvoiceCreationModal
        isOpen={showEnhancedInvoiceModal}
        onClose={() => setShowEnhancedInvoiceModal(false)}
        onSubmit={(invoiceData) => {
          console.log("Invoice created:", invoiceData);
          // Handle the created invoice
          setShowEnhancedInvoiceModal(false);
          // In a real implementation, you would update the project context or refetch invoices
          toast({
            title: "Invoice Created!",
            description: "Your smart invoice has been created successfully.",
            variant: "success",
          });
        }}
        projectId={project?.id || ""}
        projectName={project?.name || ""}
        clientName={project?.client?.name || project?.clientName || "Client"}
      />
    </WorkspaceLayout>
  );
}
