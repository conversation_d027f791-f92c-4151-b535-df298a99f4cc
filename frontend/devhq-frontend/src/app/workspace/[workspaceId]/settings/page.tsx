'use client'

import { WorkspaceLayout } from '@/components/layout/workspace-layout'
import { useParams } from 'next/navigation'

export default function WorkspaceSettingsPage() {
  const params = useParams();
  const { workspaceId } = params;

  return (
    <WorkspaceLayout>
      <div className="p-4 md:p-6">
        <h1 className="text-2xl font-semibold text-white">Workspace Settings for Workspace ID: {workspaceId}</h1>
      </div>
    </WorkspaceLayout>
  )
}
