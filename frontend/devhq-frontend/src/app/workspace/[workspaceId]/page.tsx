/** @format */

"use client";

import { WorkspaceLayout } from "@/components/layout/workspace-layout";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Plus, Search, Trash, Calendar as CalendarIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { CustomCheckbox } from "@/components/ui/custom-checkbox";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { workspaceApi, projectApi, clientApi } from "@/lib/api";

// Types for backend data
interface Project {
  id: string;
  title: string;
  description: string | null;
  client_id: string;
  user_id: string;
  workspace_id: string;
  billing_type: string;
  total_budget: number | null;
  estimated_hours: number | null;
  hourly_rate: number | null;
  currency: string;
  status: string;
  start_date: string | null;
  end_date: string | null;
  deadline: string | null;
  is_billable: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  total_logged_hours: number;
  total_billable_amount: number;
  completion_percentage: number;
}

interface Workspace {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_default: boolean;
  status: string;
  project_count: number;
  created_at: string;
  updated_at: string;
}

export default function WorkspaceDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { workspaceId } = params;
  const [activeFilter, setActiveFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [editingProject, setEditingProject] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState({
    title: "",
    status: "",
    deadline: "",
  });
  const [projects, setProjects] = useState<Project[]>([]);
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [clients, setClients] = useState<{ [key: string]: string }>({});
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load workspace and projects data
  useEffect(() => {
    const loadData = async () => {
      if (!workspaceId || typeof workspaceId !== "string") return;

      setIsLoading(true);
      setError(null);

      // Check if we're coming from project creation
      const shouldRefresh = searchParams.get("refresh") === "true";
      if (shouldRefresh) {
        // Clear the refresh parameter from URL
        router.replace(`/workspace/${workspaceId}`);
      }

      try {
        // Load workspace details
        const workspaceData = await workspaceApi.getWorkspace(workspaceId);
        setWorkspace(workspaceData);

        // Load projects for this workspace using the actual workspace UUID
        const projectsData = await projectApi.listProjects({
          workspace_id: workspaceData.id,
          per_page: 100, // Load all projects for now
        });
        console.log("Loaded projects:", projectsData.items);
        console.log("Projects count:", projectsData.items.length);
        console.log("First project:", projectsData.items[0]);
        setProjects(projectsData.items);

        // Load clients to get names (filter by workspace)
        const clientsData = await clientApi.listClients({
          workspace_id: workspaceData.id,
          per_page: 100, // Load all clients for now
        });

        // Create client ID to name mapping
        const clientMap: { [key: string]: string } = {};
        clientsData.items.forEach((client) => {
          clientMap[client.id] = client.name;
        });
        setClients(clientMap);
      } catch (err: any) {
        console.error("Error loading workspace data:", err);
        setError(err.message || "Failed to load workspace data");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [workspaceId, searchParams, router]);

  // Map backend status to display status
  const mapStatus = (status: string) => {
    switch (status) {
      case "active":
        return "In Progress";
      case "completed":
        return "Completed";
      case "draft":
      case "on_hold":
        return "Pending";
      case "cancelled":
        return "Cancelled";
      default:
        return status;
    }
  };

  const getStatusColorClass = (status: string) => {
    const mappedStatus = mapStatus(status);
    switch (mappedStatus) {
      case "In Progress":
        return "bg-orange-500/20 text-orange-300";
      case "Completed":
        return "bg-green-500/20 text-green-300";
      case "Pending":
        return "bg-red-500/20 text-red-300";
      case "Cancelled":
        return "bg-gray-500/20 text-gray-300";
      default:
        return "bg-gray-500/20 text-gray-300";
    }
  };

  const projectCounts = {
    All: projects.length,
    "In Progress": projects.filter((p) => mapStatus(p.status) === "In Progress")
      .length,
    Completed: projects.filter((p) => mapStatus(p.status) === "Completed")
      .length,
    Pending: projects.filter((p) => mapStatus(p.status) === "Pending").length,
  };

  const filteredProjects = projects.filter((project) => {
    const mappedStatus = mapStatus(project.status);
    const matchesFilter =
      activeFilter === "All" || mappedStatus === activeFilter;
    const matchesSearch =
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (project.description &&
        project.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesFilter && matchesSearch;
  });

  // Debug filtered projects
  console.log("All projects:", projects);
  console.log("Active filter:", activeFilter);
  console.log("Search term:", searchTerm);
  console.log("Filtered projects:", filteredProjects);
  console.log("Filtered projects count:", filteredProjects.length);

  const handleEditProject = (projectId: string) => {
    const project = projects.find((p) => p.id === projectId);
    if (project) {
      setEditingProject(projectId);
      setEditFormData({
        title: project.title,
        status: project.status,
        deadline: project.deadline || "",
      });
    }
  };

  const handleSaveEdit = async (projectId: string) => {
    try {
      const updateData: any = {
        title: editFormData.title,
        status: editFormData.status,
      };

      if (editFormData.deadline) {
        updateData.deadline = editFormData.deadline;
      }

      const updatedProject = await projectApi.updateProject(
        projectId,
        updateData
      );

      setProjects((prev) =>
        prev.map((p) => (p.id === projectId ? updatedProject : p))
      );
      setEditingProject(null);
      setEditFormData({ title: "", status: "", deadline: "" });
    } catch (err: any) {
      console.error("Error updating project:", err);
      setError(err.message || "Failed to update project");
    }
  };

  const handleCancelEdit = () => {
    setEditingProject(null);
    setEditFormData({ title: "", status: "", deadline: "" });
  };

  const handleDeleteProjects = async () => {
    try {
      // Delete each selected project
      await Promise.all(
        selectedProjects.map((projectId) => projectApi.deleteProject(projectId))
      );

      // Reload projects from server to ensure consistency
      const projectsData = await projectApi.listProjects({
        workspace_id: workspace?.id || workspaceId,
        per_page: 100,
      });
      setProjects(projectsData.items);
      setSelectedProjects([]);
      setIsDeleteModalOpen(false);
    } catch (err: any) {
      console.error("Error deleting projects:", err);
      setError(err.message || "Failed to delete projects");
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  return (
    <WorkspaceLayout>
      <div className="flex flex-col h-full">
        <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
          {/* Projects Section */}
          <div className="space-y-4">
            {/* WORKSPACE heading */}
            <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
              Workspace
            </h1>

            {/* Workspace heading with Add button */}
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold text-white">
                {workspace?.name || "Loading..."}
              </h2>
              <div className="flex gap-2">
                {selectedProjects.length === 0 && (
                  <Button
                    className="border border-white/20 bg-transparent hover:bg-white hover:text-black text-white rounded-none py-2 px-4 text-sm"
                    onClick={() =>
                      router.push(
                        `/create-project-form?workspaceId=${workspaceId}`
                      )
                    }
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Project
                  </Button>
                )}
                {selectedProjects.length > 0 && (
                  <Button
                    variant="outline"
                    className="border-red-500/20 bg-red-500/10 hover:bg-red-500/20 text-red-400 rounded-none py-2 px-4 text-sm"
                    onClick={handleDeleteClick}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete ({selectedProjects.length})
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Full width border line */}
        <div className="border-b border-white/20 w-full mt-8"></div>

        {/* Projects heading after border */}
        <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
          <h1 className="text-xs text-white/70 uppercase tracking-wider">
            Projects
          </h1>

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-white/60">Loading projects...</div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded p-4 mt-4">
              <div className="text-red-400 text-sm">{error}</div>
              <Button
                onClick={() => window.location.reload()}
                className="mt-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 border-red-500/20 rounded-none text-xs"
              >
                Retry
              </Button>
            </div>
          )}

          {/* Content - only show when not loading and no error */}
          {!isLoading && !error && (
            <>
              {/* Filter Buttons */}
              <div className="flex mt-4 rounded-none overflow-hidden">
                <Button
                  variant="ghost"
                  className={`border border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === "All" ? "bg-green-500/20" : "bg-transparent hover:bg-white/10"}`}
                  onClick={() => setActiveFilter("All")}
                >
                  All ({projectCounts.All})
                </Button>
                <Button
                  variant="ghost"
                  className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === "In Progress" ? "bg-green-500/20" : "bg-transparent hover:bg-white/10"}`}
                  onClick={() => setActiveFilter("In Progress")}
                >
                  In Progress ({projectCounts["In Progress"]})
                </Button>
                <Button
                  variant="ghost"
                  className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === "Completed" ? "bg-green-500/20" : "bg-transparent hover:bg-white/10"}`}
                  onClick={() => setActiveFilter("Completed")}
                >
                  Completed ({projectCounts.Completed})
                </Button>
                <Button
                  variant="ghost"
                  className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === "Pending" ? "bg-green-500/20" : "bg-transparent hover:bg-white/10"}`}
                  onClick={() => setActiveFilter("Pending")}
                >
                  Pending ({projectCounts.Pending})
                </Button>
              </div>

              <div className="relative mt-4">
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full p-2 pl-10 bg-transparent border border-white/20 text-white placeholder-gray-500 focus:outline-none focus:ring-0 rounded-none"
                />
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
              </div>

              {/* Table Headers */}
              <div className="grid grid-cols-12 gap-2 border-b border-white/20 pb-2 mt-6 items-center">
                <div className="col-span-1 flex items-center">
                  <CustomCheckbox
                    checked={
                      selectedProjects.length === filteredProjects.length &&
                      filteredProjects.length > 0
                    }
                    onChange={(checked) => {
                      if (checked) {
                        setSelectedProjects(filteredProjects.map((p) => p.id));
                      } else {
                        setSelectedProjects([]);
                      }
                    }}
                  />
                </div>
                <div className="col-span-3 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                  Project Name
                </div>
                <div className="col-span-2 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                  Client
                </div>
                <div className="col-span-2 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                  Status
                </div>
                <div className="col-span-2 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                  Due Date
                </div>
                <div className="col-span-1 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                  Progress
                </div>
                <div className="col-span-1 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                  Actions
                </div>
              </div>

              {/* Project Rows */}
              <div className="space-y-0">
                {filteredProjects.length === 0 ?
                  <div className="py-8 text-center text-gray-400">
                    <p>No projects found.</p>
                    <p className="text-sm mt-2">
                      {projects.length === 0 ?
                        "Create your first project to get started."
                      : "Try adjusting your search or filter."}
                    </p>
                  </div>
                : filteredProjects.map((project, index) => (
                    <div key={project.id}>
                      <div className="grid grid-cols-12 gap-2 py-4 text-sm items-center">
                        <div className="col-span-1 flex items-center">
                          <CustomCheckbox
                            checked={selectedProjects.includes(project.id)}
                            onChange={(checked) => {
                              if (checked) {
                                setSelectedProjects([
                                  ...selectedProjects,
                                  project.id,
                                ]);
                              } else {
                                setSelectedProjects(
                                  selectedProjects.filter(
                                    (id) => id !== project.id
                                  )
                                );
                              }
                            }}
                          />
                        </div>
                        <div className="col-span-3 font-medium text-white flex items-center">
                          {editingProject === project.id ?
                            <input
                              type="text"
                              value={editFormData.title}
                              onChange={(e) =>
                                setEditFormData((prev) => ({
                                  ...prev,
                                  title: e.target.value,
                                }))
                              }
                              className="bg-transparent text-white text-sm rounded-none focus:outline-none blinking-underline w-full"
                            />
                          : <span
                              className="cursor-pointer hover:underline hover:text-green-500"
                              onClick={() =>
                                router.push(
                                  `/workspace/${workspaceId}/projects/${project.id}`
                                )
                              }
                            >
                              {project.title}
                            </span>
                          }
                        </div>
                        <div className="col-span-2 flex items-center">
                          <span className="text-gray-400 text-sm">
                            {clients[project.client_id] || "Unknown Client"}
                          </span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          {editingProject === project.id ?
                            <Select
                              value={editFormData.status}
                              onValueChange={(value) =>
                                setEditFormData((prev) => ({
                                  ...prev,
                                  status: value,
                                }))
                              }
                            >
                              <SelectTrigger className="bg-transparent border-white/20 text-white rounded-none focus:ring-0 focus:ring-offset-0 hover:bg-white/10">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">
                                  <span className="flex items-center text-white">
                                    <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                                    Active
                                  </span>
                                </SelectItem>
                                <SelectItem value="completed">
                                  <span className="flex items-center text-white">
                                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                    Completed
                                  </span>
                                </SelectItem>
                                <SelectItem value="on_hold">
                                  <span className="flex items-center text-white">
                                    <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                    On Hold
                                  </span>
                                </SelectItem>
                                <SelectItem value="cancelled">
                                  <span className="flex items-center text-white">
                                    <span className="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
                                    Cancelled
                                  </span>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          : <span
                              className={`px-2 py-1 text-xs ${getStatusColorClass(project.status)}`}
                            >
                              {mapStatus(project.status)}
                            </span>
                          }
                        </div>
                        <div className="col-span-2 flex items-center">
                          {editingProject === project.id ?
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full justify-start text-left font-normal bg-transparent border border-white/20 text-white hover:bg-transparent hover:text-white rounded-none",
                                    !editFormData.deadline &&
                                      "text-muted-foreground"
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {editFormData.deadline ?
                                    format(
                                      new Date(editFormData.deadline),
                                      "PPP"
                                    )
                                  : <span>Pick a date</span>}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0 bg-black border-white/20"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={
                                    editFormData.deadline ?
                                      new Date(editFormData.deadline)
                                    : undefined
                                  }
                                  onSelect={(date) => {
                                    if (date) {
                                      setEditFormData((prev) => ({
                                        ...prev,
                                        deadline: format(date, "yyyy-MM-dd"),
                                      }));
                                    }
                                  }}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                          : <span className="text-gray-400">
                              {project.deadline || "No deadline"}
                            </span>
                          }
                        </div>
                        <div className="col-span-1 flex items-center">
                          <span className="text-gray-400">
                            {Math.round(project.completion_percentage)}%
                          </span>
                        </div>
                        <div className="col-span-1 flex gap-2">
                          {editingProject === project.id ?
                            <>
                              <Button
                                size="sm"
                                onClick={() => handleSaveEdit(project.id)}
                                className="bg-green-500 hover:bg-green-600 text-black px-2 py-1 text-xs rounded-none"
                              >
                                Save
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={handleCancelEdit}
                                className="border-white/20 text-white hover:bg-white hover:text-black px-2 py-1 text-xs rounded-none"
                              >
                                Cancel
                              </Button>
                            </>
                          : <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditProject(project.id)}
                              className="border-white/20 text-white hover:bg-white hover:text-black px-2 py-1 text-xs rounded-none"
                            >
                              Edit
                            </Button>
                          }
                        </div>
                      </div>
                      {index < filteredProjects.length - 1 && (
                        <div className="border-b border-white/20"></div>
                      )}
                    </div>
                  ))
                }
              </div>
            </>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={handleCancelDelete}>
        <DialogContent className="!rounded-none !border !border-white/20 max-w-md [&>button]:!rounded-none sm:!rounded-none">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Confirm Delete
            </DialogTitle>
            <p className="text-sm text-gray-500 mt-2">
              Are you sure you want to delete{" "}
              {selectedProjects.length === 1 ?
                "this project"
              : `these ${selectedProjects.length} projects`}
              ? This action cannot be undone.
            </p>
          </DialogHeader>

          <div className="flex justify-start space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={handleCancelDelete}
              className="rounded-none border border-white/20 hover:bg-white hover:text-black"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteProjects}
              className="rounded-none bg-red-500 hover:bg-red-600 text-white"
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </WorkspaceLayout>
  );
}
