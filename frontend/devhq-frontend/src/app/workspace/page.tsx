/** @format */

"use client";

import { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Users, Search, Trash, X } from "lucide-react";
import { workspaceApi } from "@/lib/api";
import { CustomCheckbox } from "@/components/ui/custom-checkbox";
import { CreateProjectModal } from "@/components/projects/create-project-modal";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function ProjectsPage() {
  const router = useRouter();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingWorkspace, setEditingWorkspace] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState({
    name: "",
    description: "",
  });
  const [activeFilter, setActiveFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedWorkspaces, setSelectedWorkspaces] = useState<string[]>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [workspaces, setWorkspaces] = useState<
    Array<{
      id: string;
      name: string;
      description: string;
      project_count: number;
      status: string;
      created_at: string;
      is_active: boolean;
      is_default: boolean;
    }>
  >([]);

  // Load workspaces from backend
  const loadWorkspaces = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await workspaceApi.getWorkspaces({
        search: searchTerm || undefined,
        status: activeFilter !== "All" ? activeFilter.toLowerCase() : undefined,
      });
      setWorkspaces(response.data);
    } catch (err: any) {
      console.error("Failed to load workspaces:", err);
      setError("Failed to load workspaces. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Load workspaces on component mount and when filters change
  useEffect(() => {
    loadWorkspaces();
  }, [activeFilter, searchTerm]);

  const handleCreateProject = async (projectData: any) => {
    try {
      console.log("Creating workspace:", projectData);
      const newWorkspace = await workspaceApi.createWorkspace({
        name: projectData.name,
        description: projectData.description,
      });

      // Refresh the workspace list
      await loadWorkspaces();
      setIsCreateModalOpen(false);
    } catch (err: any) {
      console.error("Failed to create workspace:", err);
      setError("Failed to create workspace. Please try again.");
    }
  };

  const handleEditWorkspace = (workspaceId: string) => {
    const workspace = workspaces.find((w) => w.id === workspaceId);
    if (workspace) {
      setEditingWorkspace(workspaceId);
      setEditFormData({
        name: workspace.name,
        description: workspace.description,
      });
    }
  };

  const handleSaveEdit = async (workspaceId: string) => {
    try {
      await workspaceApi.updateWorkspace(workspaceId, {
        name: editFormData.name,
        description: editFormData.description,
      });

      // Refresh the workspace list
      await loadWorkspaces();
      setEditingWorkspace(null);
      setEditFormData({ name: "", description: "" });
    } catch (err: any) {
      console.error("Failed to update workspace:", err);
      setError("Failed to update workspace. Please try again.");
    }
  };

  const handleCancelEdit = () => {
    setEditingWorkspace(null);
    setEditFormData({ name: "", description: "" });
  };

  const handleDeleteWorkspaces = async () => {
    try {
      // Delete each selected workspace
      for (const workspaceId of selectedWorkspaces) {
        await workspaceApi.deleteWorkspace(workspaceId);
      }

      // Refresh the workspace list
      await loadWorkspaces();
      setSelectedWorkspaces([]);
      setIsDeleteModalOpen(false);
    } catch (err: any) {
      console.error("Failed to delete workspaces:", err);
      setError("Failed to delete workspaces. Please try again.");
    }
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  // Filter and search logic
  const workspaceCounts = {
    All: workspaces.length,
    Active: workspaces.filter((w) => w.status === "Active").length,
    Inactive: workspaces.filter((w) => w.status === "Inactive").length,
  };

  const filteredWorkspaces = workspaces.filter((workspace) => {
    const matchesFilter =
      activeFilter === "All" || workspace.status === activeFilter;
    const matchesSearch =
      workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workspace.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <DashboardLayout>
      <div className="space-y-0">
        <div className="p-4 md:p-6 max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-semibold">Workspaces</h1>
            <div className="flex gap-2">
              {selectedWorkspaces.length === 0 && (
                <Button
                  variant="outline"
                  className="border-white/20 bg-transparent hover:bg-white hover:text-black text-white rounded-none py-2 px-4 text-sm"
                  onClick={() => setIsCreateModalOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Workspace
                </Button>
              )}
              {selectedWorkspaces.length > 0 && (
                <Button
                  variant="outline"
                  className="border-red-500/20 bg-red-500/10 hover:bg-red-500/20 text-red-400 rounded-none py-2 px-4 text-sm"
                  onClick={handleDeleteClick}
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete ({selectedWorkspaces.length})
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Full width border line */}
        <div className="border-b border-white/20 w-full mb-8"></div>

        {/* Filters and Search */}
        <div className="p-4 md:p-6 max-w-6xl mx-auto w-full">
          <div className="flex justify-between items-center mb-4">
            <div className="flex rounded-none overflow-hidden">
              <Button
                variant="ghost"
                className={`border border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === "All" ? "bg-green-500/20" : "bg-transparent hover:bg-white/10"}`}
                onClick={() => setActiveFilter("All")}
              >
                All ({workspaceCounts.All})
              </Button>
              <Button
                variant="ghost"
                className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === "Active" ? "bg-green-500/20" : "bg-transparent hover:bg-white/10"}`}
                onClick={() => setActiveFilter("Active")}
              >
                Active ({workspaceCounts.Active})
              </Button>
              <Button
                variant="ghost"
                className={`border-y border-r border-white/20 text-white px-3 py-1 text-xs rounded-none ${activeFilter === "Inactive" ? "bg-green-500/20" : "bg-transparent hover:bg-white/10"}`}
                onClick={() => setActiveFilter("Inactive")}
              >
                Inactive ({workspaceCounts.Inactive})
              </Button>
            </div>
            <div className="relative w-1/3">
              <input
                type="text"
                placeholder="Search workspaces..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full p-2 pl-10 bg-transparent border border-white/20 text-white placeholder-gray-500 focus:outline-none focus:ring-0 rounded-none"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
            </div>
          </div>

          {/* Workspace Headers */}
          <div className="mt-8">
            <div className="grid grid-cols-12 gap-2 border-b border-white/20 pb-2 items-center">
              <div className="col-span-1 flex items-center">
                <CustomCheckbox
                  checked={
                    selectedWorkspaces.length === filteredWorkspaces.length &&
                    filteredWorkspaces.length > 0
                  }
                  onChange={(checked) => {
                    if (checked) {
                      setSelectedWorkspaces(
                        filteredWorkspaces.map((w) => w.id)
                      );
                    } else {
                      setSelectedWorkspaces([]);
                    }
                  }}
                />
              </div>
              <div className="col-span-3 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                Workspace Name
              </div>
              <div className="col-span-2 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                Status
              </div>
              <div className="col-span-2 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                Created Date
              </div>
              <div className="col-span-3 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                Projects Count
              </div>
              <div className="col-span-1 font-mono text-xs text-white uppercase tracking-wider flex items-center">
                Actions
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-8">
                <div className="text-white">Loading workspaces...</div>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="bg-red-500/10 border border-red-500/20 text-red-400 p-4 rounded-none mb-4">
                {error}
                <button
                  onClick={loadWorkspaces}
                  className="ml-2 underline hover:no-underline"
                >
                  Try again
                </button>
              </div>
            )}

            {/* Workspace Data */}
            {!loading && !error && (
              <div className="space-y-0">
                {filteredWorkspaces.length === 0 ?
                  <div className="text-center py-8 text-gray-400">
                    {searchTerm || activeFilter !== "All" ?
                      "No workspaces match your filters."
                    : "No workspaces found. Create your first workspace to get started."
                    }
                  </div>
                : filteredWorkspaces.map((workspace, index) => (
                    <div key={workspace.id}>
                      <div className="grid grid-cols-12 gap-2 py-4 text-sm items-center">
                        <div className="col-span-1 flex items-center">
                          <CustomCheckbox
                            checked={selectedWorkspaces.includes(workspace.id)}
                            onChange={(checked) => {
                              if (checked) {
                                setSelectedWorkspaces([
                                  ...selectedWorkspaces,
                                  workspace.id,
                                ]);
                              } else {
                                setSelectedWorkspaces(
                                  selectedWorkspaces.filter(
                                    (id) => id !== workspace.id
                                  )
                                );
                              }
                            }}
                          />
                        </div>
                        <div className="col-span-3 font-medium text-white flex items-center">
                          {editingWorkspace === workspace.id ?
                            <input
                              type="text"
                              value={editFormData.name}
                              onChange={(e) =>
                                setEditFormData((prev) => ({
                                  ...prev,
                                  name: e.target.value,
                                }))
                              }
                              className="bg-transparent text-white text-sm rounded-none focus:outline-none blinking-underline"
                            />
                          : <span
                              className="cursor-pointer hover:underline hover:text-green-500"
                              onClick={() =>
                                router.push(
                                  workspace.id === "personal-projects" ?
                                    "/personal-projects"
                                  : `/workspace/${workspace.id}`
                                )
                              }
                            >
                              {workspace.name}
                            </span>
                          }
                        </div>
                        <div className="col-span-2 flex items-center">
                          <span
                            className={`px-2 py-1 text-xs ${workspace.status === "Active" ? "bg-green-500/20 text-green-300" : "bg-red-500/20 text-red-300"}`}
                          >
                            {workspace.status}
                          </span>
                        </div>
                        <div className="col-span-2 text-gray-400 flex items-center">
                          {workspace.created_at ?
                            new Date(workspace.created_at).toLocaleDateString()
                          : "N/A"}
                        </div>
                        <div className="col-span-3 text-gray-400 flex items-center">
                          {workspace.project_count} projects
                        </div>
                        <div className="col-span-1 flex gap-2">
                          {editingWorkspace === workspace.id ?
                            <>
                              <Button
                                size="sm"
                                onClick={() => handleSaveEdit(workspace.id)}
                                className="bg-green-500 hover:bg-green-600 text-black px-2 py-1 text-xs rounded-none"
                              >
                                Save
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={handleCancelEdit}
                                className="border-white/20 text-white hover:bg-white hover:text-black px-2 py-1 text-xs rounded-none"
                              >
                                Cancel
                              </Button>
                            </>
                          : <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditWorkspace(workspace.id)}
                              className="border-white/20 text-white hover:bg-white hover:text-black px-2 py-1 text-xs rounded-none"
                            >
                              Edit
                            </Button>
                          }
                        </div>
                      </div>
                      {index < filteredWorkspaces.length - 1 && (
                        <div className="border-b border-white/20"></div>
                      )}
                    </div>
                  ))
                }
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={handleCancelDelete}>
        <DialogContent className="!rounded-none !border !border-white/20 max-w-md [&>button]:!rounded-none sm:!rounded-none">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Confirm Delete
            </DialogTitle>
            <p className="text-sm text-gray-500 mt-2">
              Are you sure you want to delete{" "}
              {selectedWorkspaces.length === 1 ?
                "this workspace"
              : `these ${selectedWorkspaces.length} workspaces`}
              ? This action cannot be undone.
            </p>
          </DialogHeader>

          <div className="flex justify-start space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={handleCancelDelete}
              className="rounded-none border border-white/20 hover:bg-white hover:text-black"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteWorkspaces}
              className="rounded-none bg-red-500 hover:bg-red-600 text-white"
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateProject}
      />
    </DashboardLayout>
  );
}
