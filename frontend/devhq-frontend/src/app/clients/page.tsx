/** @format */

"use client";

import React, { useState } from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  Users,
  Plus,
  Search,
  Mail,
  Phone,
  Building,
  Star,
  DollarSign,
  Calendar,
  MessageSquare,
  Edit,
  Eye,
} from "lucide-react";

export default function ClientsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock client data
  const clients = [
    {
      id: "1",
      name: "TechCorp Inc.",
      contactPerson: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      company: "TechCorp Inc.",
      status: "active",
      totalRevenue: 25000,
      activeProjects: 3,
      totalProjects: 8,
      lastContact: "2 days ago",
      rating: 5,
      joinedDate: "2023-01-15",
    },
    {
      id: "2",
      name: "Startup<PERSON><PERSON><PERSON>",
      contactPerson: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      company: "StartupXYZ",
      status: "active",
      totalRevenue: 18500,
      activeProjects: 2,
      totalProjects: 5,
      lastContact: "1 week ago",
      rating: 4,
      joinedDate: "2023-03-22",
    },
    {
      id: "3",
      name: "Creative Agency",
      contactPerson: "Emma Wilson",
      email: "<EMAIL>",
      phone: "+****************",
      company: "Creative Agency",
      status: "active",
      totalRevenue: 12000,
      activeProjects: 1,
      totalProjects: 3,
      lastContact: "3 days ago",
      rating: 5,
      joinedDate: "2023-06-10",
    },
    {
      id: "4",
      name: "Local Business",
      contactPerson: "John Smith",
      email: "<EMAIL>",
      phone: "+****************",
      company: "Local Business",
      status: "inactive",
      totalRevenue: 8500,
      activeProjects: 0,
      totalProjects: 2,
      lastContact: "2 months ago",
      rating: 3,
      joinedDate: "2023-08-05",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "border-green-400/30 bg-green-400/10 text-green-400";
      case "inactive":
        return "border-gray-400/30 bg-gray-400/10 text-gray-400";
      default:
        return "border-gray-400/30 bg-gray-400/10 text-gray-400";
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-600"
        }`}
      />
    ));
  };

  const filteredClients = clients.filter((client) => {
    const matchesSearch =
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || client.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalRevenue = clients.reduce((sum, client) => sum + client.totalRevenue, 0);
  const activeClients = clients.filter((client) => client.status === "active").length;
  const totalProjects = clients.reduce((sum, client) => sum + client.totalProjects, 0);

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            CLIENT MANAGEMENT
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Client Relationships
              </h2>
              <p className="text-white/60 mt-1">
                Manage your client relationships and project history.
              </p>
            </div>
            <button className="border border-green-400/30 bg-green-400/10 px-4 py-2 text-green-400 hover:bg-green-400/20 flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Client
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-blue-400/10 text-blue-400">
                <Users className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">{activeClients}</div>
                <div className="text-sm text-white/60">Active Clients</div>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-green-400/10 text-green-400">
                <DollarSign className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">
                  ${totalRevenue.toLocaleString()}
                </div>
                <div className="text-sm text-white/60">Total Revenue</div>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-purple-400/10 text-purple-400">
                <Building className="h-5 w-5" />
              </div>
              <div>
                <div className="text-2xl font-bold text-white">{totalProjects}</div>
                <div className="text-sm text-white/60">Total Projects</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="flex gap-2">
            <button
              onClick={() => setStatusFilter("all")}
              className={`border px-3 py-1 text-xs ${
                statusFilter === "all"
                  ? "border-green-400 bg-green-400/10 text-green-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              All ({clients.length})
            </button>
            <button
              onClick={() => setStatusFilter("active")}
              className={`border px-3 py-1 text-xs ${
                statusFilter === "active"
                  ? "border-green-400 bg-green-400/10 text-green-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              Active ({clients.filter((c) => c.status === "active").length})
            </button>
            <button
              onClick={() => setStatusFilter("inactive")}
              className={`border px-3 py-1 text-xs ${
                statusFilter === "inactive"
                  ? "border-gray-400 bg-gray-400/10 text-gray-400"
                  : "border-white/20 text-white/60 hover:text-white"
              }`}
            >
              Inactive ({clients.filter((c) => c.status === "inactive").length})
            </button>
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder="Search clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 p-2 pl-10 bg-transparent border border-white/20 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-white/40" />
          </div>
        </div>

        {/* Clients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.map((client) => (
            <div
              key={client.id}
              className="border border-white/20 bg-transparent p-6 hover:bg-white/5 transition-colors"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-400/10 text-blue-400 flex items-center justify-center font-bold text-lg">
                    {client.name.charAt(0)}
                  </div>
                  <div>
                    <h3 className="text-white font-medium">{client.name}</h3>
                    <p className="text-white/60 text-sm">{client.contactPerson}</p>
                  </div>
                </div>
                <div
                  className={`px-2 py-1 text-xs border ${getStatusColor(client.status)}`}
                >
                  {client.status.toUpperCase()}
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex items-center gap-2 text-white/60 text-sm">
                  <Mail className="h-4 w-4" />
                  {client.email}
                </div>
                <div className="flex items-center gap-2 text-white/60 text-sm">
                  <Phone className="h-4 w-4" />
                  {client.phone}
                </div>
                <div className="flex items-center gap-2 text-white/60 text-sm">
                  <Calendar className="h-4 w-4" />
                  Last contact: {client.lastContact}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="text-white font-medium">
                    ${client.totalRevenue.toLocaleString()}
                  </div>
                  <div className="text-white/60 text-xs">Total Revenue</div>
                </div>
                <div>
                  <div className="text-white font-medium">
                    {client.activeProjects}/{client.totalProjects}
                  </div>
                  <div className="text-white/60 text-xs">Active/Total Projects</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  {renderStars(client.rating)}
                </div>
                <div className="flex items-center gap-2">
                  <button className="border border-white/20 bg-transparent px-2 py-1 text-white/60 hover:text-white">
                    <MessageSquare className="h-4 w-4" />
                  </button>
                  <button className="border border-white/20 bg-transparent px-2 py-1 text-white/60 hover:text-white">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="border border-blue-400/30 bg-blue-400/10 px-2 py-1 text-blue-400 hover:bg-blue-400/20">
                    <Eye className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}
