/** @format */

"use client";

import React from "react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Clock,
  Target,
  Users,
  Calendar,
  ArrowUp,
  ArrowDown,
} from "lucide-react";

export default function AnalyticsPage() {
  // Mock analytics data
  const metrics = [
    {
      title: "Total Revenue",
      value: "$45,230",
      change: "+12.5%",
      trend: "up",
      icon: <DollarSign className="h-6 w-6" />,
      color: "text-green-400",
      bgColor: "bg-green-400/10",
    },
    {
      title: "Hours Tracked",
      value: "1,247",
      change: "+8.2%",
      trend: "up",
      icon: <Clock className="h-6 w-6" />,
      color: "text-blue-400",
      bgColor: "bg-blue-400/10",
    },
    {
      title: "Active Projects",
      value: "23",
      change: "+3",
      trend: "up",
      icon: <Target className="h-6 w-6" />,
      color: "text-purple-400",
      bgColor: "bg-purple-400/10",
    },
    {
      title: "Client Satisfaction",
      value: "94%",
      change: "-2.1%",
      trend: "down",
      icon: <Users className="h-6 w-6" />,
      color: "text-orange-400",
      bgColor: "bg-orange-400/10",
    },
  ];

  const recentProjects = [
    {
      name: "E-commerce Platform",
      client: "TechCorp Inc.",
      revenue: "$8,500",
      hours: "142h",
      status: "In Progress",
      completion: 75,
    },
    {
      name: "Mobile App Design",
      client: "StartupXYZ",
      revenue: "$6,200",
      hours: "98h",
      status: "Completed",
      completion: 100,
    },
    {
      name: "Brand Identity",
      client: "Creative Agency",
      revenue: "$4,800",
      hours: "76h",
      status: "In Progress",
      completion: 60,
    },
    {
      name: "Website Redesign",
      client: "Local Business",
      revenue: "$3,200",
      hours: "54h",
      status: "Completed",
      completion: 100,
    },
  ];

  const monthlyData = [
    { month: "Jan", revenue: 12000, hours: 180 },
    { month: "Feb", revenue: 15000, hours: 220 },
    { month: "Mar", revenue: 18000, hours: 260 },
    { month: "Apr", revenue: 22000, hours: 310 },
    { month: "May", revenue: 25000, hours: 340 },
    { month: "Jun", revenue: 28000, hours: 380 },
  ];

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 pt-8 md:pt-12 space-y-8 max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="space-y-4">
          <h1 className="text-xs font-mono text-white/40 uppercase tracking-wider">
            ANALYTICS DASHBOARD
          </h1>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-white">
                Performance Analytics
              </h2>
              <p className="text-white/60 mt-1">
                Track your business performance and growth metrics.
              </p>
            </div>
            <div className="flex items-center gap-2 text-white/60">
              <Calendar className="h-4 w-4" />
              <span className="text-sm">Last 30 days</span>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {metrics.map((metric, index) => (
            <div
              key={index}
              className="border border-white/20 bg-transparent p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`p-2 ${metric.bgColor} ${metric.color}`}>
                  {metric.icon}
                </div>
                <div className="flex items-center gap-1">
                  {metric.trend === "up" ? (
                    <ArrowUp className="h-4 w-4 text-green-400" />
                  ) : (
                    <ArrowDown className="h-4 w-4 text-red-400" />
                  )}
                  <span
                    className={`text-sm ${
                      metric.trend === "up" ? "text-green-400" : "text-red-400"
                    }`}
                  >
                    {metric.change}
                  </span>
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white mb-1">
                  {metric.value}
                </div>
                <div className="text-sm text-white/60">{metric.title}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Revenue Chart */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center gap-2 mb-6">
            <BarChart3 className="h-5 w-5 text-blue-400" />
            <h3 className="text-lg font-medium text-white">Revenue Trend</h3>
          </div>
          <div className="space-y-4">
            {monthlyData.map((data, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-12 text-white/60 text-sm">{data.month}</div>
                <div className="flex-1 flex items-center gap-4">
                  <div className="flex-1 bg-white/10 h-8 relative">
                    <div
                      className="bg-green-400 h-full"
                      style={{
                        width: `${(data.revenue / 30000) * 100}%`,
                      }}
                    />
                  </div>
                  <div className="w-20 text-white text-sm font-medium">
                    ${data.revenue.toLocaleString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Project Performance */}
        <div className="border border-white/20 bg-transparent p-6">
          <div className="flex items-center gap-2 mb-6">
            <Target className="h-5 w-5 text-purple-400" />
            <h3 className="text-lg font-medium text-white">
              Project Performance
            </h3>
          </div>
          <div className="space-y-4">
            {recentProjects.map((project, index) => (
              <div
                key={index}
                className="border border-white/10 bg-transparent p-4"
              >
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="text-white font-medium">{project.name}</h4>
                    <p className="text-white/60 text-sm">{project.client}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-medium">
                      {project.revenue}
                    </div>
                    <div className="text-white/60 text-sm">{project.hours}</div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-full bg-white/10 h-2 max-w-32">
                      <div
                        className={`h-full ${
                          project.status === "Completed"
                            ? "bg-green-400"
                            : "bg-blue-400"
                        }`}
                        style={{ width: `${project.completion}%` }}
                      />
                    </div>
                    <span className="text-white/60 text-sm">
                      {project.completion}%
                    </span>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs ${
                      project.status === "Completed"
                        ? "bg-green-400/10 text-green-400"
                        : "bg-blue-400/10 text-blue-400"
                    }`}
                  >
                    {project.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <TrendingUp className="h-5 w-5 text-green-400" />
              <h3 className="text-lg font-medium text-white">Growth Insights</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-white/60">Monthly Growth</span>
                <span className="text-green-400">+15.2%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Best Performing Month</span>
                <span className="text-white">June 2024</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Average Project Value</span>
                <span className="text-white">$5,675</span>
              </div>
            </div>
          </div>

          <div className="border border-white/20 bg-transparent p-6">
            <div className="flex items-center gap-2 mb-4">
              <Users className="h-5 w-5 text-orange-400" />
              <h3 className="text-lg font-medium text-white">Client Metrics</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-white/60">Total Clients</span>
                <span className="text-white">18</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Repeat Clients</span>
                <span className="text-green-400">12 (67%)</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Average Rating</span>
                <span className="text-white">4.8/5.0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
