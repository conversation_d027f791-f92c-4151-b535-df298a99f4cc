'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Search, 
  Filter, 
  SortAsc, 
  SortDesc,
  TrendingUp, 
  TrendingDown, 
  Calendar,
  DollarSign,
  Tag,
  Receipt,
  FileText,
  Download,
  Trash2,
  Edit,
  Plus,
  CheckCircle,
  AlertCircle,
  X,
  MoreHorizontal,
  Link,
  Zap
} from 'lucide-react'

interface Transaction {
  id: string
  type: 'income' | 'expense'
  amount: number
  description: string
  category: string
  date: string
  isDeductible?: boolean
  receiptUrl?: string
  receiptId?: string
  invoiceId?: string
  walletId?: string
  tags?: string[]
  status: 'pending' | 'completed' | 'reconciled'
  notes?: string
}

interface TransactionManagementProps {
  transactions: Transaction[]
  onAddTransaction: (transaction: Omit<Transaction, 'id'>) => void
  onUpdateTransaction: (id: string, transaction: Partial<Transaction>) => void
  onDeleteTransaction: (id: string) => void
  onBulkUpdate: (ids: string[], updates: Partial<Transaction>) => void
  receipts?: any[]
  wallets?: any[]
}

export function TransactionManagement({ 
  transactions, 
  onAddTransaction, 
  onUpdateTransaction, 
  onDeleteTransaction,
  onBulkUpdate,
  receipts = [],
  wallets = []
}: TransactionManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense'>('all')
  const [filterCategory, setFilterCategory] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterDateRange, setFilterDateRange] = useState('all')
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'description'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [isCreateFromReceiptOpen, setIsCreateFromReceiptOpen] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getTransactionIcon = (type: string) => {
    return type === 'income' ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />
  }

  const getTransactionColor = (type: string) => {
    return type === 'income' 
      ? 'text-neon-success' 
      : 'text-neon-error'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-neon-success/20 text-neon-success border-neon-success/40'
      case 'pending': return 'bg-neon-warning/20 text-neon-warning border-neon-warning/40'
      case 'reconciled': return 'bg-neon-info/20 text-neon-info border-neon-info/40'
      default: return 'bg-neon-primary/20 text-neon-primary border-neon-primary/40'
    }
  }

  const categories = [
    'Client Payment', 'Consulting', 'Software', 'Office Expenses', 
    'Travel', 'Meals & Entertainment', 'Professional Services', 
    'Marketing', 'Equipment', 'Utilities', 'Other'
  ]

  const filteredAndSortedTransactions = useMemo(() => {
    let filtered = transactions.filter(transaction => {
      const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           transaction.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           transaction.notes?.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesType = filterType === 'all' || transaction.type === filterType
      const matchesCategory = filterCategory === 'all' || transaction.category === filterCategory
      const matchesStatus = filterStatus === 'all' || transaction.status === filterStatus
      
      let matchesDate = true
      if (filterDateRange !== 'all') {
        const transactionDate = new Date(transaction.date)
        const now = new Date()
        
        switch (filterDateRange) {
          case 'today':
            matchesDate = transactionDate.toDateString() === now.toDateString()
            break
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            matchesDate = transactionDate >= weekAgo
            break
          case 'month':
            matchesDate = transactionDate.getMonth() === now.getMonth() && 
                         transactionDate.getFullYear() === now.getFullYear()
            break
          case 'quarter':
            const quarter = Math.floor(now.getMonth() / 3)
            const transactionQuarter = Math.floor(transactionDate.getMonth() / 3)
            matchesDate = transactionQuarter === quarter && 
                         transactionDate.getFullYear() === now.getFullYear()
            break
        }
      }
      
      return matchesSearch && matchesType && matchesCategory && matchesStatus && matchesDate
    })

    // Sort transactions
    filtered.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime()
          break
        case 'amount':
          comparison = a.amount - b.amount
          break
        case 'description':
          comparison = a.description.localeCompare(b.description)
          break
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })

    return filtered
  }, [transactions, searchTerm, filterType, filterCategory, filterStatus, filterDateRange, sortBy, sortOrder])

  const handleSelectTransaction = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedTransactions([...selectedTransactions, id])
    } else {
      setSelectedTransactions(selectedTransactions.filter(tid => tid !== id))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTransactions(filteredAndSortedTransactions.map(t => t.id))
    } else {
      setSelectedTransactions([])
    }
  }

  const handleBulkAction = (action: string, value?: any) => {
    if (selectedTransactions.length === 0) return

    switch (action) {
      case 'delete':
        selectedTransactions.forEach(id => onDeleteTransaction(id))
        break
      case 'status':
        onBulkUpdate(selectedTransactions, { status: value })
        break
      case 'category':
        onBulkUpdate(selectedTransactions, { category: value })
        break
      case 'deductible':
        onBulkUpdate(selectedTransactions, { isDeductible: value })
        break
    }
    
    setSelectedTransactions([])
    setShowBulkActions(false)
  }

  const unprocessedReceipts = receipts.filter(r => !r.isProcessed && !transactions.some(t => t.receiptId === r.id))

  const createTransactionFromReceipt = (receipt: any) => {
    const transaction: Omit<Transaction, 'id'> = {
      type: 'expense',
      amount: receipt.extractedData?.amount || 0,
      description: receipt.extractedData?.vendor || receipt.fileName,
      category: receipt.category || 'Other',
      date: receipt.extractedData?.date || receipt.uploadDate.split('T')[0],
      isDeductible: true,
      receiptId: receipt.id,
      receiptUrl: receipt.fileUrl,
      status: 'completed',
      notes: `Auto-created from receipt: ${receipt.fileName}`
    }
    
    onAddTransaction(transaction)
    setIsCreateFromReceiptOpen(false)
  }

  return (
    <div className="space-y-6">
      {/* Transaction Management Header */}
      <Card className="bg-black/40 backdrop-blur-md border border-neon-electric/30 hover:border-neon-electric/50 transition-all">
        <CardHeader className="border-b border-neon-electric/20">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-neon-electric font-mono tracking-wider uppercase">
                TRANSACTION MANAGEMENT SYSTEM
              </CardTitle>
              <p className="text-foreground/60 font-mono text-xs mt-1">
                Advanced filtering, bulk operations, and AI-powered categorization
              </p>
            </div>
            <div className="flex space-x-2">
              {unprocessedReceipts.length > 0 && (
                <Dialog open={isCreateFromReceiptOpen} onOpenChange={setIsCreateFromReceiptOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="border-neon-info/30 text-neon-info hover:bg-neon-info/10 font-mono">
                      <Link className="h-4 w-4 mr-2" />
                      FROM RECEIPT ({unprocessedReceipts.length})
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="bg-black/90 backdrop-blur-md border border-neon-info/30">
                    <DialogHeader>
                      <DialogTitle className="text-neon-info font-mono tracking-wider uppercase">
                        CREATE FROM RECEIPT
                      </DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {unprocessedReceipts.map((receipt) => (
                        <Card key={receipt.id} className="border-border/50 bg-card/50 hover:border-neon-info/30 transition-all">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <Receipt className="h-8 w-8 text-neon-info" />
                                <div>
                                  <h4 className="font-mono text-sm text-foreground">{receipt.fileName}</h4>
                                  <p className="font-mono text-xs text-foreground/60">
                                    {receipt.extractedData?.vendor} • {formatCurrency(receipt.extractedData?.amount || 0)}
                                  </p>
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => createTransactionFromReceipt(receipt)}
                                className="border-neon-info/30 text-neon-info hover:bg-neon-info/10 font-mono"
                              >
                                <Plus className="h-4 w-4 mr-1" />
                                CREATE
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Advanced Filters */}
      <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all">
        <CardHeader>
          <CardTitle className="text-neon-electric font-mono tracking-wider uppercase text-sm">
            ADVANCED FILTERING SYSTEM
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Quick Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neon-primary" />
              <Input
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground"
              />
            </div>
            
            <Select value={filterType} onValueChange={(value: any) => setFilterType(value)}>
              <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
                <SelectValue placeholder="Transaction Type" />
              </SelectTrigger>
              <SelectContent className="bg-black/90 border-neon-primary/30">
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="income">Income Only</SelectItem>
                <SelectItem value="expense">Expenses Only</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent className="bg-black/90 border-neon-primary/30">
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterDateRange} onValueChange={setFilterDateRange}>
              <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent className="bg-black/90 border-neon-primary/30">
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Sort and Status Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex space-x-2">
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent className="bg-black/90 border-neon-primary/30">
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="amount">Amount</SelectItem>
                  <SelectItem value="description">Description</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="border-neon-primary/30 text-neon-primary hover:bg-neon-primary/10"
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-black/90 border-neon-primary/30">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="reconciled">Reconciled</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm('')
                  setFilterType('all')
                  setFilterCategory('all')
                  setFilterStatus('all')
                  setFilterDateRange('all')
                }}
                className="border-neon-warning/30 text-neon-warning hover:bg-neon-warning/10 font-mono"
              >
                <X className="h-4 w-4 mr-1" />
                CLEAR
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-neon-info/30 text-neon-info hover:bg-neon-info/10 font-mono"
              >
                <Download className="h-4 w-4 mr-1" />
                EXPORT
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions Bar */}
      {selectedTransactions.length > 0 && (
        <Card className="bg-gradient-to-r from-neon-warning/10 to-neon-electric/10 border-neon-warning/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-neon-warning" />
                <span className="text-neon-warning font-mono text-sm">
                  {selectedTransactions.length} TRANSACTIONS SELECTED
                </span>
              </div>
              <div className="flex space-x-2">
                <Select onValueChange={(value) => handleBulkAction('status', value)}>
                  <SelectTrigger className="w-40 bg-black/20 border-neon-warning/30 text-foreground">
                    <SelectValue placeholder="Change Status" />
                  </SelectTrigger>
                  <SelectContent className="bg-black/90 border-neon-warning/30">
                    <SelectItem value="pending">Mark Pending</SelectItem>
                    <SelectItem value="completed">Mark Completed</SelectItem>
                    <SelectItem value="reconciled">Mark Reconciled</SelectItem>
                  </SelectContent>
                </Select>
                <Select onValueChange={(value) => handleBulkAction('category', value)}>
                  <SelectTrigger className="w-40 bg-black/20 border-neon-warning/30 text-foreground">
                    <SelectValue placeholder="Change Category" />
                  </SelectTrigger>
                  <SelectContent className="bg-black/90 border-neon-warning/30">
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('deductible', true)}
                  className="border-neon-success/30 text-neon-success hover:bg-neon-success/10 font-mono"
                >
                  <Tag className="h-4 w-4 mr-1" />
                  TAX DEDUCTIBLE
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('delete')}
                  className="border-neon-error/30 text-neon-error hover:bg-neon-error/10 font-mono"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  DELETE
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transaction Table */}
      <Card className="border-border/50 bg-card/50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-neon-electric font-mono tracking-wider uppercase text-sm">
              TRANSACTION DATA TABLE
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={selectedTransactions.length === filteredAndSortedTransactions.length && filteredAndSortedTransactions.length > 0}
                onCheckedChange={handleSelectAll}
                className="border-neon-primary/30"
              />
              <span className="text-foreground/60 font-mono text-xs">
                {filteredAndSortedTransactions.length} transactions
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neon-primary/5 border-b border-neon-primary/20">
                <tr>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">
                    <Checkbox
                      checked={selectedTransactions.length === filteredAndSortedTransactions.length && filteredAndSortedTransactions.length > 0}
                      onCheckedChange={handleSelectAll}
                      className="border-neon-primary/30"
                    />
                  </th>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">TYPE</th>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">DESCRIPTION</th>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">CATEGORY</th>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">AMOUNT</th>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">DATE</th>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">STATUS</th>
                  <th className="text-left p-4 text-neon-electric font-mono text-xs tracking-wider uppercase">ACTIONS</th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedTransactions.map((transaction) => (
                  <tr 
                    key={transaction.id}
                    className="border-b border-border/30 hover:bg-neon-primary/5 hover:border-neon-primary/30 transition-all"
                  >
                    <td className="p-4">
                      <Checkbox
                        checked={selectedTransactions.includes(transaction.id)}
                        onCheckedChange={(checked) => handleSelectTransaction(transaction.id, checked as boolean)}
                        className="border-neon-primary/30"
                      />
                    </td>
                    <td className="p-4">
                      <div className={`flex items-center space-x-2 ${getTransactionColor(transaction.type)}`}>
                        {getTransactionIcon(transaction.type)}
                        <span className="font-mono text-xs uppercase">{transaction.type}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <div className="font-mono text-sm text-foreground">{transaction.description}</div>
                        {transaction.notes && (
                          <div className="font-mono text-xs text-foreground/60">{transaction.notes}</div>
                        )}
                        <div className="flex items-center space-x-2 mt-1">
                          {transaction.receiptId && (
                            <Badge className="bg-neon-info/20 text-neon-info border-neon-info/40">
                              <Receipt className="h-3 w-3 mr-1" />
                              RECEIPT
                            </Badge>
                          )}
                          {transaction.isDeductible && (
                            <Badge className="bg-neon-success/20 text-neon-success border-neon-success/40">
                              <Tag className="h-3 w-3 mr-1" />
                              TAX
                            </Badge>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge className="bg-neon-primary/20 text-neon-primary border-neon-primary/40">
                        {transaction.category}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <span className={`font-mono text-sm font-bold ${getTransactionColor(transaction.type)}`}>
                        {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-foreground/60" />
                        <span className="font-mono text-xs text-foreground/80">
                          {new Date(transaction.date).toLocaleDateString()}
                        </span>
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge className={getStatusColor(transaction.status)}>
                        {transaction.status.toUpperCase()}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-foreground/60 hover:text-neon-warning"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteTransaction(transaction.id)}
                          className="h-8 w-8 p-0 text-foreground/60 hover:text-neon-error"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-foreground/60 hover:text-neon-info"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Empty State */}
      {filteredAndSortedTransactions.length === 0 && (
        <Card className="border-border/50 bg-card/50">
          <CardContent className="p-12 text-center">
            <FileText className="h-16 w-16 text-neon-primary/50 mx-auto mb-4" />
            <h3 className="text-foreground font-mono text-lg mb-2">NO TRANSACTIONS FOUND</h3>
            <p className="text-foreground/60 font-mono text-sm">
              {searchTerm || filterType !== 'all' || filterCategory !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Create your first transaction to get started'
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}