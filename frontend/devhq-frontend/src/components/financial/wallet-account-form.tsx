/** @format */

"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Wallet,
  Settings,
  Edit,
  Trash2,
  Eye,
  EyeOff,
} from "lucide-react";

interface WalletAccount {
  id: string;
  name: string;
  balance: number;
  type: "checking" | "savings" | "tax" | "investment" | "crypto";
  currency: string;
  isDefault: boolean;
  isVisible: boolean;
  description?: string;
  bankName?: string;
  accountNumber?: string;
}

interface WalletAccountFormProps {
  wallets: WalletAccount[];
  onAddWallet: (wallet: Omit<WalletAccount, "id">) => void;
  onUpdateWallet: (id: string, wallet: Partial<WalletAccount>) => void;
  onDeleteWallet: (id: string) => void;
}

export function WalletAccountForm({
  wallets,
  onAddWallet,
  onUpdateWallet,
  onDeleteWallet,
}: WalletAccountFormProps) {
  const [isAddWalletOpen, setIsAddWalletOpen] = useState(false);
  const [editingWallet, setEditingWallet] = useState<string | null>(null);
  const [newWallet, setNewWallet] = useState({
    name: "",
    balance: "",
    type: "checking" as WalletAccount["type"],
    currency: "USD",
    isDefault: false,
    isVisible: true,
    description: "",
    bankName: "",
    accountNumber: "",
  });

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const getWalletTypeColor = (type: WalletAccount["type"]) => {
    switch (type) {
      case "checking":
        return "bg-neon-primary/20 text-neon-primary border-neon-primary/40";
      case "savings":
        return "bg-neon-success/20 text-neon-success border-neon-success/40";
      case "tax":
        return "bg-neon-warning/20 text-neon-warning border-neon-warning/40";
      case "investment":
        return "bg-neon-info/20 text-neon-info border-neon-info/40";
      case "crypto":
        return "bg-neon-electric/20 text-neon-electric border-neon-electric/40";
      default:
        return "bg-neon-primary/20 text-neon-primary border-neon-primary/40";
    }
  };

  const getWalletTypeIcon = (type: WalletAccount["type"]) => {
    return <Wallet className="h-4 w-4" />;
  };

  const handleAddWallet = () => {
    if (!newWallet.name || !newWallet.balance) return;

    const wallet: Omit<WalletAccount, "id"> = {
      name: newWallet.name,
      balance: parseFloat(newWallet.balance),
      type: newWallet.type,
      currency: newWallet.currency,
      isDefault: newWallet.isDefault,
      isVisible: newWallet.isVisible,
      description: newWallet.description || undefined,
      bankName: newWallet.bankName || undefined,
      accountNumber: newWallet.accountNumber || undefined,
    };

    onAddWallet(wallet);
    setNewWallet({
      name: "",
      balance: "",
      type: "checking",
      currency: "USD",
      isDefault: false,
      isVisible: true,
      description: "",
      bankName: "",
      accountNumber: "",
    });
    setIsAddWalletOpen(false);
  };

  const handleToggleVisibility = (
    walletId: string,
    currentVisibility: boolean
  ) => {
    onUpdateWallet(walletId, { isVisible: !currentVisibility });
  };

  const handleSetDefault = (walletId: string) => {
    // First, remove default from all wallets
    wallets.forEach((wallet) => {
      if (wallet.isDefault) {
        onUpdateWallet(wallet.id, { isDefault: false });
      }
    });
    // Then set the selected wallet as default
    onUpdateWallet(walletId, { isDefault: true });
  };

  return (
    <div className="space-y-6">
      {/* Wallet Configuration Header */}
      <Card className="bg-black/40 backdrop-blur-md border border-neon-electric/30 hover:border-neon-electric/50 transition-all">
        <CardHeader className="border-b border-neon-electric/20">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-neon-electric font-mono tracking-wider uppercase">
                WALLET CONFIGURATION
              </CardTitle>
              <p className="text-foreground/60 font-mono text-xs mt-1">
                Manage your financial accounts and balances
              </p>
            </div>
            <Dialog open={isAddWalletOpen} onOpenChange={setIsAddWalletOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="border-neon-electric/30 text-neon-electric hover:bg-neon-electric/10 font-mono"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  ADD WALLET
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-black/90 backdrop-blur-md border border-neon-electric/30">
                <DialogHeader>
                  <DialogTitle className="text-neon-electric font-mono tracking-wider uppercase">
                    CREATE NEW WALLET
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="relative">
                      <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                        WALLET NAME
                      </Label>
                      <Input
                        className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground placeholder:text-foreground/40 mt-1"
                        placeholder="Enter wallet name"
                        value={newWallet.name}
                        onChange={(e) =>
                          setNewWallet({ ...newWallet, name: e.target.value })
                        }
                      />
                      <div className="absolute inset-0 bg-neon-primary/5 rounded-md opacity-0 focus-within:opacity-100 transition-opacity pointer-events-none" />
                    </div>
                    <div className="relative">
                      <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                        INITIAL BALANCE
                      </Label>
                      <Input
                        type="number"
                        className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground placeholder:text-foreground/40 mt-1"
                        placeholder="0.00"
                        value={newWallet.balance}
                        onChange={(e) =>
                          setNewWallet({
                            ...newWallet,
                            balance: e.target.value,
                          })
                        }
                      />
                      <div className="absolute inset-0 bg-neon-primary/5 rounded-md opacity-0 focus-within:opacity-100 transition-opacity pointer-events-none" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                        ACCOUNT TYPE
                      </Label>
                      <Select
                        value={newWallet.type}
                        onValueChange={(value) =>
                          setNewWallet({
                            ...newWallet,
                            type: value as WalletAccount["type"],
                          })
                        }
                      >
                        <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-black/90 border-neon-primary/30">
                          <SelectItem value="checking">
                            Checking Account
                          </SelectItem>
                          <SelectItem value="savings">
                            Savings Account
                          </SelectItem>
                          <SelectItem value="tax">Tax Savings</SelectItem>
                          <SelectItem value="investment">
                            Investment Account
                          </SelectItem>
                          <SelectItem value="crypto">Crypto Wallet</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                        CURRENCY
                      </Label>
                      <Select
                        value={newWallet.currency}
                        onValueChange={(value) =>
                          setNewWallet({ ...newWallet, currency: value })
                        }
                      >
                        <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-black/90 border-neon-primary/30">
                          <SelectItem value="USD">USD - US Dollar</SelectItem>
                          <SelectItem value="EUR">EUR - Euro</SelectItem>
                          <SelectItem value="GBP">
                            GBP - British Pound
                          </SelectItem>
                          <SelectItem value="KES">
                            KES - Kenyan Shilling
                          </SelectItem>
                          <SelectItem value="BTC">BTC - Bitcoin</SelectItem>
                          <SelectItem value="ETH">ETH - Ethereum</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                      DESCRIPTION (OPTIONAL)
                    </Label>
                    <Input
                      className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground placeholder:text-foreground/40 mt-1"
                      placeholder="Brief description of this wallet"
                      value={newWallet.description}
                      onChange={(e) =>
                        setNewWallet({
                          ...newWallet,
                          description: e.target.value,
                        })
                      }
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                        BANK NAME (OPTIONAL)
                      </Label>
                      <Input
                        className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground placeholder:text-foreground/40 mt-1"
                        placeholder="Bank or institution name"
                        value={newWallet.bankName}
                        onChange={(e) =>
                          setNewWallet({
                            ...newWallet,
                            bankName: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                        ACCOUNT NUMBER (OPTIONAL)
                      </Label>
                      <Input
                        className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground placeholder:text-foreground/40 mt-1"
                        placeholder="Last 4 digits only"
                        value={newWallet.accountNumber}
                        onChange={(e) =>
                          setNewWallet({
                            ...newWallet,
                            accountNumber: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="isDefault"
                        checked={newWallet.isDefault}
                        onChange={(e) =>
                          setNewWallet({
                            ...newWallet,
                            isDefault: e.target.checked,
                          })
                        }
                        className="rounded border-neon-primary/30 bg-black/20 text-neon-electric focus:ring-neon-electric"
                      />
                      <Label
                        htmlFor="isDefault"
                        className="text-foreground font-mono text-xs"
                      >
                        Set as default wallet
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="isVisible"
                        checked={newWallet.isVisible}
                        onChange={(e) =>
                          setNewWallet({
                            ...newWallet,
                            isVisible: e.target.checked,
                          })
                        }
                        className="rounded border-neon-primary/30 bg-black/20 text-neon-electric focus:ring-neon-electric"
                      />
                      <Label
                        htmlFor="isVisible"
                        className="text-foreground font-mono text-xs"
                      >
                        Visible in dashboard
                      </Label>
                    </div>
                  </div>

                  <Button
                    onClick={handleAddWallet}
                    className="w-full bg-neon-electric/20 border border-neon-electric/40 text-neon-electric hover:bg-neon-electric/30 font-mono tracking-wider"
                  >
                    CREATE WALLET
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* Wallet Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {wallets
          .filter((wallet) => wallet.isVisible)
          .map((wallet) => (
            <Card
              key={wallet.id}
              className={`bg-gradient-to-br from-black/60 to-neon-primary/5 border-neon-primary/20 hover:border-neon-primary/40 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-neon-primary/10 group ${
                wallet.isDefault ? "ring-2 ring-neon-electric/50" : ""
              }`}
            >
              <CardContent className="p-6 relative">
                {/* Neon accent line */}
                <div className="absolute top-0 left-0 h-full w-px bg-gradient-to-b from-neon-primary/50 via-neon-primary/20 to-transparent" />

                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded-lg ${getWalletTypeColor(wallet.type)} transition-all group-hover:scale-110`}
                    >
                      {getWalletTypeIcon(wallet.type)}
                    </div>
                    <div>
                      <h3 className="font-mono text-sm text-foreground font-medium">
                        {wallet.name}
                        {wallet.isDefault && (
                          <Badge className="ml-2 bg-neon-electric/20 text-neon-electric border-neon-electric/40 text-xs">
                            DEFAULT
                          </Badge>
                        )}
                      </h3>
                      <p className="font-mono text-xs text-foreground/60">
                        {wallet.type.charAt(0).toUpperCase() +
                          wallet.type.slice(1)}{" "}
                        Account
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        handleToggleVisibility(wallet.id, wallet.isVisible)
                      }
                      className="h-8 w-8 p-0 text-foreground/60 hover:text-neon-primary"
                    >
                      {wallet.isVisible ?
                        <Eye className="h-4 w-4" />
                      : <EyeOff className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingWallet(wallet.id)}
                      className="h-8 w-8 p-0 text-foreground/60 hover:text-neon-warning"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteWallet(wallet.id)}
                      className="h-8 w-8 p-0 text-foreground/60 hover:text-neon-error"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="text-3xl font-bold text-foreground font-mono">
                      {formatCurrency(wallet.balance, wallet.currency)}
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getWalletTypeColor(wallet.type)}>
                        {wallet.currency}
                      </Badge>
                      {wallet.bankName && (
                        <span className="text-xs text-foreground/60 font-mono">
                          {wallet.bankName}
                        </span>
                      )}
                    </div>
                  </div>

                  {wallet.description && (
                    <p className="text-xs text-foreground/60 font-mono">
                      {wallet.description}
                    </p>
                  )}

                  <div className="flex space-x-2 pt-2">
                    {!wallet.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefault(wallet.id)}
                        className="flex-1 border-neon-primary/30 text-neon-primary hover:bg-neon-primary/10 font-mono text-xs"
                      >
                        SET DEFAULT
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-neon-info/30 text-neon-info hover:bg-neon-info/10 font-mono text-xs"
                    >
                      <Settings className="h-3 w-3 mr-1" />
                      MANAGE
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
      </div>

      {/* Wallet Selection Dropdown */}
      <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all">
        <CardHeader>
          <CardTitle className="text-neon-electric font-mono tracking-wider uppercase text-sm">
            ACTIVE WALLET SELECTOR
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Select
            value={wallets.find((w) => w.isDefault)?.id || wallets[0]?.id}
          >
            <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
              <SelectValue placeholder="Select active wallet" />
            </SelectTrigger>
            <SelectContent className="bg-black/90 border-neon-primary/30">
              {wallets.map((wallet) => (
                <SelectItem key={wallet.id} value={wallet.id}>
                  <div className="flex items-center space-x-2">
                    <Badge className={getWalletTypeColor(wallet.type)}>
                      {wallet.type}
                    </Badge>
                    <span className="font-mono">{wallet.name}</span>
                    <span className="text-foreground/60 font-mono text-xs">
                      {formatCurrency(wallet.balance, wallet.currency)}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>
    </div>
  );
}
