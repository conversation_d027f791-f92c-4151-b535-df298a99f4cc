'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Upload, 
  FileText, 
  Image, 
  File, 
  X, 
  Eye, 
  Download, 
  Trash2, 
  Edit, 
  Search,
  Filter,
  Calendar,
  DollarSign,
  Tag
} from 'lucide-react'

interface Receipt {
  id: string
  fileName: string
  fileSize: number
  fileType: string
  uploadDate: string
  amount?: number
  description?: string
  category?: string
  isProcessed: boolean
  extractedData?: {
    vendor?: string
    date?: string
    amount?: number
    items?: string[]
  }
  thumbnailUrl?: string
  fileUrl: string
}

interface ReceiptUploadProps {
  receipts: Receipt[]
  onUploadReceipt: (receipt: Omit<Receipt, 'id'>) => void
  onUpdateReceipt: (id: string, receipt: Partial<Receipt>) => void
  onDeleteReceipt: (id: string) => void
}

export function ReceiptUpload({ receipts, onUploadReceipt, onUpdateReceipt, onDeleteReceipt }: ReceiptUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [selectedReceipt, setSelectedReceipt] = useState<Receipt | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [sortBy, setSortBy] = useState('date')

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="h-6 w-6" />
    if (fileType === 'application/pdf') return <FileText className="h-6 w-6" />
    return <File className="h-6 w-6" />
  }

  const getFileTypeColor = (fileType: string) => {
    if (fileType.startsWith('image/')) return 'bg-neon-info/20 text-neon-info border-neon-info/40'
    if (fileType === 'application/pdf') return 'bg-neon-error/20 text-neon-error border-neon-error/40'
    return 'bg-neon-warning/20 text-neon-warning border-neon-warning/40'
  }

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    handleFileUpload(files)
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    handleFileUpload(files)
  }

  const handleFileUpload = async (files: File[]) => {
    setIsUploading(true)
    
    for (const file of files) {
      // Simulate file upload and processing
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const receipt: Omit<Receipt, 'id'> = {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadDate: new Date().toISOString(),
        isProcessed: false,
        fileUrl: URL.createObjectURL(file),
        // Simulate AI extraction (in real app, this would come from backend)
        extractedData: {
          vendor: 'Auto-detected Vendor',
          date: new Date().toISOString().split('T')[0],
          amount: Math.random() * 100 + 10,
          items: ['Item 1', 'Item 2']
        }
      }
      
      onUploadReceipt(receipt)
    }
    
    setIsUploading(false)
  }

  const filteredReceipts = receipts
    .filter(receipt => {
      const matchesSearch = receipt.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           receipt.description?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = filterCategory === 'all' || receipt.category === filterCategory
      return matchesSearch && matchesCategory
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime()
        case 'name':
          return a.fileName.localeCompare(b.fileName)
        case 'size':
          return b.fileSize - a.fileSize
        case 'amount':
          return (b.amount || 0) - (a.amount || 0)
        default:
          return 0
      }
    })

  return (
    <div className="space-y-6">
      {/* Receipt Upload Header */}
      <Card className="bg-black/40 backdrop-blur-md border border-neon-electric/30 hover:border-neon-electric/50 transition-all">
        <CardHeader className="border-b border-neon-electric/20">
          <CardTitle className="text-neon-electric font-mono tracking-wider uppercase">
            RECEIPT MANAGEMENT SYSTEM
          </CardTitle>
          <p className="text-foreground/60 font-mono text-xs mt-1">
            Upload, organize, and process your business receipts
          </p>
        </CardHeader>
      </Card>

      {/* Drag and Drop Upload Zone */}
      <Card className={`
        border-2 border-dashed transition-all duration-300 cursor-pointer
        ${isDragOver 
          ? 'border-neon-primary/60 bg-neon-primary/10' 
          : 'border-neon-primary/30 hover:border-neon-primary/60 bg-neon-primary/5 hover:bg-neon-primary/10'
        }
      `}>
        <CardContent 
          className="p-8 text-center"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => document.getElementById('file-input')?.click()}
        >
          <div className={`transition-all duration-300 ${isDragOver ? 'scale-110' : 'group-hover:scale-105'}`}>
            <Upload className={`h-12 w-12 mx-auto mb-4 transition-all duration-300 ${
              isDragOver ? 'text-neon-electric animate-pulse' : 'text-neon-primary group-hover:text-neon-electric'
            }`} />
            <h3 className="text-foreground font-mono text-lg mb-2">
              {isDragOver ? 'DROP RECEIPT FILES HERE' : 'UPLOAD RECEIPTS'}
            </h3>
            <p className="text-foreground/60 font-mono text-sm mb-4">
              Drag and drop files or click to browse
            </p>
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              <Badge className="bg-neon-info/20 text-neon-info border-neon-info/40">PDF</Badge>
              <Badge className="bg-neon-success/20 text-neon-success border-neon-success/40">JPG</Badge>
              <Badge className="bg-neon-warning/20 text-neon-warning border-neon-warning/40">PNG</Badge>
              <Badge className="bg-neon-error/20 text-neon-error border-neon-error/40">HEIC</Badge>
            </div>
            <p className="text-foreground/40 font-mono text-xs">
              Maximum file size: 10MB per file
            </p>
          </div>
          
          <input
            id="file-input"
            type="file"
            multiple
            accept=".pdf,.jpg,.jpeg,.png,.heic"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          {isUploading && (
            <div className="mt-4 p-4 bg-neon-primary/10 border border-neon-primary/30 rounded-lg">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-neon-primary border-t-transparent rounded-full animate-spin"></div>
                <span className="text-neon-primary font-mono text-sm">PROCESSING RECEIPTS...</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Search and Filter Controls */}
      <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neon-primary" />
              <Input
                placeholder="Search receipts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground"
              />
            </div>
            
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent className="bg-black/90 border-neon-primary/30">
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Software">Software</SelectItem>
                <SelectItem value="Office Expenses">Office Expenses</SelectItem>
                <SelectItem value="Travel">Travel</SelectItem>
                <SelectItem value="Meals">Meals</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="bg-black/20 border-neon-primary/30 focus:border-neon-primary text-foreground">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-black/90 border-neon-primary/30">
                <SelectItem value="date">Upload Date</SelectItem>
                <SelectItem value="name">File Name</SelectItem>
                <SelectItem value="size">File Size</SelectItem>
                <SelectItem value="amount">Amount</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="flex space-x-2">
              <Button variant="outline" className="border-neon-info/30 text-neon-info hover:bg-neon-info/10 font-mono text-xs">
                <Download className="h-4 w-4 mr-1" />
                EXPORT
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Receipt Gallery */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredReceipts.map((receipt) => (
          <Card 
            key={receipt.id}
            className="bg-black/20 backdrop-blur-md border border-white/10 hover:border-neon-primary/30 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-neon-primary/10 group"
          >
            <CardContent className="p-4">
              {/* File Preview */}
              <div className="aspect-square bg-neon-primary/10 rounded-lg mb-3 flex items-center justify-center group-hover:bg-neon-primary/20 transition-all relative overflow-hidden">
                {receipt.fileType.startsWith('image/') ? (
                  <img 
                    src={receipt.fileUrl} 
                    alt={receipt.fileName}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <div className="text-neon-primary">
                    {getFileIcon(receipt.fileType)}
                  </div>
                )}
                
                {/* Overlay Actions */}
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedReceipt(receipt)}
                    className="h-8 w-8 p-0 text-white hover:text-neon-primary"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-white hover:text-neon-info"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-white hover:text-neon-warning"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteReceipt(receipt.id)}
                    className="h-8 w-8 p-0 text-white hover:text-neon-error"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* File Info */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-mono text-sm text-foreground truncate flex-1">
                    {receipt.fileName}
                  </h4>
                  <Badge className={getFileTypeColor(receipt.fileType)}>
                    {receipt.fileType.split('/')[1]?.toUpperCase()}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between text-xs text-foreground/60 font-mono">
                  <span>{formatFileSize(receipt.fileSize)}</span>
                  <span>{new Date(receipt.uploadDate).toLocaleDateString()}</span>
                </div>

                {/* Extracted Data */}
                {receipt.extractedData && (
                  <div className="space-y-1 pt-2 border-t border-neon-primary/20">
                    {receipt.extractedData.vendor && (
                      <div className="flex items-center space-x-2">
                        <Tag className="h-3 w-3 text-neon-info" />
                        <span className="text-xs text-foreground/80 font-mono">
                          {receipt.extractedData.vendor}
                        </span>
                      </div>
                    )}
                    {receipt.extractedData.amount && (
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-3 w-3 text-neon-success" />
                        <span className="text-xs text-neon-success font-mono font-bold">
                          {formatCurrency(receipt.extractedData.amount)}
                        </span>
                      </div>
                    )}
                    {receipt.extractedData.date && (
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-3 w-3 text-neon-warning" />
                        <span className="text-xs text-foreground/80 font-mono">
                          {new Date(receipt.extractedData.date).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Processing Status */}
                <div className="flex items-center justify-between pt-2">
                  <Badge className={
                    receipt.isProcessed 
                      ? 'bg-neon-success/20 text-neon-success border-neon-success/40'
                      : 'bg-neon-warning/20 text-neon-warning border-neon-warning/40'
                  }>
                    {receipt.isProcessed ? 'PROCESSED' : 'PENDING'}
                  </Badge>
                  
                  {!receipt.isProcessed && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-neon-electric/30 text-neon-electric hover:bg-neon-electric/10 font-mono text-xs h-6"
                    >
                      PROCESS
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredReceipts.length === 0 && (
        <Card className="border-border/50 bg-card/50">
          <CardContent className="p-12 text-center">
            <FileText className="h-16 w-16 text-neon-primary/50 mx-auto mb-4" />
            <h3 className="text-foreground font-mono text-lg mb-2">NO RECEIPTS FOUND</h3>
            <p className="text-foreground/60 font-mono text-sm">
              {searchTerm || filterCategory !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Upload your first receipt to get started'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Receipt Preview Modal */}
      {selectedReceipt && (
        <Dialog open={!!selectedReceipt} onOpenChange={() => setSelectedReceipt(null)}>
          <DialogContent className="bg-black/90 backdrop-blur-md border border-neon-electric/30 max-w-4xl">
            <DialogHeader>
              <DialogTitle className="text-neon-electric font-mono tracking-wider uppercase">
                RECEIPT PREVIEW
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Preview */}
              <div className="space-y-4">
                <div className="aspect-square bg-black/20 rounded-lg overflow-hidden">
                  {selectedReceipt.fileType.startsWith('image/') ? (
                    <img 
                      src={selectedReceipt.fileUrl} 
                      alt={selectedReceipt.fileName}
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-center">
                        {getFileIcon(selectedReceipt.fileType)}
                        <p className="text-foreground/60 font-mono text-sm mt-2">
                          {selectedReceipt.fileName}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Details */}
              <div className="space-y-4">
                <div>
                  <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                    FILE INFORMATION
                  </Label>
                  <div className="mt-2 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-foreground/60 font-mono text-xs">Name:</span>
                      <span className="text-foreground font-mono text-xs">{selectedReceipt.fileName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-foreground/60 font-mono text-xs">Size:</span>
                      <span className="text-foreground font-mono text-xs">{formatFileSize(selectedReceipt.fileSize)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-foreground/60 font-mono text-xs">Uploaded:</span>
                      <span className="text-foreground font-mono text-xs">{new Date(selectedReceipt.uploadDate).toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {selectedReceipt.extractedData && (
                  <div>
                    <Label className="text-neon-electric font-mono text-xs uppercase tracking-wider">
                      EXTRACTED DATA
                    </Label>
                    <div className="mt-2 space-y-2">
                      {selectedReceipt.extractedData.vendor && (
                        <div className="flex justify-between">
                          <span className="text-foreground/60 font-mono text-xs">Vendor:</span>
                          <span className="text-foreground font-mono text-xs">{selectedReceipt.extractedData.vendor}</span>
                        </div>
                      )}
                      {selectedReceipt.extractedData.amount && (
                        <div className="flex justify-between">
                          <span className="text-foreground/60 font-mono text-xs">Amount:</span>
                          <span className="text-neon-success font-mono text-xs font-bold">
                            {formatCurrency(selectedReceipt.extractedData.amount)}
                          </span>
                        </div>
                      )}
                      {selectedReceipt.extractedData.date && (
                        <div className="flex justify-between">
                          <span className="text-foreground/60 font-mono text-xs">Date:</span>
                          <span className="text-foreground font-mono text-xs">
                            {new Date(selectedReceipt.extractedData.date).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex space-x-2 pt-4">
                  <Button 
                    variant="outline" 
                    className="flex-1 border-neon-info/30 text-neon-info hover:bg-neon-info/10 font-mono"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    DOWNLOAD
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex-1 border-neon-warning/30 text-neon-warning hover:bg-neon-warning/10 font-mono"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    EDIT
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}