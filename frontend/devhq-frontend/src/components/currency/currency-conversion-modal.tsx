/** @format */

"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ertTriangle, CheckCircle } from "lucide-react";
import { currencyApi } from "@/lib/api/currency";

interface CurrencyConversionModalProps {
  isOpen: boolean;
  onClose: () => void;
  oldCurrency: string;
  newCurrency: string;
  onConfirm: (convertData: boolean) => void;
}

export function CurrencyConversionModal({
  isOpen,
  onClose,
  oldCurrency,
  newCurrency,
  onConfirm,
}: CurrencyConversionModalProps) {
  const [exchangeRate, setExchangeRate] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [convertExistingData, setConvertExistingData] = useState(true);
  const [sampleConversions, setSampleConversions] = useState<
    Array<{
      original: number;
      converted: number;
    }>
  >([]);

  useEffect(() => {
    if (isOpen && oldCurrency !== newCurrency) {
      fetchExchangeRate();
    }
  }, [isOpen, oldCurrency, newCurrency]);

  const fetchExchangeRate = async () => {
    try {
      setIsLoading(true);
      const response = await currencyApi.convertCurrency({
        amount: 1,
        from_currency: oldCurrency,
        to_currency: newCurrency,
      });

      setExchangeRate(Number(response.exchange_rate));

      // Generate sample conversions
      const samples = [100, 500, 1000, 5000];
      const conversions = await Promise.all(
        samples.map(async (amount) => {
          const result = await currencyApi.convertCurrency({
            amount,
            from_currency: oldCurrency,
            to_currency: newCurrency,
          });
          return {
            original: amount,
            converted: result.converted_amount,
          };
        })
      );

      setSampleConversions(conversions);
    } catch (error) {
      console.error("Failed to fetch exchange rate:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirm = () => {
    onConfirm(convertExistingData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-black/20 backdrop-blur-md border border-white/10 max-w-md w-full p-6 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Currency Change</h2>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Currency Change Summary */}
        <div className="mb-6">
          <div className="flex items-center justify-center space-x-4 p-4 bg-green-400/10 border border-green-400/30">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{oldCurrency}</div>
              <div className="text-sm text-white/60">Current</div>
            </div>
            <ArrowRight className="h-6 w-6 text-green-400" />
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{newCurrency}</div>
              <div className="text-sm text-white/60">New</div>
            </div>
          </div>
        </div>

        {/* Exchange Rate */}
        {isLoading ?
          <div className="text-center py-4">
            <div className="text-white/60">Loading exchange rate...</div>
          </div>
        : exchangeRate ?
          <div className="mb-6">
            <div className="text-center p-4 bg-green-400/10 border border-green-400/30">
              <div className="text-sm text-white/60 mb-1">
                Current Exchange Rate
              </div>
              <div className="text-lg font-semibold text-white">
                1 {oldCurrency} ={" "}
                {exchangeRate && !isNaN(Number(exchangeRate)) ?
                  Number(exchangeRate).toFixed(4)
                : "0.0000"}{" "}
                {newCurrency}
              </div>
            </div>
          </div>
        : null}

        {/* Sample Conversions */}
        {sampleConversions.length > 0 && (
          <div className="mb-6">
            <h3 className="text-sm font-medium text-white/70 mb-3">
              Sample Conversions
            </h3>
            <div className="space-y-2">
              {sampleConversions.map((conversion, index) => (
                <div
                  key={index}
                  className="flex justify-between items-center py-2 px-3 bg-white/5 border border-white/10"
                >
                  <span className="text-white">
                    {currencyApi.formatCurrency(
                      conversion.original,
                      oldCurrency
                    )}
                  </span>
                  <ArrowRight className="h-4 w-4 text-green-400" />
                  <span className="text-white">
                    {currencyApi.formatCurrency(
                      conversion.converted,
                      newCurrency
                    )}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Conversion Option */}
        <div className="mb-6">
          <div className="p-4 bg-yellow-400/10 border border-yellow-400/30">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-white mb-2">
                  Convert Existing Financial Data?
                </h4>
                <p className="text-xs text-white/60 mb-3">
                  This will convert your existing project budgets, hourly rates,
                  and financial records from {oldCurrency} to {newCurrency}{" "}
                  using the current exchange rate.
                </p>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={convertExistingData}
                    onChange={(e) => setConvertExistingData(e.target.checked)}
                    className="w-4 h-4 text-green-400 bg-transparent border border-white/20 focus:ring-green-400 focus:ring-2"
                  />
                  <span className="text-sm text-white">
                    Yes, convert my existing financial data
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Warning */}
        <div className="mb-6 p-3 bg-red-400/10 border border-red-400/30">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-red-400 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-red-400">
              <strong>Important:</strong> Currency conversion is based on
              current exchange rates and cannot be undone. Make sure you want to
              proceed with this change.
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-white/20 text-white hover:bg-white/5 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={isLoading}
            className="flex-1 px-4 py-2 bg-green-400/10 border border-green-400/30 text-green-400 hover:bg-green-400/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            <CheckCircle className="h-4 w-4" />
            <span>Confirm Change</span>
          </button>
        </div>
      </div>
    </div>
  );
}
