'use client'

import React from 'react'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import { cn } from '@/lib/utils'

interface PhoneInputProps {
  value: string
  onChange: (value: string, country: any) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  error?: boolean
}

export function CustomPhoneInput({
  value,
  onChange,
  placeholder = "Enter phone number",
  disabled = false,
  className,
  error = false
}: PhoneInputProps) {
  return (
    <div className={cn("phone-input-container", className)}>
      <PhoneInput
        country={'us'} // Default country
        value={value}
        onChange={(phone, country) => onChange(phone, country)}
        placeholder={placeholder}
        disabled={disabled}
        enableSearch={false} // Removed search functionality
        preferredCountries={['us', 'ng', 'ke', 'gh', 'za', 'gb', 'ca']} // DevHQ target markets
        regions={['america', 'europe', 'africa']}
        inputProps={{
          name: 'phone',
          required: false,
          autoFocus: false,
          className: cn(
            "w-full pl-12 pr-3 py-2 border rounded-md",
            "bg-black border-border",
            "focus:border-neon-primary focus:ring-neon-primary/20",
            "disabled:opacity-50 disabled:cursor-not-allowed",
            error && "border-red-500 focus:border-red-500",
            "text-foreground placeholder:text-muted-foreground"
          )
        }}
        buttonStyle={{
          border: 'none',
          backgroundColor: 'transparent',
          borderRadius: '6px 0 0 6px',
          padding: '0 8px'
        }}
        dropdownStyle={{
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '6px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
          zIndex: 9999
        }}
        containerClass={cn(
          "phone-input-wrapper",
          error && "error"
        )}
      />
      
      <style jsx global>{`
        .phone-input-container .react-tel-input {
          font-family: inherit;
        }
        
        .phone-input-container .react-tel-input .form-control {
          width: 100% !important;
          height: 40px;
          font-size: 14px;
          line-height: 1.5;
        }
        
        .phone-input-container .react-tel-input .flag-dropdown {
          border: none !important;
          background: rgba(0, 0, 0, 0.5) !important; /* Darker background for glassy effect */
          backdrop-filter: blur(10px); /* Glassy effect */
          border-right: 1px solid rgba(255, 255, 255, 0.1) !important; /* Subtle border */
        }
        
        .phone-input-container .react-tel-input .flag-dropdown:hover {
          background: rgba(0, 255, 136, 0.1) !important; /* Green hover effect */
        }
        
        .phone-input-container .react-tel-input .selected-flag {
          padding: 0 8px !important;
          border-radius: 6px 0 0 6px !important;
          background: rgba(0, 0, 0, 0.5) !important; /* Darker background for glassy effect */
        }
        
        .phone-input-container .react-tel-input .selected-flag:hover {
          background: rgba(0, 255, 136, 0.1) !important; /* Green hover effect */
        }
        
        .phone-input-container.error .react-tel-input .form-control {
          border-color: #ef4444 !important;
        }
        
        .phone-input-container .react-tel-input .form-control:focus {
          border-color: rgb(59, 130, 246) !important;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
          outline: none !important;
        }
        
        .phone-input-container .country-list {
          max-height: 200px;
          overflow-y: auto;
          background-color: rgba(0, 0, 0, 0.5); /* Darker background for glassy effect */
          backdrop-filter: blur(10px); /* Glassy effect */
          border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
        }
        
        .phone-input-container .country-list .country {
          padding: 8px 12px;
          color: white !important; /* Changed to white for country names */
        }
        
        .phone-input-container .country-list .country:hover {
          background: rgba(0, 255, 136, 0.1); /* Green hover effect */
        }
        
        .phone-input-container .country-list .country.highlight {
          background: rgba(0, 255, 136, 0.2); /* Green highlight effect */
        }
      `}</style>
    </div>
  )
}