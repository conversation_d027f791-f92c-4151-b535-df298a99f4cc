/** @format */

import React from "react";
import {
  motion,
  useInView,
  useMotionValue,
  useTransform,
  animate,
} from "framer-motion";

interface CountingStatProps {
  value: number;
  className?: string;
  formatter?: (value: number) => string;
}

export function CountingStat({
  value,
  className,
  formatter,
}: CountingStatProps) {
  const ref = React.useRef(null);
  const inView = useInView(ref, { once: true, margin: "-50px 0px" });
  const count = useMotionValue(0);
  const rounded = useTransform(count, Math.round);

  const formattedValue = useTransform(rounded, (latest) => {
    if (formatter) {
      return formatter(latest);
    } else {
      return latest;
    }
  });

  React.useEffect(() => {
    if (inView) {
      const controls = animate(count, value, { duration: 2 });
      return controls.stop;
    }
  }, [inView, count, value]);

  return (
    <motion.span ref={ref} className={className}>
      {formattedValue as any}
    </motion.span>
  );
}
