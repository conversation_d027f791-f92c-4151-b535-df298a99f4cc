'use client'

import { Check } from 'lucide-react';

interface CustomCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

export const CustomCheckbox = ({ checked, onChange }: CustomCheckboxProps) => {
  return (
    <div
      className={`w-4 h-4 border border-gray-600 flex items-center justify-center cursor-pointer ${checked ? 'bg-green-500' : 'bg-transparent'}`}
      onClick={() => onChange(!checked)}
    >
      {checked && <Check className="h-3 w-3 text-black" />}
    </div>
  );
};