'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Calendar, 
  DollarSign, 
  Clock, 
  Target, 
  Plus, 
  Trash2,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface EnhancedInvoiceCreationModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (invoiceData: any) => void
  projectId: string
  projectName: string
  clientName: string
}

export function EnhancedInvoiceCreationModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  projectId,
  projectName,
  clientName
}: EnhancedInvoiceCreationModalProps) {
  const [scenario, setScenario] = useState<'milestone' | 'deposit' | 'time_materials' | 'manual'>('milestone')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [manualItems, setManualItems] = useState<Array<{description: string, quantity: number, rate: number}>>([{description: '', quantity: 1, rate: 0}])
  const [depositPercentage, setDepositPercentage] = useState(50)
  const [customDepositAmount, setCustomDepositAmount] = useState('')
  const [timePeriod, setTimePeriod] = useState({start: '', end: ''})
  const [formData, setFormData] = useState({
    dueDate: '',
    notes: '',
    taxRate: 0,
    discountAmount: 0
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  // Mock data for demonstration
  const mockMilestones = [
    { id: 'milestone-1', name: 'Design Phase Completion', description: 'Completed website design and wireframes', amount: 1500, status: 'completed' },
    { id: 'milestone-2', name: 'Development Phase Completion', description: 'Completed frontend and backend development', amount: 3000, status: 'completed' },
    { id: 'milestone-3', name: 'Testing & Deployment', description: 'Completed testing and deployment to production', amount: 1000, status: 'pending' },
  ]

  const mockTimeEntries = [
    { id: 'time-1', description: 'UI Design Work', hours: 8, rate: 75, date: '2024-01-15', amount: 600 },
    { id: 'time-2', description: 'Frontend Development', hours: 12, rate: 85, date: '2024-01-16', amount: 1020 },
    { id: 'time-3', description: 'Backend API Development', hours: 10, rate: 95, date: '2024-01-17', amount: 950 },
    { id: 'time-4', description: 'Database Optimization', hours: 6, rate: 95, date: '2024-01-18', amount: 570 },
  ]

  const handleItemSelection = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const addManualItem = () => {
    setManualItems([...manualItems, {description: '', quantity: 1, rate: 0}])
  }

  const updateManualItem = (index: number, field: string, value: string | number) => {
    const updated = [...manualItems]
    updated[index] = {...updated[index], [field]: value}
    setManualItems(updated)
  }

  const removeManualItem = (index: number) => {
    if (manualItems.length > 1) {
      setManualItems(manualItems.filter((_, i) => i !== index))
    }
  }

  const handleSubmit = async () => {
    try {
      setIsLoading(true)
      setError('')
      
      // In a real implementation, this would call the backend API
      // For now, we'll just simulate the creation
      const invoiceData = {
        projectId,
        projectName,
        clientName,
        scenario,
        selectedItems,
        manualItems,
        depositPercentage,
        customDepositAmount,
        timePeriod,
        formData,
        createdAt: new Date().toISOString(),
        status: 'draft',
        invoiceNumber: `INV-${Date.now()}`,
      }
      
      onSubmit(invoiceData)
      onClose()
    } catch (err) {
      setError('Failed to create invoice')
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate totals
  const calculateSelectedTotal = () => {
    if (scenario === 'milestone') {
      return selectedItems.reduce((total, itemId) => {
        const item = mockMilestones.find(m => m.id === itemId)
        return total + (item ? item.amount : 0)
      }, 0)
    } else if (scenario === 'time_materials') {
      return selectedItems.reduce((total, itemId) => {
        const item = mockTimeEntries.find(t => t.id === itemId)
        return total + (item ? item.amount : 0)
      }, 0)
    }
    return 0
  }

  const calculateManualTotal = () => {
    return manualItems.reduce((total, item) => {
      return total + (item.quantity * item.rate)
    }, 0)
  }

  const subtotal = scenario === 'milestone' || scenario === 'time_materials' ? calculateSelectedTotal() : 
                  scenario === 'manual' ? calculateManualTotal() : 
                  0
                  
  const taxAmount = subtotal * (formData.taxRate / 100)
  const totalAmount = subtotal + taxAmount - formData.discountAmount

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-background-elevated/95 backdrop-blur-md border-border text-foreground max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-neon-primary font-mono">
            Create Smart Invoice
          </DialogTitle>
          <DialogDescription className="text-foreground-muted">
            Generate a new invoice using flexible billing scenarios.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4 overflow-y-auto flex-1 pr-2">
          {/* Scenario Selection */}
          <div className="space-y-4">
            <Label className="text-foreground-secondary font-mono text-xs">Billing Scenario</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Button
                variant={scenario === 'milestone' ? 'default' : 'outline'}
                className={cn(
                  "flex flex-col items-center gap-1 h-auto p-3",
                  scenario === 'milestone' && "bg-neon-primary text-background"
                )}
                onClick={() => setScenario('milestone')}
              >
                <Target className="h-4 w-4" />
                <span className="text-xs">Milestone</span>
              </Button>
              <Button
                variant={scenario === 'deposit' ? 'default' : 'outline'}
                className={cn(
                  "flex flex-col items-center gap-1 h-auto p-3",
                  scenario === 'deposit' && "bg-neon-primary text-background"
                )}
                onClick={() => setScenario('deposit')}
              >
                <DollarSign className="h-4 w-4" />
                <span className="text-xs">Deposit</span>
              </Button>
              <Button
                variant={scenario === 'time_materials' ? 'default' : 'outline'}
                className={cn(
                  "flex flex-col items-center gap-1 h-auto p-3",
                  scenario === 'time_materials' && "bg-neon-primary text-background"
                )}
                onClick={() => setScenario('time_materials')}
              >
                <Clock className="h-4 w-4" />
                <span className="text-xs">Time & Materials</span>
              </Button>
              <Button
                variant={scenario === 'manual' ? 'default' : 'outline'}
                className={cn(
                  "flex flex-col items-center gap-1 h-auto p-3",
                  scenario === 'manual' && "bg-neon-primary text-background"
                )}
                onClick={() => setScenario('manual')}
              >
                <Plus className="h-4 w-4" />
                <span className="text-xs">Manual</span>
              </Button>
            </div>
          </div>

          {/* Scenario-specific content */}
          <div className="border border-border/30 rounded-lg p-4">
            {scenario === 'milestone' && (
              <div className="space-y-4">
                <h3 className="font-medium text-foreground">Milestone Payment</h3>
                <p className="text-sm text-foreground-muted">
                  Select completed milestones to invoice. These can be approved deliverables or project phases.
                </p>
                
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {mockMilestones.map((milestone) => (
                    <div 
                      key={milestone.id} 
                      className={cn(
                        "flex items-center gap-3 p-3 border rounded-lg cursor-pointer",
                        selectedItems.includes(milestone.id) 
                          ? "border-neon-primary bg-neon-primary/10" 
                          : "border-border hover:bg-background/50"
                      )}
                      onClick={() => handleItemSelection(milestone.id)}
                    >
                      <div className={`w-5 h-5 border rounded flex items-center justify-center ${
                        selectedItems.includes(milestone.id) 
                          ? "border-neon-primary bg-neon-primary" 
                          : "border-border"
                      }`}>
                        {selectedItems.includes(milestone.id) && (
                          <CheckCircle className="h-3 w-3 text-background" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-foreground">{milestone.name}</div>
                        <div className="text-xs text-foreground-muted">
                          {milestone.description}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-foreground">
                          ${milestone.amount.toFixed(2)}
                        </div>
                        <div className="text-xs text-foreground-muted capitalize">
                          {milestone.status}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {scenario === 'deposit' && (
              <div className="space-y-4">
                <h3 className="font-medium text-foreground">Upfront Deposit</h3>
                <p className="text-sm text-foreground-muted">
                  Request an upfront payment before starting work on the project.
                </p>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="deposit-percentage" className="text-xs">Deposit Percentage</Label>
                      <Select 
                        value={depositPercentage.toString()} 
                        onValueChange={(value) => setDepositPercentage(Number(value))}
                        disabled={!!customDepositAmount}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="25">25%</SelectItem>
                          <SelectItem value="50">50%</SelectItem>
                          <SelectItem value="75">75%</SelectItem>
                          <SelectItem value="100">100%</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="custom-amount" className="text-xs">Custom Amount ($)</Label>
                      <Input
                        id="custom-amount"
                        type="number"
                        placeholder="Enter custom amount"
                        value={customDepositAmount}
                        onChange={(e) => setCustomDepositAmount(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="p-3 bg-background/20 border border-border/30 rounded-lg">
                    <div className="text-sm text-foreground">
                      <div className="flex justify-between">
                        <span>Project Total: </span>
                        <span>$10,000.00</span>
                      </div>
                      <div className="flex justify-between mt-1">
                        <span>{depositPercentage}% Deposit: </span>
                        <span className="font-medium">${(10000 * depositPercentage / 100).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {scenario === 'time_materials' && (
              <div className="space-y-4">
                <h3 className="font-medium text-foreground">Time & Materials</h3>
                <p className="text-sm text-foreground-muted">
                  Invoice for tracked time and expenses over a specific period.
                </p>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-date" className="text-xs">Start Date</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={timePeriod.start}
                      onChange={(e) => setTimePeriod({...timePeriod, start: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-date" className="text-xs">End Date</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={timePeriod.end}
                      onChange={(e) => setTimePeriod({...timePeriod, end: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {mockTimeEntries.map((entry) => (
                    <div 
                      key={entry.id} 
                      className={cn(
                        "flex items-center gap-3 p-3 border rounded-lg cursor-pointer",
                        selectedItems.includes(entry.id) 
                          ? "border-neon-primary bg-neon-primary/10" 
                          : "border-border hover:bg-background/50"
                      )}
                      onClick={() => handleItemSelection(entry.id)}
                    >
                      <div className={`w-5 h-5 border rounded flex items-center justify-center ${
                        selectedItems.includes(entry.id) 
                          ? "border-neon-primary bg-neon-primary" 
                          : "border-border"
                      }`}>
                        {selectedItems.includes(entry.id) && (
                          <CheckCircle className="h-3 w-3 text-background" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-foreground">{entry.description}</div>
                        <div className="text-xs text-foreground-muted">
                          {entry.date}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-foreground">
                          ${entry.amount.toFixed(2)}
                        </div>
                        <div className="text-xs text-foreground-muted">
                          {entry.hours} hrs × ${entry.rate}/hr
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {scenario === 'manual' && (
              <div className="space-y-4">
                <h3 className="font-medium text-foreground">Manual Line Items</h3>
                <p className="text-sm text-foreground-muted">
                  Add custom line items for this invoice.
                </p>
                
                <div className="space-y-3">
                  {manualItems.map((item, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2 items-end">
                      <div className="col-span-5">
                        <Label className="text-xs">Description</Label>
                        <Input
                          value={item.description}
                          onChange={(e) => updateManualItem(index, 'description', e.target.value)}
                          placeholder="Item description"
                        />
                      </div>
                      <div className="col-span-2">
                        <Label className="text-xs">Quantity</Label>
                        <Input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateManualItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.1"
                        />
                      </div>
                      <div className="col-span-2">
                        <Label className="text-xs">Rate ($)</Label>
                        <Input
                          type="number"
                          value={item.rate}
                          onChange={(e) => updateManualItem(index, 'rate', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                        />
                      </div>
                      <div className="col-span-2">
                        <Label className="text-xs">Total</Label>
                        <div className="font-medium">
                          ${(item.quantity * item.rate).toFixed(2)}
                        </div>
                      </div>
                      <div className="col-span-1">
                        {manualItems.length > 1 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeManualItem(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addManualItem}
                    className="mt-2"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Line Item
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Invoice Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="due-date" className="text-xs">Due Date *</Label>
              <Input
                id="due-date"
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData({...formData, dueDate: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tax-rate" className="text-xs">Tax Rate (%)</Label>
              <Input
                id="tax-rate"
                type="number"
                value={formData.taxRate}
                onChange={(e) => setFormData({...formData, taxRate: parseFloat(e.target.value) || 0})}
                min="0"
                step="0.1"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes" className="text-xs">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              placeholder="Additional notes or payment terms..."
              rows={3}
            />
          </div>

          {/* Summary */}
          <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
            <h4 className="text-sm font-mono text-neon-primary mb-3">Invoice Summary</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-foreground-muted">Subtotal:</span>
                <span className="font-mono text-foreground">${subtotal.toFixed(2)}</span>
              </div>
              {formData.taxRate > 0 && (
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Tax ({formData.taxRate}%):</span>
                  <span className="font-mono text-foreground">${taxAmount.toFixed(2)}</span>
                </div>
              )}
              {formData.discountAmount > 0 && (
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Discount:</span>
                  <span className="font-mono text-foreground">-${formData.discountAmount.toFixed(2)}</span>
                </div>
              )}
              <div className="flex justify-between border-t border-border/30 pt-2 mt-2">
                <span className="font-semibold text-foreground">Total Amount:</span>
                <span className="font-mono text-neon-primary font-bold">${totalAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-400" />
              <span className="text-sm text-red-300">{error}</span>
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0 border-t border-border/30 pt-4">
          <Button variant="ghost" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            className="bg-neon-primary hover:bg-neon-primary/90 text-background"
            disabled={isLoading || 
              (scenario === 'milestone' && selectedItems.length === 0) ||
              (scenario === 'time_materials' && (!timePeriod.start || !timePeriod.end)) ||
              (scenario === 'deposit' && !depositPercentage && !customDepositAmount)
            }
          >
            {isLoading ? 'Creating...' : 'Create Invoice'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}