/** @format */

"use client";

import React, { useState } from "react";
import {
  Mail,
  Send,
  X,
  User,
  FileText,
  AlertCircle,
  CheckCircle,
  Building,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SendEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  emailType:
    | "payment_link"
    | "project_approval"
    | "invoice_receipt"
    | "milestone_update";
  data: {
    projectName?: string;
    clientName?: string;
    amount?: number;
    invoiceNumber?: string;
    milestoneTitle?: string;
    paymentLink?: string;
    approvalLink?: string;
  };
  onSend: (emailData: {
    clientEmail: string;
    clientName: string;
    customMessage?: string;
  }) => Promise<void>;
}

export default function SendEmailModal({
  isOpen,
  onClose,
  emailType,
  data,
  onSend,
}: SendEmailModalProps) {
  const [clientEmail, setClientEmail] = useState("");
  const [clientName, setClientName] = useState(data.clientName || "");
  const [customMessage, setCustomMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const { toast } = useToast();

  const getEmailTypeConfig = () => {
    switch (emailType) {
      case "payment_link":
        return {
          title: "Send Payment Link",
          icon: <Mail className="h-5 w-5 text-green-400" />,
          description: "Send a secure payment link to your client",
          defaultSubject: `Invoice Payment - ${data.projectName}`,
          defaultMessage: `Hi ${clientName},\n\nPlease find the payment link for your invoice below. You can securely pay online using your preferred payment method.\n\nProject: ${data.projectName}\nAmount: $${data.amount?.toLocaleString()}\n\nThank you for your business!`,
          buttonText: "Send Payment Link",
          buttonColor:
            "bg-green-400/10 text-green-400 border-green-400/30 hover:bg-green-400/20",
        };
      case "project_approval":
        return {
          title: "Send Approval Request",
          icon: <CheckCircle className="h-5 w-5 text-blue-400" />,
          description: "Request client approval for project deliverables",
          defaultSubject: `Approval Required - ${data.projectName}`,
          defaultMessage: `Hi ${clientName},\n\nYour project "${data.projectName}" is ready for review and approval.\n\nPlease click the link below to review the deliverables and provide your feedback.\n\nLooking forward to your response!`,
          buttonText: "Send Approval Request",
          buttonColor:
            "bg-blue-400/10 text-blue-400 border-blue-400/30 hover:bg-blue-400/20",
        };
      case "invoice_receipt":
        return {
          title: "Send Receipt",
          icon: <FileText className="h-5 w-5 text-purple-400" />,
          description: "Send payment receipt to your client",
          defaultSubject: `Payment Receipt - ${data.invoiceNumber}`,
          defaultMessage: `Hi ${clientName},\n\nThank you for your payment! Please find your receipt attached.\n\nInvoice: ${data.invoiceNumber}\nAmount Paid: $${data.amount?.toLocaleString()}\nProject: ${data.projectName}\n\nWe appreciate your business!`,
          buttonText: "Send Receipt",
          buttonColor:
            "bg-purple-400/10 text-purple-400 border-purple-400/30 hover:bg-purple-400/20",
        };
      case "milestone_update":
        return {
          title: "Send Milestone Update",
          icon: <Building className="h-5 w-5 text-orange-400" />,
          description: "Notify client about milestone completion",
          defaultSubject: `Milestone Completed - ${data.milestoneTitle}`,
          defaultMessage: `Hi ${clientName},\n\nGreat news! We've completed the milestone "${data.milestoneTitle}" for your project "${data.projectName}".\n\nYou can review the completed work and we'll be moving on to the next phase.\n\nThank you for your continued trust!`,
          buttonText: "Send Update",
          buttonColor:
            "bg-orange-400/10 text-orange-400 border-orange-400/30 hover:bg-orange-400/20",
        };
      default:
        return {
          title: "Send Email",
          icon: <Mail className="h-5 w-5 text-gray-400" />,
          description: "Send email to client",
          defaultSubject: "Update from your developer",
          defaultMessage: "",
          buttonText: "Send Email",
          buttonColor:
            "bg-gray-400/10 text-gray-400 border-gray-400/30 hover:bg-gray-400/20",
        };
    }
  };

  const config = getEmailTypeConfig();

  const handleSend = async () => {
    if (!clientEmail || !clientName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      await onSend({
        clientEmail,
        clientName,
        customMessage: customMessage || config.defaultMessage,
      });
      setEmailSent(true);
      setTimeout(() => {
        setEmailSent(false);
        onClose();
        // Reset form
        setClientEmail("");
        setClientName("");
        setCustomMessage("");
      }, 2000);
    } catch (error) {
      console.error("Failed to send email:", error);
      toast({
        title: "Email Failed",
        description: "Failed to send email. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setEmailSent(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-black border border-white/20 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              {config.icon}
              <div>
                <h3 className="text-xl font-semibold text-white">
                  {config.title}
                </h3>
                <p className="text-white/60 text-sm">{config.description}</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="text-white/60 hover:text-white disabled:opacity-50"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {emailSent ?
            /* Success State */
            <div className="text-center py-8">
              <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-white mb-2">
                Email Sent Successfully!
              </h4>
              <p className="text-white/60">
                Your email has been sent to {clientEmail}
              </p>
            </div>
          : /* Form */
            <div className="space-y-6">
              {/* Project Info */}
              <div className="border border-white/20 bg-white/5 p-4">
                <h4 className="text-white font-medium mb-2">Email Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-white/60">Subject:</span>
                    <div className="text-white">{config.defaultSubject}</div>
                  </div>
                  {data.amount && (
                    <div>
                      <span className="text-white/60">Amount:</span>
                      <div className="text-white">
                        ${data.amount.toLocaleString()}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Client Information */}
              <div className="space-y-4">
                <h4 className="text-white font-medium">Client Information</h4>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Client Name *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={clientName}
                      onChange={(e) => setClientName(e.target.value)}
                      className="w-full p-3 pl-10 bg-transparent border border-white/20 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
                      placeholder="Enter client's full name"
                      required
                    />
                    <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-white/40" />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Client Email Address *
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      value={clientEmail}
                      onChange={(e) => setClientEmail(e.target.value)}
                      className="w-full p-3 pl-10 bg-transparent border border-white/20 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none"
                      placeholder="<EMAIL>"
                      required
                    />
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-white/40" />
                  </div>
                </div>
              </div>

              {/* Custom Message */}
              <div>
                <label className="block text-sm font-medium text-white/70 mb-2">
                  Message
                </label>
                <textarea
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  rows={6}
                  className="w-full p-3 bg-transparent border border-white/20 text-white placeholder-white/40 focus:border-blue-400 focus:outline-none resize-none"
                  placeholder={config.defaultMessage}
                />
                <p className="text-white/40 text-xs mt-1">
                  Leave empty to use the default message above
                </p>
              </div>

              {/* Security Notice */}
              <div className="border border-blue-400/30 bg-blue-400/10 p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-blue-400 mt-0.5" />
                  <div className="text-sm text-blue-400">
                    <p className="font-medium mb-1">Secure Email Delivery</p>
                    <p className="opacity-90">
                      This email will be sent using your configured branding and
                      will include secure links that expire after 30 days for
                      security.
                    </p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-3 pt-4">
                <button
                  onClick={handleSend}
                  disabled={isLoading || !clientEmail || !clientName}
                  className={`flex-1 border px-6 py-3 font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 ${config.buttonColor}`}
                >
                  <Send className="h-4 w-4" />
                  {isLoading ? "Sending..." : config.buttonText}
                </button>
                <button
                  onClick={handleClose}
                  disabled={isLoading}
                  className="border border-white/20 bg-transparent px-6 py-3 text-white/60 hover:text-white disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          }
        </div>
      </div>
    </div>
  );
}
