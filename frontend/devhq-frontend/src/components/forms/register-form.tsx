/** @format */

"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CustomPhoneInput } from "@/components/ui/phone-input";
import { useState } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { useRouter } from "next/navigation";
import { Eye, EyeOff } from "lucide-react";

export function RegisterForm() {
  const router = useRouter();
  const { register } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  async function onSubmit(event: React.SyntheticEvent) {
    event.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const response = await register({
        email,
        password,
        first_name: firstName,
        last_name: lastName,
      });
      // Show success message and don't redirect
      setSuccess(
        response.message ||
          "Account created successfully! Please check your email for verification instructions."
      );
    } catch (err: any) {
      console.error("Registration error:", err);
      if (err.response?.status === 409) {
        setError("An account with this email already exists.");
      } else if (err.response?.status === 422) {
        setError("Please check your input and try again.");
      } else {
        setError(
          err.response?.data?.detail ||
            "Failed to create account. Please try again."
        );
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-2">
      {error && (
        <div className="bg-neon-error/10 border border-neon-error/30 text-neon-error p-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-500/10 border border-green-500/30 text-green-400 p-3 rounded-md text-sm">
          {success}
        </div>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName" className="text-foreground">
            First Name
          </Label>
          <Input
            id="firstName"
            placeholder="John"
            type="text"
            autoCapitalize="none"
            autoCorrect="off"
            disabled={isLoading}
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            className="border border-white/20 bg-transparent rounded-none focus:border-neon-primary focus:ring-neon-primary/20"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastName" className="text-foreground">
            Last Name
          </Label>
          <Input
            id="lastName"
            placeholder="Doe"
            type="text"
            autoCapitalize="none"
            autoCorrect="off"
            disabled={isLoading}
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            className="border border-white/20 bg-transparent rounded-none focus:border-neon-primary focus:ring-neon-primary/20"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="text-foreground">
          Email
        </Label>
        <Input
          id="email"
          placeholder="<EMAIL>"
          type="email"
          autoCapitalize="none"
          autoComplete="email"
          autoCorrect="off"
          disabled={isLoading}
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="border border-white/20 bg-transparent rounded-none focus:border-neon-primary focus:ring-neon-primary/20"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="password" className="text-foreground">
          Password
        </Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            autoComplete="new-password"
            disabled={isLoading}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="border border-white/20 bg-transparent rounded-none focus:border-neon-primary focus:ring-neon-primary/20 pr-10"
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-800"
          >
            {showPassword ?
              <EyeOff className="h-4 w-4" />
            : <Eye className="h-4 w-4" />}
          </button>
        </div>
        <p className="text-xs text-foreground-muted">
          Password must be at least 8 characters with uppercase, lowercase, and
          digit
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <input
          id="terms"
          type="checkbox"
          className="h-4 w-4 rounded border-border text-neon-primary focus:ring-neon-primary"
          required
        />
        <Label htmlFor="terms" className="text-foreground text-sm">
          I agree to the{" "}
          <a
            href="/terms"
            className="!text-neon-primary"
            style={{ color: "hsl(var(--neon-primary))" }}
          >
            Terms of Service
          </a>{" "}
          and{" "}
          <a
            href="/privacy"
            className="!text-neon-primary"
            style={{ color: "hsl(var(--neon-primary))" }}
          >
            Privacy Policy
          </a>
        </Label>
      </div>

      <div className="flex mb-4">
        <Button
          type="submit"
          disabled={isLoading}
          variant="neon"
          className="w-fit"
        >
          {isLoading ? "Creating account..." : "Create Account"}
        </Button>
      </div>
    </form>
  );
}
