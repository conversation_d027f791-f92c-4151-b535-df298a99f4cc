/** @format */

"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { Loader2 } from "lucide-react";

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
});

export function ForgotPasswordForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    try {
      const { authApi } = await import("@/lib/api");
      await authApi.forgotPassword(values.email);
      setIsSuccess(true);
    } catch (error: any) {
      console.error("Forgot password error:", error);
      // Still show success message for security reasons
      // (don't reveal if email exists or not)
      setIsSuccess(true);
    } finally {
      setIsLoading(false);
    }
  }

  if (isSuccess) {
    return (
      <div className="text-center p-4 bg-green-500/10 border border-green-500/20 rounded-md">
        <p className="text-green-400">
          ✅ If an account with that email exists, a password reset link has
          been sent.
        </p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input
                  placeholder="<EMAIL>"
                  {...field}
                  className="bg-transparent border-white/20 focus:border-neon-primary rounded-none"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="w-full bg-white text-black hover:bg-green-500"
          disabled={isLoading}
        >
          {isLoading ?
            <Loader2 className="animate-spin" />
          : "Send Reset Link"}
        </Button>
      </form>
    </Form>
  );
}
