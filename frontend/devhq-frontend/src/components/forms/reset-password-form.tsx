
'use client'

import { useState } from 'react'
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { apiClient } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Loader2 } from 'lucide-react'

import { Eye, EyeOff } from 'lucide-react';

const resetPasswordSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>

export function ResetPasswordForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [status, setStatus] = useState<'form' | 'success' | 'error'>('form')
  const [message, setMessage] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  
  const searchParams = useSearchParams()
  const router = useRouter()
  const token = searchParams.get('token')

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
        password: '',
        confirmPassword: ''
    }
  })

  const onSubmit = async (data: ResetPasswordFormValues) => {
    if (!token) {
      setStatus('error')
      setMessage('No reset token provided')
      return
    }

    setIsLoading(true)
    
    try {
      await apiClient.post('/auth/reset-password', {
        token: token,
        new_password: data.password
      })

      setStatus('success')
      setMessage('Your password has been successfully reset!')
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push('/login?reset=true')
      }, 3000)
    } catch (error: any) {
      console.error('Password reset error:', error)
      
      if (error.response?.status === 400) {
        setMessage('This reset link has expired or is invalid.')
      } else {
        setMessage('Failed to reset password. Please try again.')
      }
      setStatus('error')
    } finally {
      setIsLoading(false)
    }
  }

  if (status === 'success') {
    return (
        <div className="text-center p-4 bg-green-500/10 border border-green-500/20 rounded-md">
            <p className="text-green-400">✅ {message}</p>
            <p className='text-white/70 text-sm mt-2'>Redirecting to login...</p>
        </div>
    )
  }

  if (status === 'error') {
    return (
        <div className="text-center p-4 bg-red-500/10 border border-red-500/20 rounded-md">
            <p className="text-red-400">❌ {message}</p>
        </div>
    )
  }

  return (
    <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>New Password</FormLabel>
                        <FormControl>
                            <div className="relative">
                                <Input 
                                    type={showPassword ? 'text' : 'password'}
                                    placeholder="••••••••"
                                    {...field}
                                    className="bg-transparent border-white/20 focus:border-neon-primary rounded-none pr-10"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute inset-y-0 right-0 px-3 flex items-center text-white/70 hover:text-white"
                                >
                                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                </button>
                            </div>
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}
            />
            <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>Confirm New Password</FormLabel>
                        <FormControl>
                            <div className="relative">
                                <Input 
                                    type={showPassword ? 'text' : 'password'}
                                    placeholder="••••••••"
                                    {...field}
                                    className="bg-transparent border-white/20 focus:border-neon-primary rounded-none pr-10"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute inset-y-0 right-0 px-3 flex items-center text-white/70 hover:text-white"
                                >
                                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                </button>
                            </div>
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}
            />
            <Button 
                type="submit" 
                className="w-full bg-white text-black hover:bg-green-500"
                disabled={isLoading}
                >
                {isLoading ? <Loader2 className="animate-spin" /> : 'Reset Password'}
            </Button>
      </form>
    </Form>
  )
}
