/** @format */

"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { useRouter } from "next/navigation";
import { Eye, EyeOff } from "lucide-react";

export function LoginForm() {
  const router = useRouter();
  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");

  async function onSubmit(event: React.SyntheticEvent) {
    event.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      await login(email, password);
      setTimeout(() => {
        router.push("/dashboard");
      }, 1000);
    } catch (err: any) {
      if (err.response?.status === 401) {
        setError("Invalid email or password. Please try again.");
      } else if (err.response?.status === 403) {
        setError("Account is deactivated. Please contact support.");
      } else {
        setError(
          err.response?.data?.detail ||
            "Failed to sign in. Please check your credentials."
        );
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <form onSubmit={onSubmit} className="space-y-2">
      {error && (
        <div className="bg-neon-error/10 border border-neon-error/30 text-neon-error p-3 rounded-md text-sm">
          {error}
        </div>
      )}
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="border border-white/20 bg-transparent rounded-none focus:border-neon-primary focus:ring-neon-primary/20"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="border border-white/20 bg-transparent rounded-none focus:border-neon-primary focus:ring-neon-primary/20 pr-10"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-800"
          >
            {showPassword ?
              <EyeOff className="h-4 w-4" />
            : <Eye className="h-4 w-4" />}
          </button>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <input
          id="remember"
          type="checkbox"
          className="h-4 w-4 rounded border-border text-neon-primary focus:ring-neon-primary"
        />
        <Label htmlFor="remember" className="text-foreground text-sm">
          Remember me
        </Label>
      </div>
      <Button
        type="submit"
        disabled={isLoading}
        variant="neon"
        className="w-fit"
      >
        {isLoading ? "Signing in..." : "Sign In"}
      </Button>
    </form>
  );
}
