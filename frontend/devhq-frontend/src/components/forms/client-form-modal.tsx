'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Save, ChevronDown, Star } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ClientFormModalProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  onSave: (clientData: any) => void
  editClient?: any // Optional: client data to pre-fill form for editing
}

export function ClientFormModal({ isOpen, setIsOpen, onSave, editClient }: ClientFormModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    contactPerson: '',
    email: '',
    phone: '',
    address: '',
    status: 'active', // Default status
    priority: 'medium', // Default priority
    industry: '',
    companySize: 'small', // Default company size
    starred: false,
    notes: '',
  })

  useEffect(() => {
    if (editClient) {
      setFormData(editClient)
    } else {
      setFormData({
        name: '',
        contactPerson: '',
        email: '',
        phone: '',
        address: '',
        status: 'active',
        priority: 'medium',
        industry: '',
        companySize: 'small',
        starred: false,
        notes: '',
      })
    }
  }, [editClient, isOpen]) // Reset form when modal opens or editClient changes

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({ ...prev, [id]: value }))
  }

  const handleDropdownChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleStarredToggle = () => {
    setFormData(prev => ({ ...prev, starred: !prev.starred }))
  }

  const handleSubmit = () => {
    onSave(formData)
    setIsOpen(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="bg-background-elevated/95 backdrop-blur-md border-border text-foreground max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-neon-primary font-mono">
            {editClient ? 'Edit Client' : 'Add New Client'}
          </DialogTitle>
          <DialogDescription className="text-foreground-muted">
            {editClient ? 'Update client details.' : 'Enter new client information.'}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4 overflow-y-auto flex-1 pr-2">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-foreground-secondary font-mono text-xs">Client Name *</Label>
              <Input id="name" value={formData.name} onChange={handleChange} placeholder="e.g., Cyberdyne Systems" className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="contactPerson" className="text-foreground-secondary font-mono text-xs">Contact Person</Label>
              <Input id="contactPerson" value={formData.contactPerson} onChange={handleChange} placeholder="e.g., Sarah Connor" className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-foreground-secondary font-mono text-xs">Email *</Label>
              <Input id="email" type="email" value={formData.email} onChange={handleChange} placeholder="e.g., <EMAIL>" className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-foreground-secondary font-mono text-xs">Phone</Label>
              <Input id="phone" type="tel" value={formData.phone} onChange={handleChange} placeholder="e.g., +****************" className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="text-foreground-secondary font-mono text-xs">Address</Label>
            <Textarea id="address" value={formData.address} onChange={handleChange} placeholder="e.g., 123 Future St, Los Angeles" rows={2} className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-foreground-secondary font-mono text-xs">Status</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary">
                    {formData.status.charAt(0).toUpperCase() + formData.status.slice(1)}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                  <DropdownMenuItem onClick={() => handleDropdownChange('status', 'active')} className="text-xs">
                    Active
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDropdownChange('status', 'inactive')} className="text-xs">
                    Inactive
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDropdownChange('status', 'completed')} className="text-xs">
                    Completed
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDropdownChange('status', 'archived')} className="text-xs">
                    Archived
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="space-y-2">
              <Label className="text-foreground-secondary font-mono text-xs">Priority</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary">
                    {formData.priority.charAt(0).toUpperCase() + formData.priority.slice(1)}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                  <DropdownMenuItem onClick={() => handleDropdownChange('priority', 'high')} className="text-xs">
                    High
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDropdownChange('priority', 'medium')} className="text-xs">
                    Medium
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDropdownChange('priority', 'low')} className="text-xs">
                    Low
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="industry" className="text-foreground-secondary font-mono text-xs">Industry</Label>
              <Input id="industry" value={formData.industry} onChange={handleChange} placeholder="e.g., Technology, Finance" className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground-secondary font-mono text-xs">Company Size</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary">
                    {formData.companySize.charAt(0).toUpperCase() + formData.companySize.slice(1)}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                  <DropdownMenuItem onClick={() => handleDropdownChange('companySize', 'small')} className="text-xs">
                    Small (1-50 employees)
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDropdownChange('companySize', 'medium')} className="text-xs">
                    Medium (51-500 employees)
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDropdownChange('companySize', 'enterprise')} className="text-xs">
                    Enterprise (500+ employees)
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-foreground-secondary font-mono text-xs">Favorite Client</Label>
            <Button
              type="button"
              variant="outline"
              onClick={handleStarredToggle}
              className={cn(
                "w-full justify-start bg-background border-border/50 focus-visible:border-neon-primary",
                formData.starred && "border-neon-warning/50 bg-neon-warning/5"
              )}
            >
              <Star className={cn("h-4 w-4 mr-2", formData.starred ? "text-neon-warning fill-current" : "text-foreground-muted")} />
              {formData.starred ? "Starred Client" : "Mark as Starred"}
            </Button>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes" className="text-foreground-secondary font-mono text-xs">Notes</Label>
            <Textarea id="notes" value={formData.notes} onChange={handleChange} placeholder="Any additional notes about the client..." rows={3} className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" />
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 border-t border-border/30 pt-4">
          <Button variant="ghost" onClick={() => setIsOpen(false)}>Cancel</Button>
          <Button onClick={handleSubmit} className="bg-neon-primary hover:bg-neon-primary/90 text-background">
            <Save className="h-4 w-4 mr-2" />
            {editClient ? 'Update Client' : 'Save Client'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
