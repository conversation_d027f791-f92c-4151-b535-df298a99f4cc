'use client'

import { useEffect, useState } from 'react'

interface CountingBalanceProps {
  value: number
  duration?: number
  currency?: string
  className?: string
  prefix?: string
  suffix?: string
}

export function CountingBalance({ 
  value, 
  duration = 1000, 
  currency = 'USD', 
  className = '',
  prefix = '',
  suffix = ''
}: CountingBalanceProps) {
  const [count, setCount] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    setIsAnimating(true)
    let startTime: number
    let animationFrame: number

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp
      const progress = Math.min((timestamp - startTime) / duration, 1)
      
      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3)
      const currentValue = easeOutCubic * value
      
      setCount(currentValue)
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      } else {
        setIsAnimating(false)
      }
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [value, duration])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  const formatNumber = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  return (
    <span className={`${className} ${isAnimating ? 'animate-text-glow' : ''} transition-all duration-300`}>
      {prefix}
      {currency ? formatCurrency(count) : formatNumber(count)}
      {suffix}
    </span>
  )
}

// Enhanced metrics card with counting animation
interface MetricsCardProps {
  title: string
  value: number
  change?: string
  trend?: 'up' | 'down' | 'neutral'
  icon?: React.ReactNode
  className?: string
  currency?: string
  prefix?: string
  suffix?: string
}

export function MetricsCard({ 
  title, 
  value, 
  change, 
  trend = 'neutral', 
  icon, 
  className = '',
  currency = 'USD',
  prefix = '',
  suffix = ''
}: MetricsCardProps) {
  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-neon-success'
      case 'down': return 'text-neon-error'
      default: return 'text-foreground'
    }
  }

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return '↗'
      case 'down': return '↘'
      default: return '→'
    }
  }

  return (
    <div className={`
      bg-gradient-to-br from-black/60 to-neon-primary/5 
      border border-neon-primary/20 hover:border-neon-primary/40 
      rounded-lg p-6 transition-all duration-300 
      hover:-translate-y-1 hover:shadow-lg hover:shadow-neon-primary/10
      group relative overflow-hidden
      ${className}
    `}>
      {/* Neon accent line */}
      <div className="absolute top-0 left-0 h-full w-px bg-gradient-to-b from-neon-primary/50 via-neon-primary/20 to-transparent" />
      
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {icon && (
            <div className="p-2 rounded-lg bg-neon-primary/20 text-neon-primary transition-all group-hover:scale-110">
              {icon}
            </div>
          )}
          <div>
            <h3 className="text-neon-electric font-mono tracking-wider uppercase text-xs">
              {title}
            </h3>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <CountingBalance
          value={value}
          currency={currency}
          prefix={prefix}
          suffix={suffix}
          className="text-3xl font-bold text-foreground font-mono"
        />
        
        {change && (
          <div className={`flex items-center space-x-1 ${getTrendColor()}`}>
            <span className="font-mono text-sm">{getTrendIcon()}</span>
            <span className="font-mono text-sm">{change}</span>
          </div>
        )}
      </div>

      {/* Subtle glow effect on hover */}
      <div className="absolute inset-0 bg-neon-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </div>
  )
}