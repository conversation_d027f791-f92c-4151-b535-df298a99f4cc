"use client";

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

const codeSnippets = [
  'const app = () => {}',
  'function build() {}',
  'npm install',
  'git commit -m',
  'docker run',
  'yarn dev',
  'console.log()',
  'import React',
  'useState()',
  'useEffect()',
  'async/await',
  'fetch()',
];

const languages = [
  { color: '#61DAFB', name: '<PERSON>act' },
  { color: '#F7DF1E', name: 'JavaScript' },
  { color: '#3776AB', name: 'Python' },
  { color: '#E34F26', name: 'HTML' },
  { color: '#1572B6', name: 'CSS' },
  { color: '#339933', name: 'Node.js' },
];

interface FloatingCodeBlockProps {
  text: string;
  color: string;
  delay: number;
  duration: number;
  x: number;
  y: number;
}

function FloatingCodeBlock({ text, color, delay, duration, x, y }: FloatingCodeBlockProps) {
  return (
    <motion.div
      className="absolute px-3 py-2 rounded-lg backdrop-blur-sm border"
      style={{
        backgroundColor: `${color}20`,
        borderColor: `${color}40`,
        color: color,
        left: `${x}%`,
        top: `${y}%`,
      }}
      initial={{ opacity: 0, scale: 0.8, rotateY: -90 }}
      animate={{
        opacity: [0, 1, 1, 0],
        scale: [0.8, 1, 1, 0.8],
        rotateY: [-90, 0, 0, 90],
        y: [-20, 0, 0, -20],
      }}
      transition={{
        duration: duration,
        delay: delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      <code className="text-sm font-mono">{text}</code>
    </motion.div>
  );
}

export function FloatingCodeElements() {
  const [blocks, setBlocks] = useState<Array<{
    id: number;
    text: string;
    color: string;
    delay: number;
    duration: number;
    x: number;
    y: number;
  }>>([]);

  useEffect(() => {
    const newBlocks = Array.from({ length: 12 }, (_, i) => {
      const snippet = codeSnippets[i % codeSnippets.length];
      const language = languages[i % languages.length];
      
      return {
        id: i,
        text: snippet,
        color: language.color,
        delay: i * 0.5,
        duration: 4 + Math.random() * 2,
        x: 10 + (i % 4) * 20 + Math.random() * 10,
        y: 10 + Math.floor(i / 4) * 25 + Math.random() * 10,
      };
    });
    
    setBlocks(newBlocks);
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {blocks.map((block) => (
        <FloatingCodeBlock
          key={block.id}
          text={block.text}
          color={block.color}
          delay={block.delay}
          duration={block.duration}
          x={block.x}
          y={block.y}
        />
      ))}
    </div>
  );
}