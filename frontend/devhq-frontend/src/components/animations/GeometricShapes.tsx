"use client";

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface Shape {
  id: number;
  type: 'square' | 'circle' | 'triangle' | 'diamond';
  x: number;
  y: number;
  size: number;
  color: string;
  rotation: number;
  duration: number;
  delay: number;
}

function ShapeElement({ shape }: { shape: Shape }) {
  const getShapeStyles = () => {
    const baseStyles = {
      width: shape.size,
      height: shape.size,
      backgroundColor: shape.color,
      position: 'absolute' as const,
      left: `${shape.x}%`,
      top: `${shape.y}%`,
    };

    switch (shape.type) {
      case 'circle':
        return { ...baseStyles, borderRadius: '50%' };
      case 'triangle':
        return {
          ...baseStyles,
          backgroundColor: 'transparent',
          borderLeft: `${shape.size / 2}px solid transparent`,
          borderRight: `${shape.size / 2}px solid transparent`,
          borderBottom: `${shape.size}px solid ${shape.color}`,
          width: 0,
          height: 0,
        };
      case 'diamond':
        return { ...baseStyles, transform: 'rotate(45deg)' };
      default:
        return baseStyles;
    }
  };

  return (
    <motion.div
      style={getShapeStyles()}
      animate={{
        rotate: [shape.rotation, shape.rotation + 360],
        scale: [0.8, 1.2, 0.8],
        opacity: [0.4, 0.8, 0.4],
        y: [-10, 10, -10],
      }}
      transition={{
        duration: shape.duration,
        delay: shape.delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    />
  );
}

export function GeometricShapes({ shapeCount = 15 }: { shapeCount?: number }) {
  const [shapes, setShapes] = useState<Shape[]>([]);

  useEffect(() => {
    const colors = ['#00ff88', '#00ffff', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
    const types: Shape['type'][] = ['square', 'circle', 'triangle', 'diamond'];
    
    const newShapes = Array.from({ length: shapeCount }, (_, i) => ({
      id: i,
      type: types[Math.floor(Math.random() * types.length)],
      x: Math.random() * 90,
      y: Math.random() * 90,
      size: 20 + Math.random() * 40,
      color: colors[Math.floor(Math.random() * colors.length)] + '40',
      rotation: Math.random() * 360,
      duration: 4 + Math.random() * 6,
      delay: Math.random() * 3,
    }));
    
    setShapes(newShapes);
  }, [shapeCount]);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {shapes.map((shape) => (
        <ShapeElement key={shape.id} shape={shape} />
      ))}
    </div>
  );
}