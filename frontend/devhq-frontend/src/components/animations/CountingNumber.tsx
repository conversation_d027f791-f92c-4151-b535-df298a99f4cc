"use client";

import { motion, useInView } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';

interface CountingNumberProps {
  target: number;
  duration?: number;
  className?: string;
  suffix?: string;
}

export function CountingNumber({ 
  target, 
  duration = 3, 
  className = "",
  suffix = "+"
}: CountingNumberProps) {
  const ref = useRef<HTMLSpanElement>(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [currentNumber, setCurrentNumber] = useState(0);

  useEffect(() => {
    if (isInView) {
      let startTime: number;
      let animationFrame: number;

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);
        
        // Easing function for smooth animation
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(easeOut * target);
        
        setCurrentNumber(current);

        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      animationFrame = requestAnimationFrame(animate);

      return () => {
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }
      };
    }
  }, [isInView, target, duration]);

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(num >= 10000 ? 0 : 1) + 'K';
    }
    return num.toString();
  };

  return (
    <motion.span
      ref={ref}
      className={className}
      style={{
        background: "linear-gradient(90deg, #00ff88, #00ffff, #ff6b6b, #ffff00, #00ff88)",
        backgroundSize: "400% 400%",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text",
      }}
      animate={{ 
        backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
        scale: isInView ? [1, 1.05, 1] : 1,
      }}
      transition={{ 
        backgroundPosition: { duration: 4, repeat: Infinity, ease: "linear" },
        scale: { duration: 0.5, delay: duration }
      }}
    >
      <motion.span
        animate={isInView ? {
          textShadow: [
            "0 0 10px rgba(0, 255, 136, 0.3)",
            "0 0 20px rgba(0, 255, 136, 0.6)",
            "0 0 30px rgba(0, 255, 136, 0.4)",
            "0 0 10px rgba(0, 255, 136, 0.3)"
          ]
        } : {}}
        transition={{ 
          duration: 2, 
          repeat: Infinity,
          delay: duration + 0.5
        }}
      >
        <motion.span>{formatNumber(currentNumber)}</motion.span>
        {suffix}
      </motion.span>
      
      {/* Animated underline that appears after counting */}
      <motion.div
        className="absolute -bottom-2 left-0 h-1 bg-gradient-to-r from-neon-electric via-cyan-400 to-neon-electric rounded-full"
        initial={{ width: 0, opacity: 0 }}
        animate={isInView ? { 
          width: "100%", 
          opacity: 1,
          backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
        } : {}}
        transition={{ 
          width: { duration: 0.8, delay: duration + 0.2 },
          opacity: { duration: 0.5, delay: duration + 0.2 },
          backgroundPosition: { duration: 3, repeat: Infinity, ease: "linear", delay: duration + 1 }
        }}
      />
      
      {/* Sparkle effects that appear during counting */}
      {Array.from({ length: 6 }, (_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-neon-electric rounded-full"
          style={{
            left: `${20 + i * 15}%`,
            top: `${-10 + (i % 2) * 20}%`,
          }}
          initial={{ scale: 0, opacity: 0 }}
          animate={isInView ? {
            scale: [0, 1, 0],
            opacity: [0, 1, 0],
            y: [0, -10, -20],
          } : {}}
          transition={{
            duration: 1,
            delay: (duration * i) / 6,
            ease: "easeOut"
          }}
        />
      ))}
    </motion.span>
  );
}