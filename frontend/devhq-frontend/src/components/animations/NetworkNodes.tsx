/** @format */

"use client";

import { motion } from "framer-motion";
import {
  SiGith<PERSON>,
  SiVercel,
  SiR<PERSON>ct,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Figma,
  <PERSON><PERSON>la<PERSON>,
} from "react-icons/si";

interface Node {
  id: string;
  x: number;
  y: number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  label: string;
}

interface Connection {
  from: string;
  to: string;
}

function NetworkNode({ node, isCenter }: { node: Node; isCenter?: boolean }) {
  const IconComponent = node.icon;

  return (
    <motion.div
      className="absolute flex flex-col items-center"
      style={{
        left: `${node.x}%`,
        top: `${node.y}%`,
        transform: "translate(-50%, -50%)",
      }}
      animate={{
        scale: isCenter ? [1, 1.2, 1] : [0.8, 1, 0.8],
        rotate: isCenter ? [0, 360] : [0, 180, 0],
      }}
      transition={{
        duration: isCenter ? 4 : 6,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      <motion.div
        className="relative p-4 rounded-full backdrop-blur-sm border-2"
        style={{
          backgroundColor: `${node.color}20`,
          borderColor: `${node.color}60`,
        }}
        whileHover={{ scale: 1.1 }}
      >
        <div style={{ color: node.color }}>
          <IconComponent className="w-8 h-8" />
        </div>

        {/* Glow effect */}
        <motion.div
          className="absolute inset-0 rounded-full"
          style={{
            backgroundColor: node.color,
            filter: "blur(10px)",
            opacity: 0.3,
          }}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </motion.div>

      <motion.span
        className="mt-2 text-xs font-medium text-white"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        {node.label}
      </motion.span>
    </motion.div>
  );
}

function ConnectionLine({
  from,
  to,
  nodes,
}: {
  from: string;
  to: string;
  nodes: Node[];
}) {
  const fromNode = nodes.find((n) => n.id === from);
  const toNode = nodes.find((n) => n.id === to);

  if (!fromNode || !toNode) return null;

  const x1 = fromNode.x;
  const y1 = fromNode.y;
  const x2 = toNode.x;
  const y2 = toNode.y;

  const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  const angle = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;

  return (
    <motion.div
      className="absolute origin-left"
      style={{
        left: `${x1}%`,
        top: `${y1}%`,
        width: `${length}%`,
        height: "2px",
        backgroundColor: "#00ff88",
        transform: `rotate(${angle}deg)`,
        transformOrigin: "0 50%",
      }}
      initial={{ scaleX: 0, opacity: 0 }}
      animate={{
        scaleX: 1,
        opacity: [0.3, 0.8, 0.3],
      }}
      transition={{
        scaleX: { duration: 1, delay: 0.5 },
        opacity: { duration: 2, repeat: Infinity, ease: "easeInOut" },
      }}
    >
      {/* Data flow animation */}
      <motion.div
        className="absolute w-2 h-2 bg-cyan-400 rounded-full"
        style={{ top: "-1px" }}
        animate={{
          x: ["0%", "100%"],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "linear",
        }}
      />
    </motion.div>
  );
}

export function NetworkNodes() {
  const nodes: Node[] = [
    {
      id: "devhq",
      x: 50,
      y: 50,
      icon: () => (
        <div className="w-8 h-8 bg-gradient-to-r from-cyan-400 to-green-400 rounded-full flex items-center justify-center text-black font-bold">
          D
        </div>
      ),
      color: "#00ff88",
      label: "DevHQ",
    },
    {
      id: "github",
      x: 20,
      y: 20,
      icon: SiGithub,
      color: "#333333",
      label: "GitHub",
    },
    {
      id: "vercel",
      x: 80,
      y: 20,
      icon: SiVercel,
      color: "#000000",
      label: "Vercel",
    },
    {
      id: "react",
      x: 20,
      y: 80,
      icon: SiReact,
      color: "#61DAFB",
      label: "React",
    },
    {
      id: "docker",
      x: 80,
      y: 80,
      icon: SiDocker,
      color: "#2496ED",
      label: "Docker",
    },
    {
      id: "figma",
      x: 15,
      y: 50,
      icon: SiFigma,
      color: "#F24E1E",
      label: "Figma",
    },
    {
      id: "slack",
      x: 85,
      y: 50,
      icon: SiSlack,
      color: "#4A154B",
      label: "Slack",
    },
  ];

  const connections: Connection[] = [
    { from: "devhq", to: "github" },
    { from: "devhq", to: "vercel" },
    { from: "devhq", to: "react" },
    { from: "devhq", to: "docker" },
    { from: "devhq", to: "figma" },
    { from: "devhq", to: "slack" },
  ];

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Connection lines */}
      {connections.map((connection, index) => (
        <ConnectionLine
          key={index}
          from={connection.from}
          to={connection.to}
          nodes={nodes}
        />
      ))}

      {/* Nodes */}
      {nodes.map((node) => (
        <NetworkNode key={node.id} node={node} isCenter={node.id === "devhq"} />
      ))}
    </div>
  );
}
