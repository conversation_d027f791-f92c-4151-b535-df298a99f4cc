/** @format */

"use client";

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import { authApi } from "@/lib/api";

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  avatar_url: string | null;
  phone: string | null;
  bio: string | null;
  is_active: boolean;
  is_verified: boolean;
  oauth_provider: string | null;
  oauth_id: string | null;
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    phone?: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // Define protected routes
  const protectedRoutes = [
    "/dashboard",
    "/projects",
    "/clients",
    "/time",
    "/invoices",
  ];
  const authRoutes = ["/login", "/register", "/forgot-password"];

  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== "undefined") {
      // Check if user is already authenticated
      const initializeAuth = async () => {
        const token = localStorage.getItem("access_token");
        if (token) {
          try {
            await refreshUser();
          } catch (error) {
            // If token is invalid, it will be cleared by the API client
            setUser(null);
          }
        }
        setIsLoading(false);
      };

      initializeAuth();
    } else {
      // Server-side, set loading to false
      setIsLoading(false);
    }
  }, []);

  // Handle route protection
  useEffect(() => {
    if (!isLoading && typeof window !== "undefined") {
      const isProtectedRoute = protectedRoutes.some((route) =>
        pathname.startsWith(route)
      );
      const isAuthRoute = authRoutes.includes(pathname);

      console.log("🛡️ Route protection check:", {
        pathname,
        isProtectedRoute,
        isAuthRoute,
        user: user ? "logged in" : "not logged in",
        isLoading,
      });

      // If accessing a protected route without authentication, redirect to login
      if (isProtectedRoute && !user) {
        console.log("🚫 Redirecting to login - protected route without auth");
        router.push("/login");
      }

      // If accessing auth routes while logged in, redirect to dashboard
      if (isAuthRoute && user) {
        console.log("✅ Redirecting to dashboard - auth route with user");
        router.push("/dashboard");
      }
    }
  }, [isLoading, user, pathname, router]);

  const refreshUser = async () => {
    try {
      const userData = await authApi.getCurrentUser();
      setUser(userData);
    } catch (error) {
      throw error;
    }
  };

  const login = async (email: string, password: string) => {
    try {
      console.log("🔐 Starting login process...");
      const response = await authApi.login(email, password);
      console.log("✅ Login API response:", response);

      // Set user (tokens are handled automatically by the API client)
      setUser(response.user);
      console.log("👤 User set in context:", response.user);
    } catch (error) {
      console.error("❌ Login error in auth provider:", error);
      throw error;
    }
  };

  const register = async (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    phone?: string;
  }) => {
    try {
      const response = await authApi.register(userData);
      // Don't set user - they need to verify email first
      // Response now contains a message instead of user/tokens
      return response;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    await authApi.logout();
    setUser(null);
    router.push("/login");
  };

  const value = {
    user,
    isLoading,
    login,
    register,
    logout,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
