'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  X, Calendar, DollarSign, Clock, Users, Star, 
  UserCheck, UserX, CheckCircle2, Archive, TrendingUp,
  TrendingDown, Target, Activity, Building, Edit, Mail,
  Phone, MapPin, Briefcase, Globe, FileText
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ViewClientModalProps {
  isOpen: boolean
  onClose: () => void
  onEdit: () => void
  client: any
}

const statusConfig = {
  active: { 
    label: 'Active', 
    color: 'text-neon-success', 
    bg: 'bg-neon-success/10', 
    border: 'border-neon-success/30',
    icon: UserCheck
  },
  inactive: { 
    label: 'Inactive', 
    color: 'text-neon-warning', 
    bg: 'bg-neon-warning/10', 
    border: 'border-neon-warning/30',
    icon: UserX
  },
  completed: { 
    label: 'Completed', 
    color: 'text-neon-info', 
    bg: 'bg-neon-info/10', 
    border: 'border-neon-info/30',
    icon: CheckCircle2
  },
  archived: { 
    label: 'Archived', 
    color: 'text-foreground-muted', 
    bg: 'bg-foreground-muted/10', 
    border: 'border-foreground-muted/30',
    icon: Archive
  }
}

const priorityConfig = {
  high: { label: 'High', color: 'text-neon-error', bg: 'bg-neon-error/10' },
  medium: { label: 'Medium', color: 'text-neon-warning', bg: 'bg-neon-warning/10' },
  low: { label: 'Low', color: 'text-neon-info', bg: 'bg-neon-info/10' }
}

export function ViewClientModal({ isOpen, onClose, onEdit, client }: ViewClientModalProps) {
  if (!isOpen || !client) return null

  const status = statusConfig[client.status as keyof typeof statusConfig]
  const priority = priorityConfig[client.priority as keyof typeof priorityConfig]
  const StatusIcon = status.icon

  const projectCompletionRate = client.totalProjects > 0 ? ((client.totalProjects - client.activeProjects) / client.totalProjects) * 100 : 0
  const averageProjectValue = client.totalProjects > 0 ? client.totalRevenue / client.totalProjects : 0

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-background/20 backdrop-blur-xl border border-border/40 shadow-2xl">
        {/* Glass reflection effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-60 rounded-lg" />
        
        {/* Glass highlight edge */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
        
        <CardHeader className="border-b border-border/50 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-neon-primary rounded-full animate-pulse" />
              <CardTitle className="text-2xl font-mono text-neon-primary">
                {client.name}
              </CardTitle>
              {client.starred && (
                <Star className="h-5 w-5 text-neon-warning fill-current" />
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={onEdit} className="bg-background/20 backdrop-blur-md border-border/30">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 mt-2">
            <Badge className={cn("text-sm font-mono", status.color, status.bg, status.border)}>
              <StatusIcon className="h-4 w-4 mr-1" />
              {status.label}
            </Badge>
            <Badge variant="outline" className={cn("text-sm", priority.color, priority.bg)}>
              {priority.label} Priority
            </Badge>
            <div className="text-sm text-foreground-muted font-mono">
              Contact: {client.contactPerson}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Building className="h-4 w-4 text-neon-primary" />
                      <div>
                        <p className="text-sm text-foreground-muted">Contact Person</p>
                        <p className="text-foreground font-medium">{client.contactPerson}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-neon-primary" />
                      <div>
                        <p className="text-sm text-foreground-muted">Email</p>
                        <p className="text-foreground font-medium">{client.email}</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-neon-primary" />
                      <div>
                        <p className="text-sm text-foreground-muted">Phone</p>
                        <p className="text-foreground font-medium">{client.phone}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-4 w-4 text-neon-primary mt-1" />
                      <div>
                        <p className="text-sm text-foreground-muted">Address</p>
                        <p className="text-foreground font-medium leading-relaxed">{client.address}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Business Overview */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Business Overview</h3>
                
                {/* Revenue Progress */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-mono text-foreground-muted">Total Revenue</span>
                    <span className="text-lg font-bold text-neon-primary">${client.totalRevenue.toLocaleString()}</span>
                  </div>
                  <div className="w-full bg-background-tertiary rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-neon-primary to-neon-cyan h-3 rounded-full transition-all duration-500 shadow-[0_0_10px_rgba(0,255,136,0.3)]"
                      style={{ width: `${Math.min((client.totalRevenue / 150000) * 100, 100)}%` }}
                    />
                  </div>
                  <div className="text-xs text-foreground-muted font-mono">
                    Average project value: ${averageProjectValue.toLocaleString()}
                  </div>
                </div>

                {/* Project Completion Rate */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-mono text-foreground-muted">Project Completion Rate</span>
                    <span className="text-lg font-bold text-foreground">
                      {projectCompletionRate.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-background-tertiary rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-neon-success to-neon-primary h-3 rounded-full transition-all duration-500"
                      style={{ width: `${projectCompletionRate}%` }}
                    />
                  </div>
                  <div className="text-xs text-foreground-muted font-mono">
                    {client.totalProjects - client.activeProjects} of {client.totalProjects} projects completed
                  </div>
                </div>
              </div>

              {/* Company Details */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-foreground">Company Details</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Briefcase className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Industry</span>
                    </div>
                    <Badge variant="outline" className="bg-background/20 backdrop-blur-md border-border/30">
                      {client.industry}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Company Size</span>
                    </div>
                    <Badge variant="outline" className="bg-background/20 backdrop-blur-md border-border/30">
                      {client.companySize}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {client.notes && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-foreground">Notes</h3>
                  <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
                    <p className="text-foreground-muted leading-relaxed">
                      {client.notes}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Key Metrics */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Target className="h-4 w-4 mr-2" />
                    Key Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Joined Date</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {new Date(client.joinedDate).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Last Contact</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {client.lastContact}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Briefcase className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Total Projects</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {client.totalProjects}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Active Projects</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {client.activeProjects}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Project History */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Activity className="h-4 w-4 mr-2" />
                    Project History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-background/20 rounded-lg border border-border/20">
                      <div>
                        <p className="text-sm font-medium text-foreground">Active Projects</p>
                        <p className="text-xs text-foreground-muted">Currently in progress</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-neon-success">{client.activeProjects}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-background/20 rounded-lg border border-border/20">
                      <div>
                        <p className="text-sm font-medium text-foreground">Completed Projects</p>
                        <p className="text-xs text-foreground-muted">Successfully delivered</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-neon-info">{client.totalProjects - client.activeProjects}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Globe className="h-4 w-4 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <Phone className="h-4 w-4 mr-2" />
                    Call Client
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <Briefcase className="h-4 w-4 mr-2" />
                    New Project
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}