/** @format */

"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Save, ChevronDown, Calendar, DollarSign, Star } from "lucide-react";
import { cn } from "@/lib/utils";

interface EditInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (invoiceData: any) => void;
  invoice: any;
  clients?: any[];
}

const statusOptions = [
  { value: "draft", label: "Draft" },
  { value: "pending", label: "Pending" },
  { value: "paid", label: "Paid" },
  { value: "overdue", label: "Overdue" },
  { value: "cancelled", label: "Cancelled" },
];

export function EditInvoiceModal({
  isOpen,
  onClose,
  onSave,
  invoice,
  clients = [],
}: EditInvoiceModalProps) {
  const [formData, setFormData] = useState({
    clientName: "",
    clientEmail: "",
    projectName: "",
    description: "",
    hoursWorked: "",
    hourlyRate: "",
    taxAmount: "",
    dueDate: "",
    paidDate: "",
    paymentMethod: "",
    notes: "",
    status: "draft",
    starred: false,
  });

  useEffect(() => {
    if (invoice && isOpen) {
      setFormData({
        clientName: invoice.clientName || "",
        clientEmail: invoice.clientEmail || "",
        projectName: invoice.projectName || "",
        description: invoice.description || "",
        hoursWorked: invoice.hoursWorked?.toString() || "",
        hourlyRate: invoice.hourlyRate?.toString() || "",
        taxAmount: invoice.taxAmount?.toString() || "",
        dueDate: invoice.dueDate || "",
        paidDate: invoice.paidDate || "",
        paymentMethod: invoice.paymentMethod || "",
        notes: invoice.notes || "",
        status: invoice.status || "draft",
        starred: invoice.starred || false,
      });
    }
  }, [invoice, isOpen]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleDropdownChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleStarredToggle = () => {
    setFormData((prev) => ({ ...prev, starred: !prev.starred }));
  };

  const handleSubmit = () => {
    const amount =
      parseFloat(formData.hoursWorked) * parseFloat(formData.hourlyRate);
    const taxAmount = parseFloat(formData.taxAmount) || 0;
    const totalAmount = amount + taxAmount;

    const updatedInvoice = {
      ...invoice,
      ...formData,
      amount,
      taxAmount,
      totalAmount,
      hoursWorked: parseFloat(formData.hoursWorked),
      hourlyRate: parseFloat(formData.hourlyRate),
    };

    onSave(updatedInvoice);
    onClose();
  };

  const amount =
    parseFloat(formData.hoursWorked || "0") *
    parseFloat(formData.hourlyRate || "0");
  const taxAmount = parseFloat(formData.taxAmount || "0");
  const totalAmount = amount + taxAmount;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-background-elevated/95 backdrop-blur-md border-border text-foreground max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-neon-primary font-mono">
            Edit Invoice - {invoice?.invoiceNumber}
          </DialogTitle>
          <DialogDescription className="text-foreground-muted">
            Update invoice details and information.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4 overflow-y-auto flex-1 pr-2">
          {/* Client Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-foreground-secondary font-mono text-xs">
                Client Name *
              </Label>
              {clients.length > 0 ?
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary"
                    >
                      {formData.clientName || "Select Client"}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                    {clients.map((client) => (
                      <DropdownMenuItem
                        key={client.id}
                        onClick={() => {
                          handleDropdownChange("clientName", client.name);
                          handleDropdownChange("clientEmail", client.email);
                        }}
                        className="text-xs"
                      >
                        {client.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              : <Input
                  id="clientName"
                  value={formData.clientName}
                  onChange={handleChange}
                  placeholder="Enter client name"
                  className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              }
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="clientEmail"
                className="text-foreground-secondary font-mono text-xs"
              >
                Client Email *
              </Label>
              <Input
                id="clientEmail"
                type="email"
                value={formData.clientEmail}
                onChange={handleChange}
                placeholder="<EMAIL>"
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
          </div>

          {/* Project and Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="projectName"
                className="text-foreground-secondary font-mono text-xs"
              >
                Project Name *
              </Label>
              <Input
                id="projectName"
                value={formData.projectName}
                onChange={handleChange}
                placeholder="e.g., Website Development"
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground-secondary font-mono text-xs">
                Status
              </Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary"
                  >
                    {statusOptions.find((s) => s.value === formData.status)
                      ?.label || "Select Status"}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                  {statusOptions.map((status) => (
                    <DropdownMenuItem
                      key={status.value}
                      onClick={() =>
                        handleDropdownChange("status", status.value)
                      }
                      className="text-xs"
                    >
                      {status.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label
              htmlFor="description"
              className="text-foreground-secondary font-mono text-xs"
            >
              Description *
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Describe the work performed..."
              rows={3}
              className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* Line Items */}
          <div className="space-y-2">
            <Label className="text-foreground-secondary font-mono text-xs">
              Line Items
            </Label>
            <div className="text-sm text-foreground-secondary">
              Line items editing will be available in a future update.
            </div>
          </div>

          {/* Tax and Due Date */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="taxAmount"
                className="text-foreground-secondary font-mono text-xs"
              >
                Tax Amount ($)
              </Label>
              <Input
                id="taxAmount"
                type="number"
                step="0.01"
                value={formData.taxAmount}
                onChange={handleChange}
                placeholder="e.g., 150.00"
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="dueDate"
                className="text-foreground-secondary font-mono text-xs"
              >
                Due Date *
              </Label>
              <Input
                id="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={handleChange}
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
          </div>

          {/* Payment Information (if paid) */}
          {formData.status === "paid" && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="paidDate"
                  className="text-foreground-secondary font-mono text-xs"
                >
                  Paid Date
                </Label>
                <Input
                  id="paidDate"
                  type="date"
                  value={formData.paidDate}
                  onChange={handleChange}
                  className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="paymentMethod"
                  className="text-foreground-secondary font-mono text-xs"
                >
                  Payment Method
                </Label>
                <Input
                  id="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleChange}
                  placeholder="e.g., Bank Transfer, PayPal"
                  className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
            </div>
          )}

          {/* Amount Calculation Preview */}
          {formData.hoursWorked && formData.hourlyRate && (
            <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
              <h4 className="text-sm font-mono text-neon-primary mb-3">
                Invoice Summary
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-foreground-muted">
                    Subtotal ({formData.hoursWorked}h × ${formData.hourlyRate}
                    /h):
                  </span>
                  <span className="font-mono text-foreground">
                    ${amount.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Tax:</span>
                  <span className="font-mono text-foreground">
                    ${taxAmount.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between border-t border-border/30 pt-2">
                  <span className="font-semibold text-foreground">
                    Total Amount:
                  </span>
                  <span className="font-mono text-neon-primary font-bold">
                    ${totalAmount.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Starred Toggle */}
          <div className="space-y-2">
            <Label className="text-foreground-secondary font-mono text-xs">
              Priority Invoice
            </Label>
            <Button
              type="button"
              variant="outline"
              onClick={handleStarredToggle}
              className={cn(
                "w-full justify-start bg-background border-border/50 focus-visible:border-neon-primary",
                formData.starred && "border-neon-warning/50 bg-neon-warning/5"
              )}
            >
              <Star
                className={cn(
                  "h-4 w-4 mr-2",
                  formData.starred ?
                    "text-neon-warning fill-current"
                  : "text-foreground-muted"
                )}
              />
              {formData.starred ? "Priority Invoice" : "Mark as Priority"}
            </Button>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label
              htmlFor="notes"
              className="text-foreground-secondary font-mono text-xs"
            >
              Notes
            </Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Additional notes or payment terms..."
              rows={3}
              className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 border-t border-border/30 pt-4">
          <Button variant="ghost" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="bg-neon-primary hover:bg-neon-primary/90 text-background"
            disabled={
              !formData.clientName ||
              !formData.projectName ||
              !formData.description ||
              !formData.dueDate
            }
          >
            <Save className="h-4 w-4 mr-2" />
            Update Invoice
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
