'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  X, Calendar, DollarSign, Clock, FileText, Building, 
  CheckCircle2, AlertCircle, XCircle, Edit, Download,
  Send, Mail, Phone, User, CreditCard, Hash
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ViewInvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onEdit: () => void
  invoice: any
}

const statusConfig = {
  paid: { 
    label: 'Paid', 
    color: 'text-neon-success', 
    bg: 'bg-neon-success/10', 
    border: 'border-neon-success/30',
    icon: CheckCircle2
  },
  pending: { 
    label: 'Pending', 
    color: 'text-neon-warning', 
    bg: 'bg-neon-warning/10', 
    border: 'border-neon-warning/30',
    icon: Clock
  },
  overdue: { 
    label: 'Overdue', 
    color: 'text-neon-error', 
    bg: 'bg-neon-error/10', 
    border: 'border-neon-error/30',
    icon: AlertCircle
  },
  draft: { 
    label: 'Draft', 
    color: 'text-neon-info', 
    bg: 'bg-neon-info/10', 
    border: 'border-neon-info/30',
    icon: FileText
  },
  cancelled: { 
    label: 'Cancelled', 
    color: 'text-foreground-muted', 
    bg: 'bg-foreground-muted/10', 
    border: 'border-foreground-muted/30',
    icon: XCircle
  }
}

export function ViewInvoiceModal({ isOpen, onClose, onEdit, invoice }: ViewInvoiceModalProps) {
  if (!isOpen || !invoice) return null

  const status = statusConfig[invoice.status as keyof typeof statusConfig]
  const StatusIcon = status.icon

  const isOverdue = invoice.status === 'overdue'
  const daysSinceIssue = Math.floor((new Date().getTime() - new Date(invoice.issueDate).getTime()) / (1000 * 3600 * 24))

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-background/20 backdrop-blur-xl border border-border/40 shadow-2xl">
        {/* Glass reflection effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-60 rounded-lg" />
        
        {/* Glass highlight edge */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
        
        <CardHeader className="border-b border-border/50 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-neon-primary rounded-full animate-pulse" />
              <CardTitle className="text-2xl font-mono text-neon-primary">
                {invoice.invoiceNumber}
              </CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="bg-background/20 backdrop-blur-md border-border/30">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              {invoice.status === 'draft' && (
                <Button variant="outline" size="sm" className="bg-background/20 backdrop-blur-md border-border/30">
                  <Send className="h-4 w-4 mr-2" />
                  Send Invoice
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={onEdit} className="bg-background/20 backdrop-blur-md border-border/30">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 mt-2">
            <Badge className={cn("text-sm font-mono", status.color, status.bg, status.border)}>
              <StatusIcon className="h-4 w-4 mr-1" />
              {status.label}
            </Badge>
            <div className="text-sm text-foreground-muted font-mono">
              Total: ${invoice.totalAmount.toLocaleString()}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Invoice Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Invoice Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Client Information */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-mono text-neon-primary">Bill To:</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-foreground-muted" />
                        <span className="font-medium text-foreground">{invoice.clientName}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-foreground-muted" />
                        <span className="text-foreground-muted">{invoice.clientEmail}</span>
                      </div>
                    </div>
                  </div>

                  {/* Invoice Information */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-mono text-neon-primary">Invoice Info:</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Hash className="h-4 w-4 text-foreground-muted" />
                        <span className="text-foreground-muted">Number:</span>
                        <span className="font-mono text-foreground">{invoice.invoiceNumber}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-foreground-muted" />
                        <span className="text-foreground-muted">Issue Date:</span>
                        <span className="font-mono text-foreground">{new Date(invoice.issueDate).toLocaleDateString('en-US')}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-foreground-muted" />
                        <span className="text-foreground-muted">Due Date:</span>
                        <span className={cn(
                          "font-mono",
                          isOverdue ? "text-neon-error" : "text-foreground"
                        )}>
                          {new Date(invoice.dueDate).toLocaleDateString('en-US')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Project Description */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-foreground">Project Description</h3>
                <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="h-4 w-4 text-neon-primary" />
                    <span className="font-medium text-foreground">{invoice.projectName}</span>
                  </div>
                  <p className="text-foreground-muted leading-relaxed">
                    {invoice.description}
                  </p>
                </div>
              </div>

              {/* Work Summary */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Work Summary</h3>
                <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg overflow-hidden">
                  <div className="bg-background/30 px-4 py-3 border-b border-border/30">
                    <div className="grid grid-cols-4 gap-4 text-xs font-mono text-foreground-muted uppercase tracking-wider">
                      <span>Description</span>
                      <span className="text-center">Hours</span>
                      <span className="text-center">Rate</span>
                      <span className="text-right">Amount</span>
                    </div>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-4 gap-4 items-center">
                      <span className="text-foreground">{invoice.description}</span>
                      <span className="text-center font-mono text-foreground">{invoice.hoursWorked}h</span>
                      <span className="text-center font-mono text-foreground">${invoice.hourlyRate}/h</span>
                      <span className="text-right font-mono text-foreground">${invoice.amount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Status */}
              {invoice.status === 'paid' && invoice.paidDate && (
                <div className="bg-neon-success/10 border border-neon-success/30 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <CheckCircle2 className="h-5 w-5 text-neon-success" />
                    <div>
                      <p className="font-medium text-neon-success">Payment Received</p>
                      <p className="text-sm text-foreground-muted">
                        Paid on {new Date(invoice.paidDate).toLocaleDateString('en-US')}
                        {invoice.paymentMethod && ` via ${invoice.paymentMethod}`}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Overdue Warning */}
              {isOverdue && (
                <div className="bg-neon-error/10 border border-neon-error/30 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <AlertCircle className="h-5 w-5 text-neon-error" />
                    <div>
                      <p className="font-medium text-neon-error">Payment Overdue</p>
                      <p className="text-sm text-foreground-muted">
                        This invoice is {daysSinceIssue - Math.floor((new Date(invoice.dueDate).getTime() - new Date(invoice.issueDate).getTime()) / (1000 * 3600 * 24))} days overdue
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Notes */}
              {invoice.notes && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-foreground">Notes</h3>
                  <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
                    <p className="text-foreground-muted leading-relaxed">
                      {invoice.notes}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Amount Breakdown */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <DollarSign className="h-4 w-4 mr-2" />
                    Amount Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-foreground-muted">Subtotal</span>
                    <span className="text-sm font-mono text-foreground">${invoice.amount.toFixed(2)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-foreground-muted">Tax</span>
                    <span className="text-sm font-mono text-foreground">${invoice.taxAmount.toFixed(2)}</span>
                  </div>
                  <div className="border-t border-border/30 pt-3">
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-foreground">Total</span>
                      <span className="text-lg font-bold font-mono text-neon-primary">${invoice.totalAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <Mail className="h-4 w-4 mr-2" />
                    Email Client
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                  {invoice.status === 'draft' && (
                    <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                      <Send className="h-4 w-4 mr-2" />
                      Send to Client
                    </Button>
                  )}
                  {invoice.status === 'pending' && (
                    <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                      <CreditCard className="h-4 w-4 mr-2" />
                      Mark as Paid
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}