'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Save, ChevronDown, Calendar, DollarSign } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CreateInvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (invoiceData: any) => void
  clients?: any[]
  projects?: any[]
}

export function CreateInvoiceModal({ isOpen, onClose, onSubmit, clients = [], projects = [] }: CreateInvoiceModalProps) {
  const [formData, setFormData] = useState({
    clientName: '',
    projectName: '',
    description: '',
    hoursWorked: '',
    hourlyRate: '',
    taxRate: '10', // Default 10%
    dueDate: '',
    notes: '',
    status: 'draft'
  })

  useEffect(() => {
    if (!isOpen) {
      // Reset form when modal closes
      setFormData({
        clientName: '',
        projectName: '',
        description: '',
        hoursWorked: '',
        hourlyRate: '',
        taxRate: '10',
        dueDate: '',
        notes: '',
        status: 'draft'
      })
    }
  }, [isOpen])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({ ...prev, [id]: value }))
  }

  const handleDropdownChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = () => {
    const amount = parseFloat(formData.hoursWorked) * parseFloat(formData.hourlyRate)
    const taxAmount = amount * (parseFloat(formData.taxRate) / 100)
    const totalAmount = amount + taxAmount

    const invoiceData = {
      ...formData,
      amount,
      taxAmount,
      totalAmount,
      hoursWorked: parseFloat(formData.hoursWorked),
      hourlyRate: parseFloat(formData.hourlyRate),
      issueDate: new Date().toISOString().split('T')[0],
      invoiceNumber: `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`
    }

    onSubmit(invoiceData)
    onClose()
  }

  const amount = parseFloat(formData.hoursWorked || '0') * parseFloat(formData.hourlyRate || '0')
  const taxAmount = amount * (parseFloat(formData.taxRate || '0') / 100)
  const totalAmount = amount + taxAmount

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-background-elevated/95 backdrop-blur-md border-border text-foreground max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-neon-primary font-mono">
            Create New Invoice
          </DialogTitle>
          <DialogDescription className="text-foreground-muted">
            Generate a new invoice for your client.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4 overflow-y-auto flex-1 pr-2">
          {/* Client and Project Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-foreground-secondary font-mono text-xs">Client *</Label>
              {clients.length > 0 ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary">
                      {formData.clientName || 'Select Client'}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                    {clients.map((client) => (
                      <DropdownMenuItem 
                        key={client.id} 
                        onClick={() => handleDropdownChange('clientName', client.name)} 
                        className="text-xs"
                      >
                        {client.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Input 
                  id="clientName" 
                  value={formData.clientName} 
                  onChange={handleChange} 
                  placeholder="Enter client name" 
                  className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
                />
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="projectName" className="text-foreground-secondary font-mono text-xs">Project Name *</Label>
              <Input 
                id="projectName" 
                value={formData.projectName} 
                onChange={handleChange} 
                placeholder="e.g., Website Development" 
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
              />
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-foreground-secondary font-mono text-xs">Description *</Label>
            <Textarea 
              id="description" 
              value={formData.description} 
              onChange={handleChange} 
              placeholder="Describe the work performed..." 
              rows={3} 
              className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
            />
          </div>

          {/* Hours and Rate */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="hoursWorked" className="text-foreground-secondary font-mono text-xs">Hours Worked *</Label>
              <Input 
                id="hoursWorked" 
                type="number" 
                step="0.5"
                value={formData.hoursWorked} 
                onChange={handleChange} 
                placeholder="e.g., 40" 
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="hourlyRate" className="text-foreground-secondary font-mono text-xs">Hourly Rate ($) *</Label>
              <Input 
                id="hourlyRate" 
                type="number" 
                step="0.01"
                value={formData.hourlyRate} 
                onChange={handleChange} 
                placeholder="e.g., 150" 
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
              />
            </div>
          </div>

          {/* Tax Rate and Due Date */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="taxRate" className="text-foreground-secondary font-mono text-xs">Tax Rate (%)</Label>
              <Input 
                id="taxRate" 
                type="number" 
                step="0.1"
                value={formData.taxRate} 
                onChange={handleChange} 
                placeholder="e.g., 10" 
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="dueDate" className="text-foreground-secondary font-mono text-xs">Due Date *</Label>
              <Input 
                id="dueDate" 
                type="date" 
                value={formData.dueDate} 
                onChange={handleChange} 
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
              />
            </div>
          </div>

          {/* Amount Calculation Preview */}
          {(formData.hoursWorked && formData.hourlyRate) && (
            <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
              <h4 className="text-sm font-mono text-neon-primary mb-3">Invoice Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Subtotal ({formData.hoursWorked}h × ${formData.hourlyRate}/h):</span>
                  <span className="font-mono text-foreground">${amount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Tax ({formData.taxRate}%):</span>
                  <span className="font-mono text-foreground">${taxAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between border-t border-border/30 pt-2">
                  <span className="font-semibold text-foreground">Total Amount:</span>
                  <span className="font-mono text-neon-primary font-bold">${totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="text-foreground-secondary font-mono text-xs">Notes</Label>
            <Textarea 
              id="notes" 
              value={formData.notes} 
              onChange={handleChange} 
              placeholder="Additional notes or payment terms..." 
              rows={3} 
              className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
            />
          </div>
        </div>

        <DialogFooter className="flex-shrink-0 border-t border-border/30 pt-4">
          <Button variant="ghost" onClick={onClose}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            className="bg-neon-primary hover:bg-neon-primary/90 text-background"
            disabled={!formData.clientName || !formData.projectName || !formData.description || !formData.hoursWorked || !formData.hourlyRate || !formData.dueDate}
          >
            <Save className="h-4 w-4 mr-2" />
            Create Invoice
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}