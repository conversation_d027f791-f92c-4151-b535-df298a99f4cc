'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  BarChart3, TrendingUp, TrendingDown, Clock, DollarSign, 
  Target, Calendar, Activity, Zap, Users, FolderOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface TimeAnalyticsProps {
  className?: string
}

// Mock analytics data
const weeklyData = [
  { day: 'Mon', hours: 8.5, earnings: 637.5 },
  { day: 'Tue', hours: 7.2, earnings: 540 },
  { day: 'Wed', hours: 9.1, earnings: 682.5 },
  { day: 'Thu', hours: 6.8, earnings: 510 },
  { day: 'Fri', hours: 8.0, earnings: 600 },
  { day: 'Sat', hours: 3.5, earnings: 262.5 },
  { day: 'Sun', hours: 2.0, earnings: 150 }
]

const projectBreakdown = [
  { project: 'DevHQ Platform', hours: 24.5, percentage: 45, earnings: 1837.5, color: 'neon-primary' },
  { project: 'E-Commerce Redesign', hours: 16.2, percentage: 30, earnings: 1377, color: 'neon-cyan' },
  { project: 'Mobile Banking App', hours: 8.1, percentage: 15, earnings: 769.5, color: 'neon-warning' },
  { project: 'Analytics Dashboard', hours: 5.4, percentage: 10, earnings: 432, color: 'neon-info' }
]

const timeStats = {
  thisWeek: {
    totalHours: 45.1,
    billableHours: 38.7,
    totalEarnings: 3382.5,
    avgHourlyRate: 87.4,
    productivity: 85.8,
    change: {
      hours: 12.5,
      earnings: 15.2,
      productivity: -2.1
    }
  },
  thisMonth: {
    totalHours: 186.4,
    billableHours: 158.2,
    totalEarnings: 13845,
    avgHourlyRate: 87.5,
    completedProjects: 3,
    activeProjects: 5
  }
}

export function TimeAnalytics({ className }: TimeAnalyticsProps) {
  const maxHours = Math.max(...weeklyData.map(d => d.hours))

  return (
    <div className={cn("space-y-6", className)}>
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-border/30 bg-background/20 backdrop-blur-md hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  This Week
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    {timeStats.thisWeek.totalHours}h
                  </p>
                  <div className="flex items-center text-xs font-mono text-neon-success">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{timeStats.thisWeek.change.hours}%
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <Clock className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/30 bg-background/20 backdrop-blur-md hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  Earnings
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    ${(timeStats.thisWeek.totalEarnings / 1000).toFixed(1)}k
                  </p>
                  <div className="flex items-center text-xs font-mono text-neon-success">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{timeStats.thisWeek.change.earnings}%
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <DollarSign className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/30 bg-background/20 backdrop-blur-md hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  Avg Rate
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    ${timeStats.thisWeek.avgHourlyRate}
                  </p>
                  <div className="text-xs font-mono text-foreground-muted">
                    /hour
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <Target className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/30 bg-background/20 backdrop-blur-md hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  Productivity
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    {timeStats.thisWeek.productivity}%
                  </p>
                  <div className="flex items-center text-xs font-mono text-neon-error">
                    <TrendingDown className="h-3 w-3 mr-1" />
                    {timeStats.thisWeek.change.productivity}%
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <Zap className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Weekly Chart */}
      <Card className="border-border/30 bg-background/20 backdrop-blur-md">
        <CardHeader>
          <CardTitle className="text-base font-mono text-neon-primary flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Weekly Overview
            <div className="w-2 h-2 bg-neon-primary rounded-full ml-auto animate-pulse" />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-7 gap-2">
            {weeklyData.map((day, index) => (
              <div key={day.day} className="text-center space-y-2">
                <div className="text-xs font-mono text-foreground-muted">
                  {day.day}
                </div>
                <div className="relative h-32 bg-background-tertiary rounded-md overflow-hidden">
                  <div 
                    className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-neon-primary to-neon-cyan transition-all duration-500 hover:shadow-[0_0_10px_rgba(0,255,136,0.3)]"
                    style={{ height: `${(day.hours / maxHours) * 100}%` }}
                  />
                  <div className="absolute inset-0 flex items-end justify-center pb-1">
                    <span className="text-xs font-mono text-foreground font-bold">
                      {day.hours}h
                    </span>
                  </div>
                </div>
                <div className="text-xs font-mono text-neon-primary">
                  ${day.earnings}
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex items-center justify-between pt-4 border-t border-border/30">
            <div className="text-sm text-foreground-muted">
              Total: {weeklyData.reduce((sum, day) => sum + day.hours, 0)}h
            </div>
            <div className="text-sm font-mono text-neon-primary">
              ${weeklyData.reduce((sum, day) => sum + day.earnings, 0).toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Project Breakdown */}
      <Card className="border-border/30 bg-background/20 backdrop-blur-md">
        <CardHeader>
          <CardTitle className="text-base font-mono text-neon-primary flex items-center">
            <FolderOpen className="h-5 w-5 mr-2" />
            Project Breakdown
            <Badge variant="outline" className="ml-auto text-xs bg-neon-primary/10 border-neon-primary/30 text-neon-primary">
              This Week
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {projectBreakdown.map((project, index) => (
            <div key={project.project} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    "w-3 h-3 rounded-full",
                    project.color === 'neon-primary' && "bg-neon-primary",
                    project.color === 'neon-cyan' && "bg-neon-cyan",
                    project.color === 'neon-warning' && "bg-neon-warning",
                    project.color === 'neon-info' && "bg-neon-info"
                  )} />
                  <span className="font-medium text-foreground">{project.project}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-mono text-foreground">{project.hours}h</span>
                  <span className="text-sm font-mono text-neon-primary">${project.earnings}</span>
                  <span className="text-xs text-foreground-muted w-8">{project.percentage}%</span>
                </div>
              </div>
              <div className="w-full bg-background-tertiary rounded-full h-2">
                <div 
                  className={cn(
                    "h-2 rounded-full transition-all duration-500",
                    project.color === 'neon-primary' && "bg-gradient-to-r from-neon-primary to-neon-primary/80",
                    project.color === 'neon-cyan' && "bg-gradient-to-r from-neon-cyan to-neon-cyan/80",
                    project.color === 'neon-warning' && "bg-gradient-to-r from-neon-warning to-neon-warning/80",
                    project.color === 'neon-info' && "bg-gradient-to-r from-neon-info to-neon-info/80"
                  )}
                  style={{ width: `${project.percentage}%` }}
                />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Monthly Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-border/30 bg-background/20 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-base font-mono text-neon-primary flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Monthly Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-foreground">{timeStats.thisMonth.totalHours}h</div>
                <div className="text-xs text-foreground-muted">Total Hours</div>
              </div>
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-neon-primary">${(timeStats.thisMonth.totalEarnings / 1000).toFixed(1)}k</div>
                <div className="text-xs text-foreground-muted">Total Earnings</div>
              </div>
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-foreground">{timeStats.thisMonth.billableHours}h</div>
                <div className="text-xs text-foreground-muted">Billable Hours</div>
              </div>
              <div className="text-center space-y-1">
                <div className="text-2xl font-bold text-foreground">${timeStats.thisMonth.avgHourlyRate}</div>
                <div className="text-xs text-foreground-muted">Avg Rate</div>
              </div>
            </div>
            
            <div className="pt-4 border-t border-border/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FolderOpen className="h-4 w-4 text-foreground-muted" />
                  <span className="text-sm text-foreground-muted">Active Projects</span>
                </div>
                <span className="text-sm font-mono text-foreground">{timeStats.thisMonth.activeProjects}</span>
              </div>
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-foreground-muted" />
                  <span className="text-sm text-foreground-muted">Completed</span>
                </div>
                <span className="text-sm font-mono text-neon-success">{timeStats.thisMonth.completedProjects}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/30 bg-background/20 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-base font-mono text-neon-primary flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
              <BarChart3 className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
              <DollarSign className="h-4 w-4 mr-2" />
              Export for Billing
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
              <Calendar className="h-4 w-4 mr-2" />
              View Calendar
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
              <Target className="h-4 w-4 mr-2" />
              Set Goals
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}