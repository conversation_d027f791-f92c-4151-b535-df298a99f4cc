'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription, 
  DialogFooter 
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'

interface TimerState {
  task: string
  rate: number
}

interface EditRateModalProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  timer: TimerState
  onSave: (newDetails: { task: string; rate: number }) => void
}

export function EditRateModal({ isOpen, setIsOpen, timer, onSave }: EditRateModalProps) {
  const [task, setTask] = useState(timer.task)
  const [rate, setRate] = useState(timer.rate)

  useEffect(() => {
    setTask(timer.task)
    setRate(timer.rate)
  }, [timer])

  const handleSave = () => {
    onSave({ task, rate: Number(rate) })
    setIsOpen(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="bg-background-elevated/95 backdrop-blur-md border-border text-foreground">
        <DialogHeader>
          <DialogTitle className="text-neon-primary font-mono">Edit Time Entry</DialogTitle>
          <DialogDescription className="text-foreground-muted">
            Update the details and billing rate for this timer.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="task" className="text-foreground-secondary font-mono text-xs">Task / Details</Label>
            <Textarea 
              id="task"
              value={task}
              onChange={(e) => setTask(e.target.value)}
              className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
              placeholder="What are you working on?"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="rate" className="text-foreground-secondary font-mono text-xs">Hourly Rate ($)</Label>
            <Input 
              id="rate"
              type="number"
              value={rate}
              onChange={(e) => setRate(Number(e.target.value))}
              className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="ghost" onClick={() => setIsOpen(false)}>Cancel</Button>
          <Button onClick={handleSave} className="bg-neon-primary hover:bg-neon-primary/90 text-background">Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
