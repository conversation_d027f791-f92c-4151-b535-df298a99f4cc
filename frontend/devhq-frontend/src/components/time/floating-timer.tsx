'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Play, Pause, Square, Timer, Minimize2, Maximize2,
  MoreVertical, Edit, Trash2, DollarSign
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import { EditRateModal } from './edit-rate-modal' // Import the new modal

interface FloatingTimerProps {
  isVisible: boolean
  onClose: () => void
  className?: string
}

interface TimerState {
  isRunning: boolean
  startTime: Date | null
  elapsedTime: number // in seconds
  project: string
  task: string
  client: string
  billable: boolean
  rate: number
}

export function FloatingTimer({ isVisible, onClose, className }: FloatingTimerProps) {
  const [isMinimized, setIsMinimized] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false) // State for the modal
  const [position, setPosition] = useState({ x: 20, y: 20 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  
  const [timer, setTimer] = useState<TimerState>({
    isRunning: false,
    startTime: null,
    elapsedTime: 0,
    project: 'DevHQ Platform',
    task: 'Feature Development',
    client: 'Internal',
    billable: true,
    rate: 75
  })

  // Update timer every second
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (timer.isRunning && timer.startTime) {
      interval = setInterval(() => {
        const now = new Date()
        const elapsed = Math.floor((now.getTime() - timer.startTime!.getTime()) / 1000)
        setTimer(prev => ({ ...prev, elapsedTime: elapsed }))
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [timer.isRunning, timer.startTime])

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const startTimer = () => {
    setTimer(prev => ({
      ...prev,
      isRunning: true,
      startTime: new Date(),
      elapsedTime: 0
    }))
  }

  const pauseTimer = () => {
    setTimer(prev => ({
      ...prev,
      isRunning: false,
      startTime: null
    }))
  }

  const stopTimer = () => {
    setTimer(prev => ({
      ...prev,
      isRunning: false,
      startTime: null,
      elapsedTime: 0
    }))
  }

  const calculateEarnings = () => {
    if (!timer.billable) return 0
    return (timer.elapsedTime / 3600) * timer.rate
  }

  const handleSaveDetails = (newDetails: { task: string; rate: number }) => {
    setTimer(prev => ({ ...prev, ...newDetails }))
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).closest('.timer-header')) {
      setIsDragging(true)
      setDragOffset({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      })
    }
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragOffset])

  if (!isVisible) return null

  return (
    <>
      <div
        className={cn(
          "fixed z-50 select-none",
          isDragging && "cursor-grabbing",
          className
        )}
        style={{ 
          left: position.x, 
          top: position.y,
          transform: isMinimized ? 'scale(0.8)' : 'scale(1)'
        }}
        onMouseDown={handleMouseDown}
      >
        <Card className={cn(
          "bg-background/90 backdrop-blur-xl border transition-all duration-300 shadow-2xl",
          timer.isRunning 
            ? "border-neon-success/40 shadow-[0_0_30px_rgba(0,255,136,0.2)]" 
            : "border-border/40",
          isMinimized ? "w-64" : "w-80",
          isDragging ? "cursor-grabbing" : "cursor-grab"
        )}>
          {/* Glass reflection effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-60 rounded-lg pointer-events-none" />
          
          {/* Header */}
          <div className="timer-header flex items-center justify-between p-3 border-b border-border/30">
            <div className="flex items-center space-x-2">
              <div className={cn(
                "w-2 h-2 rounded-full",
                timer.isRunning ? "bg-neon-success animate-pulse" : "bg-foreground-muted"
              )} />
              <span className="text-sm font-mono text-neon-primary">TIMER</span>
              {timer.billable && (
                <Badge variant="outline" className="text-xs bg-neon-primary/10 border-neon-primary/30 text-neon-primary">
                  Billable
                </Badge>
              )}
            </div>
            
            <div className="flex items-center space-x-1">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6"
                onClick={() => setIsMinimized(!isMinimized)}
              >
                {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-6 w-6">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                  <DropdownMenuItem className="text-xs" onSelect={() => setIsModalOpen(true)}>
                    <Edit className="h-3 w-3 mr-2" />
                    Edit / Set Rate
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-xs text-neon-error" onClick={onClose}>
                    <Trash2 className="h-3 w-3 mr-2" />
                    Close Timer
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <CardContent className="p-4 relative z-10">
            {!isMinimized && (
              <>
                {/* Project Info */}
                <div className="mb-4">
                  <h3 className="font-semibold text-foreground text-sm">{timer.project}</h3>
                  <p className="text-xs text-foreground-muted">{timer.task}</p>
                  <p className="text-xs text-foreground-subtle">{timer.client}</p>
                </div>
              </>
            )}

            {/* Timer Display */}
            <div className="text-center mb-4">
              <div className={cn(
                "text-3xl font-mono font-bold transition-colors",
                timer.isRunning ? "text-neon-success" : "text-foreground"
              )}>
                {formatTime(timer.elapsedTime)}
              </div>
              {timer.billable && timer.elapsedTime > 0 && (
                <div className="text-sm text-neon-primary font-mono">
                  ${calculateEarnings().toFixed(2)} earned
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="flex items-center justify-center space-x-2">
              {!timer.isRunning ? (
                <Button 
                  onClick={startTimer}
                  size="sm"
                  className="bg-neon-success hover:bg-neon-success/90 text-background"
                >
                  <Play className="h-4 w-4 mr-1" />
                  Start
                </Button>
              ) : (
                <Button 
                  onClick={pauseTimer}
                  size="sm"
                  className="bg-neon-warning hover:bg-neon-warning/90 text-background"
                >
                  <Pause className="h-4 w-4 mr-1" />
                  Pause
                </Button>
              )}
              
              <Button 
                onClick={stopTimer}
                size="sm"
                variant="outline"
                className="bg-background/20 backdrop-blur-md border-border/30 hover:border-neon-error/30 hover:text-neon-error"
              >
                <Square className="h-4 w-4 mr-1" />
                Stop
              </Button>
            </div>

            {/* Quick Stats */}
            {!isMinimized && timer.elapsedTime > 0 && (
              <div className="mt-4 pt-4 border-t border-border/30">
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div className="text-center">
                    <div className="text-foreground-muted">Duration</div>
                    <div className="font-mono text-foreground">{formatTime(timer.elapsedTime)}</div>
                  </div>
                  {timer.billable && (
                    <div className="text-center">
                      <div className="text-foreground-muted">Rate</div>
                      <div className="font-mono text-neon-primary">${timer.rate}/hr</div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>

          {/* Pulse animation for running timer */}
          {timer.isRunning && (
            <div className="absolute inset-0 rounded-lg border border-neon-success/30 animate-pulse pointer-events-none" />
          )}
        </Card>
      </div>
      <EditRateModal 
        isOpen={isModalOpen} 
        setIsOpen={setIsModalOpen} 
        timer={timer} 
        onSave={handleSaveDetails} 
      />
    </>
  )
}
