'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  X, Clock, Calendar, DollarSign, ChevronDown, 
  Save, Timer, FolderOpen, User, Target
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface TimeEntryModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (entryData: any) => void
  editEntry?: any
}

const projects = [
  { id: 1, name: 'DevHQ Platform', client: 'Internal', color: 'neon-primary', rate: 75 },
  { id: 2, name: 'E-Commerce Redesign', client: 'TechCorp Inc.', color: 'neon-cyan', rate: 85 },
  { id: 3, name: 'Mobile Banking App', client: 'FinanceFlow', color: 'neon-warning', rate: 95 },
  { id: 4, name: 'Analytics Dashboard', client: 'DataViz Solutions', color: 'neon-info', rate: 80 },
  { id: 5, name: 'AI Chatbot Integration', client: 'CustomerFirst', color: 'neon-purple', rate: 90 }
]

const commonTasks = [
  'Development',
  'Bug Fixing',
  'Code Review',
  'Testing',
  'Documentation',
  'Meeting',
  'Research',
  'Planning',
  'Deployment',
  'Maintenance'
]

export function TimeEntryModal({ isOpen, onClose, onSave, editEntry }: TimeEntryModalProps) {
  const [formData, setFormData] = useState({
    project: '',
    task: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '17:00',
    duration: 480, // minutes
    billable: true,
    rate: 75
  })

  const [selectedProject, setSelectedProject] = useState<any>(null)
  const [durationMode, setDurationMode] = useState<'time' | 'duration'>('time')

  // Initialize form data when editing
  useEffect(() => {
    if (editEntry) {
      const startDate = new Date(editEntry.startTime)
      const endDate = editEntry.endTime ? new Date(editEntry.endTime) : new Date()
      
      setFormData({
        project: editEntry.project,
        task: editEntry.task,
        description: editEntry.description || '',
        date: startDate.toISOString().split('T')[0],
        startTime: startDate.toTimeString().slice(0, 5),
        endTime: endDate.toTimeString().slice(0, 5),
        duration: editEntry.duration,
        billable: editEntry.billable,
        rate: editEntry.rate
      })

      const project = projects.find(p => p.name === editEntry.project)
      setSelectedProject(project)
    } else {
      // Reset form for new entry
      setFormData({
        project: '',
        task: '',
        description: '',
        date: new Date().toISOString().split('T')[0],
        startTime: '09:00',
        endTime: '17:00',
        duration: 480,
        billable: true,
        rate: 75
      })
      setSelectedProject(null)
    }
  }, [editEntry, isOpen])

  // Calculate duration when times change
  useEffect(() => {
    if (durationMode === 'time' && formData.startTime && formData.endTime) {
      const start = new Date(`${formData.date}T${formData.startTime}`)
      const end = new Date(`${formData.date}T${formData.endTime}`)
      
      if (end > start) {
        const diffMs = end.getTime() - start.getTime()
        const diffMinutes = Math.floor(diffMs / (1000 * 60))
        setFormData(prev => ({ ...prev, duration: diffMinutes }))
      }
    }
  }, [formData.startTime, formData.endTime, formData.date, durationMode])

  // Update end time when duration changes
  useEffect(() => {
    if (durationMode === 'duration' && formData.startTime && formData.duration) {
      const start = new Date(`${formData.date}T${formData.startTime}`)
      const end = new Date(start.getTime() + formData.duration * 60 * 1000)
      setFormData(prev => ({ 
        ...prev, 
        endTime: end.toTimeString().slice(0, 5)
      }))
    }
  }, [formData.duration, formData.startTime, formData.date, durationMode])

  const handleProjectSelect = (project: any) => {
    setSelectedProject(project)
    setFormData(prev => ({
      ...prev,
      project: project.name,
      rate: project.rate
    }))
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  const calculateEarnings = () => {
    if (!formData.billable) return 0
    return (formData.duration / 60) * formData.rate
  }

  const handleSubmit = () => {
    const entryData = {
      ...formData,
      client: selectedProject?.client || 'Unknown',
      startTime: new Date(`${formData.date}T${formData.startTime}`).toISOString(),
      endTime: new Date(`${formData.date}T${formData.endTime}`).toISOString(),
      id: editEntry?.id || Date.now()
    }
    
    onSave(entryData)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-background/20 backdrop-blur-xl border border-border/40 shadow-2xl">
        {/* Glass reflection effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-60 rounded-lg" />
        
        {/* Glass highlight edge */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
        
        <CardHeader className="border-b border-border/50 relative z-10">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-mono text-neon-primary flex items-center">
              <Timer className="h-6 w-6 mr-2" />
              {editEntry ? 'Edit Time Entry' : 'Add Time Entry'}
              <div className="w-2 h-2 bg-neon-primary rounded-full ml-2 animate-pulse" />
            </CardTitle>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6 relative z-10">
          <div className="space-y-6">
            {/* Project Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-mono text-foreground">Project *</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="w-full justify-between bg-background/20 backdrop-blur-md border-border/30 text-foreground hover:bg-background/30"
                  >
                    <div className="flex items-center">
                      <FolderOpen className="h-4 w-4 mr-2" />
                      {selectedProject ? selectedProject.name : 'Select a project'}
                    </div>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full bg-background-elevated/95 backdrop-blur-md border border-border">
                  {projects.map((project) => (
                    <DropdownMenuItem 
                      key={project.id}
                      onClick={() => handleProjectSelect(project)}
                      className="text-sm"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div>
                          <div className="font-medium">{project.name}</div>
                          <div className="text-xs text-foreground-muted">{project.client}</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          ${project.rate}/hr
                        </Badge>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              {selectedProject && (
                <div className="flex items-center space-x-2 text-xs text-foreground-muted">
                  <User className="h-3 w-3" />
                  <span>{selectedProject.client}</span>
                  <span>•</span>
                  <DollarSign className="h-3 w-3" />
                  <span>${selectedProject.rate}/hour</span>
                </div>
              )}
            </div>

            {/* Task */}
            <div className="space-y-3">
              <Label htmlFor="task" className="text-sm font-mono text-foreground">Task *</Label>
              <div className="space-y-2">
                <Input
                  id="task"
                  value={formData.task}
                  onChange={(e) => setFormData(prev => ({ ...prev, task: e.target.value }))}
                  placeholder="What did you work on?"
                  className="bg-background/20 backdrop-blur-md border-border/30 text-foreground placeholder:text-foreground-muted focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                />
                <div className="flex flex-wrap gap-1">
                  {commonTasks.map((task) => (
                    <Button
                      key={task}
                      variant="outline"
                      size="sm"
                      onClick={() => setFormData(prev => ({ ...prev, task }))}
                      className="h-6 text-xs bg-background/20 backdrop-blur-md border-border/30 hover:border-neon-primary/30"
                    >
                      {task}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-mono text-foreground">Description</Label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional details about the work..."
                rows={3}
                className="w-full px-3 py-2 bg-background/20 backdrop-blur-md border border-border/30 rounded-md text-sm text-foreground placeholder:text-foreground-muted focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date" className="text-sm font-mono text-foreground">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  className="bg-background/20 backdrop-blur-md border-border/30 text-foreground focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="startTime" className="text-sm font-mono text-foreground">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  className="bg-background/20 backdrop-blur-md border-border/30 text-foreground focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime" className="text-sm font-mono text-foreground">End Time</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                  disabled={durationMode === 'duration'}
                  className="bg-background/20 backdrop-blur-md border-border/30 text-foreground focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0 disabled:opacity-50"
                />
              </div>
            </div>

            {/* Duration Mode Toggle */}
            <div className="space-y-3">
              <div className="flex items-center space-x-4">
                <Label className="text-sm font-mono text-foreground">Duration</Label>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={durationMode === 'time' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setDurationMode('time')}
                    className={cn(
                      "text-xs",
                      durationMode === 'time' 
                        ? "bg-neon-primary text-background" 
                        : "bg-background/20 backdrop-blur-md border-border/30"
                    )}
                  >
                    Time Range
                  </Button>
                  <Button
                    variant={durationMode === 'duration' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setDurationMode('duration')}
                    className={cn(
                      "text-xs",
                      durationMode === 'duration' 
                        ? "bg-neon-primary text-background" 
                        : "bg-background/20 backdrop-blur-md border-border/30"
                    )}
                  >
                    Manual Duration
                  </Button>
                </div>
              </div>

              {durationMode === 'duration' && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="duration" className="text-sm font-mono text-foreground">Duration (minutes)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={formData.duration}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
                      className="bg-background/20 backdrop-blur-md border-border/30 text-foreground focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-mono text-foreground">Formatted</Label>
                    <div className="h-10 px-3 py-2 bg-background/20 backdrop-blur-md border border-border/30 rounded-md flex items-center text-sm text-foreground">
                      {formatDuration(formData.duration)}
                    </div>
                  </div>
                </div>
              )}

              {durationMode === 'time' && (
                <div className="text-sm text-foreground-muted font-mono">
                  Duration: {formatDuration(formData.duration)}
                </div>
              )}
            </div>

            {/* Billing Settings */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="billable"
                  checked={formData.billable}
                  onChange={(e) => setFormData(prev => ({ ...prev, billable: e.target.checked }))}
                  className="h-4 w-4 rounded border-border text-neon-primary focus:ring-neon-primary"
                />
                <Label htmlFor="billable" className="text-sm font-mono text-foreground">
                  Billable time
                </Label>
              </div>

              {formData.billable && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="rate" className="text-sm font-mono text-foreground">Hourly Rate ($)</Label>
                    <Input
                      id="rate"
                      type="number"
                      value={formData.rate}
                      onChange={(e) => setFormData(prev => ({ ...prev, rate: parseFloat(e.target.value) || 0 }))}
                      className="bg-background/20 backdrop-blur-md border-border/30 text-foreground focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-mono text-foreground">Estimated Earnings</Label>
                    <div className="h-10 px-3 py-2 bg-neon-primary/10 border border-neon-primary/30 rounded-md flex items-center text-sm text-neon-primary font-mono">
                      ${calculateEarnings().toFixed(2)}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Summary Card */}
            <Card className="bg-background/20 backdrop-blur-md border-border/30">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-foreground">Time Entry Summary</div>
                    <div className="text-xs text-foreground-muted">
                      {formData.project && `${formData.project} • `}
                      {formatDuration(formData.duration)}
                      {formData.billable && ` • $${calculateEarnings().toFixed(2)}`}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-neon-primary" />
                    <span className="text-lg font-mono font-bold text-neon-primary">
                      {formatDuration(formData.duration)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-border/50">
              <Button variant="outline" onClick={onClose} className="bg-background/20 backdrop-blur-md border-border/30">
                Cancel
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={!formData.project || !formData.task}
                className="bg-neon-primary hover:bg-neon-primary/90 text-background shadow-[0_0_20px_rgba(0,255,136,0.3)]"
              >
                <Save className="h-4 w-4 mr-2" />
                {editEntry ? 'Update Entry' : 'Save Entry'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}