'use client'

import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const data = [
  { name: 'Jan', revenue: 4000 },
  { name: 'Feb', revenue: 3000 },
  { name: 'Mar', revenue: 5000 },
  { name: 'Apr', revenue: 4500 },
  { name: 'May', revenue: 6000 },
  { name: 'Jun', revenue: 5500 },
  { name: 'Jul', revenue: 7000 },
];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="p-2 bg-card/80 backdrop-blur-sm border border-neon-primary/50 rounded-lg shadow-lg">
        <p className="label text-sm font-bold" style={{ color: 'hsl(var(--neon-primary))' }}>{`${label} : ${payload[0].value}`}</p>
      </div>
    );
  }

  return null;
};

export function RevenueChart() {
  return (
    <Card className="border-border/50 bg-card/50">
      <CardHeader>
        <CardTitle className="text-base font-mono text-neon-primary flex items-center">
          <div className="w-2 h-2 bg-neon-primary rounded-full mr-2 animate-pulse" />
          Revenue Analytics
        </CardTitle>
        <CardDescription className="text-xs font-mono text-foreground-muted">
          Last 6 months
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[250px] w-full p-0">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
            <defs>
              <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--neon-primary))" stopOpacity={0.4}/>
                <stop offset="95%" stopColor="hsl(var(--neon-primary))" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <XAxis 
              dataKey="name" 
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tick={{ fill: 'hsl(var(--foreground))', opacity: 0.8 }}
            />
            <YAxis 
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value / 1000}k`}
              tick={{ fill: 'hsl(var(--foreground))', opacity: 0.8 }}
            />
            <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border) / 0.5)" />
            <Tooltip content={<CustomTooltip />} cursor={{ stroke: 'hsl(var(--neon-primary))', strokeWidth: 1, strokeDasharray: '3 3' }} />
            <Area 
              type="monotone" 
              dataKey="revenue" 
              stroke="hsl(var(--neon-primary))" 
              fillOpacity={1} 
              fill="url(#colorRevenue)" 
              strokeWidth={2}
              activeDot={{ r: 6, style: { fill: 'hsl(var(--neon-primary))', stroke: 'hsl(var(--card))' } }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
