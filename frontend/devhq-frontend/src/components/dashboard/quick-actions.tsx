'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusIcon, PlayIcon, DocumentTextIcon } from "@heroicons/react/24/outline"

export function QuickActions() {
  return (
    <Card className="border-border/50 bg-card/50 h-full">
      <CardHeader>
        <CardTitle className="text-base font-mono text-foreground flex items-center">
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col space-y-2">
        <Button variant="outline" className="justify-start border-neon-primary/20 hover:bg-neon-primary/10 hover:text-neon-primary">
          <PlusIcon className="h-4 w-4 mr-2" />
          New Project
        </Button>
        <Button variant="outline" className="justify-start border-neon-primary/20 hover:bg-neon-primary/10 hover:text-neon-primary">
          <PlayIcon className="h-4 w-4 mr-2" />
          Start Timer
        </Button>
        <Button variant="outline" className="justify-start border-neon-primary/20 hover:bg-neon-primary/10 hover:text-neon-primary">
          <DocumentTextIcon className="h-4 w-4 mr-2" />
          Create Invoice
        </Button>
      </CardContent>
    </Card>
  )
}
