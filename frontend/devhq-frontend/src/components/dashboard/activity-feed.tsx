'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { CommandLineIcon } from "@heroicons/react/24/outline"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

const activities = [
  { id: 1, timestamp: "2025-08-23 14:32:01", action: "USER_LOGIN", details: "User 'codegoddy' logged in successfully.", type: "success" },
  { id: 2, timestamp: "2025-08-23 14:30:15", action: "PROJECT_UPDATE", details: "Project 'DevHQ API' was updated.", type: "info" },
  { id: 3, timestamp: "2025-08-23 14:28:45", action: "INVOICE_PAID", details: "Invoice #1234 for Client X was paid.", type: "success" },
  { id: 4, timestamp: "2025-08-23 14:25:10", action: "NEW_CLIENT", details: "Client 'TechCorp' was added.", type: "info" },
  { id: 5, timestamp: "2025-08-23 14:22:55", action: "SECURITY_ALERT", details: "Failed login attempt from IP ************", type: "warning" },
];

export function ActivityFeed() {
  return (
    <Card className="border-border/50 bg-card/50 h-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 font-mono text-base text-neon-primary">
          <CommandLineIcon className="h-5 w-5" />
          <span>SYSTEM_LOG</span>
          <div className="w-2 h-2 bg-neon-primary rounded-full animate-pulse ml-auto" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3 font-mono text-xs">
        {activities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, x: -15 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-start space-x-3"
          >
            <div className="flex-shrink-0 pt-0.5">
              <div className={cn(
                "w-1.5 h-1.5 rounded-full",
                activity.type === "success" && "bg-neon-success",
                activity.type === "warning" && "bg-neon-warning",
                activity.type === "info" && "bg-neon-info"
              )} />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-foreground-secondary">
                <span className="text-neon-primary/80">[{activity.timestamp}]</span>
                <span className="text-foreground mx-2">{activity.action}</span>
                <span className="text-foreground-muted">{activity.details}</span>
              </p>
            </div>
          </motion.div>
        ))}
      </CardContent>
    </Card>
  )
}
