'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

const data = [
  { name: 'DevHQ Frontend', value: 400 },
  { name: 'API Gateway', value: 300 },
  { name: 'Auth Service', value: 300 },
  { name: 'Client Project X', value: 200 },
];

const COLORS = [
    'hsl(var(--neon-primary))', 
    'hsl(var(--neon-cyan))', 
    'hsl(var(--neon-purple))', 
    'hsl(var(--neon-orange))'
];

const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="p-2 bg-card/80 backdrop-blur-sm border border-border/50 rounded-lg shadow-lg">
          <p className="label text-sm text-foreground">{`${payload[0].name} : ${payload[0].value} hrs`}</p>
        </div>
      );
    }
  
    return null;
  };

export function TimeDistributionChart() {
  return (
    <Card className="border-border/50 bg-card/50">
      <CardHeader>
        <CardTitle className="text-base font-mono text-neon-cyan flex items-center">
            <div className="w-2 h-2 bg-neon-cyan rounded-full mr-2 animate-pulse" />
            Time Distribution
        </CardTitle>
        <CardDescription className="text-xs font-mono text-foreground-muted">
          Current month
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[250px] w-full p-0">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Tooltip content={<CustomTooltip />} />
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              stroke="hsl(var(--card))"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Legend iconSize={10} layout="vertical" verticalAlign="middle" align="right" wrapperStyle={{ color: 'hsl(var(--foreground))', fontSize: '12px', opacity: 0.8 }} />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
