'use client'

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline"

interface MetricsCardProps {
  title: string
  value: string
  change: string
  trend: "up" | "down"
  icon: React.ReactNode
}

export function MetricsCard({ title, value, change, trend, icon }: MetricsCardProps) {
  return (
    <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group cursor-pointer">
      <CardContent className="p-4 relative">
        <div className="absolute top-0 left-0 h-full w-px bg-gradient-to-b from-neon-primary/50 via-neon-primary/20 to-transparent"></div>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
              {title}
            </p>
            <div className="flex items-baseline space-x-2">
              <p className="text-2xl font-bold text-foreground">
                {value}
              </p>
              <div className={cn(
                "flex items-center text-xs font-mono",
                trend === "up" ? "text-neon-success" : "text-neon-error"
              )}>
                {trend === "up" ? (
                  <ArrowUpIcon className="h-3 w-3 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-3 w-3 mr-1" />
                )}
                {change}
              </div>
            </div>
          </div>
          <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
