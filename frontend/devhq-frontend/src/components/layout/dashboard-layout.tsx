'use client'

import { Sidebar } from '@/components/layout/sidebar'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { UserMenu } from '@/components/layout/user-menu'
import { Menu, Plus, HelpCircle, ArrowUpCircle, Home, Folder } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { navigation } from '@/components/layout/sidebar'

console.log('Value of navigation:', navigation);


interface DashboardLayoutProps {
  children: React.ReactNode
  actions?: React.ReactNode
  currentProjectName?: string
  hideNavigation?: boolean
  showBackButton?: boolean
}

export function DashboardLayout({
  children,
  actions,
  currentProjectName,
  hideNavigation,
  showBackButton
}: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const currentNavItem = navigation.find(item => item.href === pathname);

  const breadcrumb = () => {
    if (pathname.startsWith('/projects/') && currentProjectName) {
      return (
        <>
          <Home className="h-4 w-4 mr-1 text-gray-400" />
          <span className="mr-1 hover:underline text-gray-400">Overview</span>
          <span className="text-xs text-gray-500 mx-1">/</span>
          <Folder className="h-4 w-4 mr-1 text-gray-400" />
          <span className="mr-1 hover:underline text-gray-400">Personal Projects</span>
          <span className="text-xs text-gray-500 mx-1">/</span>
          <span>{currentProjectName}</span>
        </>
      );
    }
    return currentNavItem ? (
      <>
        <currentNavItem.icon className="h-6 w-6 mr-2" />
        <span className="font-semibold text-lg">{currentNavItem.name}</span>
      </>
    ) : (
      <>
        <Home className="h-6 w-6 mr-2" />
        <span className="font-semibold text-lg">Overview</span>
      </>
    );
  };

  return (
    <div className="flex h-screen flex-col overflow-hidden bg-background">
      <div className="flex flex-1 overflow-hidden">
        <Sidebar open={sidebarOpen} setOpen={setSidebarOpen} hideNavigation={hideNavigation} showBackButton={showBackButton} currentProjectName={currentProjectName} />
        <div className="flex flex-1 flex-col overflow-y-auto">
          <header className="flex-shrink-0 border-b border-border bg-background">
            <div className="flex h-16 items-center justify-between px-4">
                <div className="flex items-center">
                  <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(true)} className="md:hidden">
                    <Menu className="h-6 w-6" />
                  </Button>
                  <div className="flex items-center ml-4">
                    {currentProjectName ? (
                      <span className="flex items-center text-sm font-normal text-white">{breadcrumb()}</span>
                    ) : (
                      currentNavItem && (
                        <>
                          <currentNavItem.icon className="h-6 w-6 mr-2" />
                          <span className="text-sm font-normal">{currentNavItem.name}</span>
                        </>
                      )
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 px-2 text-xs">
                    <Plus className="h-4 w-4 mr-1" />
                    New
                  </Button>
                  <Button variant="ghost" className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 px-2 text-xs">
                    <ArrowUpCircle className="h-4 w-4 mr-1" />
                    Upgrade
                  </Button>
                  <Button variant="ghost" size="icon" className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 w-8">
                    <HelpCircle className="h-5 w-5" />
                  </Button>
                  <UserMenu />
                </div>
            </div>
          </header>
          <main className="flex-1 overflow-y-auto">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}

