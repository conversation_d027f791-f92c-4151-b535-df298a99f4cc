'use client'

import { But<PERSON> } from '@/components/ui/button'
import { UserMenu } from '@/components/layout/user-menu'
import { Menu } from 'lucide-react'

interface TopNavProps {
  title?: string
  subtitle?: string
  actions?: React.ReactNode
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
}

export function TopNav({ 
  title, 
  subtitle, 
  actions, 
  sidebarOpen, 
  setSidebarOpen 
}: TopNavProps) {
  return (
    <div className="w-full border-b border-border bg-card">
      <div className="flex items-center justify-between h-16 px-4">
        <div className="flex items-center flex-grow">
          <Button 
            variant="ghost" 
            size="icon" 
            className="md:hidden mr-2"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>
          
          <div className="w-full">
            {title && <h1 className="text-lg font-semibold">{title}</h1>}
            {subtitle && <p className="text-sm text-foreground-secondary">{subtitle}</p>}
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {actions}
          <UserMenu />
        </div>
      </div>
    </div>
  )
}