/** @format */

"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";
import {
  FolderOpenIcon,
  DocumentTextIcon,
  CogIcon,
  XMarkIcon,
  ChatBubbleLeftIcon,
  ChevronLeftIcon,
  ChevronDownIcon,
  PlusIcon,
  ArrowLeftIcon,
  HomeIcon,
} from "@heroicons/react/24/outline";
import {
  Square,
  BarChart3,
  Users,
  FileText,
  Building2,
  CreditCard,
  Wallet,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export const navigation = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: HomeIcon,
  },
  {
    name: "Workspaces",
    href: "/workspace",
    icon: Building2,
  },
  {
    name: "Payouts",
    href: "/payouts",
    icon: Wallet,
  },
  {
    name: "Analytics",
    href: "/analytics",
    icon: BarChart3,
  },
  {
    name: "Invoices",
    href: "/invoices",
    icon: CreditCard,
  },
  {
    name: "Clients",
    href: "/clients",
    icon: Users,
  },
  {
    name: "Documents",
    href: "/documents",
    icon: FileText,
  },
  {
    name: "Settings",
    href: "/settings",
    icon: CogIcon,
  },
];

export function Sidebar({
  open,
  setOpen,
  hideNavigation,
  showBackButton,
  currentProjectName,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  hideNavigation?: boolean;
  showBackButton?: boolean;
  currentProjectName?: string;
}) {
  const pathname = usePathname();
  const router = useRouter(); // Initialize useRouter
  const { user } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);

  console.log("Sidebar - showBackButton:", showBackButton);
  console.log("Sidebar - router:", router);

  return (
    <>
      {/* Mobile sidebar */}
      {open && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div
            className="fixed inset-0 bg-card/80 backdrop-blur-sm"
            onClick={() => setOpen(false)}
          />
          <div className="relative flex-1 flex flex-col w-full max-w-xs border-r border-border h-full bg-background">
            <div className="flex items-center justify-between h-16 px-4 border-b border-border flex-shrink-0">
              <div className="flex items-center space-x-3">
                <Square className="h-6 w-6 text-primary border border-neon-electric animate-blink" />
                <span className="font-bold">DevHQ</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setOpen(false)}
              >
                <XMarkIcon className="h-6 w-6" />
              </Button>
            </div>
            {/* Organization Header */}
            <div className="px-4 pt-4 pb-2 flex-shrink-0">
              <h2 className="text-xs font-bold text-white tracking-wide">
                {user?.first_name || "User"}
              </h2>
            </div>
            {showBackButton && (
              <div className="px-4 pt-4 pb-2 flex-shrink-0">
                <Link
                  href="/personal-projects"
                  className="flex items-center text-xs font-medium text-foreground-secondary hover:text-gray-400"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back to Projects
                </Link>
                {currentProjectName && (
                  <div className="mt-4">
                    <h2 className="text-sm font-semibold text-white tracking-wide">
                      {currentProjectName}
                    </h2>
                    <div className="mt-4">
                      <Link
                        href={pathname}
                        className="flex items-center px-3 py-2 text-sm font-medium bg-green-500/20 text-neon-primary"
                      >
                        <DocumentTextIcon className="h-5 w-5 mr-3" />
                        <span className="truncate">Details</span>
                      </Link>
                      <Link
                        href={`${pathname}/settings`}
                        className="flex items-center px-3 py-2 text-sm font-medium text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                      >
                        <CogIcon className="h-5 w-5 mr-3" />
                        <span className="truncate">Settings</span>
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            )}
            <nav className="flex-1 px-2 py-2 space-y-1 overflow-y-auto min-h-0">
              {!hideNavigation &&
                navigation.map((item) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center px-3 py-2 text-xs font-medium ${
                        isActive ?
                          "bg-green-500/20 text-neon-primary"
                        : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                      }`}
                      onClick={() => setOpen(false)}
                    >
                      <Icon className="mr-3 h-5 w-5" />
                      {item.name}
                    </Link>
                  );
                })}
            </nav>

            {/* Bottom Actions */}
            <div className="px-2 py-2 space-y-1 border-t border-border flex-shrink-0">
              <Link
                href="/feedback"
                className="flex items-center w-full justify-start px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground"
                onClick={() => setOpen(false)}
              >
                <ChatBubbleLeftIcon className="mr-3 h-5 w-5" />
                Feedback
              </Link>
              <Button
                variant="ghost"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="w-full justify-start px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground"
              >
                <ChevronLeftIcon className="mr-3 h-5 w-5" />
                Collapse
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div
          className={`flex flex-col border-r border-border transition-all duration-300 h-full overflow-hidden ${isCollapsed ? "w-16" : "w-64"} bg-background`}
        >
          <div className="flex items-center justify-between h-16 px-4 border-b border-border flex-shrink-0">
            <div className="flex items-center space-x-3 pr-3 border-r border-border h-full">
              <Square className="h-8 w-8 text-primary border border-neon-electric animate-blink" />
            </div>
            <div className="flex items-center space-x-3 pl-3">
              <Avatar className="h-8 w-8 border border-white/20 rounded-full">
                <AvatarImage
                  src={user?.avatar_url}
                  alt={user?.full_name || "User"}
                  className="rounded-full object-cover"
                />
                <AvatarFallback className="bg-green-400/20 text-green-400 rounded-full text-xs font-semibold border border-white/20">
                  {user?.first_name?.charAt(0).toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              <span className="font-medium text-sm">
                {user?.first_name || "User"}
              </span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="flex flex-col items-center text-sm hover:bg-transparent p-1"
                  >
                    <span className="text-xs">▲</span>
                    <ChevronDownIcon className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-black/20 backdrop-blur-md border border-white/10 shadow-xl text-xs w-48">
                  <DropdownMenuLabel className="text-xs text-gray-300">
                    ORGANIZATIONS
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-white/10" />
                  <DropdownMenuItem className="hover:bg-white/10 focus:bg-white/10 text-xs">
                    {user?.first_name ?
                      user.first_name.charAt(0).toUpperCase() +
                      user.first_name.slice(1).toLowerCase()
                    : "Godwin"}
                  </DropdownMenuItem>
                  <DropdownMenuItem className="hover:bg-white/10 focus:bg-white/10 text-xs flex items-center">
                    <PlusIcon className="h-3 w-3 mr-2" />
                    CREATE ORGANIZATION
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          {showBackButton && (
            <div className="px-4 pt-4 pb-2 flex-shrink-0">
              <Link
                href="/personal-projects"
                className="flex items-center text-xs font-medium text-foreground-secondary hover:text-gray-400"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Projects
              </Link>
              {currentProjectName && (
                <div className="mt-4">
                  <h2 className="text-sm font-semibold text-white tracking-wide">
                    {currentProjectName}
                  </h2>
                  <div className="mt-4">
                    <Link
                      href={pathname}
                      className="flex items-center px-3 py-2 text-sm font-medium bg-green-500/20 text-neon-primary"
                    >
                      <DocumentTextIcon className="h-5 w-5 mr-3" />
                      <span className="truncate">Details</span>
                    </Link>
                    <Link
                      href={`${pathname}/settings`}
                      className="flex items-center px-3 py-2 text-sm font-medium text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                    >
                      <CogIcon className="h-5 w-5 mr-3" />
                      <span className="truncate">Settings</span>
                    </Link>
                  </div>
                </div>
              )}
            </div>
          )}
          <nav className="flex-1 px-2 py-2 space-y-1 overflow-y-auto">
            {!hideNavigation &&
              navigation.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center px-3 py-2 text-xs font-medium ${
                      isActive ?
                        "bg-green-500/20 text-neon-primary"
                      : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                    } ${isCollapsed ? "justify-center" : ""}`}
                    title={isCollapsed ? item.name : undefined}
                  >
                    <Icon className={`h-5 w-5 ${isCollapsed ? "" : "mr-3"}`} />
                    {!isCollapsed && item.name}
                  </Link>
                );
              })}
          </nav>

          {/* Bottom Actions */}
          <div className="px-2 py-2 space-y-1 border-t border-border">
            <Link
              href="/feedback"
              className={`flex items-center w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground ${isCollapsed ? "justify-center" : "justify-start"}`}
              title={isCollapsed ? "Feedback" : undefined}
            >
              <ChatBubbleLeftIcon
                className={`h-5 w-5 ${isCollapsed ? "" : "mr-3"}`}
              />
              {!isCollapsed && "Feedback"}
            </Link>
            <Button
              variant="ghost"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className={`w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground ${isCollapsed ? "justify-center" : "justify-start"}`}
              title={isCollapsed ? "Expand" : "Collapse"}
            >
              <ChevronLeftIcon
                className={`h-5 w-5 transition-transform ${isCollapsed ? "rotate-180 " : "mr-3"}`}
              />
              {!isCollapsed && "Collapse"}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
