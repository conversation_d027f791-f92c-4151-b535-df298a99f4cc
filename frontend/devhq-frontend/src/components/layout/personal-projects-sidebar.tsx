'use client'

import { But<PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import { 
  HomeIcon,
  FolderOpenIcon, 
  ClockIcon, 
  UsersIcon, 
  DocumentTextIcon, 
  CogIcon,
  Bars3Icon,
  XMarkIcon,
  ChatBubbleLeftIcon,
  ChevronLeftIcon,
  CheckCircleIcon,
  WalletIcon,
  ArrowTrendingUpIcon,
  CheckIcon,
  CreditCardIcon,
  GlobeAltIcon,
  ReceiptPercentIcon,
  ViewColumnsIcon,
  ChartBarIcon,
  ChevronDownIcon,
  PlusIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline'
import { Square } from 'lucide-react'
import { useAuth } from '@/components/providers/auth-provider'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'

export const personalProjectsNavigation = [
  {
    name: 'Overview',
    href: '/personal-projects',
    icon: HomeIcon,
  },
  {
    name: 'Settings',
    href: '/personal-projects/settings',
    icon: CogIcon,
  },
]

export function PersonalProjectsSidebar({ open, setOpen }: { open: boolean; setOpen: (open: boolean) => void }) {
  const pathname = usePathname()
  const { user } = useAuth()
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <>
      {/* Mobile sidebar */}
      {open && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div className="fixed inset-0 bg-card/80 backdrop-blur-sm" onClick={() => setOpen(false)} />
          <div className="relative flex-1 flex flex-col w-full max-w-xs border-r border-border h-full bg-sidebar">
            <div className="flex items-center justify-between h-16 px-4 border-b border-border flex-shrink-0">
                <div className="flex items-center space-x-3">
                    <Square className="h-6 w-6 text-primary border border-neon-electric animate-blink" />
                    <span className="font-bold">Personal Projects</span>
                </div>
              <Button variant="ghost" size="icon" onClick={() => setOpen(false)}>
                <XMarkIcon className="h-6 w-6" />
              </Button>
            </div>
            
            {/* Back to Dashboard Button */}
            <div className="px-2 pt-4 pb-2 flex-shrink-0">
              <Link
                href="/dashboard"
                className="flex items-center w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                onClick={() => setOpen(false)}
              >
                <ArrowLeftIcon className="mr-3 h-4 w-4" />
                Back to Dashboard
              </Link>
            </div>
            
            {/* Organization Header */}
            <div className="px-4 pt-2 pb-2 flex-shrink-0">
              <h2 className="text-xs font-bold text-white tracking-wide">
                {user?.first_name || 'User'}
              </h2>
            </div>
            <nav className="flex-1 px-2 py-2 space-y-1 overflow-y-auto min-h-0">
              {/* Personal Projects Heading */}
              <div className="flex items-center px-3 py-2 text-sm font-medium text-white">
                <FolderOpenIcon className="mr-3 h-5 w-5" />
                Personal Projects
              </div>
              
              {personalProjectsNavigation.map((item, index) => {
                const Icon = item.icon
                const isActive = pathname === item.href
                
                return (
                  <div key={item.name}>
                    {/* Add Manage heading before Settings */}
                    {item.name === 'Settings' && (
                      <div className="px-3 py-2 pt-4 text-xs font-mono text-white uppercase tracking-wider">
                        Manage
                      </div>
                    )}
                    
                    <Link
                      href={item.href}
                      className={`flex items-center px-3 py-2 text-xs font-medium ${
                        isActive
                          ? 'bg-green-500/20 text-neon-primary'
                          : 'text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground'
                      }`}
                      onClick={() => setOpen(false)}
                    >
                      <Icon className="mr-3 h-5 w-5" />
                      {item.name}
                    </Link>
                  </div>
                )
              })}
            </nav>
            
            {/* Bottom Actions */}
            <div className="px-2 py-2 space-y-1 border-t border-border flex-shrink-0">
              <Link
                href="/feedback"
                className="flex items-center w-full justify-start px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground"
                onClick={() => setOpen(false)}
              >
                <ChatBubbleLeftIcon className="mr-3 h-5 w-5" />
                Feedback
              </Link>
              <Button
                variant="ghost"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="w-full justify-start px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground"
              >
                <ChevronLeftIcon className="mr-3 h-5 w-5" />
                Collapse
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className={`flex flex-col border-r border-border transition-all duration-300 h-full overflow-hidden ${isCollapsed ? 'w-16' : 'w-64'} bg-sidebar`}>
            <div className="flex items-center justify-between h-16 px-4 border-b border-border flex-shrink-0">
                <div className="flex items-center space-x-3 pr-3 border-r border-border h-full">
                    <Square className="h-8 w-8 text-primary border border-neon-electric animate-blink" />
                </div>
                <div className="flex items-center space-x-3 pl-3">
                    <span className="font-medium">{isCollapsed ? 'PP' : 'Personal Projects'}</span>
                    <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="flex flex-col items-center text-sm hover:bg-transparent p-1">
                        <span className="text-xs">▲</span>
                        <ChevronDownIcon className="h-3 w-3" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-black/20 backdrop-blur-md border border-white/10 shadow-xl text-xs w-48">
                        <DropdownMenuLabel className="text-xs text-gray-300">PROJECTS</DropdownMenuLabel>
                        <DropdownMenuSeparator className="bg-white/10" />
                        <DropdownMenuItem className="hover:bg-white/10 focus:bg-white/10 text-xs">
                        Personal Projects
                        </DropdownMenuItem>
                        <Link href="/create-project-form">
                          <DropdownMenuItem className="hover:bg-white/10 focus:bg-white/10 text-xs flex items-center cursor-pointer">
                            <PlusIcon className="h-3 w-3 mr-2" />
                            CREATE PROJECT
                          </DropdownMenuItem>
                        </Link>
                    </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            
          {/* Back to Dashboard Button */}
          <div className="px-2 pt-2 pb-2">
            <Link
              href="/dashboard"
              className={`flex items-center w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground ${isCollapsed ? 'justify-center' : 'justify-start'}`}
              title={isCollapsed ? 'Back to Dashboard' : undefined}
            >
              <ArrowLeftIcon className={`h-4 w-4 ${isCollapsed ? '' : 'mr-3'}`} />
              {!isCollapsed && 'Back to Dashboard'}
            </Link>
          </div>
          
          <nav className="flex-1 px-2 py-2 space-y-1 overflow-y-auto">
            {/* Personal Projects Heading */}
            {!isCollapsed && (
              <div className="flex items-center px-3 py-2 text-sm font-medium text-white">
                <FolderOpenIcon className="mr-3 h-5 w-5" />
                Personal Projects
              </div>
            )}
            
            {personalProjectsNavigation.map((item, index) => {
              const Icon = item.icon
              const isActive = pathname === item.href
              
              return (
                <div key={item.name}>
                  {/* Add Manage heading before Settings */}
                  {item.name === 'Settings' && !isCollapsed && (
                    <div className="px-3 py-2 pt-4 text-xs font-mono text-white uppercase tracking-wider">
                      Manage
                    </div>
                  )}
                  
                  <Link
                    href={item.href}
                    className={`flex items-center px-3 py-2 text-xs font-medium ${
                      isActive
                        ? 'bg-green-500/20 text-neon-primary'
                        : 'text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground'
                    } ${isCollapsed ? 'justify-center' : ''}`}
                    title={isCollapsed ? item.name : undefined}
                  >
                    <Icon className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'}`} />
                    {!isCollapsed && item.name}
                  </Link>
                </div>
              )
            })}
          </nav>
          
          {/* Bottom Actions */}
          <div className="px-2 py-2 space-y-1 border-t border-border">
            <Link
              href="/feedback"
              className={`flex items-center w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground ${isCollapsed ? 'justify-center' : 'justify-start'}`}
              title={isCollapsed ? 'Feedback' : undefined}
            >
              <ChatBubbleLeftIcon className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'}`} />
              {!isCollapsed && 'Feedback'}
            </Link>
            <Button
              variant="ghost"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className={`w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground ${isCollapsed ? 'justify-center' : 'justify-start'}`}
              title={isCollapsed ? 'Expand' : 'Collapse'}
            >
              <ChevronLeftIcon className={`h-5 w-5 transition-transform ${isCollapsed ? 'rotate-180 ' : 'mr-3'}`} />
              {!isCollapsed && 'Collapse'}
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}