/** @format */

"use client";

import { Button } from "@/components/ui/button";
import Link from "next/link";
import { usePathname, useParams } from "next/navigation";
import { useState, useEffect } from "react";
import {
  HomeIcon,
  FolderOpenIcon,
  CogIcon,
  Bars3Icon,
  XMarkIcon,
  ChatBubbleLeftIcon,
  ChevronLeftIcon,
  ChevronDownIcon,
  PlusIcon,
  ArrowLeftIcon,
  DocumentTextIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import {
  Square,
  DollarSign,
  CheckSquare,
  MessageSquare,
  Target,
  StickyNote,
  CreditCard,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { workspaceApi, projectApi } from "@/lib/api";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

export function WorkspaceSidebar({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const pathname = usePathname();
  const params = useParams();
  const { user } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [currentProject, setCurrentProject] = useState<any>(null);
  const [workspaceName, setWorkspaceName] = useState<string>("Loading...");
  const workspaceId = params.workspaceId as string;
  const projectId = params.projectId as string;

  // Check if we're on a project details page
  const isProjectDetailsPage = pathname.includes("/projects/") && projectId;

  // Fetch workspace name from API
  useEffect(() => {
    const fetchWorkspaceName = async () => {
      if (!workspaceId || typeof workspaceId !== "string") return;

      try {
        const workspace = await workspaceApi.getWorkspace(workspaceId);
        setWorkspaceName(workspace.name);
      } catch (error) {
        console.error("Error fetching workspace name:", error);
        setWorkspaceName(`Workspace ${workspaceId}`);
      }
    };

    fetchWorkspaceName();
  }, [workspaceId]);

  // Get current project details if on project page
  useEffect(() => {
    const fetchCurrentProject = async () => {
      if (isProjectDetailsPage && workspaceId && projectId) {
        try {
          const project = await projectApi.getProject(projectId);
          setCurrentProject(project);
        } catch (error) {
          console.error("Error fetching current project for sidebar:", error);
          setCurrentProject(null);
        }
      } else {
        setCurrentProject(null);
      }
    };

    fetchCurrentProject();
  }, [isProjectDetailsPage, workspaceId, projectId]);

  const workspaceNavigation = [
    {
      name: "Overview",
      href: `/workspace/${workspaceId}`,
      icon: HomeIcon,
    },
    {
      name: "Settings",
      href: `/workspace/${workspaceId}/settings`,
      icon: CogIcon,
    },
  ];

  const projectFeatures = [
    { name: "Planning & Budget", href: "planning", icon: Target },
    { name: "Time Tracking", href: "time", icon: ClockIcon },
    { name: "Client Approvals", href: "approvals", icon: CheckSquare },
    { name: "Files & Assets", href: "files", icon: FolderOpenIcon },
    { name: "Financial Dashboard", href: "financials", icon: DollarSign },
    { name: "Payments & Invoices", href: "payments", icon: CreditCard },
    { name: "Project Notes", href: "notes", icon: StickyNote },
    { name: "Milestones", href: "milestones", icon: Target },
    { name: "Communication", href: "communication", icon: MessageSquare },
  ];

  return (
    <>
      {/* Mobile sidebar */}
      {open && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div
            className="fixed inset-0 bg-card/80 backdrop-blur-sm"
            onClick={() => setOpen(false)}
          />
          <div className="relative flex-1 flex flex-col w-full max-w-xs border-r border-border h-full bg-sidebar">
            <div className="flex items-center justify-between h-16 px-4 border-b border-border flex-shrink-0">
              <div className="flex items-center space-x-3">
                <Square className="h-6 w-6 text-primary border border-neon-electric animate-blink" />
                <span className="font-bold">{workspaceName}</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setOpen(false)}
              >
                <XMarkIcon className="h-6 w-6" />
              </Button>
            </div>

            {/* Organization Header */}
            <div className="px-4 pt-2 pb-2 flex-shrink-0">
              <h2 className="text-xs font-bold text-white tracking-wide">
                {workspaceName}
              </h2>
            </div>
            <nav className="flex-1 px-2 py-2 space-y-1 overflow-y-auto min-h-0">
              {/* Project Navigation or Workspace Navigation */}
              {isProjectDetailsPage && currentProject ?
                <>
                  {/* Back to Workspace */}
                  <Link
                    href={`/workspace/${workspaceId}`}
                    className="flex items-center px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                    onClick={() => setOpen(false)}
                  >
                    <ArrowLeftIcon className="mr-3 h-4 w-4" />
                    Back to Workspace
                  </Link>

                  {/* Project Name */}
                  <div className="flex items-center px-3 py-2 text-sm font-medium text-white">
                    <FolderOpenIcon className="mr-3 h-5 w-5" />
                    {currentProject.name}
                  </div>

                  {/* Project Details Link */}
                  <Link
                    href={`/workspace/${workspaceId}/projects/${projectId}`}
                    className={`flex items-center px-3 py-2 text-xs font-medium ${
                      (
                        pathname ===
                        `/workspace/${workspaceId}/projects/${projectId}`
                      ) ?
                        "bg-green-500/20 text-neon-primary"
                      : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                    }`}
                    onClick={() => setOpen(false)}
                  >
                    <DocumentTextIcon className="mr-3 h-5 w-5" />
                    Details
                  </Link>

                  {/* Project Features Heading */}
                  <div className="px-3 py-2 pt-4 text-xs font-mono text-white uppercase tracking-wider">
                    Project Features
                  </div>

                  {projectFeatures.map((item) => {
                    const Icon = item.icon;
                    const href = `/workspace/${workspaceId}/projects/${projectId}/${item.href}`;
                    const isActive = pathname === href;
                    return (
                      <Link
                        key={item.name}
                        href={href}
                        className={`flex items-center px-3 py-2 text-xs font-medium ${
                          isActive ?
                            "bg-green-500/20 text-neon-primary"
                          : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                        }`}
                        onClick={() => setOpen(false)}
                      >
                        <Icon className="mr-3 h-5 w-5" />
                        {item.name}
                      </Link>
                    );
                  })}
                </>
              : <>
                  {/* Main Navigation */}
                  <div className="px-3 py-2 text-xs font-mono text-white uppercase tracking-wider">
                    Main
                  </div>
                  {workspaceNavigation.map((item) => {
                    const Icon = item.icon;
                    const isActive = pathname === item.href;

                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`flex items-center px-3 py-2 text-xs font-medium ${
                          isActive ?
                            "bg-green-500/20 text-neon-primary"
                          : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                        }`}
                        onClick={() => setOpen(false)}
                      >
                        <Icon className="mr-3 h-5 w-5" />
                        {item.name}
                      </Link>
                    );
                  })}

                  {/* Workspace Heading */}
                  <div className="flex items-center px-3 py-2 pt-4 text-sm font-medium text-white">
                    <FolderOpenIcon className="mr-3 h-5 w-5" />
                    {workspaceName}
                  </div>
                </>
              }

              {!isProjectDetailsPage &&
                workspaceNavigation.map((item, index) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href;

                  return (
                    <div key={item.name}>
                      {/* Add Manage heading before Settings */}
                      {item.name === "Settings" && (
                        <div className="px-3 py-2 pt-4 text-xs font-mono text-white uppercase tracking-wider">
                          Manage
                        </div>
                      )}

                      <Link
                        href={item.href}
                        className={`flex items-center px-3 py-2 text-xs font-medium ${
                          isActive ?
                            "bg-green-500/20 text-neon-primary"
                          : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                        }`}
                        onClick={() => setOpen(false)}
                      >
                        <Icon className="mr-3 h-5 w-5" />
                        {item.name}
                      </Link>
                    </div>
                  );
                })}
            </nav>

            {/* Bottom Actions */}
            <div className="px-2 py-2 space-y-1 border-t border-border flex-shrink-0">
              <Link
                href="/feedback"
                className="flex items-center w-full justify-start px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground"
                onClick={() => setOpen(false)}
              >
                <ChatBubbleLeftIcon className="mr-3 h-5 w-5" />
                Feedback
              </Link>
              <Button
                variant="ghost"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="w-full justify-start px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground"
              >
                <ChevronLeftIcon className="mr-3 h-5 w-5" />
                Collapse
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div
          className={`flex flex-col border-r border-border transition-all duration-300 h-full overflow-hidden ${isCollapsed ? "w-16" : "w-64"} bg-sidebar`}
        >
          <div className="flex items-center justify-between h-16 px-4 border-b border-border flex-shrink-0">
            <div className="flex items-center space-x-3 pr-3 border-r border-border h-full">
              <Square className="h-8 w-8 text-primary border border-neon-electric animate-blink" />
            </div>
            <div className="flex items-center space-x-3 pl-3">
              <span className="font-medium">
                {isCollapsed ? "WS" : workspaceName}
              </span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="flex flex-col items-center text-sm hover:bg-transparent p-1"
                  >
                    <span className="text-xs">▲</span>
                    <ChevronDownIcon className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-black/20 backdrop-blur-md border border-white/10 shadow-xl text-xs w-48">
                  <DropdownMenuLabel className="text-xs text-gray-300">
                    WORKSPACES
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-white/10" />
                  <DropdownMenuItem className="hover:bg-white/10 focus:bg-white/10 text-xs">
                    {workspaceName}
                  </DropdownMenuItem>
                  <Link href="/create-project-form">
                    <DropdownMenuItem className="hover:bg-white/10 focus:bg-white/10 text-xs flex items-center cursor-pointer">
                      <PlusIcon className="h-3 w-3 mr-2" />
                      CREATE PROJECT
                    </DropdownMenuItem>
                  </Link>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <nav className="flex-1 px-2 py-2 space-y-1 overflow-y-auto">
            {/* Project Navigation or Workspace Navigation */}
            {isProjectDetailsPage && currentProject ?
              <>
                {/* Back to Workspace */}
                <Link
                  href={`/workspace/${workspaceId}`}
                  className={`flex items-center w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground ${isCollapsed ? "justify-center" : "justify-start"}`}
                  title={isCollapsed ? "Back to Workspace" : undefined}
                >
                  <ArrowLeftIcon
                    className={`h-4 w-4 ${isCollapsed ? "" : "mr-3"}`}
                  />
                  {!isCollapsed && "Back to Workspace"}
                </Link>

                {/* Project Name */}
                {!isCollapsed && (
                  <div className="flex items-center px-3 py-2 text-sm font-medium text-white">
                    <FolderOpenIcon className="mr-3 h-5 w-5" />
                    {currentProject.name}
                  </div>
                )}

                {/* Project Details Link */}
                <Link
                  href={`/workspace/${workspaceId}/projects/${projectId}`}
                  className={`flex items-center px-3 py-2 text-xs font-medium ${
                    (
                      pathname ===
                      `/workspace/${workspaceId}/projects/${projectId}`
                    ) ?
                      "bg-green-500/20 text-neon-primary"
                    : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                  } ${isCollapsed ? "justify-center" : ""}`}
                  title={isCollapsed ? "Details" : undefined}
                >
                  <DocumentTextIcon
                    className={`h-5 w-5 ${isCollapsed ? "" : "mr-3"}`}
                  />
                  {!isCollapsed && "Details"}
                </Link>

                {/* Project Features Heading */}
                {!isCollapsed && (
                  <div className="px-3 py-2 pt-4 text-xs font-mono text-white uppercase tracking-wider">
                    Project Features
                  </div>
                )}

                {projectFeatures.map((item) => {
                  const Icon = item.icon;
                  const href = `/workspace/${workspaceId}/projects/${projectId}/${item.href}`;
                  const isActive = pathname === href;
                  return (
                    <Link
                      key={item.name}
                      href={href}
                      className={`flex items-center px-3 py-2 text-xs font-medium ${
                        isActive ?
                          "bg-green-500/20 text-neon-primary"
                        : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                      } ${isCollapsed ? "justify-center" : ""}`}
                      title={isCollapsed ? item.name : undefined}
                    >
                      <Icon
                        className={`h-5 w-5 ${isCollapsed ? "" : "mr-3"}`}
                      />
                      {!isCollapsed && item.name}
                    </Link>
                  );
                })}
              </>
            : <>
                {/* Workspace Heading */}
                {!isCollapsed && (
                  <div className="flex items-center px-3 py-2 pt-4 text-sm font-medium text-white">
                    <FolderOpenIcon className="mr-3 h-5 w-5" />
                    {workspaceName}
                  </div>
                )}
              </>
            }

            {!isProjectDetailsPage &&
              workspaceNavigation.map((item, index) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;

                return (
                  <div key={item.name}>
                    {/* Add Manage heading before Settings */}
                    {item.name === "Settings" && !isCollapsed && (
                      <div className="px-3 py-2 pt-4 text-xs font-mono text-white uppercase tracking-wider">
                        Manage
                      </div>
                    )}

                    <Link
                      href={item.href}
                      className={`flex items-center px-3 py-2 text-xs font-medium ${
                        isActive ?
                          "bg-green-500/20 text-neon-primary"
                        : "text-foreground-secondary hover:bg-[rgba(77,77,77,0.5)] hover:text-foreground"
                      } ${isCollapsed ? "justify-center" : ""}`}
                      title={isCollapsed ? item.name : undefined}
                    >
                      <Icon
                        className={`h-5 w-5 ${isCollapsed ? "" : "mr-3"}`}
                      />
                      {!isCollapsed && item.name}
                    </Link>
                  </div>
                );
              })}
          </nav>

          {/* Bottom Actions */}
          <div className="px-2 py-2 space-y-1 border-t border-border">
            <Link
              href="/feedback"
              className={`flex items-center w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground ${isCollapsed ? "justify-center" : "justify-start"}`}
              title={isCollapsed ? "Feedback" : undefined}
            >
              <ChatBubbleLeftIcon
                className={`h-5 w-5 ${isCollapsed ? "" : "mr-3"}`}
              />
              {!isCollapsed && "Feedback"}
            </Link>
            <Button
              variant="ghost"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className={`w-full px-3 py-2 text-xs font-medium text-foreground-secondary hover:bg-nav-item-hover hover:text-foreground ${isCollapsed ? "justify-center" : "justify-start"}`}
              title={isCollapsed ? "Expand" : "Collapse"}
            >
              <ChevronLeftIcon
                className={`h-5 w-5 transition-transform ${isCollapsed ? "rotate-180 " : "mr-3"}`}
              />
              {!isCollapsed && "Collapse"}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
