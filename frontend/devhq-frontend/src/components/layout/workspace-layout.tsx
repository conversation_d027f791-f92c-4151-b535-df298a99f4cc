/** @format */

"use client";

import { WorkspaceSidebar } from "@/components/layout/workspace-sidebar";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { UserMenu } from "@/components/layout/user-menu";
import {
  Menu,
  Plus,
  HelpCircle,
  ArrowUpCircle,
  Home,
  Folder,
} from "lucide-react";
import { usePathname, useParams } from "next/navigation";
import { workspaceApi } from "@/lib/api";

interface WorkspaceLayoutProps {
  children: React.ReactNode;
  actions?: React.ReactNode;
}

export function WorkspaceLayout({ children, actions }: WorkspaceLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [workspaceName, setWorkspaceName] = useState<string>("Loading...");
  const pathname = usePathname();
  const params = useParams();
  const workspaceId = params.workspaceId as string;

  // Fetch workspace name from API
  useEffect(() => {
    const fetchWorkspaceName = async () => {
      if (!workspaceId || typeof workspaceId !== "string") return;

      try {
        const workspace = await workspaceApi.getWorkspace(workspaceId);
        setWorkspaceName(workspace.name);
      } catch (error) {
        console.error("Error fetching workspace name:", error);
        setWorkspaceName(`Workspace ${workspaceId}`);
      }
    };

    fetchWorkspaceName();
  }, [workspaceId]);

  const breadcrumb = () => {
    return (
      <>
        <Home className="h-4 w-4 mr-1 text-gray-400" />
        <span className="mr-1 hover:underline text-gray-400">Overview</span>
        <span className="text-xs text-gray-500 mx-1">/</span>
        <Folder className="h-4 w-4 mr-1 text-gray-400" />
        <span className="mr-1 hover:underline text-gray-400">
          {workspaceName}
        </span>
      </>
    );
  };

  return (
    <div className="flex h-screen flex-col overflow-hidden bg-background">
      <div className="flex flex-1 overflow-hidden">
        <WorkspaceSidebar open={sidebarOpen} setOpen={setSidebarOpen} />
        <div className="flex flex-1 flex-col overflow-y-auto">
          <header className="flex-shrink-0 border-b border-border bg-background">
            <div className="flex h-16 items-center justify-between px-4">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(true)}
                  className="md:hidden"
                >
                  <Menu className="h-6 w-6" />
                </Button>
                <div className="flex items-center ml-4">
                  <span className="flex items-center text-sm font-normal text-white">
                    {breadcrumb()}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 px-2 text-xs"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  New
                </Button>
                <Button
                  variant="ghost"
                  className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 px-2 text-xs"
                >
                  <ArrowUpCircle className="h-4 w-4 mr-1" />
                  Upgrade
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 w-8"
                >
                  <HelpCircle className="h-5 w-5" />
                </Button>
                <UserMenu />
              </div>
            </div>
          </header>
          <main className="flex-1 overflow-y-auto">{children}</main>
        </div>
      </div>
    </div>
  );
}
