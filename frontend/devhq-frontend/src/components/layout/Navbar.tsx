"use client";

import * as React from "react";
import Link from "next/link";
import { Zap, Briefcase, Users, FileText, DollarSign, Building, ChevronDown, User, Info, BookOpen, Mail, BarChart, Sparkles, Square } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

const productFeatures = [
  { name: "Project Management", href: "/#features", icon: <Briefcase className="mr-2 h-4 w-4" /> },
  { name: "Time Tracking", href: "/#features", icon: <Zap className="mr-2 h-4 w-4" /> },
  { name: "Invoicing", href: "/#features", icon: <DollarSign className="mr-2 h-4 w-4" /> },
  { name: "Client Portal", href: "/#features", icon: <Users className="mr-2 h-4 w-4" /> },
  { name: "Analytics", href: "/#features", icon: <BarChart className="mr-2 h-4 w-4" /> },
];

const solutions = [
  { name: "For Freelancers", href: "#", icon: <User className="mr-2 h-4 w-4" /> },
  { name: "For Agencies", href: "#", icon: <Building className="mr-2 h-4 w-4" /> },
  { name: "For Consultants", href: "#", icon: <Users className="mr-2 h-4 w-4" /> },
];

const companyLinks = [
  { name: "About Us", href: "#", icon: <Info className="mr-2 h-4 w-4" /> },
  { name: "Blog", href: "#", icon: <BookOpen className="mr-2 h-4 w-4" /> },
  { name: "Careers", href: "#", icon: <Briefcase className="mr-2 h-4 w-4" /> },
  { name: "Contact", href: "#", icon: <Mail className="mr-2 h-4 w-4" /> },
];

export function Navbar() {
  const [scrolled, setScrolled] = React.useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);

  const handleDashboardClick = () => {
    window.location.href = "/login";
  };

  React.useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 0) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  React.useEffect(() => {
    if (isDropdownOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isDropdownOpen]);

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full transition-all duration-500 ease-out", 
      scrolled ? "py-2" : "py-4"
    )}>
      <div className="container flex items-center justify-center">
        {/* Left Section: Logo */}
        <Link href="/" className="flex items-center space-x-3 absolute left-4">
          <Square className="h-10 w-10 text-primary border border-neon-electric animate-blink" />
          <span className="hidden font-bold text-lg sm:inline-block">DevHQ</span>
        </Link>

        <div className="flex justify-center">
          {/* Middle Section: Glassy Nav Links */}
          <nav className={cn(
            "glassy-navbar flex justify-center items-center space-x-4 text-sm font-medium rounded-full backdrop-blur-lg border border-white/20 bg-white/10 shadow-2xl transition-all duration-500 ease-out",
            scrolled 
              ? "px-4 py-2 mx-2" 
              : "px-6 py-3 mx-0"
          )}>
            <NavMenu title="Product" items={productFeatures} onOpenChange={setIsDropdownOpen} />
            <NavMenu title="Solutions" items={solutions} onOpenChange={setIsDropdownOpen} />
            <Link href="/docs" className="transition-colors duration-300 hover:text-white text-white/70 focus-visible:outline-none focus-visible:ring-0 px-2 py-1">
              Docs
            </Link>
            <Link href="/pricing" className="transition-colors duration-300 hover:text-white text-white/70 focus-visible:outline-none focus-visible:ring-0 px-2 py-1">
              Pricing
            </Link>
            <NavMenu title="Company" items={companyLinks} onOpenChange={setIsDropdownOpen} />
          </nav>
        </div>

        {/* Right Section: Contact/Dashboard Buttons */}
        <div className="flex items-center space-x-1 ml-auto absolute right-0"> 
          <nav className="flex items-center space-x-4">
            <Link
              href="/contact"
              className="text-base font-medium text-white/70 transition-colors duration-300 hover:text-white px-2 py-1"
            >
              Contact
            </Link>
            <Button asChild className="dashboard-button-hover-effect" size="lg" variant="white-no-hover" onClick={handleDashboardClick}>
              <Link href="#" className="text-black" data-text="Dashboard">
                <span>Dashboard</span>
              </Link>
            </Button>
          </nav>
        </div>
      </div>
    </header>
  );
}

interface NavMenuProps {
  title: string;
  items: { name: string; href: string; icon?: React.ReactNode }[];
  onOpenChange?: (open: boolean) => void;
}

function NavMenu({ title, items, onOpenChange }: NavMenuProps) {
  return (
    <DropdownMenu onOpenChange={onOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="flex items-center text-sm font-medium text-white/70 hover:text-white focus-visible:outline-none focus-visible:ring-0 px-2 py-1 transition-colors duration-300">
          {title}
          <ChevronDown className="ml-1 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="glassy-dropdown bg-black/20 backdrop-blur-xl border border-white/20 shadow-2xl rounded-xl p-3">
        {items.map((item) => (
          <DropdownMenuItem key={item.name} asChild>
            <Link href={item.href} className="flex items-center px-3 py-2 transition-colors duration-200 text-white/70 hover:text-white focus:outline-none">
              {item.icon}
              {item.name}
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
