/** @format */

"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Settings, LogOut, Mail, Shield } from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useRouter } from "next/navigation";

export function UserMenu() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    router.push("/login");
  };

  const handleProfileClick = () => {
    router.push("/profile");
  };

  const handleSettingsClick = () => {
    router.push("/settings");
  };

  // Generate user initials
  const getUserInitials = () => {
    if (user?.first_name && user?.last_name) {
      return `${user.first_name.charAt(0).toUpperCase()}${user.last_name.charAt(0).toUpperCase()}`;
    }
    if (user?.first_name) {
      return user.first_name.charAt(0).toUpperCase();
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return "U";
  };

  // Generate background color based on user initials (matching app's color scheme)
  const getAvatarBgColor = () => {
    const initials = getUserInitials();
    const colors = [
      "bg-blue-400/20 text-blue-400",
      "bg-green-400/20 text-green-400",
      "bg-purple-400/20 text-purple-400",
      "bg-yellow-400/20 text-yellow-400",
      "bg-orange-400/20 text-orange-400",
      "bg-red-400/20 text-red-400",
    ];
    const index = initials.charCodeAt(0) % colors.length;
    return colors[index];
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="relative h-10 w-10 rounded-full p-0 hover:bg-white/5"
        >
          <Avatar className="h-10 w-10 border border-white/20 rounded-full hover:border-white/40 transition-all duration-200">
            <AvatarImage
              src={user?.avatar_url}
              alt={user?.full_name || "User"}
              className="rounded-full object-cover"
            />
            <AvatarFallback
              className={`${getAvatarBgColor()} rounded-full text-sm font-semibold border border-white/20`}
            >
              {getUserInitials()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-64 bg-black/20 backdrop-blur-md border border-white/10 shadow-xl p-2"
        align="end"
        forceMount
        sideOffset={8}
      >
        {/* User Info Header */}
        <DropdownMenuLabel className="px-3 py-3 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12 border border-white/20 rounded-full">
              <AvatarImage
                src={user?.avatar_url}
                alt={user?.full_name || "User"}
                className="rounded-full object-cover"
              />
              <AvatarFallback
                className={`${getAvatarBgColor()} rounded-full text-sm font-semibold border border-white/20`}
              >
                {getUserInitials()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-white truncate">
                {user?.full_name ||
                  `${user?.first_name} ${user?.last_name}` ||
                  "User"}
              </p>
              <p className="text-xs text-white/60 truncate flex items-center">
                <Mail className="h-3 w-3 mr-1" />
                {user?.email || "<EMAIL>"}
              </p>
              {user?.is_verified && (
                <p className="text-xs text-green-400 flex items-center mt-1">
                  <Shield className="h-3 w-3 mr-1" />
                  Verified Account
                </p>
              )}
            </div>
          </div>
        </DropdownMenuLabel>

        {/* Menu Items */}
        <div className="py-1">
          <DropdownMenuItem
            className="cursor-pointer hover:bg-white/10 focus:bg-white/10 text-white px-3 py-2 transition-colors text-xs"
            onClick={handleProfileClick}
          >
            <User className="mr-3 h-4 w-4 text-blue-400" />
            <span>View Profile</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer hover:bg-white/10 focus:bg-white/10 text-white px-3 py-2 transition-colors text-xs"
            onClick={handleSettingsClick}
          >
            <Settings className="mr-3 h-4 w-4 text-green-400" />
            <span>Settings</span>
          </DropdownMenuItem>
        </div>

        <DropdownMenuSeparator className="bg-white/10 my-2" />

        <div className="py-1">
          <DropdownMenuItem
            className="cursor-pointer hover:bg-white/10 focus:bg-white/10 text-red-400 px-3 py-2 transition-colors text-xs"
            onClick={handleLogout}
          >
            <LogOut className="mr-3 h-4 w-4" />
            <span>Sign Out</span>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
