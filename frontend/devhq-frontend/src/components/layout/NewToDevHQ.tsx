"use client";

import * as React from "react";
import Link from "next/link";
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { motion } from "framer-motion";

export function NewToDevHQ() {
  return (
    <div className="bg-black text-center text-sm font-medium text-white border-b border-white/10 py-3">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="flex items-center justify-center gap-2"
      >
        <span>Are you new to DevHQ?</span>
        <Link href="/register" className="underline hover:text-green-300 transition-colors flex items-center gap-1">
          Sign up now <ArrowRight className="h-4 w-4" />
        </Link>
      </motion.div>
    </div>
  );
}
