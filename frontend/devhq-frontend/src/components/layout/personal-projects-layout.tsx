'use client'

import { useState } from 'react'
import { PersonalProjectsSidebar } from './personal-projects-sidebar'
import { Button } from '@/components/ui/button'
import { UserMenu } from '@/components/layout/user-menu'
import { Menu, Plus, HelpCircle, ArrowUpCircle, FolderOpenIcon } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { personalProjectsNavigation } from './personal-projects-sidebar'

interface PersonalProjectsLayoutProps {
  children: React.ReactNode
}

export function PersonalProjectsLayout({ children }: PersonalProjectsLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const currentNavItem = personalProjectsNavigation.find(item => item.href === pathname)

  return (
    <div className="flex h-screen flex-col overflow-hidden bg-background">
      <div className="flex flex-1 overflow-hidden">
        <PersonalProjectsSidebar open={sidebarOpen} setOpen={setSidebarOpen} />
        <div className="flex flex-1 flex-col overflow-y-auto">
          <header className="flex-shrink-0 border-b border-border bg-background">
            <div className="flex h-16 items-center justify-between px-4">
                <div className="flex items-center">
                  <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(true)} className="md:hidden">
                    <Menu className="h-6 w-6" />
                  </Button>
                  {currentNavItem && (
                    <div className="flex items-center ml-4">
                      <currentNavItem.icon className="h-6 w-6 mr-2" />
                      <span className="font-semibold text-lg">{currentNavItem.name}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 px-2 text-xs">
                    <Plus className="h-4 w-4 mr-1" />
                    New
                  </Button>
                  <Button variant="ghost" className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 px-2 text-xs">
                    <ArrowUpCircle className="h-4 w-4 mr-1" />
                    Upgrade
                  </Button>
                  <Button variant="ghost" size="icon" className="rounded-none bg-transparent hover:bg-white hover:text-black border border-white/20 h-8 w-8">
                    <HelpCircle className="h-5 w-5" />
                  </Button>
                  <UserMenu />
                </div>
            </div>
          </header>
          <main className="flex-1 overflow-y-auto">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}