/** @format */

"use client";

import React, { useState } from "react";
import { Plus, Save } from "lucide-react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useProject } from "@/contexts/project-context";

type NoteVersion = {
  version: number;
  title: string;
  content: string;
  at: string;
};
type NoteLink = { milestoneId?: string; taskId?: string; fileId?: string };
type Note = {
  id: string;
  title: string;
  content: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  versions?: NoteVersion[];
  links?: NoteLink[];
};

const seedNotes = [
  {
    id: 1,
    title: "Initial project setup notes",
    content:
      "This note contains the initial setup instructions for the project.",
  },
  {
    id: 2,
    title: "API endpoint documentation",
    content: "A list of all the API endpoints and their usage.",
  },
];

export function ProjectNotes() {
  const { project } = useProject();
  const params = useParams() as { workspaceId: string; projectId: string };
  const { workspaceId, projectId } = params;

  const [notes, setNotes] = useState<Note[]>([]);
  const [selectedId, setSelectedId] = useState<string>("");
  const [title, setTitle] = useState<string>("");
  const [content, setContent] = useState<string>("");

  // Link editor state
  const [linkType, setLinkType] = useState<"milestone" | "task" | "file">(
    "milestone"
  );
  const [linkId, setLinkId] = useState<string>("");

  React.useEffect(() => {
    const proj = project || ({} as any);
    const arr: Note[] = Array.isArray(proj.notes) ? proj.notes : [];
    setNotes(arr);
    if (arr.length) {
      const first = arr[0];
      setSelectedId(first.id);
      setTitle(first.title);
      setContent(first.content);
    }
  }, [project]);

  const saveProject = (updater: (proj: any) => any) => {
    // Note: In a real app, this would update the project through the context
    const proj = project || ({} as any);
    const next = updater(proj);
    setNotes(next.notes || []);
    return next;
  };

  const selectedNote = React.useMemo(
    () => notes.find((n) => n.id === selectedId) || null,
    [notes, selectedId]
  );

  const createNote = () => {
    const id = `note_${Date.now()}`;
    const now = new Date().toISOString();
    const n: Note = {
      id,
      title: "Untitled Note",
      content: "",
      version: 1,
      createdAt: now,
      updatedAt: now,
      versions: [],
      links: [],
    };
    saveProject((proj) => {
      proj.notes = [...(proj.notes || []), n];
      return proj;
    });
    setSelectedId(id);
    setTitle(n.title);
    setContent(n.content);
  };

  const persistNote = () => {
    if (!selectedNote) return;
    const now = new Date().toISOString();
    // push current stored version to history before saving new content
    saveProject((proj) => {
      const arr: Note[] = Array.isArray(proj.notes) ? [...proj.notes] : [];
      const idx = arr.findIndex((n) => n.id === selectedNote.id);
      if (idx >= 0) {
        const before = arr[idx];
        const history =
          Array.isArray(before.versions) ? [...before.versions] : [];
        history.push({
          version: before.version,
          title: before.title,
          content: before.content,
          at: now,
        });
        arr[idx] = {
          ...before,
          title,
          content,
          version: before.version + 1,
          updatedAt: now,
          versions: history,
        };
      }
      proj.notes = arr;
      return proj;
    });
  };

  const restoreVersion = (v: NoteVersion) => {
    if (!selectedNote) return;
    const now = new Date().toISOString();
    saveProject((proj) => {
      const arr: Note[] = Array.isArray(proj.notes) ? [...proj.notes] : [];
      const idx = arr.findIndex((n) => n.id === selectedNote.id);
      if (idx >= 0) {
        const before = arr[idx];
        const history =
          Array.isArray(before.versions) ? [...before.versions] : [];
        // push current into history
        history.push({
          version: before.version,
          title: before.title,
          content: before.content,
          at: now,
        });
        arr[idx] = {
          ...before,
          title: v.title,
          content: v.content,
          version: before.version + 1,
          updatedAt: now,
          versions: history,
        };
      }
      proj.notes = arr;
      return proj;
    });
    setTitle(v.title);
    setContent(v.content);
  };

  const addLink = () => {
    if (!selectedNote || !linkId.trim()) return;
    const entry: NoteLink =
      linkType === "milestone" ? { milestoneId: linkId }
      : linkType === "task" ? { taskId: linkId }
      : { fileId: linkId };
    saveProject((proj) => {
      const arr: Note[] = Array.isArray(proj.notes) ? [...proj.notes] : [];
      const idx = arr.findIndex((n) => n.id === selectedNote.id);
      if (idx >= 0) {
        const links = Array.isArray(arr[idx].links) ? [...arr[idx].links!] : [];
        links.push(entry);
        arr[idx] = { ...arr[idx], links };
      }
      proj.notes = arr;
      return proj;
    });
    setLinkId("");
  };

  const removeLink = (i: number) => {
    if (!selectedNote) return;
    saveProject((proj) => {
      const arr: Note[] = Array.isArray(proj.notes) ? [...proj.notes] : [];
      const idx = arr.findIndex((n) => n.id === selectedNote.id);
      if (idx >= 0) {
        const links = Array.isArray(arr[idx].links) ? [...arr[idx].links!] : [];
        links.splice(i, 1);
        arr[idx] = { ...arr[idx], links };
      }
      proj.notes = arr;
      return proj;
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-1 space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-white">Notes</h3>
          <Button
            variant="outline"
            className="rounded-none text-xs"
            onClick={createNote}
          >
            <Plus className="h-4 w-4 mr-1" /> New Note
          </Button>
        </div>
        {notes.length === 0 ?
          <div className="text-white/60 text-sm">No notes yet.</div>
        : <ul className="space-y-2">
            {notes.map((n) => (
              <li
                key={n.id}
                className={`p-3 cursor-pointer border ${selectedId === n.id ? "border-white/60" : "border-white/20"}`}
                onClick={() => {
                  setSelectedId(n.id);
                  setTitle(n.title);
                  setContent(n.content);
                }}
              >
                <div className="text-white text-sm">
                  {n.title || "Untitled"}
                </div>
                <div className="text-white/40 text-xs">
                  v{n.version} • {new Date(n.updatedAt).toLocaleString()}
                </div>
              </li>
            ))}
          </ul>
        }
      </div>
      <div className="md:col-span-2 space-y-4">
        {!selectedNote ?
          <div className="text-white/60 text-sm">
            Select or create a note to begin.
          </div>
        : <>
            <div className="flex items-center justify-between">
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Note title"
                className="bg-transparent border-white/20 text-white rounded-none"
              />
              <Button
                variant="outline"
                className="rounded-none"
                onClick={persistNote}
              >
                <Save className="h-4 w-4 mr-1" /> Save (v
                {selectedNote.version + 1})
              </Button>
            </div>
            <div>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={10}
                placeholder="Write your note..."
                className="w-full bg-transparent border border-white/20 text-white rounded-none p-2"
              />
            </div>

            {/* Link manager */}
            <div className="border border-white/20 p-3">
              <div className="text-white/80 text-sm mb-2 flex items-center">
                <span className="h-4 w-4 mr-1">🔗</span>
                Links
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Select
                  value={linkType}
                  onValueChange={(v) => setLinkType(v as any)}
                >
                  <SelectTrigger className="bg-transparent border-white/20 text-white rounded-none">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                    <SelectItem value="milestone">Milestone</SelectItem>
                    <SelectItem value="task">Task</SelectItem>
                    <SelectItem value="file">File</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  value={linkId}
                  onChange={(e) => setLinkId(e.target.value)}
                  placeholder="ID"
                  className="bg-transparent border-white/20 text-white rounded-none"
                />
                <Button
                  variant="outline"
                  className="rounded-none"
                  onClick={addLink}
                >
                  Add
                </Button>
              </div>
              {(
                Array.isArray(selectedNote.links) &&
                selectedNote.links.length > 0
              ) ?
                <ul className="space-y-1">
                  {selectedNote.links.map((lnk, i) => (
                    <li
                      key={i}
                      className="text-white/80 text-sm flex items-center justify-between"
                    >
                      <span>
                        {lnk.milestoneId ?
                          `Milestone: ${lnk.milestoneId}`
                        : lnk.taskId ?
                          `Task: ${lnk.taskId}`
                        : lnk.fileId ?
                          `File: ${lnk.fileId}`
                        : ""}
                      </span>
                      <Button
                        variant="ghost"
                        className="rounded-none text-xs"
                        onClick={() => removeLink(i)}
                      >
                        <span className="h-4 w-4">🗑️</span>
                      </Button>
                    </li>
                  ))}
                </ul>
              : <div className="text-white/60 text-xs">No links</div>}
            </div>

            {/* Versions */}
            <div className="border border-white/20 p-3">
              <div className="text-white/80 text-sm mb-2 flex items-center">
                <span className="h-4 w-4 mr-1">🔄</span>
                Version History
              </div>
              {(
                Array.isArray(selectedNote.versions) &&
                selectedNote.versions.length > 0
              ) ?
                <ul className="space-y-1">
                  {[...selectedNote.versions].reverse().map((v, idx) => (
                    <li
                      key={idx}
                      className="flex items-center justify-between text-white/80 text-sm"
                    >
                      <div>
                        v{v.version} • {new Date(v.at).toLocaleString()} —{" "}
                        {v.title}
                      </div>
                      <Button
                        variant="outline"
                        className="rounded-none text-xs"
                        onClick={() => restoreVersion(v)}
                      >
                        Restore
                      </Button>
                    </li>
                  ))}
                </ul>
              : <div className="text-white/60 text-xs">No versions yet.</div>}
            </div>
          </>
        }
      </div>
    </div>
  );
}
