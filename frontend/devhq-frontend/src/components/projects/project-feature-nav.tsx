
'use client'

import React from 'react';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';

export function ProjectFeatureNav() {
  const params = useParams();
  const pathname = usePathname();
  const { workspaceId, projectId } = params;

  const navLinks = [
    { label: 'Overview', path: `/workspace/${workspaceId}/projects/${projectId}` },
    { label: 'Financials', path: `/workspace/${workspaceId}/projects/${projectId}/financials` },
    { label: 'Tasks', path: `/workspace/${workspaceId}/projects/${projectId}/tasks` },
    { label: 'Files', path: `/workspace/${workspaceId}/projects/${projectId}/files` },
    { label: 'Communication', path: `/workspace/${workspaceId}/projects/${projectId}/communication` },
    { label: 'Milestones', path: `/workspace/${workspaceId}/projects/${projectId}/milestones` },
    { label: 'Analytics', path: `/workspace/${workspaceId}/projects/${projectId}/analytics` },
    { label: 'Notes', path: `/workspace/${workspaceId}/projects/${projectId}/notes` },
    { label: 'Team', path: `/workspace/${workspaceId}/projects/${projectId}/team` },
  ];

  return (
    <div className="border-b border-white/20">
      <nav className="-mb-px flex space-x-8 px-4" aria-label="Project Features">
        {navLinks.map((link) => (
          <Link
            key={link.label}
            href={link.path}
            className={`${
              pathname === link.path
                ? 'border-neon-primary text-neon-primary'
                : 'border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-200'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all`}
          >
            {link.label}
          </Link>
        ))}
      </nav>
    </div>
  );
}
