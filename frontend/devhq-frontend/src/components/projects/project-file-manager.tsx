
'use client'

import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, Download, Trash } from 'lucide-react';

const files = [
  { name: 'project-brief.pdf', size: '2.3 MB' },
  { name: 'design-mockups.zip', size: '15.8 MB' },
  { name: 'contract.docx', size: '450 KB' },
];

export function ProjectFileManager() {
  const onDrop = useCallback((acceptedFiles: any) => {
    // Do something with the files
    console.log(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop });

  return (
    <div className="space-y-6">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed border-neon-primary/30 hover:border-neon-primary/60 bg-neon-primary/5 hover:bg-neon-primary/10 transition-all duration-300 rounded-lg p-8 text-center group cursor-pointer ${
          isDragActive ? 'border-neon-primary/60 bg-neon-primary/10' : ''
        }`}>
        <input {...getInputProps()} />
        <Upload className="h-12 w-12 text-neon-primary mx-auto mb-4 group-hover:animate-pulse group-hover:text-neon-electric" />
        <p className="text-foreground font-mono text-sm">DROP FILES HERE</p>
        <p className="text-foreground-muted font-mono text-xs mt-2">
          OR CLICK TO BROWSE
        </p>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neon-primary">Uploaded Files</h3>
        <div className="border border-border/50 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-neon-primary/5 border-b border-neon-primary/20">
              <tr>
                <th className="text-left text-neon-electric font-mono text-xs tracking-wider uppercase p-4">File Name</th>
                <th className="text-left text-neon-electric font-mono text-xs tracking-wider uppercase p-4">Size</th>
                <th className="text-right text-neon-electric font-mono text-xs tracking-wider uppercase p-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              {files.map((file, index) => (
                <tr key={index} className="border-b border-border/30 hover:bg-neon-primary/5 hover:border-neon-primary/30 transition-all">
                  <td className="p-4 font-mono text-sm flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    {file.name}
                  </td>
                  <td className="p-4 font-mono text-sm">{file.size}</td>
                  <td className="p-4 font-mono text-sm text-right">
                    <button className="text-gray-400 hover:text-white mr-2">
                      <Download size={16} />
                    </button>
                    <button className="text-gray-400 hover:text-red-500">
                      <Trash size={16} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
