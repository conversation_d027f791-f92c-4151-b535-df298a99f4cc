/** @format */

"use client";

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import {
  Calculator,
  Clock,
  DollarSign,
  Target,
  AlertTriangle,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Plus,
  Minus,
  Calendar as CalendarIcon,
} from "lucide-react";

interface Milestone {
  id: string;
  name: string;
  estimatedHours: number;
  value: number;
  description: string;
}

interface ProjectPlan {
  name: string;
  description: string;
  billingType: "fixed_price" | "time_and_materials";
  totalBudget: number;
  internalHourlyRate: number;
  deadline: string;
  milestones: Milestone[];
}

const milestoneTemplates = {
  web_app: [
    { name: "Project Setup & Planning", percentage: 10 },
    { name: "UI/UX Design", percentage: 25 },
    { name: "Frontend Development", percentage: 35 },
    { name: "Backend Development", percentage: 20 },
    { name: "Testing & Deployment", percentage: 10 },
  ],
  mobile_app: [
    { name: "Project Setup & Planning", percentage: 10 },
    { name: "UI/UX Design", percentage: 20 },
    { name: "iOS Development", percentage: 30 },
    { name: "Android Development", percentage: 30 },
    { name: "Testing & App Store Submission", percentage: 10 },
  ],
  api_integration: [
    { name: "Requirements Analysis", percentage: 15 },
    { name: "API Design", percentage: 25 },
    { name: "Implementation", percentage: 45 },
    { name: "Testing & Documentation", percentage: 15 },
  ],
  custom: [],
};

interface ProjectPlanningWizardProps {
  onComplete: (plan: ProjectPlan) => void;
  onCancel: () => void;
  initialData?: Partial<Pick<ProjectPlan, "name" | "description" | "deadline">>;
}

export function ProjectPlanningWizard({
  onComplete,
  onCancel,
  initialData,
}: ProjectPlanningWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [projectPlan, setProjectPlan] = useState<ProjectPlan>({
    name: initialData?.name || "",
    description: initialData?.description || "",
    billingType: "fixed_price",
    totalBudget: 0,
    internalHourlyRate: 80,
    deadline: initialData?.deadline || "",
    milestones: [],
  });
  const [selectedTemplate, setSelectedTemplate] = useState<string>("web_app");
  const [calculationMode, setCalculationMode] = useState<
    "budget_to_hours" | "hours_to_rate"
  >("budget_to_hours");

  const totalTimeBudget =
    projectPlan.totalBudget / projectPlan.internalHourlyRate;
  const totalEstimatedHours = projectPlan.milestones.reduce(
    (sum, m) => sum + m.estimatedHours,
    0
  );
  const effectiveHourlyRate =
    totalEstimatedHours > 0 ? projectPlan.totalBudget / totalEstimatedHours : 0;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const generateMilestonesFromTemplate = () => {
    const template =
      milestoneTemplates[selectedTemplate as keyof typeof milestoneTemplates];
    if (template.length === 0) return;

    const milestones: Milestone[] = template.map((item, index) => {
      const estimatedHours =
        calculationMode === "budget_to_hours" ?
          (totalTimeBudget * item.percentage) / 100
        : 0;
      const value = (projectPlan.totalBudget * item.percentage) / 100;

      return {
        id: `milestone-${index}`,
        name: item.name,
        estimatedHours: Math.round(estimatedHours * 10) / 10,
        value: Math.round(value),
        description: "",
      };
    });

    setProjectPlan((prev) => ({ ...prev, milestones }));
  };

  const updateMilestone = (id: string, field: keyof Milestone, value: any) => {
    setProjectPlan((prev) => ({
      ...prev,
      milestones: prev.milestones.map((m) =>
        m.id === id ? { ...m, [field]: value } : m
      ),
    }));
  };

  const addMilestone = () => {
    const newMilestone: Milestone = {
      id: `milestone-${Date.now()}`,
      name: "",
      estimatedHours: 0,
      value: 0,
      description: "",
    };
    setProjectPlan((prev) => ({
      ...prev,
      milestones: [...prev.milestones, newMilestone],
    }));
  };

  const removeMilestone = (id: string) => {
    setProjectPlan((prev) => ({
      ...prev,
      milestones: prev.milestones.filter((m) => m.id !== id),
    }));
  };

  const getHealthCheck = () => {
    const issues = [];
    const warnings = [];

    if (calculationMode === "budget_to_hours") {
      if (totalTimeBudget < 40) {
        issues.push(
          "Very tight timeline - consider increasing budget or reducing scope"
        );
      } else if (totalTimeBudget < 80) {
        warnings.push("Moderate timeline - ensure scope is well-defined");
      }
    } else {
      if (effectiveHourlyRate < 50) {
        issues.push("Low effective hourly rate - consider increasing budget");
      } else if (effectiveHourlyRate < 70) {
        warnings.push("Below-market rate - review pricing strategy");
      }
    }

    if (projectPlan.milestones.length < 3) {
      warnings.push(
        "Consider breaking project into more milestones for better tracking"
      );
    }

    return { issues, warnings };
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Project Basics</h3>
        <div className="space-y-4">
          <div>
            <Label>Project Name</Label>
            <Input
              value={projectPlan.name}
              onChange={(e) =>
                setProjectPlan((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter project name"
            />
          </div>
          <div>
            <Label>Description</Label>
            <Textarea
              value={projectPlan.description}
              onChange={(e) =>
                setProjectPlan((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Brief project description"
              rows={3}
            />
          </div>
          <div>
            <Label>Billing Type</Label>
            <Select
              value={projectPlan.billingType}
              onValueChange={(value) =>
                setProjectPlan((prev) => ({
                  ...prev,
                  billingType: value as "fixed_price" | "time_and_materials",
                }))
              }
            >
              <SelectTrigger className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none">
                <SelectValue placeholder="Select billing type" />
              </SelectTrigger>
              <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
                <SelectItem value="fixed_price">Fixed Price</SelectItem>
                <SelectItem value="time_and_materials">
                  Time & Materials
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          {projectPlan.billingType === "fixed_price" && (
            <div>
              <Label>Project Deadline</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal bg-transparent border-white/20 text-white rounded-none hover:bg-white/10"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {projectPlan.deadline ?
                      format(new Date(projectPlan.deadline), "PPP")
                    : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto p-0 bg-black/80 backdrop-blur-md border-white/20 rounded-none"
                  align="start"
                >
                  <Calendar
                    mode="single"
                    selected={
                      projectPlan.deadline ?
                        new Date(projectPlan.deadline)
                      : undefined
                    }
                    onSelect={(date) =>
                      setProjectPlan((prev) => ({
                        ...prev,
                        deadline: date ? format(date, "yyyy-MM-dd") : "",
                      }))
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Budget & Rate Planning</h3>
        <div className="space-y-4">
          <div>
            <Label>Total Project Budget</Label>
            <Input
              type="number"
              value={projectPlan.totalBudget || ""}
              onChange={(e) =>
                setProjectPlan((prev) => ({
                  ...prev,
                  totalBudget: parseFloat(e.target.value) || 0,
                }))
              }
              placeholder="10000"
            />
          </div>
          <div>
            <Label>Your Internal Hourly Rate</Label>
            <Input
              type="number"
              value={projectPlan.internalHourlyRate || ""}
              onChange={(e) =>
                setProjectPlan((prev) => ({
                  ...prev,
                  internalHourlyRate: parseFloat(e.target.value) || 0,
                }))
              }
              placeholder="80"
            />
            <p className="text-sm text-muted-foreground mt-1">
              This is for planning purposes only and won't be shown to the
              client
            </p>
          </div>

          <Separator />

          <div>
            <Label>Planning Mode</Label>
            <Select
              value={calculationMode}
              onValueChange={(value) =>
                setCalculationMode(value as "budget_to_hours" | "hours_to_rate")
              }
            >
              <SelectTrigger className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none">
                <SelectValue placeholder="Select planning mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="budget_to_hours">
                  Calculate Available Hours
                </SelectItem>
                <SelectItem value="hours_to_rate">
                  Calculate Effective Rate
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {calculationMode === "budget_to_hours" ?
            <Card className="border border-white/20 bg-transparent rounded-none">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Calculator className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">
                    Time Budget Calculation
                  </span>
                </div>
                <div className="text-2xl font-bold text-blue-900">
                  {totalTimeBudget.toFixed(1)} hours
                </div>
                <p className="text-sm text-blue-700">
                  {formatCurrency(projectPlan.totalBudget)} ÷{" "}
                  {formatCurrency(projectPlan.internalHourlyRate)}/hour
                </p>
              </CardContent>
            </Card>
          : <Card className="border border-white/20 bg-transparent rounded-none">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900">
                    Effective Rate Calculation
                  </span>
                </div>
                <div className="text-2xl font-bold text-green-900">
                  {formatCurrency(effectiveHourlyRate)}/hour
                </div>
                <p className="text-sm text-green-700">
                  {formatCurrency(projectPlan.totalBudget)} ÷{" "}
                  {totalEstimatedHours} hours
                </p>
              </CardContent>
            </Card>
          }
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Milestone Planning</h3>

        <div className="mb-4">
          <Label>Project Template</Label>
          <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
            <SelectTrigger className="bg-transparent border border-white/30 text-white rounded-none text-sm focus:border-green-400 focus:ring-1 focus:ring-green-400 focus:outline-none">
              <SelectValue placeholder="Select template" />
            </SelectTrigger>
            <SelectContent className="bg-black/80 backdrop-blur-md border-white/20 rounded-none">
              <SelectItem value="web_app">Web Application</SelectItem>
              <SelectItem value="mobile_app">Mobile Application</SelectItem>
              <SelectItem value="api_integration">API Integration</SelectItem>
              <SelectItem value="custom">
                Custom (Start from scratch)
              </SelectItem>
            </SelectContent>
          </Select>
          {selectedTemplate !== "custom" && (
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={generateMilestonesFromTemplate}
            >
              Apply Template
            </Button>
          )}
        </div>

        <div className="space-y-4">
          {projectPlan.milestones.map((milestone, index) => (
            <Card key={milestone.id}>
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  <div className="flex-1 space-y-3">
                    <div>
                      <Label>Milestone Name</Label>
                      <Input
                        value={milestone.name}
                        onChange={(e) =>
                          updateMilestone(milestone.id, "name", e.target.value)
                        }
                        placeholder="Enter milestone name"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label>Estimated Hours</Label>
                        <Input
                          type="number"
                          value={milestone.estimatedHours || ""}
                          onChange={(e) =>
                            updateMilestone(
                              milestone.id,
                              "estimatedHours",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          placeholder="0"
                        />
                      </div>
                      <div>
                        <Label>Value</Label>
                        <Input
                          type="number"
                          value={milestone.value || ""}
                          onChange={(e) =>
                            updateMilestone(
                              milestone.id,
                              "value",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          placeholder="0"
                        />
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeMilestone(milestone.id)}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          <Button variant="outline" onClick={addMilestone} className="w-full">
            <Plus className="h-4 w-4 mr-2" />
            Add Milestone
          </Button>
        </div>
      </div>
    </div>
  );

  const renderStep4 = () => {
    const { issues, warnings } = getHealthCheck();

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Project Summary</h3>

          {/* Health Check */}
          {(issues.length > 0 || warnings.length > 0) && (
            <div className="space-y-3 mb-6">
              {issues.map((issue, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg"
                >
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-800">{issue}</span>
                </div>
              ))}
              {warnings.map((warning, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                >
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">{warning}</span>
                </div>
              ))}
            </div>
          )}

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Budget</span>
                </div>
                <div className="text-2xl font-bold">
                  {formatCurrency(projectPlan.totalBudget)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Total Hours</span>
                </div>
                <div className="text-2xl font-bold">{totalEstimatedHours}h</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-purple-600" />
                  <span className="font-medium">Effective Rate</span>
                </div>
                <div className="text-2xl font-bold">
                  {formatCurrency(effectiveHourlyRate)}/h
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Milestones</span>
                </div>
                <div className="text-2xl font-bold">
                  {projectPlan.milestones.length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Milestone Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Milestone Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {projectPlan.milestones.map((milestone, index) => (
                  <div
                    key={milestone.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <div className="font-medium">{milestone.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {milestone.estimatedHours}h •{" "}
                        {formatCurrency(milestone.value)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">
                        {(
                          (milestone.value / projectPlan.totalBudget) *
                          100
                        ).toFixed(1)}
                        % of budget
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return projectPlan.name && projectPlan.billingType;
      case 2:
        return (
          projectPlan.totalBudget > 0 && projectPlan.internalHourlyRate > 0
        );
      case 3:
        return (
          projectPlan.milestones.length > 0 &&
          projectPlan.milestones.every(
            (m) => m.name && m.estimatedHours > 0 && m.value > 0
          )
        );
      default:
        return true;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Project Planner & Budgeter</CardTitle>
            <Badge variant="outline">Step {currentStep} of 4</Badge>
          </div>
          <Progress value={(currentStep / 4) * 100} className="mt-2" />
        </CardHeader>
        <CardContent className="p-6">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
          {currentStep === 4 && renderStep4()}

          <div className="flex items-center justify-between mt-8 pt-6 border-t">
            <Button
              variant="outline"
              onClick={
                currentStep === 1 ? onCancel : (
                  () => setCurrentStep(currentStep - 1)
                )
              }
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {currentStep === 1 ? "Cancel" : "Previous"}
            </Button>

            {currentStep < 4 ?
              <Button
                onClick={() => setCurrentStep(currentStep + 1)}
                disabled={!canProceed()}
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            : <Button
                onClick={() => onComplete(projectPlan)}
                disabled={!canProceed()}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Create Project
              </Button>
            }
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
