
'use client'

import React from 'react';
import { MessageSquare, Plus } from 'lucide-react';

const communicationLog = [
  {
    id: 1,
    type: 'Email',
    subject: 'Project Kick-off',
    date: '2024-08-26',
    author: '<PERSON>',
  },
  {
    id: 2,
    type: 'Meeting',
    subject: 'Design Review',
    date: '2024-08-27',
    author: '<PERSON>',
  },
  {
    id: 3,
    type: 'Call',
    subject: 'Follow-up on feedback',
    date: '2024-08-28',
    author: '<PERSON>',
  },
];

export function ProjectCommunicationLog() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-neon-primary">Communication Log</h3>
        <button className="flex items-center text-gray-400 hover:text-white">
          <Plus size={16} className="mr-2" />
          Add Log
        </button>
      </div>
      <div className="border border-border/50 rounded-lg p-4 space-y-4">
        {communicationLog.map((log) => (
          <div key={log.id} className="flex items-start space-x-4">
            <div className="bg-card/50 p-2 rounded-full">
              <MessageSquare size={20} className="text-neon-primary" />
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <p className="font-semibold">{log.subject}</p>
                <p className="text-xs text-gray-400">{log.date}</p>
              </div>
              <p className="text-sm text-gray-400">Type: {log.type}</p>
              <p className="text-sm text-gray-400">Author: {log.author}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
