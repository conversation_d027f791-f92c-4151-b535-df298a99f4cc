/** @format */

"use client";

import React, { useState } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { Plus, MoreHorizontal } from "lucide-react";

const initialData = {
  tasks: {
    "task-1": { id: "task-1", content: "Design the new landing page" },
    "task-2": {
      id: "task-2",
      content: "Develop the API for user authentication",
    },
    "task-3": { id: "task-3", content: "Test the new features" },
    "task-4": { id: "task-4", content: "Deploy the application to production" },
  },
  columns: {
    "column-1": {
      id: "column-1",
      title: "To Do",
      taskIds: ["task-1", "task-2", "task-3", "task-4"],
    },
    "column-2": {
      id: "column-2",
      title: "In Progress",
      taskIds: [],
    },
    "column-3": {
      id: "column-3",
      title: "Done",
      taskIds: [],
    },
  },
  columnOrder: ["column-1", "column-2", "column-3"],
};

export function ProjectTaskBoard() {
  const [data, setData] = useState(initialData);

  const onDragEnd = (result: any) => {
    const { destination, source, draggableId } = result;

    if (!destination) {
      return;
    }

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const start = (data.columns as any)[source.droppableId];
    const end = (data.columns as any)[destination.droppableId];

    if (start === end) {
      const newTaskIds = Array.from(start.taskIds);
      newTaskIds.splice(source.index, 1);
      newTaskIds.splice(destination.index, 0, draggableId);

      const newColumn = {
        ...start,
        taskIds: newTaskIds,
      };

      const newState = {
        ...data,
        columns: {
          ...data.columns,
          [newColumn.id]: newColumn,
        },
      };

      setData(newState);
      return;
    }

    // Moving from one list to another
    const startTaskIds = Array.from(start.taskIds);
    startTaskIds.splice(source.index, 1);
    const newStart = {
      ...start,
      taskIds: startTaskIds,
    };

    const endTaskIds = Array.from(end.taskIds);
    endTaskIds.splice(destination.index, 0, draggableId);
    const newEnd = {
      ...end,
      taskIds: endTaskIds,
    };

    const newState = {
      ...data,
      columns: {
        ...data.columns,
        [newStart.id]: newStart,
        [newEnd.id]: newEnd,
      },
    };

    setData(newState);
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex space-x-4">
        {data.columnOrder.map((columnId) => {
          const column = (data.columns as any)[columnId];
          const tasks = column.taskIds.map(
            (taskId: string) => (data.tasks as any)[taskId]
          );

          return (
            <div key={column.id} className="w-72 bg-card/50 rounded-lg p-2">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-semibold text-neon-primary">
                  {column.title}
                </h3>
                <button className="text-gray-400 hover:text-white">
                  <MoreHorizontal size={20} />
                </button>
              </div>
              <Droppable droppableId={column.id}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={`space-y-2 ${snapshot.isDraggingOver ? "bg-card/70" : ""}`}
                  >
                    {tasks.map((task: any, index: number) => (
                      <Draggable
                        key={task.id}
                        draggableId={task.id}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={`bg-card border border-border/50 rounded-md p-3 ${snapshot.isDragging ? "shadow-lg shadow-neon-primary/20" : ""}`}
                          >
                            {task.content}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
              <button className="w-full flex items-center justify-center mt-2 text-gray-400 hover:text-white">
                <Plus size={16} className="mr-2" />
                Add a card
              </button>
            </div>
          );
        })}
      </div>
    </DragDropContext>
  );
}
