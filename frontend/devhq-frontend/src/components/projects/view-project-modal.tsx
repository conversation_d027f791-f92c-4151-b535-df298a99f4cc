'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  X, Calendar, DollarSign, Clock, Users, Star, 
  Play, Pause, CheckCircle2, Archive, TrendingUp,
  TrendingDown, Target, Activity, GitBranch, Edit
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ViewProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onEdit: () => void
  project: any
}

const statusConfig = {
  active: { 
    label: 'Active', 
    color: 'text-neon-success', 
    bg: 'bg-neon-success/10', 
    border: 'border-neon-success/30',
    icon: Play
  },
  paused: { 
    label: 'Paused', 
    color: 'text-neon-warning', 
    bg: 'bg-neon-warning/10', 
    border: 'border-neon-warning/30',
    icon: Pause
  },
  completed: { 
    label: 'Completed', 
    color: 'text-neon-info', 
    bg: 'bg-neon-info/10', 
    border: 'border-neon-info/30',
    icon: CheckCircle2
  },
  archived: { 
    label: 'Archived', 
    color: 'text-foreground-muted', 
    bg: 'bg-foreground-muted/10', 
    border: 'border-foreground-muted/30',
    icon: Archive
  }
}

const priorityConfig = {
  high: { label: 'High', color: 'text-neon-error', bg: 'bg-neon-error/10' },
  medium: { label: 'Medium', color: 'text-neon-warning', bg: 'bg-neon-warning/10' },
  low: { label: 'Low', color: 'text-neon-info', bg: 'bg-neon-info/10' }
}

export function ViewProjectModal({ isOpen, onClose, onEdit, project }: ViewProjectModalProps) {
  if (!isOpen || !project) return null

  const status = statusConfig[project.status as keyof typeof statusConfig]
  const priority = priorityConfig[project.priority as keyof typeof priorityConfig]
  const StatusIcon = status.icon

  const budgetUsed = (project.spent / project.budget) * 100
  const hoursUsed = (project.hoursTracked / project.estimatedHours) * 100

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-background/20 backdrop-blur-xl border border-border/40 shadow-2xl">
        {/* Glass reflection effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-60 rounded-lg" />
        
        {/* Glass highlight edge */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
        
        <CardHeader className="border-b border-border/50 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-neon-primary rounded-full animate-pulse" />
              <CardTitle className="text-2xl font-mono text-neon-primary">
                {project.name}
              </CardTitle>
              {project.starred && (
                <Star className="h-5 w-5 text-neon-warning fill-current" />
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={onEdit} className="bg-background/20 backdrop-blur-md border-border/30">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 mt-2">
            <Badge className={cn("text-sm font-mono", status.color, status.bg, status.border)}>
              <StatusIcon className="h-4 w-4 mr-1" />
              {status.label}
            </Badge>
            <Badge variant="outline" className={cn("text-sm", priority.color, priority.bg)}>
              {priority.label} Priority
            </Badge>
            <div className="text-sm text-foreground-muted font-mono">
              Client: {project.client}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Description */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-foreground">Description</h3>
                <p className="text-foreground-muted leading-relaxed">
                  {project.description}
                </p>
              </div>

              {/* Progress Overview */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Progress Overview</h3>
                
                {/* Project Progress */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-mono text-foreground-muted">Project Completion</span>
                    <span className="text-lg font-bold text-neon-primary">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-background-tertiary rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-neon-primary to-neon-cyan h-3 rounded-full transition-all duration-500 shadow-[0_0_10px_rgba(0,255,136,0.3)]"
                      style={{ width: `${project.progress}%` }}
                    />
                  </div>
                </div>

                {/* Budget Progress */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-mono text-foreground-muted">Budget Used</span>
                    <span className="text-lg font-bold text-foreground">
                      ${project.spent.toLocaleString()} / ${project.budget.toLocaleString()}
                    </span>
                  </div>
                  <div className="w-full bg-background-tertiary rounded-full h-3">
                    <div 
                      className={cn(
                        "h-3 rounded-full transition-all duration-500",
                        budgetUsed > 90 ? "bg-gradient-to-r from-neon-error to-neon-warning" :
                        budgetUsed > 75 ? "bg-gradient-to-r from-neon-warning to-neon-primary" :
                        "bg-gradient-to-r from-neon-primary to-neon-cyan"
                      )}
                      style={{ width: `${Math.min(budgetUsed, 100)}%` }}
                    />
                  </div>
                  <div className="text-xs text-foreground-muted font-mono">
                    {budgetUsed.toFixed(1)}% of budget used
                  </div>
                </div>

                {/* Time Progress */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-mono text-foreground-muted">Time Tracked</span>
                    <span className="text-lg font-bold text-foreground">
                      {project.hoursTracked}h / {project.estimatedHours}h
                    </span>
                  </div>
                  <div className="w-full bg-background-tertiary rounded-full h-3">
                    <div 
                      className={cn(
                        "h-3 rounded-full transition-all duration-500",
                        hoursUsed > 90 ? "bg-gradient-to-r from-neon-error to-neon-warning" :
                        hoursUsed > 75 ? "bg-gradient-to-r from-neon-warning to-neon-primary" :
                        "bg-gradient-to-r from-neon-primary to-neon-cyan"
                      )}
                      style={{ width: `${Math.min(hoursUsed, 100)}%` }}
                    />
                  </div>
                  <div className="text-xs text-foreground-muted font-mono">
                    {hoursUsed.toFixed(1)}% of estimated time used
                  </div>
                </div>
              </div>

              {/* Technologies */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-foreground">Technology Stack</h3>
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech: string, index: number) => (
                    <Badge key={index} variant="outline" className="bg-background/20 backdrop-blur-md border-border/30">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Key Metrics */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Target className="h-4 w-4 mr-2" />
                    Key Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Deadline</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {new Date(project.deadline).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Last Activity</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {project.lastActivity}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Remaining Budget</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      ${(project.budget - project.spent).toLocaleString()}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Remaining Hours</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {Math.max(0, project.estimatedHours - project.hoursTracked)}h
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Team Members */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    Team Members
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {project.team.map((member: string, index: number) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-neon-primary/20 border border-neon-primary/30 flex items-center justify-center text-xs font-mono text-neon-primary">
                          {member}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">Team Member {index + 1}</p>
                          <p className="text-xs text-foreground-muted">Developer</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <GitBranch className="h-4 w-4 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <Play className="h-4 w-4 mr-2" />
                    Start Timer
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <DollarSign className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <Activity className="h-4 w-4 mr-2" />
                    View Reports
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}