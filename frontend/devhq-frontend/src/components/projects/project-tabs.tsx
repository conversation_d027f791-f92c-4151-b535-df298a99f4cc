
'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ProjectFinancialDashboard } from './project-financial-dashboard'
import { ProjectTaskBoard } from './project-task-board'
import { ProjectFileManager } from './project-file-manager'
import { ProjectCommunicationLog } from './project-communication-log'
import { ProjectMilestoneTracker } from './project-milestone-tracker'
import { ProjectAnalyticsDashboard } from './project-analytics-dashboard'
import { ProjectNotes } from './project-notes'
import { ProjectTeamManager } from './project-team-manager'

const tabs = [
  { name: 'Financials', component: ProjectFinancialDashboard },
  { name: 'Tasks', component: ProjectTaskBoard },
  { name: 'Files', component: ProjectFileManager },
  { name: 'Communication', component: ProjectCommunicationLog },
  { name: 'Milestones', component: ProjectMilestoneTracker },
  { name: 'Analytics', component: ProjectAnalyticsDashboard },
  { name: 'Notes', component: ProjectNotes },
  { name: 'Team', component: ProjectTeamManager },
]

export function ProjectTabs() {
  const [activeTab, setActiveTab] = useState(tabs[0].name)

  const ActiveComponent = tabs.find((tab) => tab.name === activeTab)?.component

  return (
    <div>
      <div className="border-b border-white/20">
        <div className="-mb-px flex space-x-8 px-4" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.name}
              onClick={() => setActiveTab(tab.name)}
              className={`${
                activeTab === tab.name
                  ? 'border-neon-primary text-neon-primary'
                  : 'border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-200'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all`}
            >
              {tab.name}
            </button>
          ))}
        </div>
      </div>
      <div className="mt-8">
        {ActiveComponent && <ActiveComponent />}
      </div>
    </div>
  )
}
