'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

interface CreateProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (projectData: any) => void
}

export function CreateProjectModal({ isOpen, onClose, onSubmit }: CreateProjectModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.name.trim()) {
      onSubmit(formData)
      setFormData({ name: '', description: '' })
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="!rounded-none !border !border-white/10 max-w-2xl [&>button]:!rounded-none sm:!rounded-none">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Create a workspace</DialogTitle>
          <p className="text-sm text-gray-500 mt-2">Set up a new workspace to organize your work and track progress</p>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="workspace-name" className="text-sm font-medium">
              Workspace Name
            </Label>
            <Input
              id="workspace-name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter workspace name"
              className="rounded-none border border-white/10 focus:border-green-500 bg-transparent"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="workspace-description" className="text-sm font-medium">
              Workspace Description
            </Label>
            <Textarea
              id="workspace-description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter workspace description"
              className="rounded-none border border-white/10 focus:border-green-500 min-h-[100px]"
              rows={4}
            />
          </div>

          <div className="flex justify-start space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="rounded-none border border-white/10 hover:bg-white hover:text-black"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="rounded-none bg-green-500 hover:bg-green-600 text-black"
            >
              Create Workspace
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}