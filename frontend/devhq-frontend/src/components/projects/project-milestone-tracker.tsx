
'use client'

import React from 'react';
import { Target, Plus } from 'lucide-react';

const milestones = [
  {
    id: 1,
    name: 'Phase 1: Discovery & Planning',
    dueDate: '2024-09-15',
    progress: 100,
  },
  {
    id: 2,
    name: 'Phase 2: Design & Prototyping',
    dueDate: '2024-10-31',
    progress: 75,
  },
  {
    id: 3,
    name: 'Phase 3: Development & Implementation',
    dueDate: '2024-12-31',
    progress: 25,
  },
  {
    id: 4,
    name: 'Phase 4: Testing & Deployment',
    dueDate: '2025-01-31',
    progress: 0,
  },
];

export function ProjectMilestoneTracker() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-neon-primary">Milestone Tracker</h3>
        <button className="flex items-center text-gray-400 hover:text-white">
          <Plus size={16} className="mr-2" />
          Add Milestone
        </button>
      </div>
      <div className="space-y-4">
        {milestones.map((milestone) => (
          <div key={milestone.id} className="bg-card/50 border border-border/50 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <p className="font-semibold flex items-center">
                <Target size={16} className="mr-2 text-neon-primary" />
                {milestone.name}
              </p>
              <p className="text-xs text-gray-400">Due: {milestone.dueDate}</p>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <div
                className="bg-neon-primary h-2.5 rounded-full"
                style={{ width: `${milestone.progress}%` }}></div>
            </div>
            <p className="text-right text-xs text-gray-400 mt-1">{milestone.progress}%</p>
          </div>
        ))}
      </div>
    </div>
  );
}
