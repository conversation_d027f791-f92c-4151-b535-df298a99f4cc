
'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { DollarSign, TrendingUp, TrendingDown, Target } from 'lucide-react'
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts'

const budgetData = [
  { name: 'Q1', budget: 4000, spent: 2400 },
  { name: 'Q2', budget: 3000, spent: 1398 },
  { name: 'Q3', budget: 2000, spent: 9800 },
  { name: 'Q4', budget: 2780, spent: 3908 },
]

export function ProjectFinancialDashboard() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Total Budget
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$45,231.89</div>
            <p className="text-xs text-muted-foreground">
              +20.1% from last month
            </p>
          </CardContent>
        </Card>
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Amount Spent
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$25,123.45</div>
            <p className="text-xs text-muted-foreground">
              +180.1% from last month
            </p>
          </CardContent>
        </Card>
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Remaining Budget
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$20,108.44</div>
            <p className="text-xs text-muted-foreground">
              +19% from last month
            </p>
          </CardContent>
        </Card>
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Burn Rate
            </CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">55.5%</div>
            <p className="text-xs text-muted-foreground">
              Rate of budget consumption
            </p>
          </CardContent>
        </Card>
      </div>
      <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
        <CardHeader>
          <CardTitle className="text-neon-primary group-hover:text-neon-primary/80">
            Budget vs. Actual Spending
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={budgetData}>
              <XAxis
                dataKey="name"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `$${value}`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  borderColor: 'hsl(var(--border))',
                }}
              />
              <Legend />
              <Bar
                dataKey="budget"
                fill="hsl(var(--primary))"
                radius={[4, 4, 0, 0]}
              />
              <Bar
                dataKey="spent"
                fill="hsl(var(--neon-primary))"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}
