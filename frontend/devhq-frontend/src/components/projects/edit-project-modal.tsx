'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface EditProjectModalProps {
  isOpen: boolean
  onClose: () => void
  project: any // Replace 'any' with a proper Project type later
  onSave: (updatedProject: any) => void // Replace 'any' with a proper Project type later
}

export function EditProjectModal({ isOpen, onClose, project, onSave }: EditProjectModalProps) {
  const [editedProject, setEditedProject] = useState(project)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setEditedProject((prev: any) => ({ ...prev, [id]: value }))
  }

  const handleLanguagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    // Split by comma and trim whitespace
    const languagesArray = value.split(',').map(lang => lang.trim()).filter(lang => lang.length > 0)
    setEditedProject((prev: any) => ({ ...prev, languages: languagesArray }))
  }

  const handleSave = () => {
    onSave(editedProject)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] min-h-[500px] rounded-none !rounded-none [&>button]:rounded-none [&>button]:hover:bg-green-500/20 [&>button]:hover:outline-none [&>button]:focus:outline-none [&>button]:focus:ring-0">
        <DialogHeader>
          <DialogTitle>Edit Project</DialogTitle>
          <DialogDescription>
            Make changes to your project here. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="space-y-2">
            <Label htmlFor="type">
              Project Type
            </Label>
            <Input
              id="type"
              value={editedProject.type}
              onChange={handleChange}
              className="w-full rounded-none bg-background"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="name">
              Project Name
            </Label>
            <Input
              id="name"
              value={editedProject.name}
              onChange={handleChange}
              className="w-full rounded-none bg-background"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="site">
              Website Link
            </Label>
            <Input
              id="site"
              value={editedProject.site}
              onChange={handleChange}
              className="w-full rounded-none bg-background"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="languages">
              Programming Languages
            </Label>
            <Input
              id="languages"
              value={editedProject.languages ? editedProject.languages.join(', ') : ''}
              onChange={handleLanguagesChange}
              placeholder="e.g. React, TypeScript, Node.js"
              className="w-full rounded-none bg-background"
            />
            <p className="text-xs text-muted-foreground">
              Separate multiple languages with commas
            </p>
          </div>
        </div>
        <DialogFooter className="!justify-start !flex-row">
          <Button variant="outline" onClick={onClose} className="rounded-none hover:bg-white hover:text-black">Cancel</Button>
          <Button onClick={handleSave} className="rounded-none">Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
