'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  FolderOpen, Clock, DollarSign, TrendingUp, TrendingDown, 
  Users, Target, Zap, Activity, AlertTriangle, CheckCircle2,
  Play, Pause, Archive
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProjectOverviewProps {
  className?: string
}

const projectStats = {
  total: 12,
  active: 8,
  paused: 2,
  completed: 15,
  archived: 3,
  totalRevenue: 245000,
  monthlyRevenue: 42000,
  revenueChange: 15.2,
  totalHours: 1847,
  monthlyHours: 324,
  hoursChange: -8.5,
  teamMembers: 12,
  avgProjectDuration: 3.2,
  onTimeDelivery: 87
}

const recentActivity = [
  {
    id: 1,
    type: 'project_created',
    message: 'New project "AI Chatbot Integration" created',
    timestamp: '2 hours ago',
    icon: FolderOpen,
    color: 'text-neon-success'
  },
  {
    id: 2,
    type: 'milestone_completed',
    message: 'DevHQ Platform reached 75% completion',
    timestamp: '4 hours ago',
    icon: Target,
    color: 'text-neon-info'
  },
  {
    id: 3,
    type: 'project_paused',
    message: 'Mobile Banking App temporarily paused',
    timestamp: '1 day ago',
    icon: Pause,
    color: 'text-neon-warning'
  },
  {
    id: 4,
    type: 'deadline_approaching',
    message: 'E-Commerce Redesign deadline in 3 days',
    timestamp: '2 days ago',
    icon: AlertTriangle,
    color: 'text-neon-error'
  }
]

export function ProjectOverview({ className }: ProjectOverviewProps) {
  return (
    <div className={cn("space-y-6", className)}>
      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Active Projects */}
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  Active Projects
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    {projectStats.active}
                  </p>
                  <div className="flex items-center text-xs font-mono text-neon-success">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +2
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <Play className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Revenue */}
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  Monthly Revenue
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    ${(projectStats.monthlyRevenue / 1000).toFixed(0)}k
                  </p>
                  <div className="flex items-center text-xs font-mono text-neon-success">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +{projectStats.revenueChange}%
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <DollarSign className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Hours This Month */}
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  Hours This Month
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    {projectStats.monthlyHours}h
                  </p>
                  <div className="flex items-center text-xs font-mono text-neon-error">
                    <TrendingDown className="h-3 w-3 mr-1" />
                    {projectStats.hoursChange}%
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <Clock className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members */}
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-xs font-mono text-foreground-muted uppercase tracking-wider">
                  Team Members
                </p>
                <div className="flex items-baseline space-x-2">
                  <p className="text-2xl font-bold text-foreground">
                    {projectStats.teamMembers}
                  </p>
                  <div className="flex items-center text-xs font-mono text-neon-info">
                    <Users className="h-3 w-3 mr-1" />
                    Active
                  </div>
                </div>
              </div>
              <div className="text-neon-primary opacity-30 group-hover:opacity-80 transition-opacity">
                <Users className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Status Distribution */}
      <Card className="border-border/50 bg-card/50">
        <CardHeader>
          <CardTitle className="text-base font-mono text-neon-primary flex items-center">
            <div className="w-2 h-2 bg-neon-primary rounded-full mr-2 animate-pulse" />
            Project Status Distribution
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center space-y-2">
              <div className="w-12 h-12 mx-auto bg-neon-success/10 border border-neon-success/30 rounded-lg flex items-center justify-center">
                <Play className="h-6 w-6 text-neon-success" />
              </div>
              <div>
                <p className="text-2xl font-bold text-foreground">{projectStats.active}</p>
                <p className="text-xs text-foreground-muted font-mono">Active</p>
              </div>
            </div>
            
            <div className="text-center space-y-2">
              <div className="w-12 h-12 mx-auto bg-neon-warning/10 border border-neon-warning/30 rounded-lg flex items-center justify-center">
                <Pause className="h-6 w-6 text-neon-warning" />
              </div>
              <div>
                <p className="text-2xl font-bold text-foreground">{projectStats.paused}</p>
                <p className="text-xs text-foreground-muted font-mono">Paused</p>
              </div>
            </div>
            
            <div className="text-center space-y-2">
              <div className="w-12 h-12 mx-auto bg-neon-info/10 border border-neon-info/30 rounded-lg flex items-center justify-center">
                <CheckCircle2 className="h-6 w-6 text-neon-info" />
              </div>
              <div>
                <p className="text-2xl font-bold text-foreground">{projectStats.completed}</p>
                <p className="text-xs text-foreground-muted font-mono">Completed</p>
              </div>
            </div>
            
            <div className="text-center space-y-2">
              <div className="w-12 h-12 mx-auto bg-foreground-muted/10 border border-foreground-muted/30 rounded-lg flex items-center justify-center">
                <Archive className="h-6 w-6 text-foreground-muted" />
              </div>
              <div>
                <p className="text-2xl font-bold text-foreground">{projectStats.archived}</p>
                <p className="text-xs text-foreground-muted font-mono">Archived</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="border-border/50 bg-card/50">
        <CardHeader>
          <CardTitle className="text-base font-mono text-neon-primary flex items-center">
            <Activity className="h-5 w-5 text-neon-primary mr-2" />
            Recent Activity
            <div className="w-2 h-2 bg-neon-primary rounded-full ml-auto animate-pulse" />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {recentActivity.map((activity) => {
            const Icon = activity.icon
            return (
              <div
                key={activity.id}
                className="flex items-center space-x-3 p-3 rounded-md bg-background-tertiary/50 border border-border/50 hover:border-neon-primary/20 transition-colors"
              >
                <div className="flex-shrink-0">
                  <div className={cn(
                    "w-8 h-8 rounded-lg flex items-center justify-center",
                    activity.color === 'text-neon-success' && "bg-neon-success/10 border border-neon-success/30",
                    activity.color === 'text-neon-info' && "bg-neon-info/10 border border-neon-info/30",
                    activity.color === 'text-neon-warning' && "bg-neon-warning/10 border border-neon-warning/30",
                    activity.color === 'text-neon-error' && "bg-neon-error/10 border border-neon-error/30"
                  )}>
                    <Icon className={cn("h-4 w-4", activity.color)} />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-foreground">
                    {activity.message}
                  </p>
                  <p className="text-xs text-foreground-muted font-mono">
                    {activity.timestamp}
                  </p>
                </div>
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-border/50 bg-card/50">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="w-12 h-12 mx-auto bg-neon-primary/10 border border-neon-primary/30 rounded-lg flex items-center justify-center">
                <Target className="h-6 w-6 text-neon-primary" />
              </div>
              <div>
                <p className="text-2xl font-bold text-foreground">{projectStats.onTimeDelivery}%</p>
                <p className="text-xs text-foreground-muted font-mono">On-Time Delivery</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50 bg-card/50">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="w-12 h-12 mx-auto bg-neon-cyan/10 border border-neon-cyan/30 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-neon-cyan" />
              </div>
              <div>
                <p className="text-2xl font-bold text-foreground">{projectStats.avgProjectDuration}</p>
                <p className="text-xs text-foreground-muted font-mono">Avg Duration (months)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50 bg-card/50">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="w-12 h-12 mx-auto bg-neon-purple/10 border border-neon-purple/30 rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-neon-purple" />
              </div>
              <div>
                <p className="text-2xl font-bold text-foreground">${(projectStats.totalRevenue / 1000).toFixed(0)}k</p>
                <p className="text-xs text-foreground-muted font-mono">Total Revenue</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}