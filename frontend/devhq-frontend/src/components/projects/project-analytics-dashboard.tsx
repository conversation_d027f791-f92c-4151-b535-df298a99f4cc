/** @format */

"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Line<PERSON>hart,
  Line,
} from "recharts";
import { Activity, Clock, Zap } from "lucide-react";

const taskData = [
  { name: "Jan", tasks: 30 },
  { name: "Feb", tasks: 25 },
  { name: "<PERSON>", tasks: 45 },
  { name: "Apr", tasks: 40 },
  { name: "May", tasks: 50 },
  { name: "<PERSON>", tasks: 48 },
];

const timeData: any[] = [];

import { useParams } from "next/navigation";
import { useProject } from "@/contexts/project-context";

function formatCurrency(n: number) {
  try {
    return new Intl.NumberFormat(undefined, {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(n);
  } catch {
    return `$${Math.round(n).toLocaleString()}`;
  }
}

export function ProjectAnalyticsDashboard() {
  const { project } = useProject();
  const params = useParams() as { workspaceId: string; projectId: string };
  const { workspaceId, projectId } = params;
  const proj = project || ({} as any);
  const milestones: Array<any> =
    Array.isArray(proj.milestones) ? proj.milestones : [];
  const expenses: Array<any> =
    Array.isArray(proj.expenses) ? proj.expenses : [];
  const tasks: Array<any> = Array.isArray(proj.tasks) ? proj.tasks : [];
  const invoices: Array<any> =
    Array.isArray(proj.invoices) ? proj.invoices : [];

  // Load time entries for this project (same key as time page)
  let timeEntries: Array<any> = [];
  try {
    const timeStorageKey = `timeEntries_${workspaceId}_${projectId}`;
    const raw = localStorage.getItem(timeStorageKey);
    timeEntries = raw ? JSON.parse(raw) : [];
  } catch {}

  const byMilestone = milestones.map((m: any) => {
    const planned = Number(m.plannedBudget) || 0;

    // Actual spend from expenses
    const actual = expenses
      .filter((e: any) => String(e.milestoneId || "") === String(m.id))
      .reduce((sum: number, e: any) => sum + (Number(e.amount) || 0), 0);
    const variance = planned - actual;
    const usagePct = planned > 0 ? actual / planned : 0;

    // Hours vs estimate (plannedHours vs logged time)
    const plannedHours = Number(m.plannedHours) || 0;
    const hoursLogged = timeEntries
      .filter((te: any) => String(te.milestoneId || "") === String(m.id))
      .reduce((sum: number, te: any) => sum + (Number(te.duration) || 0), 0);

    const alert = planned > 0 && usagePct >= 0.75;
    return {
      id: String(m.id),
      name: String(m.name || m.id),
      planned,
      actual,
      variance,
      usagePct,
      alert,
      dueDate: m.dueDate || null,
      plannedHours,
      hoursLogged,
    };
  });

  const totals = byMilestone.reduce(
    (acc, it) => {
      acc.planned += it.planned;
      acc.actual += it.actual;
      return acc;
    },
    { planned: 0, actual: 0 }
  );

  // Sprint 4: Effective rate and contribution
  const totalPaid = invoices
    .filter((i: any) => i.status === "Paid")
    .reduce((s: number, i: any) => s + (Number(i.amount) || 0), 0);
  const totalHours = timeEntries.reduce(
    (s: number, te: any) => s + (Number(te.duration) || 0),
    0
  );
  const effectiveRate = totalHours > 0 ? totalPaid / totalHours : 0;

  const contributionByMember: Record<string, number> = {};
  for (const te of timeEntries) {
    const u = te.userId || "unknown";
    contributionByMember[u] =
      (contributionByMember[u] || 0) + (Number(te.duration) || 0);
  }
  const contributionChart = Object.entries(contributionByMember).map(
    ([user, hours]) => ({ name: String(user), Hours: hours })
  );

  const alerts = byMilestone.filter((m) => m.alert);

  const chart = byMilestone.map((m) => ({
    name: m.name,
    Planned: m.planned,
    Actual: m.actual,
  }));

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Total Planned Budget
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(totals.planned)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all milestones
            </p>
          </CardContent>
        </Card>
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Total Actual Spend
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(totals.actual)}
            </div>
            <p className="text-xs text-muted-foreground">
              Sum of milestone expenses
            </p>
          </CardContent>
        </Card>
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Budget Alerts
            </CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{alerts.length}</div>
            <p className="text-xs text-muted-foreground">
              Milestones at or above 75% usage
            </p>
          </CardContent>
        </Card>
        <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neon-primary group-hover:text-neon-primary/80">
              Effective Rate
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {effectiveRate > 0 ? `$${effectiveRate.toFixed(2)}/hr` : "—"}
            </div>
            <p className="text-xs text-muted-foreground">
              Paid amount per hour logged
            </p>
          </CardContent>
        </Card>
      </div>
      <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
        <CardHeader>
          <CardTitle className="text-neon-primary group-hover:text-neon-primary/80">
            Budget vs Actual by Milestone
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chart}>
              <XAxis
                dataKey="name"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "hsl(var(--background))",
                  borderColor: "hsl(var(--border))",
                }}
              />
              <Legend />
              <Bar
                dataKey="Planned"
                fill="hsl(var(--neon-primary))"
                radius={[4, 4, 0, 0]}
              />
              <Bar
                dataKey="Actual"
                fill="hsl(var(--foreground))"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
        <CardHeader>
          <CardTitle className="text-neon-primary group-hover:text-neon-primary/80">
            Contribution by Member
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={260}>
            <BarChart data={contributionChart}>
              <XAxis
                dataKey="name"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "hsl(var(--background))",
                  borderColor: "hsl(var(--border))",
                }}
              />
              <Legend />
              <Bar
                dataKey="Hours"
                fill="hsl(var(--foreground))"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card className="border-border/50 bg-card/50 hover:border-neon-primary/30 transition-all group">
        <CardHeader>
          <CardTitle className="text-neon-primary group-hover:text-neon-primary/80">
            Milestone Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {byMilestone.map((m) => (
              <div
                key={m.id}
                className="text-sm text-white/80 border border-white/20 p-3"
              >
                <div className="flex items-center justify-between">
                  <div className="font-medium text-white/90">{m.name}</div>
                  {m.alert && (
                    <span className="text-xs text-yellow-300">
                      {Math.round(m.usagePct * 100)}% used
                    </span>
                  )}
                </div>
                <div className="mt-2">Planned: {formatCurrency(m.planned)}</div>
                <div>Actual: {formatCurrency(m.actual)}</div>
                <div>
                  Variance: {formatCurrency(m.variance)}
                  {m.variance < 0 ? " (over)" : ""}
                </div>
                <div className="mt-1 text-white/70">
                  Hours: {Math.round(m.hoursLogged)}h /{" "}
                  {Math.round(m.plannedHours)}h
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
