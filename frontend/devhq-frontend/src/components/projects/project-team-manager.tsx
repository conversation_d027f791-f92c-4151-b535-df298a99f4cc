
'use client'

import React from 'react';
import { Plus, Trash } from 'lucide-react';

const teamMembers = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Project Manager',
    avatar: 'https://i.pravatar.cc/150?u=a042581f4e29026024d',
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Lead Developer',
    avatar: 'https://i.pravatar.cc/150?u=a042581f4e29026704d',
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'UI/UX Designer',
    avatar: 'https://i.pravatar.cc/150?u=a04258114e29026702d',
  },
];

export function ProjectTeamManager() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-neon-primary">Team Members</h3>
        <button className="flex items-center text-gray-400 hover:text-white">
          <Plus size={16} className="mr-2" />
          Add Member
        </button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {teamMembers.map((member) => (
          <div key={member.id} className="bg-card/50 border border-border/50 rounded-lg p-4 flex items-center space-x-4">
            <img src={member.avatar} alt={member.name} className="w-12 h-12 rounded-full" />
            <div className="flex-1">
              <p className="font-semibold">{member.name}</p>
              <p className="text-sm text-gray-400">{member.role}</p>
            </div>
            <button className="text-gray-400 hover:text-red-500">
              <Trash size={16} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
