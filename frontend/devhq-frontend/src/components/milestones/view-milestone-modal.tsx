'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  X, Calendar, DollarSign, Building, FileText, Target,
  CheckCircle2, Clock, XCircle, Edit, Send, ExternalLink,
  Eye, User, MessageSquare, Star, AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ViewMilestoneModalProps {
  isOpen: boolean
  onClose: () => void
  onEdit: () => void
  milestone: any
}

const statusConfig = {
  draft: { 
    label: 'Draft', 
    color: 'text-foreground-muted', 
    bg: 'bg-foreground-muted/10', 
    border: 'border-foreground-muted/30',
    icon: FileText
  },
  pending: { 
    label: 'Pending Review', 
    color: 'text-neon-warning', 
    bg: 'bg-neon-warning/10', 
    border: 'border-neon-warning/30',
    icon: Clock
  },
  in_review: { 
    label: 'In Review', 
    color: 'text-neon-info', 
    bg: 'bg-neon-info/10', 
    border: 'border-neon-info/30',
    icon: Eye
  },
  approved: { 
    label: 'Approved', 
    color: 'text-neon-success', 
    bg: 'bg-neon-success/10', 
    border: 'border-neon-success/30',
    icon: CheckCircle2
  },
  rejected: { 
    label: 'Rejected', 
    color: 'text-neon-error', 
    bg: 'bg-neon-error/10', 
    border: 'border-neon-error/30',
    icon: XCircle
  }
}

const priorityConfig = {
  high: { label: 'High', color: 'text-neon-error', bg: 'bg-neon-error/10' },
  medium: { label: 'Medium', color: 'text-neon-warning', bg: 'bg-neon-warning/10' },
  low: { label: 'Low', color: 'text-neon-info', bg: 'bg-neon-info/10' }
}

export function ViewMilestoneModal({ isOpen, onClose, onEdit, milestone }: ViewMilestoneModalProps) {
  if (!isOpen || !milestone) return null

  const status = statusConfig[milestone.status as keyof typeof statusConfig]
  const priority = priorityConfig[milestone.priority as keyof typeof priorityConfig]
  const StatusIcon = status.icon

  const isOverdue = new Date(milestone.dueDate) < new Date() && milestone.status !== 'approved'

  const generateClientPortalLink = () => {
    // In a real app, this would generate a secure link
    return `${window.location.origin}/client-portal?milestone=${milestone.id}&token=secure-token`
  }

  const copyPortalLink = () => {
    const link = generateClientPortalLink()
    navigator.clipboard.writeText(link)
    // You could show a toast notification here
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-background/20 backdrop-blur-xl border border-border/40 shadow-2xl">
        {/* Glass reflection effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-60 rounded-lg" />
        
        {/* Glass highlight edge */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
        
        <CardHeader className="border-b border-border/50 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-neon-primary rounded-full animate-pulse" />
              <CardTitle className="text-2xl font-mono text-neon-primary">
                {milestone.title}
              </CardTitle>
              {milestone.starred && (
                <Star className="h-5 w-5 text-neon-warning fill-current" />
              )}
            </div>
            <div className="flex items-center space-x-2">
              {milestone.status === 'draft' && (
                <Button variant="outline" size="sm" className="bg-background/20 backdrop-blur-md border-border/30">
                  <Send className="h-4 w-4 mr-2" />
                  Submit for Review
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={copyPortalLink} className="bg-background/20 backdrop-blur-md border-border/30">
                <ExternalLink className="h-4 w-4 mr-2" />
                Copy Portal Link
              </Button>
              <Button variant="outline" size="sm" onClick={onEdit} className="bg-background/20 backdrop-blur-md border-border/30">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 mt-2">
            <Badge className={cn("text-sm font-mono", status.color, status.bg, status.border)}>
              <StatusIcon className="h-4 w-4 mr-1" />
              {status.label}
            </Badge>
            <Badge variant="outline" className={cn("text-sm", priority.color, priority.bg)}>
              {priority.label} Priority
            </Badge>
            <div className="text-sm text-foreground-muted font-mono">
              Value: ${milestone.amount.toLocaleString()}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Project Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Project Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Building className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Client</span>
                    </div>
                    <p className="font-medium text-foreground">{milestone.clientName}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Target className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Project</span>
                    </div>
                    <p className="font-medium text-foreground">{milestone.projectName}</p>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-foreground">Description</h3>
                <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
                  <p className="text-foreground-muted leading-relaxed">
                    {milestone.description}
                  </p>
                </div>
              </div>

              {/* Deliverables */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Deliverables</h3>
                <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
                  <div className="space-y-3">
                    {milestone.deliverables.map((deliverable: string, index: number) => (
                      <div key={index} className="flex items-start space-x-3">
                        <CheckCircle2 className="h-5 w-5 text-neon-success mt-0.5 flex-shrink-0" />
                        <span className="text-foreground">{deliverable}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Status-specific Information */}
              {milestone.status === 'approved' && milestone.approvedDate && (
                <div className="bg-neon-success/10 border border-neon-success/30 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <CheckCircle2 className="h-5 w-5 text-neon-success" />
                    <div>
                      <p className="font-medium text-neon-success">Milestone Approved</p>
                      <p className="text-sm text-foreground-muted">
                        Approved on {new Date(milestone.approvedDate).toLocaleDateString('en-US')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {milestone.status === 'rejected' && milestone.clientFeedback && (
                <div className="bg-neon-error/10 border border-neon-error/30 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <XCircle className="h-5 w-5 text-neon-error mt-0.5" />
                    <div className="flex-1">
                      <p className="font-medium text-neon-error mb-2">Client Feedback</p>
                      <p className="text-sm text-foreground-muted">{milestone.clientFeedback}</p>
                    </div>
                  </div>
                </div>
              )}

              {milestone.status === 'in_review' && milestone.clientFeedback && (
                <div className="bg-neon-info/10 border border-neon-info/30 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <MessageSquare className="h-5 w-5 text-neon-info mt-0.5" />
                    <div className="flex-1">
                      <p className="font-medium text-neon-info mb-2">Client Feedback</p>
                      <p className="text-sm text-foreground-muted">{milestone.clientFeedback}</p>
                    </div>
                  </div>
                </div>
              )}

              {isOverdue && (
                <div className="bg-neon-error/10 border border-neon-error/30 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <AlertCircle className="h-5 w-5 text-neon-error" />
                    <div>
                      <p className="font-medium text-neon-error">Milestone Overdue</p>
                      <p className="text-sm text-foreground-muted">
                        This milestone was due on {new Date(milestone.dueDate).toLocaleDateString('en-US')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Internal Notes */}
              {milestone.notes && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-foreground">Internal Notes</h3>
                  <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
                    <p className="text-foreground-muted leading-relaxed">
                      {milestone.notes}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Key Details */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Target className="h-4 w-4 mr-2" />
                    Key Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Value</span>
                    </div>
                    <span className="text-lg font-bold font-mono text-neon-primary">
                      ${milestone.amount.toLocaleString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Due Date</span>
                    </div>
                    <span className={cn(
                      "text-sm font-mono",
                      isOverdue ? "text-neon-error" : "text-foreground"
                    )}>
                      {new Date(milestone.dueDate).toLocaleDateString('en-US')}
                    </span>
                  </div>

                  {milestone.submittedDate && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Send className="h-4 w-4 text-foreground-muted" />
                        <span className="text-sm text-foreground-muted">Submitted</span>
                      </div>
                      <span className="text-sm font-mono text-foreground">
                        {new Date(milestone.submittedDate).toLocaleDateString('en-US')}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4 text-foreground-muted" />
                      <span className="text-sm text-foreground-muted">Deliverables</span>
                    </div>
                    <span className="text-sm font-mono text-foreground">
                      {milestone.deliverables.length} items
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Timeline */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-neon-primary rounded-full mt-2" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-foreground">Milestone Created</p>
                        <p className="text-xs text-foreground-muted">Draft status</p>
                      </div>
                    </div>
                    
                    {milestone.submittedDate && (
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-neon-warning rounded-full mt-2" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">Submitted for Review</p>
                          <p className="text-xs text-foreground-muted">
                            {new Date(milestone.submittedDate).toLocaleDateString('en-US')}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {milestone.approvedDate && (
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-neon-success rounded-full mt-2" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">Approved by Client</p>
                          <p className="text-xs text-foreground-muted">
                            {new Date(milestone.approvedDate).toLocaleDateString('en-US')}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="bg-background/20 backdrop-blur-md border-border/30">
                <CardHeader>
                  <CardTitle className="text-base font-mono text-neon-primary flex items-center">
                    <Target className="h-4 w-4 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" size="sm" onClick={copyPortalLink} className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Copy Portal Link
                  </Button>
                  {milestone.status === 'draft' && (
                    <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                      <Send className="h-4 w-4 mr-2" />
                      Submit for Review
                    </Button>
                  )}
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <User className="h-4 w-4 mr-2" />
                    Contact Client
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start bg-background/20 backdrop-blur-md border-border/30">
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}