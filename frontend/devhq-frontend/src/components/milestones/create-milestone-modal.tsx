'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Save, ChevronDown, Plus, X, Target } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CreateMilestoneModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (milestoneData: any) => void
  projects?: any[]
  clients?: any[]
}

const priorityOptions = [
  { value: 'low', label: 'Low', color: 'text-neon-info' },
  { value: 'medium', label: 'Medium', color: 'text-neon-warning' },
  { value: 'high', label: 'High', color: 'text-neon-error' }
]

export function CreateMilestoneModal({ isOpen, onClose, onSubmit, projects = [], clients = [] }: CreateMilestoneModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    projectName: '',
    clientName: '',
    amount: '',
    dueDate: '',
    priority: 'medium',
    deliverables: [''],
    notes: ''
  })

  useEffect(() => {
    if (!isOpen) {
      // Reset form when modal closes
      setFormData({
        title: '',
        description: '',
        projectName: '',
        clientName: '',
        amount: '',
        dueDate: '',
        priority: 'medium',
        deliverables: [''],
        notes: ''
      })
    }
  }, [isOpen])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({ ...prev, [id]: value }))
  }

  const handleDropdownChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addDeliverable = () => {
    setFormData(prev => ({
      ...prev,
      deliverables: [...prev.deliverables, '']
    }))
  }

  const removeDeliverable = (index: number) => {
    setFormData(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter((_, i) => i !== index)
    }))
  }

  const updateDeliverable = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      deliverables: prev.deliverables.map((item, i) => i === index ? value : item)
    }))
  }

  const handleSubmit = () => {
    const milestoneData = {
      ...formData,
      amount: parseFloat(formData.amount) || 0,
      deliverables: formData.deliverables.filter(d => d.trim() !== ''),
      status: 'draft',
      submittedDate: null,
      approvedDate: null,
      clientFeedback: null,
      starred: false
    }

    onSubmit(milestoneData)
    onClose()
  }

  const isFormValid = formData.title && formData.description && formData.projectName && 
                     formData.clientName && formData.amount && formData.dueDate &&
                     formData.deliverables.some(d => d.trim() !== '')

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-background-elevated/95 backdrop-blur-md border-border text-foreground max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-neon-primary font-mono flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Create New Milestone
          </DialogTitle>
          <DialogDescription className="text-foreground-muted">
            Define a new project milestone for client approval.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4 overflow-y-auto flex-1 pr-2">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-foreground-secondary font-mono text-xs">Milestone Title *</Label>
                <Input 
                  id="title" 
                  value={formData.title} 
                  onChange={handleChange} 
                  placeholder="e.g., User Authentication System" 
                  className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="amount" className="text-foreground-secondary font-mono text-xs">Milestone Value ($) *</Label>
                <Input 
                  id="amount" 
                  type="number" 
                  step="0.01"
                  value={formData.amount} 
                  onChange={handleChange} 
                  placeholder="e.g., 5000" 
                  className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-foreground-secondary font-mono text-xs">Description *</Label>
              <Textarea 
                id="description" 
                value={formData.description} 
                onChange={handleChange} 
                placeholder="Detailed description of what this milestone includes..." 
                rows={3} 
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
              />
            </div>
          </div>

          {/* Project and Client Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Project Assignment</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-foreground-secondary font-mono text-xs">Project *</Label>
                {projects.length > 0 ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary">
                        {formData.projectName || 'Select Project'}
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                      {projects.map((project) => (
                        <DropdownMenuItem 
                          key={project.id} 
                          onClick={() => {
                            handleDropdownChange('projectName', project.name)
                            handleDropdownChange('clientName', project.client)
                          }} 
                          className="text-xs"
                        >
                          {project.name} - {project.client}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Input 
                    id="projectName" 
                    value={formData.projectName} 
                    onChange={handleChange} 
                    placeholder="Enter project name" 
                    className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
                  />
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-foreground-secondary font-mono text-xs">Client *</Label>
                {clients.length > 0 ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary">
                        {formData.clientName || 'Select Client'}
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                      {clients.map((client) => (
                        <DropdownMenuItem 
                          key={client.id} 
                          onClick={() => handleDropdownChange('clientName', client.name)} 
                          className="text-xs"
                        >
                          {client.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Input 
                    id="clientName" 
                    value={formData.clientName} 
                    onChange={handleChange} 
                    placeholder="Enter client name" 
                    className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
                  />
                )}
              </div>
            </div>
          </div>

          {/* Timeline and Priority */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Timeline & Priority</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dueDate" className="text-foreground-secondary font-mono text-xs">Due Date *</Label>
                <Input 
                  id="dueDate" 
                  type="date" 
                  value={formData.dueDate} 
                  onChange={handleChange} 
                  className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
                />
              </div>

              <div className="space-y-2">
                <Label className="text-foreground-secondary font-mono text-xs">Priority</Label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full justify-between bg-background border-border/50 focus-visible:border-neon-primary">
                      <span className={priorityOptions.find(p => p.value === formData.priority)?.color}>
                        {priorityOptions.find(p => p.value === formData.priority)?.label}
                      </span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-background-elevated/95 backdrop-blur-md border border-border">
                    {priorityOptions.map((option) => (
                      <DropdownMenuItem 
                        key={option.value}
                        onClick={() => handleDropdownChange('priority', option.value)}
                        className="text-xs"
                      >
                        <span className={option.color}>{option.label}</span>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {/* Deliverables */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-foreground">Deliverables</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addDeliverable}
                className="border-neon-primary/30 text-neon-primary hover:bg-neon-primary/10"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>
            
            <div className="space-y-2">
              {formData.deliverables.map((deliverable, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    value={deliverable}
                    onChange={(e) => updateDeliverable(index, e.target.value)}
                    placeholder={`Deliverable ${index + 1}`}
                    className="flex-1 bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                  {formData.deliverables.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeDeliverable(index)}
                      className="text-neon-error hover:bg-neon-error/10"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Additional Notes */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Additional Information</h3>
            
            <div className="space-y-2">
              <Label htmlFor="notes" className="text-foreground-secondary font-mono text-xs">Internal Notes</Label>
              <Textarea 
                id="notes" 
                value={formData.notes} 
                onChange={handleChange} 
                placeholder="Internal notes, requirements, or special instructions..." 
                rows={3} 
                className="bg-background border-border/50 focus-visible:border-neon-primary focus-visible:ring-0 focus-visible:ring-offset-0" 
              />
            </div>
          </div>

          {/* Preview Summary */}
          {isFormValid && (
            <div className="bg-background/20 backdrop-blur-md border border-border/30 rounded-lg p-4">
              <h4 className="text-sm font-mono text-neon-primary mb-3">Milestone Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Title:</span>
                  <span className="text-foreground font-medium">{formData.title}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Value:</span>
                  <span className="font-mono text-neon-primary">${parseFloat(formData.amount || '0').toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Due Date:</span>
                  <span className="font-mono text-foreground">{formData.dueDate ? new Date(formData.dueDate).toLocaleDateString('en-US') : '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Deliverables:</span>
                  <span className="text-foreground">{formData.deliverables.filter(d => d.trim() !== '').length} items</span>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0 border-t border-border/30 pt-4">
          <Button variant="ghost" onClick={onClose}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            className="bg-neon-primary hover:bg-neon-primary/90 text-background"
            disabled={!isFormValid}
          >
            <Save className="h-4 w-4 mr-2" />
            Create Milestone
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}