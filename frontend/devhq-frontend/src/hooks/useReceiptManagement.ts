/** @format */

"use client";

import { useState } from "react";

export interface Receipt {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadDate: string;
  amount?: number;
  description?: string;
  category?: string;
  isProcessed: boolean;
  extractedData?: {
    vendor?: string;
    date?: string;
    amount?: number;
    items?: string[];
  };
  thumbnailUrl?: string;
  fileUrl: string;
}

const mockReceipts: Receipt[] = [
  {
    id: "1",
    fileName: "figma-subscription-jan-2024.pdf",
    fileSize: 245760,
    fileType: "application/pdf",
    uploadDate: "2024-01-15T10:30:00Z",
    amount: 29.99,
    description: "Figma Pro Subscription",
    category: "Software",
    isProcessed: true,
    extractedData: {
      vendor: "Figma Inc.",
      date: "2024-01-15",
      amount: 29.99,
      items: ["Figma Pro Monthly Subscription"],
    },
    fileUrl: "/receipts/figma-jan-2024.pdf",
  },
  {
    id: "2",
    fileName: "office-supplies-receipt.jpg",
    fileSize: 1048576,
    fileType: "image/jpeg",
    uploadDate: "2024-01-12T14:20:00Z",
    amount: 150.0,
    description: "Office Supplies Purchase",
    category: "Office Expenses",
    isProcessed: true,
    extractedData: {
      vendor: "Staples",
      date: "2024-01-12",
      amount: 150.0,
      items: ["Notebooks", "Pens", "Printer Paper", "Desk Organizer"],
    },
    fileUrl: "/receipts/office-supplies.jpg",
  },
  {
    id: "3",
    fileName: "aws-invoice-december.pdf",
    fileSize: 512000,
    fileType: "application/pdf",
    uploadDate: "2024-01-10T09:15:00Z",
    amount: 89.45,
    description: "AWS Cloud Services",
    category: "Software",
    isProcessed: false,
    extractedData: {
      vendor: "Amazon Web Services",
      date: "2024-01-01",
      amount: 89.45,
      items: ["EC2 Instances", "S3 Storage", "CloudFront"],
    },
    fileUrl: "/receipts/aws-december.pdf",
  },
  {
    id: "4",
    fileName: "client-lunch-receipt.heic",
    fileSize: 2097152,
    fileType: "image/heic",
    uploadDate: "2024-01-08T16:45:00Z",
    amount: 85.5,
    description: "Client Business Lunch",
    category: "Meals",
    isProcessed: false,
    extractedData: {
      vendor: "The Bistro Restaurant",
      date: "2024-01-08",
      amount: 85.5,
      items: ["Business Lunch for 2"],
    },
    fileUrl: "/receipts/client-lunch.heic",
  },
];

export function useReceiptManagement() {
  const [receipts, setReceipts] = useState<Receipt[]>(mockReceipts);

  const addReceipt = (receipt: Omit<Receipt, "id">) => {
    const newReceipt: Receipt = {
      ...receipt,
      id: Date.now().toString(),
    };
    setReceipts((prev) => [newReceipt, ...prev]);
  };

  const updateReceipt = (id: string, updates: Partial<Receipt>) => {
    setReceipts((prev) =>
      prev.map((receipt) =>
        receipt.id === id ? { ...receipt, ...updates } : receipt
      )
    );
  };

  const deleteReceipt = (id: string) => {
    setReceipts((prev) => prev.filter((receipt) => receipt.id !== id));
  };

  const processReceipt = async (id: string) => {
    // Simulate AI processing
    updateReceipt(id, { isProcessed: true });

    // In a real app, this would call an AI service
    setTimeout(() => {
      const receipt = receipts.find((r) => r.id === id);
      if (receipt) {
        updateReceipt(id, {
          extractedData: {
            vendor: "AI Detected Vendor",
            date: new Date().toISOString().split("T")[0],
            amount: Math.random() * 100 + 10,
            items: ["AI Detected Item 1", "AI Detected Item 2"],
          },
        });
      }
    }, 2000);
  };

  const getReceiptsByCategory = (category: string) => {
    return receipts.filter((receipt) => receipt.category === category);
  };

  const getTotalReceiptAmount = () => {
    return receipts.reduce(
      (total, receipt) => total + (receipt.amount || 0),
      0
    );
  };

  const getUnprocessedCount = () => {
    return receipts.filter((receipt) => !receipt.isProcessed).length;
  };

  const getReceiptStats = () => {
    const total = receipts.length;
    const processed = receipts.filter((r) => r.isProcessed).length;
    const totalAmount = getTotalReceiptAmount();
    const categories = Array.from(
      new Set(receipts.map((r) => r.category).filter(Boolean))
    );

    return {
      total,
      processed,
      unprocessed: total - processed,
      totalAmount,
      categories: categories.length,
    };
  };

  return {
    receipts,
    addReceipt,
    updateReceipt,
    deleteReceipt,
    processReceipt,
    getReceiptsByCategory,
    getTotalReceiptAmount,
    getUnprocessedCount,
    getReceiptStats,
  };
}
