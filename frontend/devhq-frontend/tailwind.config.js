/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        "neon-primary": {
          DEFAULT: "hsl(var(--neon-primary))",
          foreground: "hsl(var(--background))",
          glow: "hsl(var(--neon-primary-glow))",
          dim: "hsl(var(--neon-primary-dim))",
          bg: "hsl(var(--neon-primary-bg))",
        },
        "neon-success": {
          DEFAULT: "hsl(var(--neon-success))",
          foreground: "hsl(var(--background))",
        },
        "neon-warning": {
          DEFAULT: "hsl(var(--neon-warning))",
          foreground: "hsl(var(--background))",
        },
        "neon-error": {
          DEFAULT: "hsl(var(--neon-error))",
          foreground: "hsl(var(--background))",
        },
        "neon-info": {
          DEFAULT: "hsl(var(--neon-info))",
          foreground: "hsl(var(--background))",
        },
        "neon-electric": {
          DEFAULT: "hsl(var(--neon-electric))",
          foreground: "hsl(var(--background))",
        },
      },
      fontFamily: {
        sans: ["Inter", "sans-serif"],
        mono: ["JetBrains Mono", "monospace"],
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "text-glow": {
          "0%, 100%": { 
            textShadow: "0 0 5px hsl(var(--neon-primary)), 0 0 10px hsl(var(--neon-primary)), 0 0 15px hsl(var(--neon-primary))"
          },
          "50%": { 
            textShadow: "0 0 10px hsl(var(--neon-primary)), 0 0 20px hsl(var(--neon-primary)), 0 0 30px hsl(var(--neon-primary))"
          },
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "text-glow": "text-glow 2s ease-in-out infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}